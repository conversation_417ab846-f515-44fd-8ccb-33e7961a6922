<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑预案 - 应急管理系统</title>
    <!-- 直接引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <!-- Add Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #4b5563;
            transition: all 0.2s;
        }
        
        .sidebar-menu-item:hover, .sidebar-menu-item.active {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        
        .sidebar-menu-item.active {
            border-left: 3px solid #2563eb;
        }
        
        /* 移除 .main-content 类定义 */
        /* .main-content {
            transition: margin-left 0.3s;
        } */
        
        .sidebar {
            width: 250px;
            transition: all 0.3s;
            z-index: 40;
        }
        
        .sidebar.collapsed {
            width: 0px;
            transform: translateX(-100%);
        }
        
        @media (max-width: 768px) {
            body .main-content {
                margin-left: 0;
            }
            
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.expanded {
                transform: translateX(0);
            }
        }

        /* 组织机构树形结构样式 */
        .org-tree {
            background: white;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .tree-node {
            position: relative;
        }

        .tree-node .children {
            position: relative;
            padding-left: 24px;
            margin-top: 4px;
        }

        .tree-node .children::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 12px;
            width: 1px;
            background: #d1d5db;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .node-content:hover {
            background: #f3f4f6;
            border-color: #d1d5db;
        }

        .toggle-icon {
            transition: transform 0.2s;
        }

        .toggle-icon.expanded {
            transform: rotate(90deg);
        }

        .node-details {
            transition: all 0.3s ease;
            overflow: hidden;
            margin: 0 0 12px 24px;
        }

        .root-node {
            padding-left: 0;
        }

        .root-node > .node-content {
            background: #e0e7ff;
            border-color: #c7d2fe;
        }
        
        .node-actions {
            display: flex;
        }
        
        .node-actions button {
            background: none;
            border: none;
            cursor: pointer;
        }
        
        .node-title {
            font-weight: 500;
            margin-right: 8px;
        }
        
        /* 增加动画效果 */
        .node-details.expanding {
            max-height: 0;
            opacity: 0;
        }
        
        .node-details.expanded {
            max-height: 1000px;
            opacity: 1;
            transition: max-height 0.3s ease, opacity 0.2s ease;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navbar Start -->
    <header>
        <h1>广西公路水路安全畅通与应急处置系统</h1>
        <nav class="tab-navigation">
            <a href="index.html" class="tab-button">总览</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <!-- Navbar End -->

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container" style="display: flex; height: 100vh;"> 
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area - Ensure it has id="main-content" and adjust padding if needed -->
        <main id="main-content" class="flex-1 pb-8 px-6 bg-gray-100 overflow-y-auto">
            <div class="py-6">
                <!-- 页面标题 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">编辑预案</h2>
                        <p class="text-gray-600 mt-1">请填写预案相关信息，支持分章节编辑</p>
                        </div>
                    <div class="space-x-2">
                        <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            <i class="fas fa-save mr-2"></i> 保存草稿
                        </button>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-check-circle mr-2"></i> 提交
                        </button>
                    </div>
                </div>
                
                <!-- 标签页导航 -->
                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <div class="p-4 sm:px-6">
                        <nav class="flex space-x-4 overflow-x-auto pb-1">
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none" data-tab="basic-info">
                                基本信息 & 总则
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="organization">
                                组织体系
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="prevention">
                                预防与预警
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="response">
                                应急响应
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="post-disposal">
                                后期处置
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="support">
                                应急保障
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="management">
                                预案管理
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="attachments">
                                附件管理
                            </button>
                        </nav>
                    </div>
                </div>
                
                <!-- 标签页内容区域 -->
                <div class="bg-white rounded-b-lg shadow-md p-6">
                    <!-- 基本信息 & 总则 -->
                    <div id="basic-info" class="tab-content active">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="plan_name" class="block text-sm font-medium text-gray-700 mb-1">预案名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="plan_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入预案名称" value="某区低温雨雪冰冻灾害应急预案">
                            </div>
                            
                            <div>
                                <label for="plan_type" class="block text-sm font-medium text-gray-700 mb-1">预案类型 <span class="text-red-500">*</span></label>
                                <!-- Replace with el-select -->
                                <el-select v-model="formData.planType" placeholder="请选择预案类型" class="block w-full">
                                    <el-option v-for="item in planTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                </el-select>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <label for="compile_unit" class="block text-sm font-medium text-gray-700 mb-1">编制单位 <span class="text-red-500">*</span></label>
                                <!-- Replace with el-tree-select -->
                                <el-tree-select
                                    v-model="formData.compileUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true" 
                                    placeholder="请选择编制单位"
                                    class="block w-full"
                                    clearable
                                />
                            </div>
                            
                            <div>
                                <label for="apply_unit" class="block text-sm font-medium text-gray-700 mb-1">适用单位</label>
                                <!-- Replace with el-tree-select multiple -->
                                <el-tree-select
                                    v-model="formData.applyUnits"
                                    :data="unitOptions"
                                    :multiple="true"
                                    :check-strictly="true" 
                                    placeholder="请选择适用单位 (可多选)"
                                    class="block w-full"
                                    clearable
                                    show-checkbox 
                                    collapse-tags
                                    collapse-tags-tooltip
                                />
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="purpose" class="block text-sm font-medium text-gray-700 mb-1">编制目的</label>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                    </div>
                                </div>
                                <textarea id="purpose" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入编制目的">为有效应对低温雨雪冰冻天气，最大限度减轻灾害影响和损失，保障人民群众生命财产安全，维护社会稳定，特制定本预案。</textarea>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="basis" class="block text-sm font-medium text-gray-700 mb-1">编制依据</label>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                    </div>
                                </div>
                                <textarea id="basis" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入编制依据">依据《中华人民共和国突发事件应对法》、《中华人民共和国气象法》、《国家自然灾害救助应急预案》等法律法规和相关规定。</textarea>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="scope" class="block text-sm font-medium text-gray-700 mb-1">适用范围</label>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                    </div>
                                </div>
                                <textarea id="scope" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入适用范围">本预案适用于全区范围内发生的低温雨雪冰冻灾害事件的预防、监测预警、应急处置和救援工作。</textarea>
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">事件分级与响应条件</label>
                            <div class="border border-gray-300 rounded-md p-4 mb-2">
                                <div class="flex justify-between items-center mb-3">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">1</span>
                                        <span class="ml-2 text-sm font-medium text-gray-700">Ⅰ级 - 特别重大</span>
                                    </div>
                                    <button class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                        <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                                <div class="border border-gray-300 rounded-md">
                                    <textarea rows="2" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入响应启动条件">全区范围内出现大范围持续性强降雪、低温、道路结冰等灾害，造成交通、电力、通信等基础设施大面积损毁，或造成重大人员伤亡。</textarea>
                </div>
            </div>
                                    
                            <div class="border border-gray-300 rounded-md p-4 mb-2">
                                <div class="flex justify-between items-center mb-3">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 text-blue-800 text-xs font-medium">2</span>
                                        <span class="ml-2 text-sm font-medium text-gray-700">II级 - 重大</span>
    </div>
                                    <button class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                        </div>
                                <div class="border border-gray-300 rounded-md">
                                    <textarea rows="2" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入响应启动条件">区域性强降雪、低温、冰冻灾害，造成交通阻断、电力中断等影响，部分地区受灾严重。</textarea>
                        </div>
                        </div>
                                    
                            <button class="mt-2 inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm font-medium rounded text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-plus mr-1"></i> 添加级别
                            </button>
                        </div>
                        
                        <div class="mb-6">
                            <label for="principles" class="block text-sm font-medium text-gray-700 mb-1">工作原则</label>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                            </div>
                            </div>
                                    <textarea id="principles" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入工作原则">1. 以人为本，安全第一。把保障人民群众生命财产安全作为首要任务。
2. 统一领导，分级负责。在区委、区政府统一领导下，各部门、各镇（街道）按照职责分工，分级负责。
3. 快速反应，协同应对。建立健全应急机制，提高快速反应能力，确保应对及时有效。 
4. 依靠科技，加强协作。充分发挥专业技术人员作用，提高应急处置科技水平和指挥能力。</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 组织体系 -->
                        <div id="organization" class="tab-content">
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium text-gray-800">应急指挥机构设置</h3>
                                    <div class="flex space-x-2">
                                        <!-- Removed preview button -->
                                        <button type="button" id="add-org-btn" class="ml-auto bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-sm flex items-center">
                                            <i class="fas fa-plus mr-1"></i> 新增机构
                                        </button>
                                    </div>
                                </div>

                                <!-- Removed Operation Logs Section -->

                                <!-- 组织机构树形结构 -->
                                <div class="org-tree p-4 border border-gray-300 rounded-md mb-6">
                                    <!-- 顶层机构 - 领导小组 -->
                                    <div class="tree-node root-node">
                                        <div class="node-content">
                                            <div class="flex items-center flex-1">
                                                <i class="fas fa-caret-down text-gray-600 mr-2 toggle-icon"></i>
                                                <span class="node-title">领导小组</span>
                                                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">顶级机构</span>
                                                <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级)</span>
                                            </div>
                                            <div class="node-actions">
                                                <button title="添加子机构" class="text-gray-600 hover:text-gray-800 focus:outline-none text-sm mr-2">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                    <i class="fas fa-trash-alt"></i>
                                                </button>
                                            </div>
                                        </div>
                                        
                                        <!-- 展开后显示的详细信息 -->
                                        <div class="node-details expanded bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                        负责统筹协调全区低温雨雪冰冻灾害的应急处置工作，研究决定应对灾害的重大事项。
                                                    </div>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                        <div class="flex flex-wrap gap-2">
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">区长（组长）</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">副区长（副组长）</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">应急管理局局长</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">公安局局长</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">交通运输局局长</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                <div class="flex flex-wrap gap-2">
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 子机构列表 -->
                                        <div class="children">
                                            <!-- 领导小组办公室 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="flex items-center flex-1">
                                                        <i class="fas fa-caret-right text-gray-600 mr-2 toggle-icon"></i>
                                                        <span class="node-title">领导小组办公室</span>
                                                        <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">二级机构</span>
                                                        <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级、III级)</span>
                                                    </div>
                                                    <div class="node-actions">
                                                        <button title="添加子机构" class="text-gray-600 hover:text-gray-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- 详情区域默认隐藏 -->
                                                <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                            <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                负责组织协调小组日常工作，组织开展灾情收集、信息汇总、事态分析等工作，传达小组决策部署。
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                            <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                <div class="flex flex-wrap gap-2">
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">应急管理局（办公室设在应急管理局）</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">区政府办公室</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                        <div class="flex flex-wrap gap-2">
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">III级-较大</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 应急工作组 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="flex items-center flex-1">
                                                        <i class="fas fa-caret-right text-gray-600 mr-2 toggle-icon"></i>
                                                        <span class="node-title">应急工作组</span>
                                                        <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">二级机构</span>
                                                        <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级、III级)</span>
                                                    </div>
                                                    <div class="node-actions">
                                                        <button title="添加子机构" class="text-gray-600 hover:text-gray-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- 详情区域默认隐藏 -->
                                                <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                            <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                根据分工负责相关应急处置工作，各司其职，协同配合。
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                            <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                <div class="flex flex-wrap gap-2">
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">由各相关部门及单位组成</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                        <div class="flex flex-wrap gap-2">
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">III级-较大</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 应急工作组的子机构 -->
                                                <div class="children">
                                                    <!-- 综合协调组 -->
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="flex items-center flex-1">
                                                                <i class="fas fa-angle-right text-gray-600 mr-2"></i>
                                                                <span class="node-title">综合协调组</span>
                                                                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">三级机构</span>
                                                                <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级、III级)</span>
                                                            </div>
                                                            <div class="node-actions">
                                                                <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- 详情区域默认隐藏 -->
                                                        <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                        负责灾情信息收集、汇总、分析研判和报送，协调各工作组行动，做好事件处置的综合协调工作。
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                        <div class="flex flex-wrap gap-2">
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">应急管理局（牵头）</span>
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">区政府办公室</span>
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">气象局</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                                <div class="flex flex-wrap gap-2">
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">III级-较大</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 公路保障组 -->
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="flex items-center flex-1">
                                                                <i class="fas fa-angle-right text-gray-600 mr-2"></i>
                                                                <span class="node-title">公路保障组</span>
                                                                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">三级机构</span>
                                                                <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级、III级)</span>
                                                            </div>
                                                            <div class="node-actions">
                                                                <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- 详情区域默认隐藏 -->
                                                        <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                        负责组织实施公路除雪、融冰和抢修，保障公路道路畅通。
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                        <div class="flex flex-wrap gap-2">
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">交通运输局（牵头）</span>
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">公路养护部门</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                                <div class="flex flex-wrap gap-2">
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">III级-较大</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    
                                                    <!-- 道路运输保障组 -->
                                                    <div class="tree-node">
                                                        <div class="node-content">
                                                            <div class="flex items-center flex-1">
                                                                <i class="fas fa-angle-right text-gray-600 mr-2"></i>
                                                                <span class="node-title">道路运输保障组</span>
                                                                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">三级机构</span>
                                                                <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级)</span>
                                                            </div>
                                                            <div class="node-actions">
                                                                <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                                    <i class="fas fa-edit"></i>
                                                                </button>
                                                                <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                                    <i class="fas fa-trash-alt"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- 详情区域默认隐藏 -->
                                                        <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                        负责调配运力，保障应急救援物资、救援队伍和受困人员疏散的运输工作。
                                                                    </div>
                                                                </div>
                                                                <div>
                                                                    <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                                    <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                        <div class="flex flex-wrap gap-2">
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">交通运输局（牵头）</span>
                                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">公安局</span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div>
                                                                <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                                <div class="flex flex-wrap gap-2">
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 专家组 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="flex items-center flex-1">
                                                        <i class="fas fa-angle-right text-gray-600 mr-2"></i>
                                                        <span class="node-title">专家组</span>
                                                        <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">二级机构</span>
                                                        <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级)</span>
                                                    </div>
                                                    <div class="node-actions">
                                                        <button title="添加子机构" class="text-gray-600 hover:text-gray-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- 详情区域默认隐藏 -->
                                                <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                            <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                为应急处置工作提供决策咨询和技术支持。
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                            <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                                <div class="flex flex-wrap gap-2">
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">低温雨雪冰冻灾害相关领域专家</span>
                                                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">应急管理专家</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                        <div class="flex flex-wrap gap-2">
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 现场工作组 -->
                                            <div class="tree-node">
                                                <div class="node-content">
                                                    <div class="flex items-center flex-1">
                                                        <i class="fas fa-angle-right text-gray-600 mr-2"></i>
                                                        <span class="node-title">现场工作组</span>
                                                        <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">二级机构</span>
                                                        <span class="ml-2 text-xs text-gray-500">(Ⅰ级、II级)</span>
                                                    </div>
                                                    <div class="node-actions">
                                                        <button title="添加子机构" class="text-gray-600 hover:text-gray-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button title="编辑" class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm mr-2">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button title="删除" class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                            <i class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- 详情区域默认隐藏 -->
                                                <div class="node-details hidden bg-gray-50 p-4 border border-gray-200 rounded-md mb-3">
                                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                                        <div>
                                                            <label class="block text-sm font-medium text-gray-700 mb-1">主要职责</label>
                                                        <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                            负责现场应急处置工作的组织协调和指挥。
                                                        </div>
                                                    </div>
                                                    <div>
                                                        <label class="block text-sm font-medium text-gray-700 mb-1">关联人员/单位</label>
                                                        <div class="bg-white p-3 rounded-md text-sm text-gray-700 border border-gray-200">
                                                            <div class="flex flex-wrap gap-2">
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">由分管副区长带队</span>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">相关部门负责人</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别</label>
                                                    <div class="flex flex-wrap gap-2">
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">Ⅰ级-特别重大</span>
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">II级-重大</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </div>
                    
                    <!-- 新增机构模态框 -->
                    <div id="add-org-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center hidden overflow-y-auto py-16">
                        <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl mx-4 flex flex-col">
                            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">新增机构</h3>
                                <button id="close-org-modal" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="px-6 py-4 flex-grow overflow-y-auto min-h-0">
                                <form id="orgForm">
                                    <input type="hidden" id="editing-node-id" value="">
                                    <div class="mb-4">
                                        <label for="org-name" class="block text-sm font-medium text-gray-700 mb-1">机构名称 <span class="text-red-500">*</span></label>
                                        <input type="text" id="org-name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500" required>
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">机构级别 <span class="text-red-500">*</span></label>
                                        <div class="flex items-center space-x-4">
                                            <label class="inline-flex items-center">
                                                <input type="radio" name="org_level" value="top" class="text-blue-600 focus:ring-blue-500">
                                                <span class="ml-2">顶级机构</span>
                                            </label>
                                            <label class="inline-flex items-center">
                                                <input type="radio" name="org_level" value="sub" class="text-blue-600 focus:ring-blue-500">
                                                <span class="ml-2">下级机构</span>
                                            </label>
                                        </div>
                                    </div>
                                    <div id="parent-org-field" class="mb-4 hidden">
                                        <label for="parent-org-select" class="block text-sm font-medium text-gray-700 mb-1">上级机构 <span class="text-red-500">*</span></label>
                                        <select id="parent-org-select" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                            <option value="">请选择上级机构</option>
                                            <!-- 从组织架构中获取的机构列表 -->
                                            <option value="org-1">广西壮族自治区交通运输厅</option>
                                            <option value="org-1-1">直属事业单位及专项机构</option>
                                            <option value="org-1-1-1">自治区公路发展中心</option>
                                            <option value="org-1-1-2">自治区高速公路发展中心</option>
                                            <option value="org-1-1-3">自治区道路运输发展中心</option>
                                            <option value="org-1-2">市级交通运输局</option>
                                            <option value="org-1-2-1">钦州市交通运输局</option>
                                            <option value="org-1-2-2">南宁市交通运输局</option>
                                            <option value="org-1-2-3">玉林市交通运输局</option>
                                        </select>
                                    </div>
                                    <div class="mb-4">
                                        <label for="org-duty" class="block text-sm font-medium text-gray-700 mb-1">主要职责 <span class="text-red-500">*</span></label>
                                        <textarea id="org-duty" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500" required></textarea>
                                    </div>
                                    <div class="mb-4">
                                        <div class="flex justify-between items-center mb-1">
                                            <label class="block text-sm font-medium text-gray-700">关联人员/单位 <span class="text-red-500">*</span></label>
                                        </div>
                                        <div class="bg-gray-50 p-3 rounded-lg border">
                                            <!-- 左右两栏布局 -->
                                            <div class="flex flex-col md:flex-row">
                                                <!-- 左侧单位树形图 -->
                                                <div class="w-full md:w-1/2 md:pr-3">
                                                    <div class="mb-2 flex justify-between items-center">
                                                        <h4 class="text-sm font-medium text-gray-700">组织机构</h4>
                                                        <button type="button" class="text-xs bg-blue-50 text-blue-600 py-1 px-2 rounded hover:bg-blue-100" id="select-all-orgs">
                                                            全选
                                                        </button>
                                                    </div>
                                                    <div class="overflow-y-auto bg-white p-2 rounded border">
                                                        <!-- 树形结构 -->
                                                        <ul class="org-tree">
                                                            <li class="mb-2">
                                                                <div class="flex items-center">
                                                                    <span class="toggle-btn mr-1" data-target="tree-1"><i class="fas fa-caret-down"></i></span>
                                                                    <label class="inline-flex items-center">
                                                                        <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1">
                                                                        <span class="ml-2 text-sm">广西壮族自治区交通运输厅</span>
                                                                    </label>
                                                                </div>
                                                                <ul class="pl-6 mt-1" id="tree-1">
                                                                    <li class="mb-2">
                                                                        <div class="flex items-center">
                                                                            <span class="toggle-btn mr-1" data-target="tree-1-1"><i class="fas fa-caret-down"></i></span>
                                                                            <label class="inline-flex items-center">
                                                                                <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1">
                                                                                <span class="ml-2 text-sm">直属事业单位及专项机构</span>
                                                                            </label>
                                                                        </div>
                                                                        <ul class="pl-6 mt-1" id="tree-1-1">
                                                                            <li class="mb-1">
                                                                                <label class="inline-flex items-center">
                                                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1-1">
                                                                                    <span class="ml-2 text-sm">自治区公路发展中心</span>
                                                                                </label>
                                                                            </li>
                                                                            <li class="mb-1">
                                                                                <label class="inline-flex items-center">
                                                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1-2">
                                                                                    <span class="ml-2 text-sm">自治区高速公路发展中心</span>
                                                                                </label>
                                                                            </li>
                                                                            <li class="mb-1">
                                                                                <label class="inline-flex items-center">
                                                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-1-3">
                                                                                    <span class="ml-2 text-sm">自治区道路运输发展中心</span>
                                                                                </label>
                                                                            </li>
                                                                        </ul>
                                                                    </li>
                                                                    <li class="mb-2">
                                                                        <div class="flex items-center">
                                                                            <span class="toggle-btn mr-1" data-target="tree-1-2"><i class="fas fa-caret-down"></i></span>
                                                                            <label class="inline-flex items-center">
                                                                                <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2">
                                                                                <span class="ml-2 text-sm">市级交通运输局</span>
                                                                            </label>
                                                                        </div>
                                                                        <ul class="pl-6 mt-1" id="tree-1-2">
                                                                            <li class="mb-1">
                                                                                <label class="inline-flex items-center">
                                                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2-1">
                                                                                    <span class="ml-2 text-sm">钦州市交通运输局</span>
                                                                                </label>
                                                                            </li>
                                                                            <li class="mb-1">
                                                                                <label class="inline-flex items-center">
                                                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2-2">
                                                                                    <span class="ml-2 text-sm">南宁市交通运输局</span>
                                                                                </label>
                                                                            </li>
                                                                            <li class="mb-1">
                                                                                <label class="inline-flex items-center">
                                                                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="org-1-2-3">
                                                                                    <span class="ml-2 text-sm">玉林市交通运输局</span>
                                                                                </label>
                                                                            </li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>

                                                <!-- 右侧人员列表 -->
                                                <div class="w-full md:w-1/2 md:pl-3 mt-3 md:mt-0">
                                                    <div class="mb-2 flex justify-between items-center">
                                                        <h4 class="text-sm font-medium text-gray-700">人员列表</h4>
                                                        <div>
                                                            <input type="text" id="person-search" placeholder="搜索人员" class="text-xs border border-gray-300 rounded py-1 px-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                                            <button type="button" class="text-xs bg-blue-50 text-blue-600 py-1 px-2 rounded hover:bg-blue-100 ml-1" id="select-all-persons">
                                                                全选
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="overflow-y-auto bg-white p-2 rounded border">
                                                        <div class="grid grid-cols-1 gap-1 person-list">
                                                            <!-- 默认显示的是所有人员，后续会根据左侧选择的单位动态过滤 -->
                                                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                                                <input type="checkbox" name="persons[]" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-1" data-org-id="org-1-1-1">
                                                                <span class="ml-2 text-sm">李明（自治区公路发展中心）</span>
                                                            </label>
                                                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                                                <input type="checkbox" name="persons[]" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-2" data-org-id="org-1-1-1">
                                                                <span class="ml-2 text-sm">王芳（自治区公路发展中心）</span>
                                                            </label>
                                                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                                                <input type="checkbox" name="persons[]" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-3" data-org-id="org-1-1-2">
                                                                <span class="ml-2 text-sm">张伟（自治区高速公路发展中心）</span>
                                                            </label>
                                                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                                                <input type="checkbox" name="persons[]" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-4" data-org-id="org-1-1-3">
                                                                <span class="ml-2 text-sm">赵丽（自治区道路运输发展中心）</span>
                                                            </label>
                                                            <label class="inline-flex items-center p-2 hover:bg-gray-50 rounded">
                                                                <input type="checkbox" name="persons[]" class="person-checkbox text-blue-600 focus:ring-blue-500" data-person-id="person-5" data-org-id="org-1-2-1">
                                                                <span class="ml-2 text-sm">陈刚（钦州市交通运输局）</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 已选择项展示区域 -->
                                            <div class="mt-3">
                                                <h4 class="text-sm font-medium text-gray-700 mb-2">已选择 <span class="text-xs text-gray-500">(点击标签可移除)</span></h4>
                                                <div class="min-h-8 p-2 bg-white rounded border flex flex-wrap gap-2 selected-items">
                                                    <!-- 这里会动态显示已选择的单位和人员 -->
                                                    <span class="selected-none text-sm text-gray-500">暂无选择</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mb-4">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">关联事件级别 <span class="text-red-500">*</span></label>
                                        <div class="bg-gray-50 p-3 rounded-lg border">
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                                <label class="inline-flex items-center space-x-2 bg-white p-2 rounded border hover:bg-gray-50">
                                                    <input type="checkbox" name="level[]" value="1" class="text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm">Ⅰ级（特别重大）</span>
                                                </label>
                                                <label class="inline-flex items-center space-x-2 bg-white p-2 rounded border hover:bg-gray-50">
                                                    <input type="checkbox" name="level[]" value="2" class="text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm">Ⅱ级（重大）</span>
                                                </label>
                                                <label class="inline-flex items-center space-x-2 bg-white p-2 rounded border hover:bg-gray-50">
                                                    <input type="checkbox" name="level[]" value="3" class="text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm">Ⅲ级（较大）</span>
                                                </label>
                                                <label class="inline-flex items-center space-x-2 bg-white p-2 rounded border hover:bg-gray-50">
                                                    <input type="checkbox" name="level[]" value="4" class="text-blue-600 focus:ring-blue-500">
                                                    <span class="text-sm">Ⅳ级（一般）</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="px-6 py-4 bg-gray-50 rounded-b-lg flex justify-between">
                                <div>
                                    <button id="preview-org-btn" class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <i class="fas fa-eye mr-2"></i> 预览结构图
                                    </button>
                                </div>
                                <div>
                                    <button id="cancel-add-org" class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2">
                                        取消
                                    </button>
                                    <button id="confirm-add-org" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        确认
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 预览结构图模态框 -->
                    <div id="preview-org-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">组织机构结构图预览</h3>
                                <button id="close-preview-modal" class="text-gray-400 hover:text-gray-500">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            
                            <div class="bg-gray-50 p-4 rounded-md" style="height: 500px; overflow: auto;">
                                <div class="flex justify-center">
                                    <div class="org-chart">
                                        <!-- 这里将使用JavaScript生成组织结构图 -->
                                        <div class="text-center p-4 border-2 border-blue-600 rounded-lg bg-blue-50 inline-block">
                                            <div class="font-bold mb-1">领导小组</div>
                                            <div class="text-xs text-gray-500">关联: Ⅰ级、II级</div>
                                        </div>
                                        
                                        <div class="flex justify-center mt-8 space-x-12">
                                            <div class="text-center">
                                                <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                <div class="p-3 border-2 border-green-600 rounded-lg bg-green-50 inline-block">
                                                    <div class="font-bold mb-1">领导小组办公室</div>
                                                    <div class="text-xs text-gray-500">关联: Ⅰ级、II级、III级</div>
                                                </div>
                                            </div>
                                            
                                            <div class="text-center">
                                                <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                <div class="p-3 border-2 border-green-600 rounded-lg bg-green-50 inline-block">
                                                    <div class="font-bold mb-1">应急工作组</div>
                                                    <div class="text-xs text-gray-500">关联: Ⅰ级、II级、III级</div>
                                                </div>
                                                
                                                <div class="flex justify-center mt-8 space-x-4">
                                                    <div class="text-center">
                                                        <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                        <div class="p-2 border-2 border-yellow-600 rounded-lg bg-yellow-50 inline-block">
                                                            <div class="font-bold text-sm">综合协调组</div>
                                                            <div class="text-xs text-gray-500">Ⅰ级、II级、III级</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="text-center">
                                                        <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                        <div class="p-2 border-2 border-yellow-600 rounded-lg bg-yellow-50 inline-block">
                                                            <div class="font-bold text-sm">公路保障组</div>
                                                            <div class="text-xs text-gray-500">Ⅰ级、II级、III级</div>
                                                        </div>
                                                    </div>
                                                    
                                                    <div class="text-center">
                                                        <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                        <div class="p-2 border-2 border-yellow-600 rounded-lg bg-yellow-50 inline-block">
                                                            <div class="font-bold text-sm">道路保障运输组</div>
                                                            <div class="text-xs text-gray-500">Ⅰ级、II级</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <div class="text-center">
                                                <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                <div class="p-3 border-2 border-green-600 rounded-lg bg-green-50 inline-block">
                                                    <div class="font-bold mb-1">专家组</div>
                                                    <div class="text-xs text-gray-500">关联: Ⅰ级、II级</div>
                                                </div>
                                            </div>
                                            
                                            <div class="text-center">
                                                <div class="border-l-2 border-gray-400 h-8 mx-auto"></div>
                                                <div class="p-3 border-2 border-green-600 rounded-lg bg-green-50 inline-block">
                                                    <div class="font-bold mb-1">现场工作组</div>
                                                    <div class="text-xs text-gray-500">关联: Ⅰ级、II级</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-end mt-4">
                                <button id="close-preview-btn" class="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    关闭预览
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他标签页内容 -->
                <div id="prevention" class="tab-content">
                    <div class="mb-6">
                        <label for="prevention_measures" class="block text-sm font-medium text-gray-700 mb-2">预防措施</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="prevention_measures" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预防措施">1. 气象部门加强气象监测预报预警，做好灾害性天气预测。
2. 交通部门提前储备除雪融冰物资，对易结冰路段进行排查和防护。
3. 电力部门对易受低温雨雪影响的电力设施进行巡查和隐患排除。
4. 各基层组织开展居民防冻防滑知识宣传和培训。</textarea>
                        </div>
                </div>
                
                    <div class="mb-6">
                        <label for="warning_principle" class="block text-sm font-medium text-gray-700 mb-2">预警原则</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="warning_principle" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预警原则相关内容">1. 早发现、早报告、早预警、早处置的"四早"原则。
2. 快速反应、协调联动、分级负责、属地为主的原则。
3. 以人为本、科学预警、精准发布、公众参与的原则。
4. 确保预警信息发布及时、准确、权威、可靠。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="warning_collection" class="block text-sm font-medium text-gray-700 mb-2">预警信息收集</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="warning_collection" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预警信息收集相关内容">气象、交通、水利、电力等部门通过监测系统收集灾害性天气等信息，及时向区应急管理局报送，由综合协调组组织分析研判，为预警信息发布提供依据。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="warning_level" class="block text-sm font-medium text-gray-700 mb-2">预警分级</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="warning_level" rows="8" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预警分级相关内容">根据雪灾可能造成的危害程度、紧急程度和发展态势，预警级别由高到低分为红色、橙色、黄色和蓝色四级：

1. 红色预警（I级）：预计未来24小时内可能出现大暴雪（24小时降雪量≥15.0mm），或已出现大暴雪且可能持续；可能或已经造成重大交通阻断、大范围电力通信中断等严重影响。

2. 橙色预警（II级）：预计未来24小时内可能出现暴雪（24小时降雪量10.0～14.9mm），或已出现暴雪且可能持续；可能或已经造成道路结冰、交通受阻等严重影响。

3. 黄色预警（III级）：预计未来24小时内可能出现大雪（24小时降雪量5.0～9.9mm），或已出现大雪且可能持续；对交通出行等有较大影响。

4. 蓝色预警（IV级）：预计未来24小时内可能出现中雪（24小时降雪量2.5～4.9mm），或已出现中雪且可能持续；对交通出行等有一定影响。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="warning_release" class="block text-sm font-medium text-gray-700 mb-2">预警发布</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="warning_release" rows="6" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预警发布相关内容">1. 发布主体：区气象局负责发布气象灾害预警信息，区应急管理局负责统筹协调预警信息发布工作。

2. 发布内容：包括预警区域、预警级别、预警起始时间、可能影响范围、警示事项、应对措施、发布机构等。

3. 发布渠道：
   - 通过电视、广播、报纸、互联网、手机短信等方式发布预警信息
   - 各级政府网站、政务新媒体平台发布
   - 使用预警大喇叭、电子显示屏等设施
   - 基层网格员入户告知等方式

4. 发布流程：区气象局发现灾害性天气→分析研判→提出预警信息发布建议→区应急管理局审核→发布预警信息</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="warning_measures" class="block text-sm font-medium text-gray-700 mb-2">预警措施</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="warning_measures" rows="8" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预警措施相关内容">1. 红色预警（I级）措施：
   - 启动24小时应急值守
   - 停止一切户外集会、活动
   - 停课、停工、停业
   - 调集应急物资和队伍到位
   - 加强巡查，及时排除险情

2. 橙色预警（II级）措施：
   - 加强值班值守
   - 考虑停止户外集会和高空等危险作业
   - 学校停止户外活动
   - 准备应急物资和队伍

3. 黄色预警（III级）措施：
   - 加强监测预报
   - 相关部门做好应急准备
   - 提醒居民注意防寒保暖

4. 蓝色预警（IV级）措施：
   - 密切关注天气变化
   - 注意防寒保暖
   - 做好防滑防冻准备</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- 应急响应 -->
                <div id="response" class="tab-content">
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">响应分级启动</h3>
                        <p class="text-sm text-gray-600 mb-4">根据事件严重程度，设置不同级别的应急响应。每个级别对应不同的启动条件、处置措施和应急资源。</p>
                        
                        <!-- 事件级别选择和添加 -->
                        <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                            <!-- 添加新事件级别 -->
                            <div class="flex justify-end mb-4">
                                <button id="add-level-btn" class="inline-flex items-center px-3 py-2 border border-blue-600 text-sm font-medium rounded text-blue-600 bg-white hover:bg-blue-50 focus:outline-none">
                                    <i class="fas fa-plus mr-1"></i> 添加事件级别
                                </button>
                            </div>
                            
                            <!-- 事件级别列表 -->
                            <div id="level-container">
                                <!-- 默认级别条目 (Example for Level 1) -->
                                <div class="level-entry border border-gray-200 rounded-md p-4 mb-4" data-level-index="0">
                                    <div class="flex items-start justify-between mb-4">
                                        <div class="w-3/4">
                                            <div class="flex items-center mb-4">
                                                <label class="block text-sm font-medium text-gray-700 mr-2 w-24">事件级别</label>
                                                <select class="event-level-select block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                    <option value="1">Ⅰ级（特别重大）</option>
                                                    <option value="2">Ⅱ级（重大）</option>
                                                    <option value="3">Ⅲ级（较大）</option>
                                                    <option value="4">Ⅳ级（一般）</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="remove-level-btn text-red-600 hover:text-red-900 focus:outline-none" style="display: none;">
                                                <i class="fas fa-trash-alt"></i>
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- 响应启动条件 - Changed to display div -->
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">响应启动条件</label>
                                        <div class="response-condition-display bg-gray-100 p-3 rounded-md text-sm text-gray-700 border border-gray-200 min-h-[50px]">
                                            <!-- Conditions defined in 'Basic Info' tab will be linked here -->
                                            (根据 '基本信息 & 总则' 标签页中定义的条件自动关联)
                                        </div>
                                    </div>
                                    
                                    <!-- 应急资源配置 - Modified section -->
                                    <div class="mb-6">
                                        <h4 class="text-base font-medium text-gray-700 mb-3">应急资源配置</h4>
                                        <div class="flex space-x-4 mb-4">
                                             <button class="select-material-btn inline-flex items-center px-3 py-2 border border-blue-600 text-sm font-medium rounded text-blue-600 bg-white hover:bg-blue-50 focus:outline-none">
                                                <i class="fas fa-boxes mr-1"></i> 选择应急物资
                                            </button>
                                            <button class="select-vehicle-btn inline-flex items-center px-3 py-2 border border-blue-600 text-sm font-medium rounded text-blue-600 bg-white hover:bg-blue-50 focus:outline-none">
                                                <i class="fas fa-truck mr-1"></i> 选择应急车辆
                                            </button>
                                        </div>
                                        <!-- Area to display selected resources -->
                                         <div class="selected-resources mb-4">
                                             <h5 class="text-sm font-medium text-gray-600 mb-2">已选物资:</h5>
                                             <div class="selected-materials-display min-h-[30px] bg-gray-50 p-2 rounded border border-gray-200 flex flex-wrap gap-2">
                                                 <span class="text-xs text-gray-500 italic">暂未选择</span>
                                                 <!-- Selected materials will be listed here as tags -->
                                            </div>
                                        </div>
                                         <div class="selected-resources">
                                             <h5 class="text-sm font-medium text-gray-600 mb-2">已选车辆:</h5>
                                             <div class="selected-vehicles-display min-h-[30px] bg-gray-50 p-2 rounded border border-gray-200 flex flex-wrap gap-2">
                                                 <span class="text-xs text-gray-500 italic">暂未选择</span>
                                                 <!-- Selected vehicles will be listed here as tags -->
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 应急处置 -->
                                    <div class="mb-6">
                                        <label class="block text-sm font-medium text-gray-700 mb-2">应急处置流程 <span class="text-red-500">*</span></label>
                                        <div class="border border-gray-300 rounded-md">
                                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                                <div class="flex items-center">
                                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                                </div>
                                            </div>
                                            <textarea class="disposal-measures block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" rows="5" placeholder="请输入应急处置流程">1. 立即启动I级响应机制，成立区级应急处置指挥部，区长任指挥长；
2. 及时向上级部门报告灾情和救灾工作进展情况；
3. 组织抢通交通要道，确保救灾车辆通行；
4. 调动除雪车辆，对主要道路进行除雪作业；
5. 调配医疗救护力量，做好伤员转移和救治工作；
6. 调配电力抢修队伍，抢修电力设施，恢复供电；
7. 组织转移安置受灾群众，保障基本生活需求。</textarea>
                                        </div>
                                    </div>
                                </div>
                                <!-- Add more level entries if needed, cloning the structure above -->
                            </div>
                        </div>
                        
                        <!-- 信息报送 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-800 mb-4">信息报送</h3>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                    </div>
                                </div>
                                <textarea id="info_reporting" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入信息报送要求">按照"快速、准确、全面"的原则，及时收集和报告灾情信息。涉及人员伤亡的，应在事发后30分钟内电话报告，1小时内书面报告。I、II级事件须每2小时续报一次，直至应急处置工作结束。</textarea>
                            </div>
                        </div>
                        
                        <!-- 新闻发布 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-800 mb-4">新闻发布</h3>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                    </div>
                                </div>
                                <textarea id="news_release" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入新闻发布要求">按照及时、准确、客观、全面的原则，由区政府新闻办负责组织协调新闻发布工作。重大灾情信息发布，应征得区政府和上级主管部门同意。</textarea>
                            </div>
                        </div>
                        
                        <!-- 响应调整与终止 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-800 mb-4">响应调整与终止</h3>
                            <div class="border border-gray-300 rounded-md">
                                <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                    <div class="flex items-center">
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                        <span class="border-r border-gray-300 h-5 mx-2"></span>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                        <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                    </div>
                                </div>
                                <textarea id="response_adjustment" rows="3" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入响应调整与终止条件">根据灾害事态发展情况和处置工作实际需要，及时调整应急响应级别。当灾害得到有效控制，险情已经消除，受灾地区社会秩序恢复正常，由启动响应的同级政府决定终止应急响应。</textarea>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="post-disposal" class="tab-content">
                    <div class="mb-6">
                        <label for="post_care" class="block text-sm font-medium text-gray-700 mb-2">善后处置</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="post_care" rows="5" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入善后处置相关内容">1. 及时恢复水、电、气、通信等基础设施功能，恢复正常生产生活秩序。
2. 救助受灾群众，做好受灾群众的基本生活保障工作，安排好受灾群众临时住所。
3. 组织开展卫生防疫工作，防止因灾引发疫情。
4. 调拨救灾资金和物资，迅速调集救灾储备物资，支援灾区人民群众生活。
5. 对因灾伤亡人员家属进行抚慰，对紧急调集、征用的人力、物力按规定给予补偿。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="post_evaluation" class="block text-sm font-medium text-gray-700 mb-2">总结评估</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="post_evaluation" rows="5" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入总结评估相关内容">1. 灾害处置工作结束后，区应急指挥部组织开展灾害调查评估工作，全面调查灾害发生经过、损失情况、救援过程和应急处置效果。
2. 各相关单位对灾害应急处置情况进行总结，分析存在的问题，提出改进建议。
3. 形成灾害事件调查评估报告，报区政府，同时抄送相关部门。
4. 根据总结评估结果，进一步完善应急预案，加强应急队伍建设，改进应急保障措施。</textarea>
                        </div>
                    </div>
                </div>
                
                <div id="support" class="tab-content">
                    <div class="mb-6">
                        <label for="material_support" class="block text-sm font-medium text-gray-700 mb-2">物资保障</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="material_support" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入物资保障相关内容">1. 区应急管理局负责组织调运区级储备的应急物资。
2. 区城管委负责组织调运融雪剂、铁锹、扫帚等除雪物资。
3. 区交通局负责除雪设备和车辆的储备和调度。
4. 各街道、社区和重点单位按职责储备必要的应急装备和物资。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="communication_support" class="block text-sm font-medium text-gray-700 mb-2">通信保障</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="communication_support" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入通信保障相关内容">1. 区通信管理办公室协调各电信运营商确保通信系统正常运行。
2. 建立应急广播系统，保障信息及时发布。
3. 配备应急通信设备，确保关键岗位人员通信畅通。
4. 当固定通信设施遭到破坏时，启用卫星电话等备用通信手段。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="traffic_support" class="block text-sm font-medium text-gray-700 mb-2">交通保障</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="traffic_support" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入交通保障相关内容">1. 区公安分局交警大队负责指挥灾区交通，保障救援道路畅通。
2. 区交通局负责协调公共交通工具，保障群众疏散和救援物资运输。
3. 紧急情况下，可征用社会车辆用于抢险救灾。
4. 建立应急车辆调度机制，确保应急车辆优先通行。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="medical_support" class="block text-sm font-medium text-gray-700 mb-2">医疗保障</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="medical_support" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入医疗保障相关内容">1. 区卫健委负责组织医疗队伍，开展现场救治和卫生防疫工作。
2. 各医院做好接收伤员的准备，设立专门病区。
3. 建立伤员转运绿色通道，确保伤员及时得到救治。
4. 储备充足的医疗物资和设备，确保应急医疗需求。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="funding_support" class="block text-sm font-medium text-gray-700 mb-2">经费保障</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="funding_support" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入经费保障相关内容">1. 区财政局负责应急救灾专项资金的筹集、拨付和使用监督。
2. 建立应急资金快速拨付机制，确保应急处置工作经费及时到位。
3. 对因灾受损的基础设施修复给予资金支持。
4. 安排专项资金用于灾后恢复重建工作。</textarea>
                        </div>
                    </div>
                </div>
                
                <div id="management" class="tab-content">
                    <div class="mb-6">
                        <label for="plan_revise" class="block text-sm font-medium text-gray-700 mb-2">预案修订</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="plan_revise" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预案修订相关内容">1. 有下列情形之一的，应当及时修订本预案：
   - 有关法律、法规、规章、标准、上位预案中的有关规定发生变化的
   - 应急指挥机构及其职责发生重大调整的
   - 面临的风险发生重大变化的
   - 在实际应对和演练中发现问题需要修订的
   - 其他需要修订的情况

2. 预案修订由区应急管理局负责组织实施，修订内容须经区政府审批后才能实施。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="plan_training" class="block text-sm font-medium text-gray-700 mb-2">宣传培训</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="plan_training" rows="4" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入宣传培训相关内容">1. 应急管理局负责组织对应急预案的宣传教育培训工作。
2. 各相关单位应当组织本部门、本单位或本行业的应急管理人员学习本预案。
3. 充分利用广播、电视、报纸、互联网等媒体，加强对公众的宣传教育。
4. 积极组织专家进学校、进社区、进企业开展防灾减灾知识宣讲活动。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="plan_drill" class="block text-sm font-medium text-gray-700 mb-2">预案演练</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="plan_drill" rows="5" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入预案演练相关内容">1. 演练形式：桌面推演、功能演练、综合演练等。

2. 演练频次：区应急管理局应当每年至少组织一次应急演练。各相关单位根据各自职责，有计划地组织相关人员进行专项应急预案演练。

3. 演练要求：演练前制定周密的演练方案，演练过程中认真评估演练效果，演练结束后及时进行总结，查找问题，提出改进建议。

4. 演练内容：预警信息发布、应急响应、指挥协调、人员疏散、物资调配、应急通信等。</textarea>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="implementation" class="block text-sm font-medium text-gray-700 mb-2">实施时间</label>
                        <div class="border border-gray-300 rounded-md">
                            <div class="px-4 py-2 bg-gray-50 border-b border-gray-300">
                                <div class="flex items-center">
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-bold"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-italic"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-underline"></i></button>
                                    <span class="border-r border-gray-300 h-5 mx-2"></span>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ul"></i></button>
                                    <button class="text-gray-600 mr-2 hover:text-gray-800 focus:outline-none"><i class="fas fa-list-ol"></i></button>
                                </div>
                            </div>
                            <textarea id="implementation" rows="2" class="block w-full border-0 px-4 py-3 focus:outline-none focus:ring-0 sm:text-sm" placeholder="请输入实施时间相关内容">本预案自发布之日起实施。</textarea>
                        </div>
                    </div>
                </div>
                
                <div id="attachments" class="tab-content">
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-800">附件列表</h3>
                            <button class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm font-medium rounded text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-plus mr-1"></i> 添加附件
                            </button>
                        </div>
                        
                        <!-- 附件列表 -->
                        <div class="bg-white rounded-md shadow-sm border border-gray-200">
                            <ul class="divide-y divide-gray-200">
                                <li class="p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-file-pdf text-red-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-800">附件1：区应急指挥机构及成员名单</p>
                                                <p class="text-xs text-gray-500 mt-1">PDF格式，上传于 2023-06-15</p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm">
                                                <i class="fas fa-eye"></i> 预览
                                            </button>
                                            <button class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </li>
                                <li class="p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-file-word text-blue-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-800">附件2：低温雨雪冰冻灾害应急处置流程图</p>
                                                <p class="text-xs text-gray-500 mt-1">Word格式，上传于 2023-06-15</p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm">
                                                <i class="fas fa-eye"></i> 预览
                                            </button>
                                            <button class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </li>
                                <li class="p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-file-excel text-green-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-800">附件3：应急物资储备清单</p>
                                                <p class="text-xs text-gray-500 mt-1">Excel格式，上传于 2023-06-15</p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm">
                                                <i class="fas fa-eye"></i> 预览
                                            </button>
                                            <button class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </li>
                                <li class="p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-file-image text-purple-500 text-xl"></i>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-800">附件4：应急避难场所分布图</p>
                                                <p class="text-xs text-gray-500 mt-1">JPG格式，上传于 2023-06-15</p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none text-sm">
                                                <i class="fas fa-eye"></i> 预览
                                            </button>
                                            <button class="text-red-600 hover:text-red-800 focus:outline-none text-sm">
                                                <i class="fas fa-trash-alt"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- 上传附件表单 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">上传新附件</h3>
                        <div class="bg-white rounded-md shadow-sm border border-gray-200 p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label for="attachment_name" class="block text-sm font-medium text-gray-700 mb-1">附件名称 <span class="text-red-500">*</span></label>
                                    <input type="text" id="attachment_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入附件名称">
                                </div>
                                
                                <div>
                                    <label for="attachment_type" class="block text-sm font-medium text-gray-700 mb-1">附件类型 <span class="text-red-500">*</span></label>
                                    <select id="attachment_type" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">请选择附件类型</option>
                                        <option value="1">应急组织机构</option>
                                        <option value="2">应急流程图</option>
                                        <option value="3">物资清单</option>
                                        <option value="4">地图信息</option>
                                        <option value="5">专家名单</option>
                                        <option value="6">其他</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <label for="attachment_desc" class="block text-sm font-medium text-gray-700 mb-1">附件描述</label>
                                <textarea id="attachment_desc" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入附件描述信息"></textarea>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">上传文件</label>
                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <i class="fas fa-upload text-gray-400 text-2xl mb-2"></i>
                                        <div class="flex text-sm text-gray-600">
                                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                                <span>选择文件</span>
                                                <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                            </label>
                                            <p class="pl-1">或拖放文件到此处</p>
                                        </div>
                                        <p class="text-xs text-gray-500">支持的文件格式：PDF, Word, Excel, JPG, PNG</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-end">
                                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2">
                                    取消
                                </button>
                                <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <i class="fas fa-save mr-2"></i> 保存
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
            <!-- 底部操作栏 -->
            <div class="mt-6 flex justify-between">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    <i class="fas fa-arrow-left mr-2"></i> 返回
                </button>
                <div class="space-x-2">
                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-save mr-2"></i> 保存草稿
                    </button>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-check-circle mr-2"></i> 提交
                    </button>
                </div>
            </div>
        </div>
    </main>

<script>
        // 设置活动菜单项
    document.addEventListener('DOMContentLoaded', function() {
            // 根据当前页面设置活动菜单项
            const currentPage = window.location.pathname.split('/').pop().split('.')[0];
            const menuItems = document.querySelectorAll('.sidebar-menu-item');
            
            menuItems.forEach(item => {
                const page = item.getAttribute('data-page');
                if (currentPage.includes(page)) {
                    item.classList.add('active');
                }
            });
            
            // 移除侧边栏切换相关代码，因为按钮已移除
            /*
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.querySelector('.sidebar');
            const body = document.body;

            // 从本地存储获取侧边栏状态
            const sidebarState = localStorage.getItem('sidebarState');
            if (sidebarState === 'collapsed') {
                sidebar.classList.add('collapsed');
                body.classList.remove('sidebar-expanded');
                body.classList.add('sidebar-collapsed');
            }

            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');

                if (sidebar.classList.contains('collapsed')) {
                    body.classList.remove('sidebar-expanded');
                    body.classList.add('sidebar-collapsed');
                    localStorage.setItem('sidebarState', 'collapsed');
                } else {
                    body.classList.remove('sidebar-collapsed');
                    body.classList.add('sidebar-expanded');
                    localStorage.setItem('sidebarState', 'expanded');
                }
            });
            */

        // 标签页切换功能
        const tabBtns = document.querySelectorAll('.tab-btn');
        
        // 默认只显示第一个标签页内容，隐藏其他标签页
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabId = btn.getAttribute('data-tab');
                
                // 移除所有按钮的活动状态
                tabBtns.forEach(t => {
                    t.classList.remove('text-blue-600', 'border-blue-600');
                    t.classList.add('text-gray-500', 'border-transparent');
                });
                
                // 隐藏所有标签页内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                });
                
                // 设置当前按钮为活动状态
                btn.classList.remove('text-gray-500', 'border-transparent');
                btn.classList.add('text-blue-600', 'border-blue-600');
                
                // 显示当前标签页内容
                document.getElementById(tabId).style.display = 'block';
            });
        });
        
        // 处置措施分级标签页切换
        const responseLevelBtns = document.querySelectorAll('.response-level-btn');
        
        if (responseLevelBtns.length > 0) {
            responseLevelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const level = btn.getAttribute('data-level');
                    
                    // 隐藏所有内容
                    document.querySelectorAll('.response-level-content').forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // 移除所有按钮的活动状态
                    responseLevelBtns.forEach(b => {
                        b.classList.remove('text-blue-600', 'border-blue-600', 'bg-white');
                        b.classList.add('text-gray-500', 'border-transparent');
                    });
                    
                    // 设置当前按钮为活动状态
                    btn.classList.remove('text-gray-500', 'border-transparent');
                    btn.classList.add('text-blue-600', 'border-blue-600', 'bg-white');
                    
                    // 显示对应内容
                    document.getElementById(level).classList.remove('hidden');
                });
            });
        }
        
        // 预警响应措施标签页切换
        const warningLevelBtns = document.querySelectorAll('.warning-level-btn');
        
        if (warningLevelBtns.length > 0) {
            warningLevelBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const level = btn.getAttribute('data-level');
                    
                    // 隐藏所有内容
                    document.querySelectorAll('.warning-level-content').forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // 移除所有按钮的活动状态
                    warningLevelBtns.forEach(b => {
                        b.classList.remove('text-red-600', 'border-red-600', 'bg-white');
                        b.classList.add('text-gray-500', 'border-transparent');
                    });
                    
                    // 设置当前按钮为活动状态
                    btn.classList.remove('text-gray-500', 'border-transparent');
                    btn.classList.add('text-red-600', 'border-red-600', 'bg-white');
                    
                    // 显示对应内容
                    document.getElementById(level).classList.remove('hidden');
                });
            });
        }
        
        // 事件分级标签切换
        const eventLevelBtns = document.querySelectorAll('.event-level-btn');
        const eventLevelContents = document.querySelectorAll('.event-level-content');
        
        eventLevelBtns.forEach(button => {
            button.addEventListener('click', () => {
                // 取消所有标签页的激活状态
                eventLevelBtns.forEach(btn => {
                    btn.classList.remove('text-blue-600', 'border-blue-600');
                    btn.classList.add('text-gray-500', 'border-transparent');
                });
                
                // 激活当前点击的标签页
                button.classList.remove('text-gray-500', 'border-transparent');
                button.classList.add('text-blue-600', 'border-blue-600');
                
                // 隐藏所有内容区域
                eventLevelContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // 显示对应的内容区域
                const levelId = button.getAttribute('data-level');
                document.getElementById(levelId).classList.remove('hidden');
            });
        });
        
        // 组织体系 - 树形结构折叠/展开
        document.querySelectorAll('.node-content').forEach(node => {
            node.addEventListener('click', function(e) {
                // 防止点击操作按钮时触发展开/折叠
                if (e.target.closest('.node-actions')) {
                    return;
                }
                
                // 处理节点详情的展开/折叠
                const toggleIcon = this.querySelector('.toggle-icon');
                const details = this.nextElementSibling;
                const isDetails = details && details.classList.contains('node-details');
                
                if (isDetails) {
                    if (details.classList.contains('hidden')) {
                        details.classList.remove('hidden');
                        details.classList.add('expanded');
                        if (toggleIcon) {
                            toggleIcon.classList.remove('fa-caret-right');
                            toggleIcon.classList.add('fa-caret-down');
                        }
                    } else {
                        details.classList.add('hidden');
                        details.classList.remove('expanded');
                        if (toggleIcon) {
                            toggleIcon.classList.remove('fa-caret-down');
                            toggleIcon.classList.add('fa-caret-right');
                        }
                    }
                }
                
                // 处理子机构列表的展开/折叠
                const parent = this.parentNode;
                const children = parent.querySelector('.children');
                if (children) {
                    if (children.classList.contains('hidden')) {
                        children.classList.remove('hidden');
                        if (toggleIcon && !isDetails) {
                            toggleIcon.classList.remove('fa-caret-right');
                            toggleIcon.classList.add('fa-caret-down');
                        }
                    } else {
                        children.classList.add('hidden');
                        if (toggleIcon && !isDetails) {
                            toggleIcon.classList.remove('fa-caret-down');
                            toggleIcon.classList.add('fa-caret-right');
                        }
                    }
                }
                
                e.stopPropagation(); // 阻止事件冒泡
            });
        });
        
        // 操作按钮事件处理
        document.querySelectorAll('.node-actions button').forEach(button => {
            button.addEventListener('click', function(e) {
                const action = this.getAttribute('title');
                const nodeContent = this.closest('.node-content');
                const nodeName = nodeContent.querySelector('.node-title').textContent;
                
                if (action === '添加子机构') {
                    // 显示添加子机构模态框，并设置父级机构
                    document.getElementById('modalTitle').textContent = '添加子机构';
                    document.getElementById('parent-org-field').classList.remove('hidden');
                    // 设置父级机构值
                    document.getElementById('add-org-modal').classList.remove('hidden');
                } else if (action === '编辑') {
                    // 显示编辑模态框，填充现有数据
                    document.getElementById('modalTitle').textContent = '编辑机构';
                    // 获取并设置正在编辑的节点ID
                    const nodeId = nodeContent.dataset.orgId || '';
                    document.getElementById('editing-node-id').value = nodeId;
                    // 填充表单
                    document.getElementById('org-name').value = nodeName;
                    
                    // 判断是否为顶级机构
                    const isTopLevel = nodeContent.querySelector('.bg-blue-100') !== null;
                    if (isTopLevel) {
                        document.querySelector('input[name="org_level"][value="top"]').checked = true;
                        document.getElementById('parent-org-field').classList.add('hidden');
                    } else {
                        document.querySelector('input[name="org_level"][value="sub"]').checked = true;
                        document.getElementById('parent-org-field').classList.remove('hidden');
                        
                        // 设置父级机构
                        // 这里需要根据实际情况获取父级机构ID
                        // document.getElementById('parent-org-select').value = parentId;
                    }
                    
                    // 修复：定义 nodeDetails 变量
                    const nodeDetails = nodeContent.nextElementSibling;
                    // 如果有详情节点，提取详情信息
                    if (nodeDetails && nodeDetails.classList.contains('node-details')) {
                        // 提取职责信息
                        const dutyDiv = nodeDetails.querySelector('label[class*="主要职责"] + div div');
                        if (dutyDiv) {
                            document.getElementById('org-duty').value = dutyDiv.textContent.trim();
                        }
                        
                        // 清除所有复选框选中状态
                        document.querySelectorAll('input[name="persons[]"], input[name="level[]"]').forEach(cb => {
                            cb.checked = false;
                        });
                        
                        // 提取关联人员/单位信息
                        const personsDiv = nodeDetails.querySelector('label[class*="关联人员"] + div div');
                        if (personsDiv) {
                            const personSpans = personsDiv.querySelectorAll('span');
                            personSpans.forEach(span => {
                                const personText = span.textContent.trim();
                                document.querySelectorAll('input[name="persons[]"]').forEach(cb => {
                                    const cbLabel = cb.nextElementSibling.textContent.trim();
                                    if (personText.includes(cbLabel)) {
                                        cb.checked = true;
                                        // 如果人员有角色信息，更新标签
                                        if (personText.includes('（')) {
                                            const roleText = personText.match(/（(.+)）/)[1];
                                            cb.nextElementSibling.textContent = `${cbLabel}（${roleText}）`;
                                        }
                                    }
                                });
                            });
                        }
                        
                        // 提取关联事件级别信息
                        const levelsDiv = nodeDetails.querySelector('label[class*="关联事件级别"] + div');
                        if (levelsDiv) {
                            const levelSpans = levelsDiv.querySelectorAll('span');
                            levelSpans.forEach(span => {
                                const levelText = span.textContent.trim();
                                document.querySelectorAll('input[name="level[]"]').forEach(cb => {
                                    if (levelText.includes(cb.nextElementSibling.textContent.trim())) {
                                        cb.checked = true;
                                    }
                                });
                            });
                        }
                    }
                    
                    // 显示模态框
                    document.getElementById('add-org-modal').classList.remove('hidden');
                } else if (action === '删除') {
                    // 显示删除确认对话框
                    if (confirm('确定要删除"' + nodeName + '"及其所有子机构吗？此操作不可撤销。')) {
                        // 这里添加删除逻辑
                        alert('已删除"' + nodeName + '"及其子机构');
                    }
                }
                
                e.stopPropagation(); // 阻止事件冒泡，防止触发节点的点击事件
            });
        });
        
        // 保存机构状态到本地存储
        function saveOrgTreeState() {
            const expandedNodes = {};
            document.querySelectorAll('.node-content').forEach((node, index) => {
                const details = node.nextElementSibling;
                const children = node.parentNode.querySelector('.children');
                const nodeId = 'node-' + index;
                
                expandedNodes[nodeId] = {
                    details: details && !details.classList.contains('hidden'),
                    children: children && !children.classList.contains('hidden')
                };
            });
            
            localStorage.setItem('orgTreeState', JSON.stringify(expandedNodes));
        }
        
        // 加载机构状态
        function loadOrgTreeState() {
            const savedState = localStorage.getItem('orgTreeState');
            if (savedState) {
                const expandedNodes = JSON.parse(savedState);
                document.querySelectorAll('.node-content').forEach((node, index) => {
                    const nodeId = 'node-' + index;
                    const state = expandedNodes[nodeId];
                    
                    if (state) {
                        const details = node.nextElementSibling;
                        const children = node.parentNode.querySelector('.children');
                        const toggleIcon = node.querySelector('.toggle-icon');
                        
                        if (details && state.details) {
                            details.classList.remove('hidden');
                            details.classList.add('expanded');
                        } else if (details) {
                            details.classList.add('hidden');
                            details.classList.remove('expanded');
                        }
                        
                        if (children && state.children) {
                            children.classList.remove('hidden');
                            if (toggleIcon) {
                                toggleIcon.classList.remove('fa-caret-right');
                                toggleIcon.classList.add('fa-caret-down');
                            }
                        } else if (children) {
                            children.classList.add('hidden');
                            if (toggleIcon) {
                                toggleIcon.classList.remove('fa-caret-down');
                                toggleIcon.classList.add('fa-caret-right');
                            }
                        }
                    }
                });
            }
        }
        
        // 在页面加载时初始显示状态
        // 默认只展开第一级节点
        document.querySelectorAll('.tree-node').forEach(node => {
            const isRoot = node.classList.contains('root-node');
            const children = node.querySelector('.children');
            
            if (!isRoot && children) {
                children.classList.add('hidden');
                const toggleIcon = node.querySelector('.toggle-icon');
                if (toggleIcon) {
                    toggleIcon.classList.remove('fa-caret-down');
                    toggleIcon.classList.add('fa-caret-right');
                }
            }
            
            // 默认隐藏所有详情，除了根节点的详情
            const details = node.querySelector('.node-details');
            const nodeContent = node.querySelector('.node-content');
            
            if (details && !isRoot) {
                details.classList.add('hidden');
                details.classList.remove('expanded');
                const toggleIcon = nodeContent?.querySelector('.toggle-icon');
                if (toggleIcon) {
                    toggleIcon.classList.remove('fa-caret-down');
                    toggleIcon.classList.add('fa-caret-right');
                }
            }
        });
        
        // 尝试从本地存储恢复状态
        // loadOrgTreeState();
        
        // 在操作后保存状态
        document.querySelectorAll('.node-content').forEach(node => {
            node.addEventListener('click', function() {
                setTimeout(saveOrgTreeState, 100);
            });
        });
        
        // 在初始化 tree-node 相关代码后添加
        
        // 模态框控制
        const addOrgBtn = document.getElementById('add-org-btn');
        const closeOrgModalBtn = document.getElementById('close-org-modal');
        const cancelAddOrgBtn = document.getElementById('cancel-add-org');
        const confirmAddOrgBtn = document.getElementById('confirm-add-org');
        const addOrgModal = document.getElementById('add-org-modal');
        const previewOrgBtn = document.getElementById('preview-org-btn');
        const closePreviewModalBtn = document.getElementById('close-preview-modal');
        const closePreviewBtn = document.getElementById('close-preview-btn');
        const previewOrgModal = document.getElementById('preview-org-modal');
        
        // 打开新增机构模态框
        if (addOrgBtn && addOrgModal) {
            addOrgBtn.addEventListener('click', function() {
                // 重置表单
                document.getElementById('orgForm').reset();
                document.getElementById('modalTitle').textContent = '新增机构';
                // 默认选择顶级机构
                document.querySelector('input[name="org_level"][value="top"]').checked = true;
                document.getElementById('parent-org-field').classList.add('hidden');
                
                addOrgModal.classList.remove('hidden');
            });
        }
        
        // 关闭新增机构模态框
        if (closeOrgModalBtn && addOrgModal) {
            closeOrgModalBtn.addEventListener('click', function() {
                addOrgModal.classList.add('hidden');
            });
        }
        
        // 取消按钮关闭模态框
        if (cancelAddOrgBtn && addOrgModal) {
            cancelAddOrgBtn.addEventListener('click', function() {
                addOrgModal.classList.add('hidden');
            });
        }
        
        // 机构级别选择 - 切换是否显示上级机构选择框
        const orgLevelRadios = document.querySelectorAll('input[name="org_level"]');
        const parentOrgField = document.getElementById('parent-org-field');
        
        if (orgLevelRadios.length && parentOrgField) {
            orgLevelRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'sub') {
                        parentOrgField.classList.remove('hidden');
                    } else {
                        parentOrgField.classList.add('hidden');
                    }
                });
            });
        }
        
        // 确认添加机构
        if (confirmAddOrgBtn) {
            confirmAddOrgBtn.addEventListener('click', function() {
                // 表单验证
                const orgName = document.getElementById('org-name').value.trim();
                const orgDuty = document.getElementById('org-duty').value.trim();
                const isSub = document.querySelector('input[name="org_level"][value="sub"]').checked;
                const parentOrg = document.getElementById('parent-org-select').value;
                
                // 选中的人员
                const selectedPersons = Array.from(document.querySelectorAll('input[name="persons[]"]:checked')).map(cb => cb.nextElementSibling.textContent);
                
                // 选中的事件级别
                const selectedLevels = Array.from(document.querySelectorAll('input[name="level[]"]:checked')).map(cb => cb.nextElementSibling.textContent);
                
                // 验证必填项
                if (!orgName) {
                    alert('请输入机构名称');
                    return;
                }
                
                if (!orgDuty) {
                    alert('请输入主要职责');
                    return;
                }
                
                if (isSub && !parentOrg) {
                    alert('请选择上级机构');
                    return;
                }
                
                if (selectedPersons.length === 0) {
                    alert('请至少选择一个关联人员/单位');
                    return;
                }
                
                if (selectedLevels.length === 0) {
                    alert('请至少选择一个关联事件级别');
                    return;
                }
                
                // 模拟添加机构成功
                alert('保存成功');
                
                // 在实际应用中，这里应该向后端API发送保存请求
                // 示例代码：模拟创建新节点并添加到组织结构树中
                const isEditing = document.getElementById('modalTitle').textContent.includes('编辑');
                const newNodeId = 'org-' + Math.floor(Math.random() * 10000);
                
                if (!isEditing) {
                    // 添加新节点
                    if (isSub && parentOrg) {
                        // 添加子节点
                        const parentNode = document.querySelector(`.org-tree [data-org-id="${parentOrg}"]`).closest('li');
                        let subList = parentNode.querySelector('ul');
                        
                        if (!subList) {
                            // 如果父节点还没有子列表，创建一个
                            subList = document.createElement('ul');
                            subList.className = 'pl-6 mt-1';
                            subList.id = 'tree-' + newNodeId;
                            parentNode.appendChild(subList);
                            
                            // 为父节点添加折叠按钮
                            const parentLabel = parentNode.querySelector('label');
                            if (parentLabel && !parentNode.querySelector('.toggle-btn')) {
                                const toggleBtn = document.createElement('span');
                                toggleBtn.className = 'toggle-btn mr-1';
                                toggleBtn.setAttribute('data-target', 'tree-' + newNodeId);
                                toggleBtn.innerHTML = '<i class="fas fa-caret-down"></i>';
                                
                                // 添加折叠功能
                                toggleBtn.addEventListener('click', function() {
                                    const targetId = this.getAttribute('data-target');
                                    const targetEl = document.getElementById(targetId);
                                    
                                    if (targetEl) {
                                        if (targetEl.classList.contains('hidden')) {
                                            targetEl.classList.remove('hidden');
                                            this.querySelector('i').className = 'fas fa-caret-down';
                                        } else {
                                            targetEl.classList.add('hidden');
                                            this.querySelector('i').className = 'fas fa-caret-right';
                                        }
                                    }
                                });
                                
                                parentNode.querySelector('div').insertBefore(toggleBtn, parentLabel);
                            }
                        }
                        
                        // 创建新的子节点
                        const newLi = document.createElement('li');
                        newLi.className = 'mb-1';
                        newLi.innerHTML = `
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="${newNodeId}">
                                <span class="ml-2 text-sm">${orgName}</span>
                            </label>
                        `;
                        
                        subList.appendChild(newLi);
                    } else {
                        // 添加顶级节点
                        const orgTree = document.querySelector('.org-tree');
                        const newLi = document.createElement('li');
                        newLi.className = 'mb-2';
                        newLi.innerHTML = `
                            <div class="flex items-center">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="org-checkbox text-blue-600 focus:ring-blue-500" data-org-id="${newNodeId}">
                                    <span class="ml-2 text-sm">${orgName}</span>
                                </label>
                            </div>
                        `;
                        
                        orgTree.appendChild(newLi);
                    }
                    
                    // 更新组织结构图
                    updateOrgChart();
                } else {
                    // 编辑节点
                    // 获取当前编辑的节点ID
                    const editingNodeId = document.getElementById('editing-node-id')?.value;
                    if (editingNodeId) {
                        // 更新节点文本
                        const nodeSpan = document.querySelector(`.org-checkbox[data-org-id="${editingNodeId}"]`)?.nextElementSibling;
                        if (nodeSpan) {
                            nodeSpan.textContent = orgName;
                        }
                        
                        // 更新组织结构图中的节点
                        const chartNode = document.querySelector(`.org-chart [data-org-id="${editingNodeId}"] .node-title`);
                        if (chartNode) {
                            chartNode.textContent = orgName;
                        }
                    }
                }
                
                // 关闭模态框
                addOrgModal.classList.add('hidden');
            });
        }
        
        // 角色设置功能
        const addRoleBtn = document.getElementById('add-role-btn');
        const personRoleSelect = document.getElementById('person-role-select');
        
        if (addRoleBtn && personRoleSelect) {
            addRoleBtn.addEventListener('click', function() {
                const selectedPersons = Array.from(document.querySelectorAll('input[name="persons[]"]:checked'));
                const selectedRole = personRoleSelect.value;
                const roleText = personRoleSelect.options[personRoleSelect.selectedIndex].text;
                
                if (selectedPersons.length === 0) {
                    alert('请先选择人员/单位');
                    return;
                }
                
                if (!selectedRole) {
                    alert('请选择角色类型');
                    return;
                }
                
                // 为选中的人员添加角色标记
                selectedPersons.forEach(person => {
                    const personLabel = person.nextElementSibling;
                    const personText = personLabel.textContent;
                    
                    // 避免重复添加角色
                    if (!personText.includes('（')) {
                        personLabel.textContent = `${personText}（${roleText}）`;
                    } else {
                        // 已有角色，更新角色
                        personLabel.textContent = personText.replace(/（.*）/, `（${roleText}）`);
                    }
                });
            });
        }
        
        // 预览结构图
        if (previewOrgBtn && previewOrgModal) {
            previewOrgBtn.addEventListener('click', function() {
                previewOrgModal.classList.remove('hidden');
            });
        }
        
        // 关闭预览结构图
        if (closePreviewModalBtn && previewOrgModal) {
            closePreviewModalBtn.addEventListener('click', function() {
                previewOrgModal.classList.add('hidden');
            });
        }
        
        if (closePreviewBtn && previewOrgModal) {
            closePreviewBtn.addEventListener('click', function() {
                previewOrgModal.classList.add('hidden');
            });
        }
        
        // 处理编辑机构按钮点击事件
        document.querySelectorAll('.node-actions button[title="编辑"]').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                
                // 获取当前节点信息
                const nodeContent = this.closest('.node-content');
                const nodeName = nodeContent.querySelector('.node-title').textContent;
                const nodeDetails = nodeContent.nextElementSibling;
                
                // 设置模态框标题
                document.getElementById('modalTitle').textContent = '编辑机构';
                
                // 填充表单
                document.getElementById('org-name').value = nodeName;
                
                // 判断是否为顶级机构
                const isTopLevel = nodeContent.querySelector('.bg-blue-100') !== null;
                if (isTopLevel) {
                    document.querySelector('input[name="org_level"][value="top"]').checked = true;
                    document.getElementById('parent-org-field').classList.add('hidden');
                } else {
                    document.querySelector('input[name="org_level"][value="sub"]').checked = true;
                    document.getElementById('parent-org-field').classList.remove('hidden');
                    
                    // 设置父级机构
                    // 这里需要根据实际情况获取父级机构ID
                    // document.getElementById('parent-org-select').value = parentId;
                }
                
                // 如果有详情节点，提取详情信息
                if (nodeDetails && nodeDetails.classList.contains('node-details')) {
                    // 提取职责信息
                    const dutyDiv = nodeDetails.querySelector('label[class*="主要职责"] + div div');
                    if (dutyDiv) {
                        document.getElementById('org-duty').value = dutyDiv.textContent.trim();
                    }
                    
                    // 清除所有复选框选中状态
                    document.querySelectorAll('input[name="persons[]"], input[name="level[]"]').forEach(cb => {
                        cb.checked = false;
                    });
                    
                    // 提取关联人员/单位信息
                    const personsDiv = nodeDetails.querySelector('label[class*="关联人员"] + div div');
                    if (personsDiv) {
                        const personSpans = personsDiv.querySelectorAll('span');
                        personSpans.forEach(span => {
                            const personText = span.textContent.trim();
                            document.querySelectorAll('input[name="persons[]"]').forEach(cb => {
                                const cbLabel = cb.nextElementSibling.textContent.trim();
                                if (personText.includes(cbLabel)) {
                                    cb.checked = true;
                                    // 如果人员有角色信息，更新标签
                                    if (personText.includes('（')) {
                                        const roleText = personText.match(/（(.+)）/)[1];
                                        cb.nextElementSibling.textContent = `${cbLabel}（${roleText}）`;
                                    }
                                }
                            });
                        });
                    }
                    
                    // 提取关联事件级别信息
                    const levelsDiv = nodeDetails.querySelector('label[class*="关联事件级别"] + div');
                    if (levelsDiv) {
                        const levelSpans = levelsDiv.querySelectorAll('span');
                        levelSpans.forEach(span => {
                            const levelText = span.textContent.trim();
                            document.querySelectorAll('input[name="level[]"]').forEach(cb => {
                                if (levelText.includes(cb.nextElementSibling.textContent.trim())) {
                                    cb.checked = true;
                                }
                            });
                        });
                    }
                }
                
                // 显示模态框
                document.getElementById('add-org-modal').classList.remove('hidden');
            });
        });
    });

    // 添加事件级别管理相关代码
    document.addEventListener('DOMContentLoaded', function() {
        // 已有的标签切换代码
        
        // 添加事件级别的功能
        const addLevelBtn = document.getElementById('add-level-btn');
        const levelContainer = document.getElementById('level-container');
        
        if (addLevelBtn && levelContainer) {
            addLevelBtn.addEventListener('click', function() {
                // 获取第一个级别条目作为模板
                const template = levelContainer.querySelector('.level-entry');
                const clone = template.cloneNode(true);
                
                // 清空复制后的表单内容
                clone.querySelector('.response-condition').value = '';
                clone.querySelector('.disposal-measures').value = '';
                
                // 清空物资列表，只保留一个材料行
                const materialContainer = clone.querySelector('.material-container');
                const materialTemplate = materialContainer.querySelector('.material-entry');
                
                // 删除所有已有的物资条目
                const materialEntries = materialContainer.querySelectorAll('.material-entry');
                materialEntries.forEach((entry, index) => {
                    if (index > 0) {
                        entry.remove();
                    }
                });
                
                // 清空车辆列表，只保留一个车辆行
                const vehicleContainer = clone.querySelector('.vehicle-container');
                const vehicleTemplate = vehicleContainer.querySelector('.vehicle-entry');
                
                // 删除所有已有的车辆条目
                const vehicleEntries = vehicleContainer.querySelectorAll('.vehicle-entry');
                vehicleEntries.forEach((entry, index) => {
                    if (index > 0) {
                        entry.remove();
                    }
                });
                
                // 显示删除按钮
                clone.querySelector('.remove-level-btn').style.display = 'block';
                
                // 添加到容器
                levelContainer.appendChild(clone);
                
                // 重新绑定事件
                bindLevelEvents();
            });
            
            // 初始绑定事件
            bindLevelEvents();
        }
        
        // 绑定各种事件处理函数
        function bindLevelEvents() {
            // 删除级别条目
            document.querySelectorAll('.remove-level-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 确保至少有一个级别
                    if (document.querySelectorAll('.level-entry').length > 1) {
                        this.closest('.level-entry').remove();
                    }
                });
            });
            
            // 添加物资
            document.querySelectorAll('.add-material-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const container = this.closest('.level-entry').querySelector('.material-container');
                    const template = container.querySelector('.material-entry');
                    const clone = template.cloneNode(true);
                    
                    // 添加到容器
                    container.appendChild(clone);
                    
                    // 绑定删除事件
                    clone.querySelector('.remove-material-btn').addEventListener('click', function() {
                        this.closest('.material-entry').remove();
                    });
                });
            });
            
            // 删除物资
            document.querySelectorAll('.remove-material-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 确保至少有一个物资条目
                    const container = this.closest('.material-container');
                    if (container.querySelectorAll('.material-entry').length > 1) {
                        this.closest('.material-entry').remove();
                    }
                });
            });
            
            // 添加车辆
            document.querySelectorAll('.add-vehicle-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const container = this.closest('.level-entry').querySelector('.vehicle-container');
                    const template = container.querySelector('.vehicle-entry');
                    const clone = template.cloneNode(true);
                    
                    // 添加到容器
                    container.appendChild(clone);
                    
                    // 绑定删除事件
                    clone.querySelector('.remove-vehicle-btn').addEventListener('click', function() {
                        this.closest('.vehicle-entry').remove();
                    });
                });
            });
            
            // 删除车辆
            document.querySelectorAll('.remove-vehicle-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // 确保至少有一个车辆条目
                    const container = this.closest('.vehicle-container');
                    if (container.querySelectorAll('.vehicle-entry').length > 1) {
                        this.closest('.vehicle-entry').remove();
                    }
                });
            });
            
            // 检查事件级别下拉框的唯一性
            document.querySelectorAll('.event-level-select').forEach(select => {
                select.addEventListener('change', function() {
                    const selectedValue = this.value;
                    const allSelects = document.querySelectorAll('.event-level-select');
                    
                    // 检查是否有重复选择
                    let duplicateFound = false;
                    allSelects.forEach(otherSelect => {
                        if (otherSelect !== this && otherSelect.value === selectedValue) {
                            duplicateFound = true;
                        }
                    });
                    
                    // 如果有重复，提示用户
                    if (duplicateFound) {
                        alert('事件级别不能重复选择，请选择其他级别。');
                        // 恢复默认值或者其他处理
                    }
                });
            });
        }
    });

    // 添加加载组织架构和人员数据的代码
    function loadOrganizationsAndPersonnel() {
        // 模拟从组织架构页面获取数据
        const organizations = [
            { id: 'org-1', name: '广西壮族自治区交通运输厅', level: 1 },
            { id: 'org-1-1', name: '直属事业单位及专项机构', level: 2, parentId: 'org-1' },
            { id: 'org-1-1-1', name: '自治区公路发展中心', level: 3, parentId: 'org-1-1' },
            { id: 'org-1-1-2', name: '自治区高速公路发展中心', level: 3, parentId: 'org-1-1' },
            { id: 'org-1-1-3', name: '自治区道路运输发展中心', level: 3, parentId: 'org-1-1' },
            { id: 'org-1-2', name: '市级交通运输局', level: 2, parentId: 'org-1' },
            { id: 'org-1-2-1', name: '钦州市交通运输局', level: 3, parentId: 'org-1-2' },
            { id: 'org-1-2-2', name: '南宁市交通运输局', level: 3, parentId: 'org-1-2' },
            { id: 'org-1-2-3', name: '玉林市交通运输局', level: 3, parentId: 'org-1-2' }
        ];
        
        // 模拟从人员管理页面获取数据
        const personnel = [
            { id: 'person-1', name: '李明', gender: '男', department: '安监科', position: '科长', phone: '13900139001' },
            { id: 'person-2', name: '王芳', gender: '女', department: '安监科', position: '科员', phone: '13800138002' },
            { id: 'person-3', name: '张伟', gender: '男', department: '安监科', position: '科员', phone: '13800138003' },
            { id: 'person-4', name: '赵丽', gender: '女', department: '安监科', position: '科员', phone: '13800138004' },
            { id: 'person-5', name: '陈刚', gender: '男', department: '安监科', position: '科员', phone: '13800138005' }
        ];
        
        // 实际应用中，这里应该是通过API获取数据
        // 例如：fetch('/api/organizations').then(response => response.json())...
        
        return { organizations, personnel };
    }
    
    // 初始化表单数据
    function initializeFormData() {
        const data = loadOrganizationsAndPersonnel();
        
        // 填充上级机构下拉框
        const parentOrgSelect = document.getElementById('parent-org-select');
        // 清空现有选项（除了第一个）
        while (parentOrgSelect.options.length > 1) {
            parentOrgSelect.remove(1);
        }
        
        // 添加组织架构数据
        data.organizations.forEach(org => {
            const option = document.createElement('option');
            option.value = org.id;
            option.textContent = org.name;
            parentOrgSelect.appendChild(option);
        });
        
        // 填充人员/单位复选框区域
        const personsContainer = document.querySelector('[name="persons[]"]').closest('.grid');
        personsContainer.innerHTML = ''; // 清空现有内容
        
        // 添加人员数据
        data.personnel.forEach(person => {
            const label = document.createElement('label');
            label.className = 'inline-flex items-center space-x-2 bg-white p-2 rounded border hover:bg-gray-50';
            label.innerHTML = `
                <input type="checkbox" name="persons[]" value="${person.id}" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm">${person.name}</span>
            `;
            personsContainer.appendChild(label);
        });
        
        // 添加可选的组织单位
        data.organizations.filter(org => org.level === 3).forEach(org => {
            const label = document.createElement('label');
            label.className = 'inline-flex items-center space-x-2 bg-white p-2 rounded border hover:bg-gray-50';
            label.innerHTML = `
                <input type="checkbox" name="persons[]" value="${org.id}" class="text-blue-600 focus:ring-blue-500">
                <span class="text-sm">${org.name}</span>
            `;
            personsContainer.appendChild(label);
        });
    }
    
    // 在打开模态框时加载数据
    document.getElementById('add-org-btn').addEventListener('click', function() {
        initializeFormData();
    });

    // 添加更新组织结构图的函数
    function updateOrgChart() {
        // 在实际应用中，这个函数会根据当前的组织结构生成预览图
        // 这里只是一个简单的模拟实现
        console.log('组织结构图已更新');
        
        // 获取组织机构树中的所有节点
        const orgNodes = document.querySelectorAll('.org-tree input[type="checkbox"]');
        const orgChart = document.querySelector('.org-chart');
        
        // 如果没有预览图容器，就不做任何操作
        if (!orgChart) return;
        
        // 清空现有的预览图内容
        // orgChart.innerHTML = ''; // 在实际应用中可能需要清空
        
        // 这里只是更新了操作日志，表明结构图已经更新
        const timestamp = new Date().toLocaleString();
        const logItem = document.createElement('div');
        logItem.className = 'text-xs text-gray-500 mt-2';
        logItem.textContent = `[${timestamp}] 组织结构图已更新`;
        
        // 找到操作日志区域并添加记录
        const logsArea = document.querySelector('.operation-logs');
        if (logsArea) {
            logsArea.insertBefore(logItem, logsArea.firstChild);
        }
    }
    
    // 关联人员单位选择器功能脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 树形结构折叠展开功能
        document.querySelectorAll('.toggle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const targetEl = document.getElementById(targetId);
                
                if (targetEl) {
                    if (targetEl.classList.contains('hidden')) {
                        targetEl.classList.remove('hidden');
                        this.classList.remove('collapsed');
                    } else {
                        targetEl.classList.add('hidden');
                        this.classList.add('collapsed');
                    }
                }
            });
        });

        // 处理组织机构的选择
        const orgCheckboxes = document.querySelectorAll('.org-checkbox');
        const personCheckboxes = document.querySelectorAll('.person-checkbox');
        const personList = document.querySelector('.person-list');
        const selectedItemsContainer = document.querySelector('.selected-items');
        const selectedNoneText = document.querySelector('.selected-none');

        // 角色选择功能
        const personRoleSelect = document.getElementById('person-role-select');
        const addRoleBtn = document.getElementById('add-role-btn');
        
        // 全选功能
        const selectAllOrgsBtn = document.getElementById('select-all-orgs');
        const selectAllPersonsBtn = document.getElementById('select-all-persons');
        
        // 搜索功能
        const personSearch = document.getElementById('person-search');

        // 监听组织机构选择变化
        orgCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const orgId = this.getAttribute('data-org-id');
                
                // 如果选中，添加到已选择区域
                if (this.checked) {
                    addSelectedItem(orgId, this.nextElementSibling.textContent, 'org');
                } else {
                    removeSelectedItem(orgId);
                }

                // 过滤人员列表，只显示选中组织的人员
                filterPersonList();
            });
        });

        // 监听人员选择变化
        personCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const personId = this.getAttribute('data-person-id');
                
                // 如果选中，添加到已选择区域
                if (this.checked) {
                    addSelectedItem(personId, this.nextElementSibling.textContent, 'person');
                } else {
                    removeSelectedItem(personId);
                }
            });
        });

        // 添加已选择项目
        function addSelectedItem(id, text, type) {
            if (selectedNoneText) {
                selectedNoneText.classList.add('hidden');
            }
            
            // 检查是否已存在
            const existingItem = selectedItemsContainer.querySelector(`[data-id="${id}"]`);
            if (!existingItem) {
                const span = document.createElement('span');
                span.className = 'selected-item px-2 py-1 rounded-full text-xs flex items-center';
                span.setAttribute('data-id', id);
                span.setAttribute('data-type', type);
                
                // 为组织和人员设置不同的背景色
                if (type === 'org') {
                    span.classList.add('bg-blue-100', 'text-blue-800', 'border', 'border-blue-200');
                } else {
                    span.classList.add('bg-green-100', 'text-green-800', 'border', 'border-green-200');
                }
                
                span.innerHTML = `
                    ${text}
                    <button type="button" class="ml-1 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                // 添加点击删除功能
                span.querySelector('button').addEventListener('click', function() {
                    // 取消对应的复选框选中状态
                    if (type === 'org') {
                        document.querySelector(`.org-checkbox[data-org-id="${id}"]`).checked = false;
                    } else {
                        document.querySelector(`.person-checkbox[data-person-id="${id}"]`).checked = false;
                    }
                    
                    // 移除标签
                    span.remove();
                    
                    // 如果没有选择项，显示"暂无选择"
                        if (selectedItemsContainer.querySelectorAll('.selected-item').length === 0) {
                            selectedNoneText.classList.remove('hidden');
                        }
                        
                        // 如果是组织，更新人员列表过滤
                        if (type === 'org') {
                            filterPersonList();
                        }
                    });
                    
                    selectedItemsContainer.appendChild(span);
                }
            }

            // 移除已选择项目
            function removeSelectedItem(id) {
                const item = selectedItemsContainer.querySelector(`[data-id="${id}"]`);
                if (item) {
                    item.remove();
                    
                    // 如果没有选择项，显示"暂无选择"
                    if (selectedItemsContainer.querySelectorAll('.selected-item').length === 0) {
                        selectedNoneText.classList.remove('hidden');
                    }
                }
            }

            // 根据选中的组织过滤人员列表
            function filterPersonList() {
                const selectedOrgs = Array.from(document.querySelectorAll('.org-checkbox:checked')).map(cb => 
                    cb.getAttribute('data-org-id')
                );
                
                // 如果没有选中的组织，显示所有人员
                if (selectedOrgs.length === 0) {
                    personCheckboxes.forEach(cb => {
                        cb.closest('label').style.display = '';
                    });
                    return;
                }
                
                // 显示属于选中组织的人员
                personCheckboxes.forEach(cb => {
                    const personOrgId = cb.getAttribute('data-org-id');
                    if (selectedOrgs.includes(personOrgId)) {
                        cb.closest('label').style.display = '';
                    } else {
                        cb.closest('label').style.display = 'none';
                    }
                });
            }

            // 全选组织
            if (selectAllOrgsBtn) {
                selectAllOrgsBtn.addEventListener('click', function() {
                    const anyUnchecked = Array.from(orgCheckboxes).some(cb => !cb.checked);
                    
                    orgCheckboxes.forEach(cb => {
                        cb.checked = anyUnchecked;
                        
                        if (anyUnchecked) {
                            // 添加到已选择区域
                            addSelectedItem(cb.getAttribute('data-org-id'), cb.nextElementSibling.textContent, 'org');
                        } else {
                            // 从已选择区域移除
                            removeSelectedItem(cb.getAttribute('data-org-id'));
                        }
                    });
                    
                    // 更新人员列表过滤
                    filterPersonList();
                });
            }

            // 全选人员
            if (selectAllPersonsBtn) {
                selectAllPersonsBtn.addEventListener('click', function() {
                    // 获取当前显示的人员复选框
                    const visiblePersonCheckboxes = Array.from(personCheckboxes).filter(cb => 
                        cb.closest('label').style.display !== 'none'
                    );
                    
                    const anyUnchecked = visiblePersonCheckboxes.some(cb => !cb.checked);
                    
                    visiblePersonCheckboxes.forEach(cb => {
                        cb.checked = anyUnchecked;
                        
                        if (anyUnchecked) {
                            // 添加到已选择区域
                            addSelectedItem(cb.getAttribute('data-person-id'), cb.nextElementSibling.textContent, 'person');
                        } else {
                            // 从已选择区域移除
                            removeSelectedItem(cb.getAttribute('data-person-id'));
                        }
                    });
                });
            }

            // 人员搜索功能
            if (personSearch) {
                personSearch.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase();
                    
                    personCheckboxes.forEach(cb => {
                        const personName = cb.nextElementSibling.textContent.toLowerCase();
                        const label = cb.closest('label');
                        
                        // 不符合搜索条件
                        if (searchTerm && !personName.includes(searchTerm)) {
                            label.style.display = 'none';
                        } 
                        // 符合搜索条件但需要检查组织过滤
                        else {
                            const selectedOrgs = Array.from(document.querySelectorAll('.org-checkbox:checked')).map(cb => 
                                cb.getAttribute('data-org-id')
                            );
                            
                            if (selectedOrgs.length === 0 || selectedOrgs.includes(cb.getAttribute('data-org-id'))) {
                                label.style.display = '';
                            } else {
                                label.style.display = 'none';
                            }
                        }
                    });
                });
            }

            // 添加角色功能
            if (addRoleBtn && personRoleSelect) {
                addRoleBtn.addEventListener('click', function() {
                    const selectedPersons = Array.from(document.querySelectorAll('.person-checkbox:checked'));
                    const selectedRole = personRoleSelect.value;
                    const roleText = personRoleSelect.options[personRoleSelect.selectedIndex].text;
                    
                    if (selectedPersons.length === 0) {
                        alert('请先选择人员');
                        return;
                    }
                    
                    if (!selectedRole) {
                        alert('请选择角色类型');
                        return;
                    }
                    
                    // 更新已选择区域中的人员标签，添加角色信息
                    selectedPersons.forEach(person => {
                        const personId = person.getAttribute('data-person-id');
                        const personName = person.nextElementSibling.textContent;
                        
                        // 先移除现有标签
                        removeSelectedItem(personId);
                        
                        // 添加带角色的新标签
                        addSelectedItem(personId, `${personName} (${roleText})`, 'person');
                    });
                });
            }
        });
    </script>

    <!-- Add Vue and Element Plus JS -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Add Initialization Script for Basic Info Selects -->
    <script>
         const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]},
            // Add other top-level orgs if needed
            { value: 'org-emg', label: '应急管理局' }, // Example from original select
            { value: 'org-health', label: '卫生健康委员会' },
            { value: 'org-police', label: '公安局' },
            { value: 'org-housing', label: '住建局' },
        ];

        const BasicInfoApp = {
            data() {
                return {
                    // Initialize with existing values if possible, otherwise null/empty
                    formData: {
                        planType: '1', // Default '自然灾害类'
                        compileUnit: 'org-emg', // Default '应急管理局'
                        applyUnits: ['org-emg', 'org-health', 'org-police', 'org-housing'] // Default selected multiple units
                    },
                    planTypeOptions: [
                        { value: '1', label: '自然灾害类' },
                        { value: '2', label: '事故灾难类' },
                        { value: '3', label: '公共卫生类' },
                        { value: '4', label: '社会安全类' },
                        { value: '5', label: '其他类型' }
                    ],
                    unitOptions: standardUnitOptions // Use the tree data for both unit selects
                };
            },
            methods: {
                // Add methods if needed for validation or interaction
            }
        };

        const basicInfoApp = Vue.createApp(BasicInfoApp);
        basicInfoApp.use(ElementPlus);
        basicInfoApp.mount('#basic-info'); // Mount to the basic info tab container

    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 