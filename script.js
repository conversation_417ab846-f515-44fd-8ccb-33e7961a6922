// Get the modal elements
const modal = document.getElementById("details-modal");
const modalTitle = document.getElementById("modal-title");
const modalBody = document.getElementById("modal-body");

const infoPanel = document.getElementById('info-panel');
const mapContainer = document.getElementById('map-container');
const mapLegend = document.getElementById('map-legend');
const selectAllLayersCheckbox = document.getElementById('select-all-layers'); // New: Select All Checkbox

let unitSelectApp = null; // To hold the Vue app instance for unit selection

// Function to open the modal, always centered
function openModal(clickEvent) { // clickEvent parameter is kept for compatibility but not used for positioning
    modal.style.display = 'block';

    // Always center the modal
    modal.style.left = '50%';
    modal.style.top = '50%';
    modal.style.transform = 'translate(-50%, -50%)';
}

// Function to close the modal
function closeModal() {
    modal.style.display = "none";
    modalBody.innerHTML = ""; // Clear previous content

    // If a Vue app for unit selection was created, unmount it
    if (unitSelectApp) {
        unitSelectApp.unmount();
        unitSelectApp = null;
    }

    // Clear positioning styles on close
    modal.style.left = '';
    modal.style.top = '';
    modal.style.transform = '';
    modal.classList.remove('modal-wide'); // Ensure wide class is removed
    modal.classList.remove('modal-extra-wide'); // Ensure extra-wide class is removed
}

// Close modal if user clicks outside of it or on the close button
window.onclick = function(event) {
    if (event.target == modal) {
        closeModal();
    }
}

// Sample marker and line data
// For lines: x, y are top-left. 'width' is length. 'angle' is rotation in degrees.
const allMapElementsData = [
    // Point Markers (existing types)
    {
        id: 'risk1', itemType: 'point', layerType: 'risks', name: 'G324 K1500+200 地质灾害风险',
        x: '45%', y: '35%',
        detailsTitle: '风险隐患详情：G324 K1500+200 地质灾害风险',
        data: {
            responsibleParties: { // Updated structure for responsible parties
                provincial: { unit: '广西壮族自治区交通运输厅', person: '李主管', contact: '13800138000' },
                review: { unit: '南宁市交通运输局', person: '王科长', contact: '13900139001' },
                investigation: { unit: '青秀区公路发展中心', person: '张工程师', contact: '13700137002' }
            },
            details: { // Updated structure for risk details
                checkCategory: '边坡地质灾害',
                roadNumber: 'G324',
                stakeNumber: 'K1500+200',
                riskType: '危岩落石',
                riskLevel: '重大',
                description: '该路段边坡存在多处危岩体，雨季易发生落石，严重威胁行车安全。已设置警示标志，需尽快进行专业排险加固处理。',
                recommendedMeasures: '采用主动防护网结合锚杆框架梁进行加固，清理坡面浮石。',
                photos: [
                    { url: 'lib/photos/risk_photo1.jpg', caption: '危岩体远景' },
                    { url: 'lib/photos/risk_photo2.jpg', caption: '落石区域近景' }
                ]
            },
            rectification: {
                isRectified: false,
                completionTime: null,
                dueDate: '2024-09-30',
                measures: '已完成地质勘察和治理方案设计，正在进行施工招标。预计9月底前完成治理。',
                attachments: [
                    { name: '地质勘察报告.pdf', url: '#' },
                    { name: '治理方案设计图.pdf', url: '#' }
                ]
            },
            emergencySupplies: { // Simplified for risk context, full details are on actual supply points
                warehouseName: '青秀区应急物资储备库 (约5km)',
                location: '仙葫大道8号',
                managerContact: '刘备 - 13800138000',
                materials: [
                    { name: '警示桩', model: '标准型', quantity: 50, status: '良好' },
                    { name: '反光锥', model: '70cm', quantity: 100, status: '良好' }
                ]
            },
            nearbyRescueTeams: [
                { name: '青秀区第一应急救援中队', location: '长虹路5号 (约3km)', equipmentModel: '综合救援车, 生命探测仪', contact: '关羽 - 13912345678', specialization: '综合救援、搜救' },
                { name: '市交通应急抢险队三队', location: '竹溪大道辅路 (约5km)', equipmentModel: '大型清障车, 吊车', contact: '张飞 - 13787654321', specialization: '道路清障、交通事故处理' }
            ]
        }
    },
    {
        id: 'rescue1', itemType: 'point', layerType: 'rescue', name: '高速应急救援一队',
        x: '55%', y: '60%',
        detailsTitle: '应急救援力量: 高速应急救援一队',
        data: {
            status: '待命',
            personnelCount: '30人',
            contactPerson: '赵队长',
            contactPhone: '13600000001',
            mainEquipment: ['清障车2辆', '吊车1辆', '破拆工具组'],
            get summary() {
                return `${this.contactPerson} - ${this.contactPhone}`;
            }
        }
    },
    {
        id: 'project1', itemType: 'point', layerType: 'projects', name: 'XX高速改扩建工程',
        x: '30%', y: '50%',
        detailsTitle: '在建项目详情: XX高速改扩建工程',
        data: {
            basicInfo: {
                owner: '广西交通投资集团',
                progress: '65%',
                projectManagerName: '陈工',
                projectManagerContact: '135xxxx0005',
                primaryRisksDescription: 'xxxx高速改扩建'
            },
            linkedElementIds: { // Store IDs of linked elements
                risks: ['risk1'],
                supplies: ['supplies1'],
                rescueTeams: ['rescue1']
            }
        }
    },
    {
        id: 'event1', itemType: 'point', layerType: 'events', name: 'G72追尾事故',
        x: '60%', y: '25%',
        detailsTitle: '应急事件详情: G72多车追尾',
        data: {
            basicInfo: {
                type: '多车追尾',
                status: '处置中',
                reportedTime: '14:35 (2024-07-28)',
                location: 'G72泉南高速 南宁往柳州方向 K1350+500',
                impact: '双向交通中断，造成约3公里拥堵，预计2小时内恢复单向通行。涉及3辆小汽车，2人轻伤。',
                cause: '雨天路滑，车辆跟车过近导致。'
            },
            emergencyPlan: {
                planName: '广西高速公路交通事故应急预案 (GX-HP-EP-001)',
                applicableScope: 'G72泉南高速 K1300-K1400 (南宁城区段)',
                recommendedLevel: 'III级 (较大)',
                eventLevels: [
                    { level: "IV级 (一般)", condition: "一般事故，造成3人以下轻伤或仅有财产损失" },
                    { level: "III级 (较大)", condition: "较大事故，造成3人以下死亡或10人以下重伤，或造成重要交通干线中断2小时以上" },
                    { level: "II级 (重大)", condition: "重大事故，造成3-10人死亡或10-30人重伤，或造成区域性交通瘫痪" },
                    { level: "I级 (特别重大)", condition: "特别重大事故，造成10人以上死亡或30人以上重伤，或造成大范围、长时间交通瘫痪" }
                ],
                disposalProcedure: [
                    "1. 接警与报告：核实信息，迅速上报指挥中心和相关部门。",
                    "2. 启动预案：根据事件等级启动相应级别应急响应。",
                    "3. 现场处置：设置警戒区，组织抢救伤员，疏导交通，清理现场。",
                    "4. 信息发布：及时向公众发布路况信息和绕行建议。",
                    "5. 调查与评估：事故处理完毕后，进行调查分析和总结评估。"
                ],
                commandStructure: {
                    title: "G72追尾事故应急指挥部",
                    groups: [
                        {
                            name: "总指挥组",
                            department: "市应急管理局、市交通运输局",
                            members: [
                                { role: "总指挥", name: "王局长", contact: "13800138000" },
                                { role: "副总指挥", name: "李副局长", contact: "13900139001" }
                            ]
                        },
                        {
                            name: "现场工作组",
                            department: "交警、路政、医疗、消防",
                            members: [
                                { role: "交警指挥员", name: "李警官", contact: "13800138000" },
                                { role: "路政负责人", name: "王队长", contact: "13900139001" },
                                { role: "医疗急救组长", name: "刘医生", contact: "13700137002" }
                            ]
                        },
                        {
                            name: "后勤保障组",
                            department: "高速运营公司",
                            members: [
                                { role: "物资调配", name: "钱调度", contact: "13600136000" },
                                { role: "车辆保障", name: "孙师傅", contact: "13700137002" }
                            ]
                        }
                    ]
                }
            },
            nearbySupplies: [
                { name: '附近应急物资点A', location: '高速管理站旁 (约2km)', contact: '库管员黄工 - 13000000000', itemsPreview: '反光锥, 隔离带, 应急照明'},
                { name: '移动应急物资车B', location: '巡逻中 (动态)', contact: '车载员冯工 - 13100000000', itemsPreview: '急救包, 简单工具, 饮用水'}
            ],
            nearbyRescueTeams: [
                { name: '高速交警第一大队事故处理中队', location: '事故现场', contact: '现场警员 - 13800138000', equipmentModel: '警车,勘察设备', specialization: '交通事故处理、现场勘查' },
                { name: '路政巡查一中队', location: '事故现场', contact: '现场路政员 - 13900139001 ', equipmentModel: '巡查车,清障工具', specialization: '路面巡查、清障保通' },
                { name: '120急救中心XX分站', location: '已出动赶往现场', contact: '120调度 - 13700137002', equipmentModel: '救护车', specialization: '医疗救援、伤员转运' }
            ],
            expertContacts: [
                { name: '交通安全专家 - 黄教授', field: '事故成因分析、交通疏导', phone: '13812340001' },
                { name: '应急管理专家 - 孙研究员', field: '应急预案评估、指挥协调', phone: '13912340002' }
            ]
        }
    },
    // New: Emergency Supplies Data Points
    {
        id: 'supplies1', itemType: 'point', layerType: 'supplies', name: '兴宁区应急物资储备中心',
        x: '35%', y: '20%',
        detailsTitle: '应急物资储备点: 兴宁区应急物资储备中心',
        data: {
            warehouseName: '兴宁区应急物资储备中心',
            location: '昆仑大道辅路101号',
            area: '占地500平方米，库容2000立方米',
            manager: { name: '赵管理者', contact: '13501350135' },
            lastUpdateTime: '2024-07-25 09:00',
            materials: [
                { category: '防护用品', name: 'N95口罩', model: '标准型', quantity: '20000个', unit: '个', status: '良好', notes: '有效期至2026年' },
                { category: '防护用品', name: '防护服', model: 'XL', quantity: '500套', unit: '套', status: '良好', notes: '-' },
                { category: '救援工具', name: '液压剪扩器', model: '型号A', quantity: '5台', unit: '台', status: '良好', notes: '定期保养' },
                { category: '救援工具', name: '应急照明灯组', model: '型号B', quantity: '20套', unit: '套', status: '待检修', notes: '3套电池老化' },
                { category: '生活保障', name: '瓶装饮用水', model: '500ml', quantity: '100箱', unit: '箱', status: '良好', notes: '每箱24瓶' },
                { category: '生活保障', name: '方便食品', model: '混合口味', quantity: '50箱', unit: '箱', status: '良好', notes: '注意保质期' }
            ]
        }
    },
    {
        id: 'supplies2', itemType: 'point', layerType: 'supplies', name: '江南区道路养护应急点',
        x: '65%', y: '75%',
        detailsTitle: '应急物资储备点: 江南区道路养护应急点',
        data: {
            warehouseName: '江南区道路养护应急点',
            location: '银沙大道88号（养护工区内）',
            area: '仓库50平方米',
            manager: { name: '钱库管', contact: '13601360136' },
            lastUpdateTime: '2024-07-28 11:00',
            materials: [
                { category: '交通设施', name: '反光锥桶', model: '750mm', quantity: '200个', unit: '个', status: '良好', notes: '-' },
                { category: '交通设施', name: '水马', model: '塑料', quantity: '50个', unit: '个', status: '良好', notes: '-' },
                { category: '抢险材料', name: '编织袋', model: '大号', quantity: '1000条', unit: '条', status: '良好', notes: '防汛备用' },
                { category: '抢险材料', name: '工业盐', model: '-', quantity: '5吨', unit: '吨', status: '良好', notes: '冬季除冰用（库存）' }
            ]
        }
    },
    // Line Segments (New types + existing 'congestion')
    {
        id: 'highway1', itemType: 'line', layerType: 'highways', name: 'G72泉南高速示范段',
        x: '20%', y: '30%', width: '15%', angle: 10, // Example: 15% length, slight angle
        detailsTitle: '运营高速路段: G72泉南高速示范段',
        htmlContent: `<p><strong>路况:</strong> 良好</p><p><strong>限速:</strong> 120km/h</p><p><strong>监控覆盖率:</strong> 100%</p>`
    },
    {
        id: 'waterway1', itemType: 'line', layerType: 'waterways', name: '西江航道某段',
        x: '65%', y: '70%', width: '20%', angle: -5,
        detailsTitle: '水路: 西江航道某段',
        htmlContent: `<p><strong>等级:</strong> II级航道</p><p><strong>水深:</strong> 5m</p><p><strong>通航能力:</strong> 1000吨级船舶</p>`
    },
    {
        id: 'road1', itemType: 'line', layerType: 'roads', name: 'G324国道重要连接线',
        x: '10%', y: '60%', width: '18%', angle: 0,
        detailsTitle: '重要国省干道: G324连接线',
        htmlContent: `<p><strong>维护单位:</strong> XX公路局</p><p><strong>近期养护:</strong> 是 (2024-06)</p><p><strong>路面状况:</strong> 优</p>`
    },
    {
        id: 'railway1', itemType: 'line', layerType: 'railways', name: '湘桂铁路繁忙区段',
        x: '40%', y: '15%', width: '25%', angle: 5,
        detailsTitle: '铁路运输网络: 湘桂铁路繁忙段',
        htmlContent: `<p><strong>状态:</strong> 正常运营</p><p><strong>列车密度:</strong> 高</p><p><strong>主要货品:</strong> 煤炭、集装箱</p>`
    },
    {
        id: 'congestion1', itemType: 'line', layerType: 'congestion', name: '南宁东收费站入口拥堵',
        x: '50%', y: '45%', width: '10%', angle: 0, // Representing congestion as a line segment
        detailsTitle: '拥堵路段详情: 南宁东收费站入口',
        data: {
            basicInfo: {
                roadSectionId: 'G72-NN-003',
                startStake: 'K1499+800',
                averageSpeed: '10 km/h',
                congestionLength: '约1km',
                estimatedReliefTime: '30分钟',
            },
            monitoringVideos: [
                { id: 'vid1', name: 'K1499+500 监控画面', placeholderImageUrl: 'lib/images/congestion_video_placeholder1.png' },
                { id: 'vid2', name: 'K1500+200 监控画面', placeholderImageUrl: 'lib/images/congestion_video_placeholder2.png' }
            ],
            detourPlan: "请从前方[琅东出口]驶离高速，经由[民族大道]、[青秀路]绕行，于[南宁东收费站]重新进入高速。预计增加15-20分钟行程。"
        }
    },
    {
        id: 'highway2', itemType: 'line', layerType: 'highways', name: 'G80广昆高速一段',
        x: '55%', y: '55%', width: '20%', angle: -15,
        detailsTitle: '运营高速路段: G80广昆高速',
        htmlContent: `<p><strong>路况:</strong> 畅通</p><p><strong>监控覆盖:</strong> 全覆盖</p>`
    }
];

function generateRiskDetailHTML(data) {
    let html = `<style>
        /* Main modal content grid for the 4 cards */
        .main-content-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: auto auto; /* Adjust if fixed height is needed */
            gap: 20px;
            margin-top: 20px; /* Space below initial details grid */
        }

        /* Style for individual items in the top general details grid */
        .risk-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Adjusted minmax */
            gap: 15px;
            margin-bottom: 20px;
        }
        .risk-details-grid > div,
        .responsible-parties-grid > div { /* Shared style for top items and responsible parties */
            background-color: #383c44; /* Slightly different from main cards */
            padding: 12px;
            border-radius: 5px;
            border: 1px solid #50555e;
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
        }
        .risk-details-grid > div > strong,
        .responsible-parties-grid > div > strong {
            color: #b0cce0;
            display: block;
            margin-bottom: 6px;
            font-size: 0.85em;
            font-weight: 600;
            text-transform: uppercase;
        }
        .risk-details-grid > div span,
        .responsible-parties-grid > div span {
             color: #e8e8e8;
             font-size: 0.95em;
        }

        /* Responsible parties grid at the very top */
        .responsible-parties-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* 3 columns for responsible parties */
            gap: 15px;
            margin-bottom: 25px;
        }

        /* Style for the 4 main info cards */
        .info-card {
            padding: 20px;
            border: 1px solid #50555e;
            border-radius: 8px;
            background-color: #383c44;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            display: flex; /* Using flex for better internal control if needed */
            flex-direction: column;
        }

        .info-card h5 { /* Section titles within cards */
            color: #a0d8ef;
            margin-top:0;
            margin-bottom:15px;
            border-bottom: 1px solid #5f6773;
            padding-bottom:10px;
            font-size: 1.15em;
            font-weight: 600;
        }
        .info-card p, .info-card ul, .info-card h6 {
            font-size: 0.95em;
            line-height: 1.6;
            color: #d0d0d0;
        }
        .info-card h6 {
            color: #b8cde0;
            margin-top: 12px;
            margin-bottom: 6px;
            font-weight: 600;
        }
        .info-card ul { padding-left: 20px; margin-top: 5px; }
        .info-card hr {
            border-color: #50555e;
            margin: 15px 0;
        }

        /* Specific value styling - can be reused */
        .risk-level-high { color: #ff8080; font-weight: bold; }
        .risk-level-major { color: #ff8080; font-weight: bold; } // For '重大'
        .is-hazard-yes { color: #ff8080; font-weight: bold; }
        .measures-no { color: #ffcc66; font-weight: bold; }
        .file-link { color: #80bfff; text-decoration:none; margin-right: 10px; }
        .file-link:hover { text-decoration:underline; }
        .material-status-good { color: #a1e8a1; }
        .material-status-bad { color: #ffcc66; }

        /* 表格样式 - 与应急物资模态框表格样式一致 */
        .supply-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            background-color: rgba(40, 44, 52, 0.8);
            color: #e6e6e6;
        }

        .supply-table th, .supply-table td {
            padding: 8px 12px;
            text-align: left;
            border: 1px solid #3a3f4b;
        }

        .supply-table th {
            background-color: rgba(30, 60, 120, 0.8);
            color: white;
        }

        .supply-table tr:nth-child(even) {
            background-color: rgba(50, 54, 62, 0.8);
        }

        .supply-table tr:hover {
            background-color: rgba(60, 64, 72, 0.9);
        }
    </style>`;

    const rp = data.responsibleParties || {};
    const prov = rp.provincial || {};
    const rev = rp.review || {};
    const inv = rp.investigation || {};

    const det = data.details || {};
    const rect = data.rectification || {};
    const supplies = data.emergencySupplies; // Note: structure is an object, not array in risk1 data
    const rescueTeams = data.nearbyRescueTeams || [];

    // 1. Responsible Parties (Top Row)
    html += `<div class="responsible-parties-grid">
        <div><strong>省级责任单位:</strong> <span>${prov.unit || '无'} (${prov.person || '无'} - ${prov.contact || '无'})</span></div>
        <div><strong>复核责任单位:</strong> <span>${rev.unit || '无'} (${rev.person || '无'} - ${rev.contact || '无'})</span></div>
        <div><strong>排查责任单位:</strong> <span>${inv.unit || '无'} (${inv.person || '无'} - ${inv.contact || '无'})</span></div>
    </div>`;

    // 2. Initial Details Grid (Below Responsible Parties)
    // Fields like cityDistrict, owningUnit, endStake, isHazard, measuresTaken are not in risk1.data.details
    // They will show '无' or their default from template if not handled.
    html += `<div class="risk-details-grid">
        <div><strong>检查类别:</strong> <span>${det.checkCategory || '无'}</span></div>
        <div><strong>市/区县:</strong> <span>${det.cityDistrict || '无'}</span></div>
        <div><strong>所属单位:</strong> <span>${det.owningUnit || '无'}</span></div>
        <div><strong>公路编号:</strong> <span>${det.roadNumber || '无'}</span></div>
        <div><strong>桩号:</strong> <span>${det.stakeNumber || '无'}</span></div>
        <div><strong>风险类型:</strong> <span>${det.riskType || '无'}</span></div>
        <div><strong>风险等级:</strong> <span class="${(det.riskLevel === '重大' || det.riskLevel === '高') ? 'risk-level-major' : ''}">${det.riskLevel || '无'}</span></div>
        <div><strong>是否隐患点:</strong> <span class="${det.isHazard === '是' ? 'is-hazard-yes' : ''}">${det.isHazard || '未知'}</span></div>
        <div><strong>是否已采取措施:</strong> <span class="${det.measuresTaken === '否' ? 'measures-no' : ''}">${det.measuresTaken || '未知'}</span></div>
    </div>`;

    // 3. Main Content Grid (2x2 for the four cards)
    html += `<div class="main-content-grid">`;

    // Card 1: Risk Information (Description, Planned Measures, Photos)
    const sitePhotosHtml = det.photos && det.photos.length > 0
        ? det.photos.map(p => `<a href="${p.url || '#'}" target="_blank" class="file-link">${p.caption || '照片'}</a>`).join('')
        : '无';
    // measurePhotos is not in risk1.data.details, so it will show '无'
    const measurePhotosHtml = det.measurePhotos && det.measurePhotos.length > 0
        ? det.measurePhotos.map(p => `<a href="#" class="file-link">${p}</a>`).join('')
        : '无';

    html += `<div class="info-card">
                <h5>风险点信息</h5>
                <p><strong>描述:</strong> ${det.description || '无描述信息。'}</p>
                <hr>
                <p><strong>已（拟）采取的措施:</strong> ${det.recommendedMeasures || '无相关措施信息。'}</p>
                <hr>
                <h6>现场照片/附件</h6>
                <p>${sitePhotosHtml}</p>
                <h6>措施附件</h6>
                <p>${measurePhotosHtml}</p>
             </div>`;

    // Card 2: Rectification Status
    const rectificationAttachmentsHtml = rect.attachments && rect.attachments.length > 0
        ? rect.attachments.map(f => `<a href="${f.url || '#'}" target="_blank" class="file-link">${f.name || '附件'}</a>`).join('')
        : '无';
    html += `<div class="info-card">
                <h5>整改信息</h5>
                <p><strong>是否整改:</strong> ${rect.isRectified ? '是' : '否'}</p>
                <p><strong>整改完成时间:</strong> ${rect.completionTime || '未记录'}</p>
                <p><strong>要求完成时间:</strong> ${rect.dueDate || '未设定'}</p>
                <p><strong>整改措施:</strong> ${rect.measures || '无具体措施信息。'}</p>
                <h6>对应附件</h6>
                <p>${rectificationAttachmentsHtml}</p>
             </div>`;

    // Card 3: Emergency Supplies - Adapted for single object structure in risk1.data.emergencySupplies
    let suppliesHTML = '';
    if (supplies && supplies.warehouseName) { // Check if supplies object and warehouseName exist
        suppliesHTML += `<div>
                            <p><strong>应急仓库:</strong> ${supplies.warehouseName} (位置: ${supplies.location || '无'})</p>
                            <p><strong>责任人及联系方式:</strong> ${supplies.managerContact || '无'}</p>`;

        // 使用表格格式显示物资列表，与应急物资模态框样式一致
        if (supplies.materials && supplies.materials.length > 0) {
            suppliesHTML += `
                <h6>物资列表:</h6>
                <table class="supply-table">
                    <thead>
                        <tr>
                            <th>物资名称</th>
                            <th>型号</th>
                            <th>数量</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>`;

            supplies.materials.forEach(material => {
                suppliesHTML += `
                    <tr>
                        <td>${material.name || '未知物资'}</td>
                        <td>${material.model || 'N/A'}</td>
                        <td>${material.quantity || 0}</td>
                        <td class="${material.status === '良好' ? 'material-status-good' : 'material-status-bad'}">${material.status || '未知'}</td>
                    </tr>`;
            });

            suppliesHTML += `
                    </tbody>
                </table>`;
        } else {
            suppliesHTML += `<p>无物资列表信息。</p>`;
        }
        suppliesHTML += `</div>`;
    } else {
        suppliesHTML = '<p>无应急物资储备信息。</p>';
    }
    html += `<div class="info-card">
                <h5>附近应急物资储备</h5>
                ${suppliesHTML}
             </div>`;

    // Card 4: Nearby Rescue Teams
    let rescueHTML = '';
    if (rescueTeams.length > 0) {
        rescueTeams.forEach((team, index) => {
            rescueHTML += `<div style="${index > 0 ? 'margin-top:10px; padding-top:10px; border-top:1px solid #444;' : ''}">
                            <p><strong>名称:</strong> ${team.name || '未知队伍'} (位置: ${team.location || '未知'})</p>
                            <p><strong>联系方式:</strong> ${team.contact || '无'}</p>
                            <p><strong>主要装备:</strong> ${team.equipmentModel || '无'}</p>
                            <p><strong>专业方向:</strong> ${team.specialization || '无'}</p>
                         </div>`;
        });
    } else {
        rescueHTML = '<p>无附近救援力量信息。</p>';
    }
    html += `<div class="info-card">
                <h5>附近救援力量</h5>
                ${rescueHTML}
             </div>`;

    html += `</div>`; // Close main-content-grid

    return html;
}

// New: Function to generate HTML for Emergency Supply Details
function generateSupplyDetailHTML(data) {
    let html = `<style>
        .supply-details-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin-bottom: 20px; }
        .supply-details-grid > div { background-color: #40454e; padding: 10px; border-radius: 4px; border: 1px solid #50555e; }
        .supply-details-grid > div > strong { color: #b0cce0; display: block; margin-bottom: 5px; font-size: 0.9em; }
        .supply-details-grid > div span { color: #e0e0e0; font-size: 1em; }
        .materials-table { width: 100%; border-collapse: collapse; margin-top: 15px; font-size: 0.9em; }
        .materials-table th, .materials-table td { border: 1px solid #50555e; padding: 10px; text-align: left; }
        .materials-table th { background-color: #484e58; color: #a0d8ef; font-weight: 600; }
        .materials-table td { background-color: #3a3f47; }
        .status-good { color: #a1e8a1; }
        .status-attention { color: #ffd700; }
    </style>`;

    html += `<h5>${data.warehouseName || '应急物资点详情'}</h5>`;
    html += `<div class="supply-details-grid">
        <div><strong>具体位置:</strong> <span>${data.location || '无'}</span></div>
        <div><strong>管辖/面积:</strong> <span>${data.area || '无'}</span></div>
        <div><strong>负责人:</strong> <span>${data.manager ? (data.manager.name + ' - ' + data.manager.contact) : '无'}</span></div>
        <div><strong>信息更新时间:</strong> <span>${data.lastUpdateTime || '无'}</span></div>
    </div>`;

    if (data.materials && data.materials.length > 0) {
        html += `<h6>物资列表</h6>
                 <table class="materials-table">
                    <thead>
                        <tr>
                            <th>类别</th>
                            <th>名称</th>
                            <th>型号</th>
                            <th>数量</th>
                            <th>单位</th>
                            <th>状态</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>`;
        data.materials.forEach(m => {
            html += `<tr>
                        <td>${m.category || '-'}</td>
                        <td>${m.name || '-'}</td>
                        <td>${m.model || '-'}</td>
                        <td>${m.quantity || '0'}</td>
                        <td>${m.unit || '-'}</td>
                        <td class="${m.status === '良好' ? 'status-good' : (m.status === '待检修' || m.status === '待补充' ? 'status-attention' : '')}">${m.status || '-'}</td>
                        <td>${m.notes || '-'}</td>
                     </tr>`;
        });
        html += `</tbody></table>`;
    } else {
        html += `<p>暂无物资详细信息。</p>`;
    }
    return html;
}

// New: Function to generate HTML for Event Details
function generateEventDetailHTML(data) {
    let html = `<style>
        /* Enhanced .event-section styling for clearer boundaries */
        .event-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #555c66; /* More visible border */
            border-radius: 8px;
            background-color: #31353d;
            box-shadow: 0 3px 6px rgba(0,0,0,0.25); /* Added shadow */
        }
        .event-section:last-child { margin-bottom: 0; }

        /* Layout for the event modal: top section + 2-column grid below */
        .event-top-section {
            /* This section will take full width before the grid */
        }
        .event-columns-grid {
            display: grid;
            grid-template-columns: 3fr 2fr; /* Plan details take more space. Adjust ratio as needed. e.g., 60% 40% or 2fr 1fr */
            gap: 20px;
            margin-top: 20px; /* Add space if top section exists and has margin-bottom */
        }

        /* Existing styles for elements within sections - ensure they are compatible */
        .event-section h5 { color: #a0d8ef; margin-top: 0; margin-bottom: 12px; border-bottom: 1px solid #5f6773; padding-bottom: 8px; font-size: 1.1em;}
        .event-basic-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 10px; margin-bottom:15px;}
        .event-basic-info-grid > div > strong { color: #bac8d3; display: inline-block; min-width: 90px; }
        .event-basic-info-grid > div span { color: #e0e0e0; }
        .plan-details-grid { display: grid; grid-template-columns: auto 1fr; gap: 10px 15px; margin-bottom: 15px; align-items: start; }
        .plan-details-grid strong { color: #bac8d3; white-space: nowrap; }
        .plan-details-grid span { color: #e0e0e0; }
        .command-structure .group { margin-bottom: 10px; padding-left: 15px; border-left: 2px solid #007bff; }
        .command-structure .group h6 { color: #b8cde0; margin-top:0; margin-bottom:5px; font-size: 1.05em; }
        .command-structure .group p { margin: 3px 0; font-size: 0.9em; }
        .event-levels-info { background-color: #3a3f47; padding:10px; border-radius:4px; margin-top:10px; font-size:0.85em;}
        .event-levels-info strong {color: #ffc107;}
        .procedure-list { list-style-type: none; padding-left: 0; }
        .procedure-list li { margin-bottom: 5px; padding: 8px 10px; background-color: #3a3f47; border-radius:3px; font-size:0.9em; }

        .related-info-container-title { /* Title for the whole resources/experts column */
            color: #a0d8ef; margin-top: 0; margin-bottom: 12px;
            border-bottom: 1px solid #5f6773; padding-bottom: 8px; font-size: 1.1em; font-weight:600;
        }
        .related-info-grid { display: grid; grid-template-columns: 1fr; gap: 15px; } /* Stack cards vertically in this column by default */
        /* If horizontal layout desired within this column for specific screen sizes, adjust .related-info-grid above */

        .related-info-card { background-color: #383c44; padding: 15px; border-radius: 5px; border: 1px solid #4a5058; }
        .related-info-card h6 { color: #b8cde0; margin-top:0; margin-bottom:10px; font-size: 1em; border-bottom: 1px solid #50555e; padding-bottom:5px; }
        .related-info-card p { margin: 4px 0; font-size: 0.9em; }
        .related-info-card hr { border-color: #444; margin:8px 0; opacity: 0.5;}
    </style>`;

    // 1. Basic Event Information (Top, Full Width)
    if (data.basicInfo) {
        const bi = data.basicInfo;
        html += `<div class="event-section event-top-section">
                    <h5>应急事件基本信息</h5>
                    <div class="event-basic-info-grid">
                        <div><strong>事件类型:</strong> <span>${bi.type || 'N/A'}</span></div>
                        <div><strong>事件状态:</strong> <span>${bi.status || 'N/A'}</span></div>
                        <div><strong>事发时间:</strong> <span>${bi.reportedTime || 'N/A'}</span></div>
                        <div><strong>事发地点:</strong> <span>${bi.location || 'N/A'}</span></div>
                    </div>
                    <p style="margin-top:10px;"><strong>事件影响:</strong> <span>${bi.impact || 'N/A'}</span></p>
                    <p><strong>初步原因:</strong> <span>${bi.cause || 'N/A'}</span></p>
                 </div>`;
    }

    // Start 2-column grid for Plan and Resources/Experts
    html += `<div class="event-columns-grid">`;

    // Column 1: Emergency Plan Details
    html += `<div class="event-section">`; // Column 1 content wrapper
    if (data.emergencyPlan) {
        const ep = data.emergencyPlan;
        html += `<h5 style="display:flex;justify-content:space-between;align-items:center;">
                    <span>推荐应急预案与处置</span>
                    <button id="view-plan-detail-btn" style="font-size:0.95em;padding:3px 12px;background:#0056b3;color:#fff;border:none;border-radius:4px;cursor:pointer;">查看预案详情</button>
                 </h5>
                 <div class="plan-details-grid">
                    <strong>预案名称:</strong>
                    <span>${ep.planName || 'N/A'}</span>
                    <strong>适用范围:</strong>
                    <span>${ep.applicableScope || 'N/A'}</span>
                    <strong>建议等级:</strong>
                    <span style="color: #ff8080; font-weight:bold;">${ep.recommendedLevel || 'N/A'}</span>
                    <select id="event-level-select" style="margin-left:10px; padding:2px 6px; font-size:1em;">
                        ${(ep.eventLevels||[]).map(lv => `<option value="${lv.level}" ${lv.level === ep.recommendedLevel ? 'selected' : ''}>${lv.level}</option>`).join('')}
                    </select>
                 </div>`;
        if (ep.eventLevels && ep.eventLevels.length > 0) {
            html += `<div class="event-levels-info">
                        <strong>响应条件参考:</strong><br>
                        ${ep.eventLevels.map(el => `&nbsp;&nbsp;${el.level}: ${el.condition}`).join('<br>')}
                     </div>`;
        }
        html += `<h6 style="margin-top:15px; margin-bottom:5px; color:#b8cde0;">处置流程:</h6>
                 <ul class="procedure-list">
                    ${(ep.disposalProcedure || []).map(proc => `<li>${proc}</li>`).join('')}
                 </ul>`;
        if (ep.commandStructure) {
            const cs = ep.commandStructure;
            html += `<h6 style="margin-top:15px; margin-bottom:5px; color:#b8cde0;">${cs.title || '应急指挥机构'}:</h6>
                     <div class="command-structure">`;
            (cs.groups || []).forEach(group => {
                html += `<div class="group">
                            <h6>${group.name} (${group.department || ''})</h6>
                            ${(group.members || []).map(mem => `<p>${mem.role}: ${mem.name} (${mem.contact})</p>`).join('')}
                         </div>`;
            });
            html += `</div>`; // command-structure close
        }
        // 按钮事件脚本
        html += `<script>document.getElementById('view-plan-detail-btn').onclick=function(){alert('这里可展示完整预案详情，后续可扩展为弹窗或跳转。');}</script>`;
    } else {
        html += `<h5>推荐应急预案与处置</h5><p>无相关预案信息。</p>`;
    }
    html += `</div>`; // End Column 1 (Plan Details)

    // Column 2: Nearby Supplies, Rescue Teams, Experts
    html += `<div class="event-section">`; // Column 2 content wrapper
    html += `<h5 class="related-info-container-title">关联应急资源与专家</h5>`; // Title for this whole column
    html += `<div class="related-info-grid">`;

    // Card: Nearby Supplies
    html += `<div class="related-info-card">
                <h6>邻近应急物资</h6>`;
    if (data.nearbySupplies && data.nearbySupplies.length > 0) {
        data.nearbySupplies.forEach((s, index) => {
            html += `<p><strong>${s.name}</strong> (${s.location || ''})<br>
                       联系人: ${s.contact || 'N/A'}<br>
                       主要物资: ${s.itemsPreview || 'N/A'}</p>${index < data.nearbySupplies.length - 1 ? '<hr>' : ''}`;
        });
    } else { html += `<p>无邻近应急物资信息。</p>`; }
    html += `</div>`;

    // Card: Nearby Rescue Teams
    html += `<div class="related-info-card">
                <h6>邻近救援力量</h6>`;
    if (data.nearbyRescueTeams && data.nearbyRescueTeams.length > 0) {
        data.nearbyRescueTeams.forEach((t, index) => {
            html += `<p><strong>${t.name}</strong> (${t.location || ''})<br>
                       联系方式: ${t.contact || 'N/A'}<br>
                       主要装备: ${t.equipmentModel || 'N/A'}<br>
                       专业方向: ${t.specialization || 'N/A'}</p>${index < data.nearbyRescueTeams.length - 1 ? '<hr>' : ''}`;
        });
    } else { html += `<p>无邻近救援力量信息。</p>`; }
    html += `</div>`;

    // Card: Expert Contacts
    html += `<div class="related-info-card">
                <h6>相关领域专家</h6>`;
    if (data.expertContacts && data.expertContacts.length > 0) {
        data.expertContacts.forEach((e, index) => {
            html += `<p><strong>${e.name}</strong> (${e.field || ''})<br>
                       联系电话: ${e.phone || 'N/A'}</p>${index < data.expertContacts.length - 1 ? '<hr>' : ''}`;
        });
    } else { html += `<p>无相关专家信息。</p>`; }
    html += `</div>`;

    html += `</div>`; // End .related-info-grid
    html += `</div>`; // End Column 2 (Resources/Experts)

    html += `</div>`; // End .event-columns-grid

    return html;
}

// New: Function to generate HTML for Congestion Details
function generateCongestionDetailHTML(data) {
    let html = `<style>
        .congestion-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #50555e;
            border-radius: 6px;
            background-color: #383c44; /* Similar to info-card */
        }
        .congestion-section h5 { /* Section titles */
            color: #a0d8ef;
            margin-top:0;
            margin-bottom:12px;
            border-bottom: 1px solid #5f6773;
            padding-bottom:8px;
            font-size: 1.1em;
            font-weight: 600;
        }
        .congestion-basic-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
            gap: 10px 15px; /* row-gap column-gap */
            margin-bottom: 15px;
        }
        .congestion-basic-info-grid > div > strong {
            color: #bac8d3; /* Lighter than text for emphasis */
            display: inline-block;
            min-width: 90px; /* Align values */
        }
        .congestion-basic-info-grid > div span {
            color: #e0e0e0;
        }
        .video-placeholders-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr); /* Two columns for video placeholders */
            gap: 15px;
            margin-top: 10px;
        }
        .video-placeholder {
            background-color: #2c313a;
            border: 1px solid #4a5058;
            border-radius: 4px;
            padding: 10px;
            text-align: center;
            min-height: 120px; /* Example height */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .video-placeholder img {
            max-width: 90%;
            max-height: 80px; /* Adjust as needed */
            margin-bottom: 8px;
            opacity: 0.7; /* Make placeholder image subtle */
        }
        .video-placeholder p {
            font-size: 0.85em;
            color: #b0cce0;
            margin: 0;
        }
        .detour-plan-content {
            font-size: 0.95em;
            line-height: 1.6;
            color: #d0d0d0;
            background-color: #31353d;
            padding: 12px;
            border-radius: 4px;
        }
    </style>`;

    // 1. Basic Congestion Information
    if (data.basicInfo) {
        const bi = data.basicInfo;
        html += `<div class="congestion-section">
                    <h5>基本信息</h5>
                    <div class="congestion-basic-info-grid">
                        <div><strong>路段编号:</strong> <span>${bi.roadSectionId || 'N/A'}</span></div>
                        <div><strong>起始桩号:</strong> <span>${bi.startStake || 'N/A'}</span></div>
                        <div><strong>平均速度:</strong> <span>${bi.averageSpeed || 'N/A'}</span></div>
                        <div><strong>拥堵长度:</strong> <span>${bi.congestionLength || 'N/A'}</span></div>
                        <div><strong>预计缓解:</strong> <span>${bi.estimatedReliefTime || 'N/A'}</span></div>
                    </div>
                 </div>`;
    }

    // 2. Nearby Monitoring Videos
    html += `<div class="congestion-section">
                <h5>附近监控视频 (模拟)</h5>`;
    if (data.monitoringVideos && data.monitoringVideos.length > 0) {
        html += `<div class="video-placeholders-grid">`;
        data.monitoringVideos.forEach(video => {
            html += `<div class="video-placeholder">
                        ${video.placeholderImageUrl ? `<img src="${video.placeholderImageUrl}" alt="视频监控画面">` : ''}
                        <p>${video.name || '监控画面'}</p>
                     </div>`;
        });
        html += `</div>`;
    } else {
        html += `<p>无可用监控视频信息。</p>`;
    }
    html += `</div>`;

    // 3. Detour Plan
    if (data.detourPlan) {
        html += `<div class="congestion-section">
                    <h5>建议绕行方案</h5>
                    <div class="detour-plan-content">
                        <p>${data.detourPlan}</p>
                    </div>
                 </div>`;
    }

    return html;
}

// Function to render map elements (points and lines)
function renderMapElements() {
    mapContainer.querySelectorAll('.map-marker, .map-line').forEach(el => el.remove());
    const visibleTypes = getVisibleLayerTypes();

    allMapElementsData.forEach(data => {
        let element;
        if (data.itemType === 'point') {
            element = document.createElement('div');
            element.className = `map-marker marker-${data.layerType}`;
            element.style.left = data.x;
            element.style.top = data.y;
        } else if (data.itemType === 'line') {
            element = document.createElement('div');
            element.className = `map-line line-${data.layerType}`;
            element.style.left = data.x;
            element.style.top = data.y;
            element.style.width = data.width;
            if (data.angle) {
                element.style.transform = `rotate(${data.angle}deg)`;
            }
        }

        if (element) {
            element.title = data.name;
            element.dataset.layerType = data.layerType;
            element.dataset.id = data.id;

            if (visibleTypes.includes(data.layerType)) {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }

            element.addEventListener('click', (event) => {
                // Always remove specific width classes first to reset to default width
                modal.classList.remove('modal-wide');
                modal.classList.remove('modal-extra-wide');

                modalTitle.textContent = data.detailsTitle || data.name;
                if (data.layerType === 'risks' && data.data) {
                    modal.classList.add('modal-wide'); // Apply wider class
                    modalBody.innerHTML = generateRiskDetailHTML(data.data);
                } else if (data.layerType === 'supplies' && data.data) {
                    modalBody.innerHTML = generateSupplyDetailHTML(data.data);
                } else if (data.layerType === 'events' && data.data) {
                    modal.classList.add('modal-extra-wide'); // Apply even wider class for events
                    modalBody.innerHTML = generateEventDetailHTML(data.data);
                } else if (data.layerType === 'congestion' && data.data) {
                    modalBody.innerHTML = generateCongestionDetailHTML(data.data);
                } else if (data.layerType === 'projects' && data.data) {
                    const projectDataFromMap = data;
                    const projectDetails = { ...projectDataFromMap.data };
                    projectDetails.id = projectDataFromMap.id; // Explicitly set the project ID

                    projectDetails.linkedRisksData = [];
                    projectDetails.nearbySuppliesData = [];
                    projectDetails.nearbyRescueTeamsData = [];

                    if (data.data.linkedElementIds) {
                        const { risks, supplies, rescueTeams } = data.data.linkedElementIds;
                        if (risks) {
                            risks.forEach(id => {
                                const riskItem = allMapElementsData.find(item => item.id === id && item.layerType === 'risks');
                                if (riskItem) projectDetails.linkedRisksData.push(riskItem);
                            });
                        }
                        if (supplies) {
                            supplies.forEach(id => {
                                const supplyItem = allMapElementsData.find(item => item.id === id && item.layerType === 'supplies');
                                if (supplyItem) projectDetails.nearbySuppliesData.push(supplyItem);
                            });
                        }
                        if (rescueTeams) {
                            rescueTeams.forEach(id => {
                                const rescueItem = allMapElementsData.find(item => item.id === id && item.layerType === 'rescue');
                                if (rescueItem) projectDetails.nearbyRescueTeamsData.push(rescueItem);
                            });
                        }
                    }
                    modalBody.innerHTML = generateProjectDetailHTML(projectDetails);
                } else if (data.layerType === 'rescue' && data.data) {
                    modal.classList.add('modal-wide'); // Apply wider class
                    modalBody.innerHTML = generateRescueDetailHTML(data);
                } else {
                    modalBody.innerHTML = data.htmlContent || '<p>暂无详细信息。</p>';
                }
                // Update info panel (optional, can be removed if not needed)
                if (infoPanel) {
                    infoPanel.innerHTML = `<p><strong>选定:</strong> ${data.name}<br><strong>类型:</strong> ${data.layerType.replace(/^./, str => str.toUpperCase())}</p>`;
                }
                openModal(event);
            });
            mapContainer.appendChild(element);
        }
    });
}

function getVisibleLayerTypes() {
    const checkboxes = document.querySelectorAll('.layer-control-group input[name="layer"]:checked'); // Updated selector
    return Array.from(checkboxes).map(cb => cb.value);
}

function setupLayerControls() {
    const layerCheckboxes = document.querySelectorAll('.layer-control-group input[name="layer"]'); // Updated selector

    // Event listener for "Select All" checkbox
    if (selectAllLayersCheckbox) {
        selectAllLayersCheckbox.addEventListener('change', () => {
            const isChecked = selectAllLayersCheckbox.checked;
            layerCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            filterMapElements();
            if (infoPanel) {
                infoPanel.innerHTML = `<p>所有图层已${isChecked ? '显示' : '隐藏'}。</p>`;
            }
        });
    }

    // Event listeners for individual layer checkboxes
    layerCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            filterMapElements(); // Filter first
            // Update "Select All" checkbox state based on individual checkboxes
            if (selectAllLayersCheckbox) {
                const allChecked = Array.from(layerCheckboxes).every(cb => cb.checked);
                selectAllLayersCheckbox.checked = allChecked;
            }
            if (infoPanel) {
                infoPanel.innerHTML = `<p>图层 '${checkbox.parentElement.textContent.trim().replace('全选/全不选','').trim()}' 已${checkbox.checked ? '显示' : '隐藏'}。</p>`;
            }
        });
    });

    // Initial check for selectAll state on load (if all are checked by default)
    if (selectAllLayersCheckbox) {
        const allChecked = Array.from(layerCheckboxes).every(cb => cb.checked);
        selectAllLayersCheckbox.checked = allChecked;
    }
}

function filterMapElements() {
    const visibleTypes = getVisibleLayerTypes();
    const allElements = document.querySelectorAll('.map-marker, .map-line');

    allElements.forEach(element => {
        if (visibleTypes.includes(element.dataset.layerType)) {
            element.style.display = 'block';
        } else {
            element.style.display = 'none';
        }
    });
}

function populateLegend() {
    if (!mapLegend) return;
    const legendData = [
        { label: '运营高速路', type: 'highways', itemClass: 'line-highways', symbolType: 'line' },
        { label: '水路', type: 'waterways', itemClass: 'line-waterways', symbolType: 'line' },
        { label: '重要国省干道', type: 'roads', itemClass: 'line-roads', symbolType: 'line' },
        { label: '铁路运输网络', type: 'railways', itemClass: 'line-railways', symbolType: 'line' },
        { label: '风险隐患点', type: 'risks', itemClass: 'marker-risks', symbolType: 'point' },
        { label: '应急救援力量', type: 'rescue', itemClass: 'marker-rescue', symbolType: 'point' },
        { label: '应急物资', type: 'supplies', itemClass: 'marker-supplies', symbolType: 'point' },
        { label: '在建项目', type: 'projects', itemClass: 'marker-projects', symbolType: 'point' },
        { label: '拥堵路段', type: 'congestion', itemClass: 'line-congestion', symbolType: 'line' },
        { label: '应急事件', type: 'events', itemClass: 'marker-events', symbolType: 'point' },
        { label: '气象预警区', type: 'weatherAlerts', itemClass: 'overlay-weatherAlerts', symbolType: 'overlay' } // New legend item
    ];

    let legendHTML = '<h4>图例</h4><ul>';
    legendData.forEach(item => {
        let symbolClass = 'legend-symbol';
        if (item.symbolType === 'line') {
            symbolClass += ' line';
        } else if (item.symbolType === 'overlay') {
            symbolClass += ' overlay'; // For area-like symbols in legend
        }
        // Make sure the itemClass is specific enough if you want to style legend symbols by color
        legendHTML += `<li><span class="${symbolClass} ${item.itemClass}"></span>${item.label}</li>`;
    });
    legendHTML += '</ul>';
    mapLegend.innerHTML = legendHTML;
}

// Global function to show a linked item in the main modal
function showLinkedItemInModal(itemType, itemId, originProjectId, clickEvent) { // originProjectId and clickEvent might be less relevant now
    if (clickEvent) clickEvent.stopPropagation();

    const itemData = allMapElementsData.find(el => el.id === itemId);
    // Restore finding originProjectData for the back button
    const originProjectData = allMapElementsData.find(el => el.id === originProjectId);

    if (!itemData) {
        console.error("Linked item data not found for ID:", itemId);
        modalBody.innerHTML = "<p>抱歉，无法加载所选项目的详细信息。</p>";
        if (modal.style.display !== 'block') openModal(clickEvent); // Open main modal to show error
        return;
    }
    // Restore check for originProjectData (though it might not be critical if ID is always passed correctly)
    if (!originProjectData) {
        console.error("Origin project data (for back button) not found for ID:", originProjectId);
    }

    let detailHTML = '';
    let itemTitle = itemData.name || '详情';

    switch (itemType) {
        case 'risk':
            detailHTML = generateRiskDetailHTML(itemData.data);
            itemTitle = itemData.detailsTitle || `风险: ${itemData.name}`;
            break;
        case 'supply':
            detailHTML = generateSupplyDetailHTML(itemData.data);
            itemTitle = itemData.detailsTitle || `应急物资: ${itemData.name}`;
            break;
        case 'rescue':
            detailHTML = generateRescueDetailHTML(itemData);
            itemTitle = itemData.detailsTitle || `救援力量: ${itemData.name}`;
            break;
        default:
            console.error("Unknown item type:", itemType);
            detailHTML = "<p>无法识别的项目类型。</p>";
    }

    // Restore backButtonHTML generation
    const originProjectName = originProjectData ? originProjectData.name : '项目';
    const backButtonHTML = `<button class="back-to-project-button" style="margin-bottom: 15px; padding: 8px 15px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="showProjectDetailsInModal('${originProjectId}', event)">返回 ${originProjectName} 详情</button>`;

    // Update the MAIN modal
    modalTitle.textContent = itemTitle;
    modalBody.innerHTML = backButtonHTML + detailHTML;

    // Ensure main modal is open/updated
    if (modal.style.display !== 'block' || clickEvent) { // Open if not open, or reposition if clickEvent is there
         openModal(clickEvent);
    }
}

// Restore showProjectDetailsInModal function (uncomment and ensure it's correct)
function showProjectDetailsInModal(projectId, clickEvent) {
    console.log("Attempting to show project details for:", projectId);
    const projectMapElement = allMapElementsData.find(item => item.id === projectId && item.layerType === 'projects');

    if (projectMapElement && projectMapElement.data) {
        const projectDetails = { ...projectMapElement.data };
        projectDetails.id = projectId;
        projectDetails.linkedRisksData = [];
        projectDetails.nearbySuppliesData = [];
        projectDetails.nearbyRescueTeamsData = [];

        if (projectMapElement.data.linkedElementIds) {
            const { risks, supplies, rescueTeams } = projectMapElement.data.linkedElementIds;
            if (risks) {
                risks.forEach(id => {
                    const riskItem = allMapElementsData.find(item => item.id === id && item.layerType === 'risks');
                    if (riskItem) projectDetails.linkedRisksData.push(riskItem);
                });
            }
            if (supplies) {
                supplies.forEach(id => {
                    const supplyItem = allMapElementsData.find(item => item.id === id && item.layerType === 'supplies');
                    if (supplyItem) projectDetails.nearbySuppliesData.push(supplyItem);
                });
            }
            if (rescueTeams) {
                rescueTeams.forEach(id => {
                    const rescueItem = allMapElementsData.find(item => item.id === id && item.layerType === 'rescue');
                    if (rescueItem) projectDetails.nearbyRescueTeamsData.push(rescueItem);
                });
            }
        }

        modalTitle.textContent = projectMapElement.detailsTitle || projectMapElement.name;
        modalBody.innerHTML = generateProjectDetailHTML(projectDetails);

        if (modal.style.display !== 'block' || clickEvent) {
            openModal(clickEvent);
        }
    } else {
        console.error("Project data not found for showProjectDetailsInModal, ID:", projectId);
        modalBody.innerHTML = "<p>无法加载项目详情。</p>";
    }
}

const mapImage = document.getElementById('map-image');
mapImage.addEventListener('click', (event) => {
    if (event.target.classList.contains('map-marker') || event.target.classList.contains('map-line')) return;
    const rect = mapImage.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;
    if (infoPanel) {
        infoPanel.innerHTML = `<p>点击了地图空白区域 (坐标: ${x.toFixed(1)}%, ${y.toFixed(1)}%)</p>`;
    }
});

document.addEventListener('DOMContentLoaded', () => {
    setupLayerControls();
    renderMapElements();
    populateLegend();
    displayWeatherAlertsOnMap(); // Call the function to display alerts
    // updateStatisticsPanel(); // Initial call for the stats panel -- REMOVED
});

// New: Function to generate HTML for Rescue Team Details
function generateRescueDetailHTML(rescueItem) {
    const data = rescueItem.data || {}; // Ensure data object exists
    let html = `<div class="rescue-team-details-embedded" style="padding: 10px; border: 1px solid #4a5058; border-radius: 5px; background-color: #31353d; margin-bottom:10px;">`;
    // No separate <style> tag here, relies on parent or general styles.
    // Using inline styles for the container for simplicity in embedding.

    html += `<h5 style="color: #a0d8ef; margin-top:0; margin-bottom:10px; border-bottom: 1px solid #5f6773; padding-bottom:5px;">${rescueItem.name || '救援队伍详情'}</h5>`;
    html += `<p style="margin: 5px 0; font-size: 0.9em;"><strong>状态:</strong> ${data.status || 'N/A'}</p>`;
    html += `<p style="margin: 5px 0; font-size: 0.9em;"><strong>人数:</strong> ${data.personnelCount || 'N/A'}</p>`;
    html += `<p style="margin: 5px 0; font-size: 0.9em;"><strong>联系人:</strong> ${data.contactPerson || 'N/A'} (${data.contactPhone || 'N/A'})</p>`;
    if (data.mainEquipment && data.mainEquipment.length > 0) {
        html += `<p style="margin: 5px 0; font-size: 0.9em;"><strong>主要装备:</strong> ${data.mainEquipment.join(', ')}</p>`;
    } else {
        html += `<p style="margin: 5px 0; font-size: 0.9em;"><strong>主要装备:</strong> 无</p>`;
    }
    html += `</div>`;
    return html;
}

// Function to generate HTML for Project Details
function generateProjectDetailHTML(projectData) {
    console.log("generateProjectDetailHTML called with projectData.id:", projectData.id); // Debugging line
    const { basicInfo, linkedRisksData, nearbySuppliesData, nearbyRescueTeamsData } = projectData;

    let html = `
    <style>
        .project-modal-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .project-section { /* Top level section for basic info */
            padding: 15px;
            border: 1px solid #50555e;
            border-radius: 6px;
            background-color: #383c44;
        }
        .project-section h5 { /* Titles for basic info and main linked content columns */
            color: #a0d8ef;
            margin-top: 0;
            margin-bottom: 12px;
            border-bottom: 1px solid #5f6773;
            padding-bottom: 8px;
            font-size: 1.1em;
            font-weight: 600;
        }
        .project-basic-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        .project-basic-info-grid > div > strong {
            color: #bac8d3;
            display: inline-block;
            min-width: 100px;
        }
        .project-basic-info-grid > div span {
            color: #e0e0e0;
        }

        .project-linked-content-grid { /* Renamed from project-linked-content for clarity */
            display: grid;
            grid-template-columns: 1fr 1fr; /* Two columns for linked risks and (supplies + rescue) */
            gap: 15px;
        }

        .linked-items-column { /* Common styling for columns holding linked items */
            background-color: #383c44; /* Match project-section for consistency */
            padding: 15px;
            border: 1px solid #50555e;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            gap: 10px; /* Reduced gap for accordion items */
        }
        .linked-items-column h5 { /* Title for the whole column e.g. "相关风险隐患" */
             color: #a0d8ef;
             margin-top: 0;
             margin-bottom: 10px;
             border-bottom: 1px solid #5f6773;
             padding-bottom: 8px;
             font-size: 1.05em;
             font-weight: 600;
        }

        /* Accordion Styles - To be REMOVED or REPLACED */
        /* Removing accordion styles as we are changing interaction */
        .linked-item-entry {
            background-color: #3e4450;
            color: #c8d2dc;
            padding: 10px 12px;
            border-radius: 4px;
            margin-bottom: 8px; /* Space between entries */
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .linked-item-entry span {
            flex-grow: 1;
        }
        .linked-item-entry button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 0.9em;
        }
        .linked-item-entry button:hover {
            background-color: #0056b3;
        }

        /* Old Accordion Styles - REMOVE THESE */
        /* .accordion-item, .accordion-header, .accordion-icon, .accordion-content */
        /* will be removed from CSS if they were here */

    </style>
    <div class="project-modal-container">
        <div class="project-section">
            <h5>项目基本信息</h5>
            <div class="project-basic-info-grid">
                <div><strong>业主单位:</strong> <span>${basicInfo.owner || 'N/A'}</span></div>
                <div><strong>工程进度:</strong> <span>${basicInfo.progress || 'N/A'}</span></div>
                <div><strong>项目负责人:</strong> <span>${basicInfo.projectManagerName || 'N/A'}</span></div>
                <div><strong>联系方式:</strong> <span>${basicInfo.projectManagerContact || 'N/A'}</span></div>
            </div>
            <div style="margin-top: 10px;">
                <strong>项目简介:</strong> <span style="color: #e0e0e0;">${basicInfo.primaryRisksDescription || 'N/A'}</span>
            </div>
        </div>

        <div class="project-linked-content-grid">
            <div class="linked-items-column">
                <h5>相关风险隐患</h5>
                ${linkedRisksData && linkedRisksData.length > 0 ?
                    linkedRisksData.map(item => `
                        <div class="linked-item-entry">
                            <span>${item.name || '未知风险'}</span>
                            <button onclick="showLinkedItemInModal('risk', '${item.id}', '${projectData.id}', event)">查看详情</button>
                        </div>`).join('') :
                    '<p style="color:#d0d0d0; font-size:0.9em;">无关联风险隐患信息。</p>'}
            </div>

            <div class="linked-items-column">
                <div> <!-- Sub-column for supplies -->
                    <h5>附近应急物资</h5>
                    ${nearbySuppliesData && nearbySuppliesData.length > 0 ?
                        nearbySuppliesData.map(item => `
                            <div class="linked-item-entry" style="margin-bottom: 10px;">
                                <span>${item.name || '未知物资点'}</span>
                                <button onclick="showLinkedItemInModal('supply', '${item.id}', '${projectData.id}', event)">查看详情</button>
                            </div>`).join('') :
                        '<p style="color:#d0d0d0; font-size:0.9em;">无附近应急物资信息。</p>'}
                </div>
                <div> <!-- Sub-column for rescue -->
                    <h5>附近救援力量</h5>
                    ${nearbyRescueTeamsData && nearbyRescueTeamsData.length > 0 ?
                        nearbyRescueTeamsData.map(item => `
                            <div class="linked-item-entry">
                                <span>${item.name || '未知救援队'}</span>
                                <button onclick="showLinkedItemInModal('rescue', '${item.id}', '${projectData.id}', event)">查看详情</button>
                            </div>`).join('') :
                        '<p style="color:#d0d0d0; font-size:0.9em;">无附近救援力量信息。</p>'}
                </div>
            </div>
        </div>
    </div>
    `;
    return html;
}

const mockWeatherAlerts = [
    {
        id: 'alert001',
        type: '暴雨',
        level: '橙色',
        areaName: '青秀区南部',
        description: '预计未来3小时内青秀区南部将有50毫米以上强降雨，请注意防范山洪和内涝。',
        geometry: { x: 150, y: 200, width: 100, height: 80, color: 'rgba(128, 0, 128, 0.5)' }, // Purple for heavy rain
        responsibleUnits: [
            { unitId: 'qs_gov', unitName: '青秀区应急办',负责人: '张三', phone: '13800138000', confirmed: false },
            { unitId: 'qs_traffic', unitName: '青秀区交警大队',负责人: '李四', phone: '13900139000', confirmed: false },
            { unitId: 'qs_water', unitName: '青秀区水利局',负责人: '王五', phone: '13700137000', confirmed: false }
        ]
    },
    {
        id: 'alert002',
        type: '台风',
        level: '蓝色',
        areaName: '沿海区域',
        description: '台风"悟空"预计24小时内登陆，沿海区域将有6-7级大风，请做好防风准备。',
        geometry: { x: 300, y: 350, width: 150, height: 100, color: 'rgba(0, 0, 255, 0.4)' }, // Blue for typhoon
        responsibleUnits: [
            { unitId: 'coastal_gov', unitName: '沿海指挥部',负责人: '赵六', phone: '13600136000', confirmed: false },
            { unitId: 'coastal_port', unitName: '港务局',负责人: '孙七', phone: '13500135000', confirmed: false }
        ]
    },
    {
        id: 'alert003',
        type: '高温',
        level: '黄色',
        description: '全市今日最高气温将达到37℃，请注意防暑降温。',
        areaName: '全市范围',
        geometry: { x: 50, y: 50, width: 400, height: 300, color: 'rgba(255, 165, 0, 0.3)' }, // Orange for high temp
        responsibleUnits: [
            { unitId: 'city_health', unitName: '市卫健委',负责人: '周八', phone: '13400134000', confirmed: false },
            { unitId: 'city_emergency', unitName: '市应急局',负责人: '吴九', phone: '13300133000', confirmed: false }
        ]
    }
];

// ... existing code ...

// Function to display weather alerts on the map
function displayWeatherAlertsOnMap() {
    if (!mapContainer) {
        console.error('Map container not found for displaying weather alerts.');
        return;
    }
    mapContainer.querySelectorAll('.weather-alert-overlay').forEach(el => el.remove());

    const visibleLayerTypes = getVisibleLayerTypes(); // Get current visible layers

    mockWeatherAlerts.forEach(alert => {
        if (alert.geometry && typeof alert.geometry.x !== 'undefined') {
            const alertOverlay = document.createElement('div');
            alertOverlay.className = 'weather-alert-overlay'; // General class for styling if needed
            alertOverlay.style.position = 'absolute';
            alertOverlay.style.left = alert.geometry.x + 'px';
            alertOverlay.style.top = alert.geometry.y + 'px';
            alertOverlay.style.width = alert.geometry.width + 'px';
            alertOverlay.style.height = alert.geometry.height + 'px';
            alertOverlay.style.backgroundColor = alert.geometry.color;
            alertOverlay.style.zIndex = '10';
            alertOverlay.style.cursor = 'pointer';
            alertOverlay.title = `${alert.type}预警 (${alert.level}) - ${alert.areaName}`;
            alertOverlay.dataset.alertId = alert.id;
            alertOverlay.dataset.layerType = 'weatherAlerts'; // Assign layer type for filtering

            // Set initial visibility based on layer control
            if (visibleLayerTypes.includes('weatherAlerts')) {
                alertOverlay.style.display = 'block';
            } else {
                alertOverlay.style.display = 'none';
            }

            alertOverlay.addEventListener('click', handleWeatherAlertClick);
            mapContainer.appendChild(alertOverlay);
        }
    });
}

// Placeholder for click handling function (to be defined next)
function handleWeatherAlertClick(event) {
    const alertId = event.currentTarget.dataset.alertId;
    const alertData = mockWeatherAlerts.find(a => a.id === alertId);
    if (alertData) {
        console.log('Clicked weather alert:', alertData);
        // Next step: Open a modal with alert details and confirmation options
        openWeatherNotificationModal(alertData);
    } else {
        console.error('Alert data not found for ID:', alertId);
    }
}

// Placeholder for the notification modal function
function openWeatherNotificationModal(alertData) {
    modalTitle.textContent = `气象预警通知: ${alertData.type} - ${alertData.level} (${alertData.areaName})`;

    let modalContentHTML = `<p><strong>预警详情:</strong> ${alertData.description}</p><hr>
                            <div id="unit-selector-app-container" style="margin-bottom: 15px;">
                                <!-- Vue app for el-tree-select will mount here -->
                            </div>
                            <div id="selected-units-display-area" style="margin-bottom: 15px; display: flex; flex-wrap: wrap; gap: 5px;">
                                <!-- Selected units as tags will be shown here -->
                            </div>`;

    const unconfirmedUnits = alertData.responsibleUnits.filter(unit => !unit.confirmed);
    const confirmedUnits = alertData.responsibleUnits.filter(unit => unit.confirmed);

    if (unconfirmedUnits.length > 0) {
        modalContentHTML += `<button id="send-bulk-notification-btn" style="padding: 8px 15px; background-color:#007bff; color:white; border:none; border-radius:4px;">一键发送通知给选中单位</button><hr style="margin-top:15px;">`;
    } else {
        modalContentHTML += '<p style="color:green; font-weight:bold;">所有相关单位均已通知。</p><hr style="margin-top:15px;">';
    }

    if (confirmedUnits.length > 0) {
        modalContentHTML += '<h5>已通知单位:</h5><ul>';
        confirmedUnits.forEach(unit => {
            modalContentHTML += `<li style="margin-bottom: 5px; color: green;">${unit.unitName} (负责人: ${unit.负责人})</li>`;
        });
        modalContentHTML += '</ul>';
    }

    modalBody.innerHTML = modalContentHTML;

    // Prepare data for el-tree-select
    const unitOptionsForSelect = unconfirmedUnits.map(unit => ({
        value: unit.unitId,
        label: `${unit.unitName} (负责人: ${unit.负责人} - ${unit.phone})`,
        // el-tree-select can handle flat data, no 'children' needed if it's not hierarchical
    }));

    // Mount Vue app for el-tree-select if there are units to select
    if (unconfirmedUnits.length > 0 && document.getElementById('unit-selector-app-container')) {
        if (unitSelectApp) { // Unmount previous instance if exists
            unitSelectApp.unmount();
        }
        unitSelectApp = Vue.createApp({
            data() {
                return {
                    selectedUnits: [],
                    unitOptions: unitOptionsForSelect,
                    alertDataId: alertData.id // Store alertId to pass to notification function
                };
            },
            watch: {
                selectedUnits(newSelection) {
                    this.updateSelectedUnitsDisplay(newSelection);
                }
            },
            methods: {
                updateSelectedUnitsDisplay(selection) {
                    const displayArea = document.getElementById('selected-units-display-area');
                    if (!displayArea) return;
                    displayArea.innerHTML = ''; // Clear previous tags
                    selection.forEach(unitId => {
                        const unit = unconfirmedUnits.find(u => u.unitId === unitId);
                        if (unit) {
                            const tag = document.createElement('span');
                            tag.className = 'selected-unit-tag';
                            tag.style.backgroundColor = '#007bff';
                            tag.style.color = 'white';
                            tag.style.padding = '3px 8px';
                            tag.style.borderRadius = '4px';
                            tag.style.marginRight = '5px';
                            tag.style.marginBottom = '5px'; // Ensure tags wrap nicely
                            tag.textContent = unit.unitName;
                            displayArea.appendChild(tag);
                        }
                    });
                }
            },
            template: `
                <el-tree-select
                    v-model="selectedUnits"
                    :data="unitOptions"
                    multiple
                    show-checkbox
                    check-strictly
                    filterable
                    placeholder="请选择要通知的单位"
                    style="width: 100%;"
                />
            `
        });
        unitSelectApp.use(ElementPlus);
        unitSelectApp.mount('#unit-selector-app-container');
    }

    const sendBulkButton = modalBody.querySelector('#send-bulk-notification-btn');
    if (sendBulkButton) {
        sendBulkButton.addEventListener('click', function() {
            if (!unitSelectApp || !unitSelectApp._instance) {
                alert('单位选择器未初始化。');
                return;
            }
            const selectedUnitIds = unitSelectApp._instance.data.selectedUnits;

            if (selectedUnitIds.length === 0) {
                alert('请至少选择一个单位进行通知。');
                return;
            }

            const unitsToNotify = [];
            let confirmMessage = `您确定要向以下 ${selectedUnitIds.length} 个单位发送预警通知吗？\n`;
            selectedUnitIds.forEach(unitId => {
                const unitData = unconfirmedUnits.find(u => u.unitId === unitId); // Get full unit data for name
                if (unitData) {
                    confirmMessage += `\n- ${unitData.unitName}`;
                    unitsToNotify.push({ id: unitData.unitId, name: unitData.unitName });
                }
            });

            if (confirm(confirmMessage)) {
                const currentAlertId = unitSelectApp._instance.data.alertDataId;
                unitsToNotify.forEach(unit => {
                    sendNotificationToUnit(currentAlertId, unit.id, unit.name);
                });
                // Refresh modal after sending (sendNotificationToUnit handles individual refreshes)
                // If all units were selected and notified, the list of unconfirmed might be empty now.
                // Re-fetch alertData to ensure it's up-to-date for the refresh.
                const updatedAlertData = mockWeatherAlerts.find(a => a.id === currentAlertId);
                if (updatedAlertData) {
                    openWeatherNotificationModal(updatedAlertData);
                } else {
                    closeModal(); // Close if alert data somehow became invalid
                }
            }
        });
    }

    openModal();
}

function prepareNotificationForUnit(alertId, unitId, unitName) {
    if (confirm(`您确定要向【${unitName}】发送关于此预警的通知吗？`)) {
        sendNotificationToUnit(alertId, unitId, unitName);
    }
}

function sendNotificationToUnit(alertId, unitId, unitName) {
    const alert = mockWeatherAlerts.find(a => a.id === alertId);
    if (alert) {
        const unit = alert.responsibleUnits.find(u => u.unitId === unitId);
        if (unit && !unit.confirmed) { // Assuming 'confirmed' now means 'notified' for simplicity
            unit.confirmed = true; // Mark as notified/action taken
            console.log(`Notification sent to unit ${unit.unitName} for alert ${alert.type}`);
            alert(`已向【${unitName}】发送预警通知 (模拟)。`);
            openWeatherNotificationModal(alert); // Refresh modal to show updated status
            // updateStatisticsPanel(); // Update stats panel -- REMOVED
        } else if (unit && unit.confirmed) {
            alert(`已向【${unitName}】发送过此预警通知。`);
        }
    }
}

// confirmAlertReception function might be deprecated or repurposed if all confirmation is via "send notification"
// For now, we'll keep it but it's not directly called by the new buttons.
function confirmAlertReception(alertId, unitId) {
    const alert = mockWeatherAlerts.find(a => a.id === alertId);
    if (alert) {
        const unit = alert.responsibleUnits.find(u => u.unitId === unitId);
        if (unit && !unit.confirmed) {
            unit.confirmed = true;
            console.log(`Unit ${unit.unitName} confirmed reception for alert ${alert.type}`);
            openWeatherNotificationModal(alert);
            // updateStatisticsPanel(); -- REMOVED
        } else if (unit && unit.confirmed) {
            alert('该单位已确认接收此预警。');
        }
    }
}

// Placeholder for statistics panel update function -- ENTIRE FUNCTION TO BE REMOVED
/*
function updateStatisticsPanel() {
    console.log('Updating statistics panel (to be implemented).');
    const statsPanel = document.getElementById('weather-alert-stats-panel');
    if (!statsPanel) return;

    let statsHTML = '<h5>预警确认状态统计</h5><ul>';
    let hasUnconfirmed = false;
    mockWeatherAlerts.forEach(alert => {
        statsHTML += `<li><strong>${alert.type} (${alert.areaName}):</strong><ul>`;
        alert.responsibleUnits.forEach(unit => {
            if (!unit.confirmed) {
                hasUnconfirmed = true;
                statsHTML += `<li style="color:red;">${unit.unitName} - 未确认</li>`;
            } else {
                statsHTML += `<li style="color:green;">${unit.unitName} - 已确认</li>`;
            }
        });
        statsHTML += '</ul></li>';
    });
    if (!hasUnconfirmed) {
        statsHTML += '<li>所有单位均已确认当前预警。</li>';
    }
    statsHTML += '</ul>';
    statsPanel.innerHTML = statsHTML;
}
*/

// ... existing code ...