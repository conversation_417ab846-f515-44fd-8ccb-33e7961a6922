# 导航栏组件使用指南

## 📋 概述

导航栏组件 (`js/navigation-component.js`) 是一个统一管理系统顶部导航栏的公共组件，支持动态添加/移除页面，自动高亮当前页面，大大简化了导航栏的维护工作。

## 🎯 主要优势

- ✅ **统一管理** - 所有导航栏配置集中在一个文件中
- ✅ **易于扩展** - 添加新页面只需修改配置
- ✅ **自动高亮** - 当前页面自动显示激活状态
- ✅ **代码复用** - 避免重复编写相同的HTML
- ✅ **维护简单** - 修改导航栏只需改一个地方
- ✅ **动态操作** - 支持运行时添加/移除页面

## 🚀 快速开始

### 1. 在新页面中使用导航栏组件

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- 其他head内容 -->

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 页面内容 -->
        <main class="main-content">
            <!-- 你的页面内容 -->
        </main>
    </div>

    <script>
        // 初始化导航栏
        NavigationComponent.init('your-page-id');
    </script>
</body>
</html>
```

### 2. 添加新页面到导航栏

#### 方法一：修改配置文件（推荐）

在 `js/navigation-component.js` 的 `NAVIGATION_CONFIG.pages` 数组中添加新页面：

```javascript
const NAVIGATION_CONFIG = {
    systemTitle: '广西交通运输应急管理系统',
    pages: [
        // 现有页面...
        {
            id: 'new-page',
            title: '新页面',
            url: 'new-page.html'
        }
    ]
};
```

#### 方法二：动态添加（适用于运行时）

```javascript
// 添加新页面
NavigationComponent.addPage({
    id: 'dynamic-page',
    title: '动态页面',
    url: 'dynamic-page.html'
});

// 重新渲染导航栏
NavigationComponent.render('navigation-container', 'current-page-id');
```

## 📚 API 参考

### 核心方法

#### `NavigationComponent.init(activePage, containerId)`
初始化导航栏组件

- `activePage` (string): 当前激活的页面ID
- `containerId` (string, 可选): 容器元素ID，默认为 'navigation-container'

```javascript
NavigationComponent.init('risk-map');
```

#### `NavigationComponent.render(containerId, activePage)`
渲染导航栏到指定容器

- `containerId` (string): 容器元素ID
- `activePage` (string): 当前激活的页面ID

```javascript
NavigationComponent.render('navigation-container', 'emergency-map');
```

#### `NavigationComponent.addPage(pageConfig)`
添加新的导航页面

- `pageConfig` (object): 页面配置对象
  - `id` (string): 页面唯一标识
  - `title` (string): 页面显示名称
  - `url` (string): 页面URL

```javascript
NavigationComponent.addPage({
    id: 'new-feature',
    title: '新功能',
    url: 'new-feature.html'
});
```

#### `NavigationComponent.removePage(pageId)`
移除导航页面

- `pageId` (string): 要移除的页面ID

```javascript
NavigationComponent.removePage('old-page');
```

#### `NavigationComponent.getCurrentPageId()`
获取当前页面ID（根据URL自动判断）

```javascript
const currentPageId = NavigationComponent.getCurrentPageId();
```

#### `NavigationComponent.navigateToPage(url)`
页面导航函数

- `url` (string): 目标页面URL

```javascript
NavigationComponent.navigateToPage('target-page.html');
```

## 🔧 配置说明

### 导航栏配置结构

```javascript
const NAVIGATION_CONFIG = {
    systemTitle: '系统标题',
    pages: [
        {
            id: 'page-id',        // 页面唯一标识
            title: '页面标题',     // 导航栏显示的文字
            url: 'page.html'      // 页面文件路径
        }
    ]
};
```

### 当前已配置的页面

1. **风险一张图** (`risk-map`)
2. **应急一张图** (`emergency-map`)
3. **路网运行** (`road-network`)
4. **防汛防台** (`flood-prevention`)
5. **应急演练** (`emergency-drill`)
6. **指挥调度** (`command-dispatch`)
7. **监测预警** (`monitoring-warning`)
8. **值班值守** (`duty-watch`)
9. **分析研判** (`analysis-judgment`)
10. **态势标绘** (`situation-plotting`)
11. **系统管理** (`system-management`)

## 💡 最佳实践

### 1. 页面ID命名规范
- 使用小写字母和连字符
- 保持简洁且具有描述性
- 例如：`risk-map`, `emergency-drill`, `data-analysis`

### 2. 添加新页面的完整流程

1. **创建HTML页面文件**
2. **引入导航栏组件**
3. **添加导航容器**
4. **配置页面信息**（在 `navigation-component.js` 中）
5. **初始化导航栏**
6. **测试页面跳转**

### 3. 错误处理
组件包含基本的错误处理：
- 容器不存在时会在控制台输出错误信息
- 重复添加页面时会输出警告
- 移除不存在的页面时会输出警告

## 🔍 故障排除

### 常见问题

1. **导航栏不显示**
   - 检查是否正确引入了 `navigation-component.js`
   - 确认容器元素ID是否正确
   - 查看浏览器控制台是否有错误信息

2. **页面跳转不工作**
   - 检查页面URL配置是否正确
   - 确认目标页面文件是否存在

3. **当前页面没有高亮**
   - 检查传入的 `activePage` 参数是否与配置中的 `id` 匹配
   - 确认页面ID命名是否一致

### 调试技巧

```javascript
// 查看当前配置
console.log(window.NavigationComponent);

// 查看当前页面ID
console.log(NavigationComponent.getCurrentPageId());
```

## 🎉 示例页面

访问 `example-new-page.html` 查看完整的使用示例和动态操作演示。

## 📝 更新日志

- **v1.0.0** - 初始版本，支持基本的导航栏渲染和页面管理
- 支持动态添加/移除页面
- 自动页面ID检测
- 完整的错误处理机制
