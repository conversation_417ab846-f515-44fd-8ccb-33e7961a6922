<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 广西交通运输应急管理系统</title>

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入统一样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="css/system-management.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="system-layout">
                <!-- 左侧菜单栏 -->
                <div class="system-sidebar">
                    <nav class="sidebar-nav" id="system-sidebar">
                        <!-- 菜单内容将通过JavaScript动态生成 -->
                        <div class="loading-menu">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>加载菜单中...</span>
                        </div>
                    </nav>
                </div>

                <!-- 右侧内容区域 -->
                <div class="system-content">
                    <!-- 面包屑导航 -->
                    <div id="breadcrumb-nav" class="breadcrumb-nav">
                        <nav class="breadcrumb-path">
                            <span id="breadcrumb-path">系统管理 > 概览</span>
                        </nav>
                    </div>

                    <!-- 动态内容区域 -->
                    <div id="dynamic-content" class="content-area">
                        <!-- 默认显示系统管理概览 -->
                        <div id="system-overview" class="system-overview">
                            <div class="overview-container">
                                <div class="overview-header">
                                    <div class="overview-icon">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <h1>系统管理</h1>
                                    <p>统一管理平台，整合风险隐患、应急处置和系统配置功能</p>
                                </div>

                                <div class="overview-grid">
                                    <div class="overview-card risk-card">
                                        <div class="card-header">
                                            <i class="fas fa-shield-alt"></i>
                                            <h3>风险隐患管理</h3>
                                        </div>
                                        <p>检查任务、在建项目、隐患整改等功能模块</p>
                                        <ul>
                                            <li><i class="fas fa-check"></i>检查任务管理</li>
                                            <li><i class="fas fa-check"></i>在建项目监控</li>
                                            <li><i class="fas fa-check"></i>隐患整改跟踪</li>
                                        </ul>
                                    </div>

                                    <div class="overview-card emergency-card">
                                        <div class="card-header">
                                            <i class="fas fa-ambulance"></i>
                                            <h3>应急处置管理</h3>
                                        </div>
                                        <p>预案库、应急物资、通讯录等应急资源</p>
                                        <ul>
                                            <li><i class="fas fa-check"></i>应急预案库</li>
                                            <li><i class="fas fa-check"></i>应急物资管理</li>
                                            <li><i class="fas fa-check"></i>应急通讯录</li>
                                        </ul>
                                    </div>

                                    <div class="overview-card system-card">
                                        <div class="card-header">
                                            <i class="fas fa-cogs"></i>
                                            <h3>系统管理</h3>
                                        </div>
                                        <p>用户权限、组织架构、系统配置等</p>
                                        <ul>
                                            <li><i class="fas fa-check"></i>用户权限管理</li>
                                            <li><i class="fas fa-check"></i>组织架构配置</li>
                                            <li><i class="fas fa-check"></i>系统参数设置</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="overview-footer">
                                    <p>请从左侧菜单选择具体功能模块</p>
                                    <div class="tips">
                                        <span class="tip">
                                            <i class="fas fa-mouse-pointer"></i>
                                            点击左侧菜单导航
                                        </span>
                                        <span class="tip">
                                            <i class="fas fa-layer-group"></i>
                                            支持三级菜单结构
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 加载状态指示器 -->
    <div id="loading-indicator" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center shadow-xl">
            <i class="fas fa-spinner fa-spin text-blue-600 text-xl mr-3"></i>
            <span class="text-gray-700">加载中...</span>
        </div>
    </div>

    <!-- 错误提示模态框 -->
    <div id="error-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
            <div class="flex items-center mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl mr-3"></i>
                <h3 class="text-lg font-semibold text-gray-800">加载失败</h3>
            </div>
            <p id="error-message" class="text-gray-600 mb-4">内容加载失败，请稍后重试。</p>
            <div class="flex justify-end space-x-3">
                <button id="error-retry" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重试
                </button>
                <button id="error-close" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 统一的脚本引入 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script src="js/menu-config.js"></script>
    <script src="js/content-data.js"></script>
    <script src="js/content-loader.js"></script>
    <script src="js/menu-renderer.js"></script>
    <script src="js/system-management-core.js"></script>

    <script>
        // 初始化导航栏
        NavigationComponent.init('system-management');
    </script>
</body>
</html>
