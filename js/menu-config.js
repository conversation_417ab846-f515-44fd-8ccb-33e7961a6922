/**
 * 系统管理菜单配置
 * 定义三级菜单结构和对应的内容URL
 */

const SYSTEM_MENU_CONFIG = {
    "risk-management": {
        title: "风险隐患管理",
        icon: "fas fa-shield-alt",
        expanded: false,
        children: {
            "check-tasks": {
                title: "检查任务",
                icon: "fas fa-tasks",
                contentUrl: "content/risk-management/my_check_tasks_content.html",
                breadcrumb: ["系统管理", "风险隐患管理", "检查任务"]
            },
            "construction-projects": {
                title: "在建项目",
                icon: "fas fa-building",
                contentUrl: "content/risk-management/construction_projects_content.html",
                breadcrumb: ["系统管理", "风险隐患管理", "在建项目"]
            },
            "hazard-rectification": {
                title: "隐患与整改",
                icon: "fas fa-exclamation-triangle",
                expanded: false,
                children: {
                    "hazard-list": {
                        title: "隐患列表",
                        icon: "fas fa-list",
                        contentUrl: "content/risk-management/hazard_list_content.html",
                        breadcrumb: ["系统管理", "风险隐患管理", "隐患与整改", "隐患列表"]
                    },
                    "rectification-tasks": {
                        title: "整改任务列表",
                        icon: "fas fa-clipboard-list",
                        contentUrl: "content/risk-management/rectification_tasks_content.html",
                        breadcrumb: ["系统管理", "风险隐患管理", "隐患与整改", "整改任务列表"]
                    }
                }
            },
            "check-approval": {
                title: "检查审批",
                icon: "fas fa-clipboard-check",
                contentUrl: "content/risk-management/hazard_approval_content.html",
                breadcrumb: ["系统管理", "风险隐患管理", "检查审批"]
            },
            "template-management": {
                title: "检查下发与检查模版",
                icon: "fas fa-list-alt",
                expanded: false,
                children: {
                    "check-dispatch": {
                        title: "检查下发",
                        icon: "fas fa-paper-plane",
                        contentUrl: "content/risk-management/check_dispatch_content.html",
                        breadcrumb: ["系统管理", "风险隐患管理", "检查下发与检查模版", "检查下发"]
                    },
                    "check-template": {
                        title: "检查模版",
                        icon: "fas fa-file-alt",
                        contentUrl: "content/risk-management/check_template_content.html",
                        breadcrumb: ["系统管理", "风险隐患管理", "检查下发与检查模版", "检查模版"]
                    }
                }
            }
        }
    },
    "emergency-management": {
        title: "应急处置管理",
        icon: "fas fa-ambulance",
        expanded: false,
        children: {
            "plan-library": {
                title: "预案库",
                icon: "fas fa-book",
                contentUrl: "content/plan_list_content.html",
                breadcrumb: ["系统管理", "应急处置管理", "预案库"]
            },
            "emergency-resources": {
                title: "应急物资",
                icon: "fas fa-warehouse",
                contentUrl: "content/emergency_resources_content.html",
                breadcrumb: ["系统管理", "应急处置管理", "应急物资"]
            },
            "emergency-contacts": {
                title: "应急通讯录",
                icon: "fas fa-users",
                contentUrl: "content/emergency_contacts_content.html",
                breadcrumb: ["系统管理", "应急处置管理", "应急通讯录"]
            }
        }
    },
    "system-settings": {
        title: "系统管理",
        icon: "fas fa-cogs",
        expanded: false,
        children: {
            "user-management": {
                title: "用户管理",
                icon: "fas fa-user",
                contentUrl: "content/user_management_content.html",
                breadcrumb: ["系统管理", "系统管理", "用户管理"]
            },
            "role-management": {
                title: "角色管理",
                icon: "fas fa-user-tag",
                contentUrl: "content/role_management_content.html",
                breadcrumb: ["系统管理", "系统管理", "角色管理"]
            },
            "department-management": {
                title: "部门管理",
                icon: "fas fa-sitemap",
                contentUrl: "content/org_structure_content.html",
                breadcrumb: ["系统管理", "系统管理", "部门管理"]
            },
            "position-management": {
                title: "职位管理",
                icon: "fas fa-briefcase",
                contentUrl: "content/position_management_content.html",
                breadcrumb: ["系统管理", "系统管理", "职位管理"]
            },
            "dictionary-management": {
                title: "字典管理",
                icon: "fas fa-book-open",
                contentUrl: "content/dictionary_management_content.html",
                breadcrumb: ["系统管理", "系统管理", "字典管理"]
            }
        }
    }
};

/**
 * 菜单工具函数
 */
const MenuUtils = {
    /**
     * 根据key查找菜单项
     * @param {string} key - 菜单项key
     * @returns {Object|null} 菜单项配置
     */
    findMenuItem(key) {
        return this.searchInConfig(SYSTEM_MENU_CONFIG, key);
    },

    /**
     * 递归搜索菜单配置
     * @param {Object} config - 菜单配置对象
     * @param {string} targetKey - 目标key
     * @returns {Object|null} 找到的菜单项
     */
    searchInConfig(config, targetKey) {
        for (const [itemKey, itemConfig] of Object.entries(config)) {
            if (itemKey === targetKey) {
                return { key: itemKey, ...itemConfig };
            }
            
            if (itemConfig.children) {
                const result = this.searchInConfig(itemConfig.children, targetKey);
                if (result) return result;
            }
        }
        return null;
    },

    /**
     * 获取菜单项的完整路径
     * @param {string} key - 菜单项key
     * @returns {Array} 路径数组
     */
    getMenuPath(key) {
        return this.findPath(SYSTEM_MENU_CONFIG, key, []);
    },

    /**
     * 递归查找菜单路径
     * @param {Object} config - 菜单配置
     * @param {string} targetKey - 目标key
     * @param {Array} currentPath - 当前路径
     * @returns {Array|null} 路径数组
     */
    findPath(config, targetKey, currentPath) {
        for (const [itemKey, itemConfig] of Object.entries(config)) {
            const newPath = [...currentPath, itemKey];
            
            if (itemKey === targetKey) {
                return newPath;
            }
            
            if (itemConfig.children) {
                const result = this.findPath(itemConfig.children, targetKey, newPath);
                if (result) return result;
            }
        }
        return null;
    },

    /**
     * 获取所有叶子节点（有contentUrl的菜单项）
     * @returns {Array} 叶子节点数组
     */
    getLeafNodes() {
        const leaves = [];
        this.collectLeaves(SYSTEM_MENU_CONFIG, leaves);
        return leaves;
    },

    /**
     * 递归收集叶子节点
     * @param {Object} config - 菜单配置
     * @param {Array} leaves - 叶子节点数组
     */
    collectLeaves(config, leaves) {
        for (const [itemKey, itemConfig] of Object.entries(config)) {
            if (itemConfig.contentUrl) {
                leaves.push({ key: itemKey, ...itemConfig });
            }
            
            if (itemConfig.children) {
                this.collectLeaves(itemConfig.children, leaves);
            }
        }
    }
};

// 导出配置和工具函数
window.SYSTEM_MENU_CONFIG = SYSTEM_MENU_CONFIG;
window.MenuUtils = MenuUtils;
