const navbarHTML = `<!-- 顶部导航栏 -->
<header class="bg-white shadow-sm fixed top-0 left-0 right-0 z-10">
    <div class="flex items-center justify-between h-16 px-6">
        <!-- 左侧 Logo 和系统名称 -->
        <div class="flex items-center">
            <div class="flex items-center mr-4">
                <i class="fas fa-shield-alt text-blue-600 text-2xl"></i>
                <h1 class="ml-2 text-xl font-bold text-gray-800">风险隐患系统</h1>
            </div>
            <!-- Sidebar toggle removed for now -->
            <!-- <button id="sidebar-toggle" class="text-gray-500 focus:outline-none">
                <i class="fas fa-bars"></i>
            </button> -->
        </div>

        <!-- 右侧导航项 (Simplified) -->
        <div class="flex items-center">
            <!-- Search bar removed for now -->
            <!-- <div class="relative mr-4"> ... </div> -->

            <!-- Notifications -->
            <div class="relative mr-4">
                <button class="text-gray-500 hover:text-gray-700 focus:outline-none">
                    <i class="fas fa-bell"></i>
                </button>
                <!-- Optional: Notification badge -->
                <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white"></span>
            </div>

            <!-- User info (Simplified) -->
            <div class="relative">
                <div class="flex items-center text-gray-700 focus:outline-none">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像" class="h-8 w-8 rounded-full object-cover">
                    <span class="ml-2 text-sm font-medium">张三 (管理员)</span>
                    <!-- Dropdown removed for now -->
                    <!-- <i class="fas fa-chevron-down ml-1 text-xs"></i> -->
                </div>
            </div>
        </div>
    </div>
</header>`; 