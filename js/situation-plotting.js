/**
 * 态势标绘功能
 * 用于防汛防台标签页中的态势标绘功能
 */

// 当文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const situationPlottingBtn = document.getElementById('situation-plotting-btn');
    const situationPlottingPanel = document.getElementById('situation-plotting-panel');
    const closePlottingPanelBtn = document.getElementById('close-plotting-panel');
    const plottingIcons = document.querySelectorAll('.plotting-icon');
    const clearPlottingBtn = document.getElementById('clear-plotting');
    const savePlottingBtn = document.getElementById('save-plotting');
    const floodMapImage = document.getElementById('flood-map-image');
    const mapDisplayArea = document.querySelector('#flood-typhoon-prevention-content .map-display-area');

    // 当前选中的图标
    let selectedIcon = null;
    // 已添加的标绘标记
    let plottingMarkers = [];
    // 标记计数器（用于生成唯一ID）
    let markerCounter = 0;

    // 打开态势标绘面板
    situationPlottingBtn.addEventListener('click', function() {
        situationPlottingPanel.style.display = 'block';
    });

    // 关闭态势标绘面板
    closePlottingPanelBtn.addEventListener('click', function() {
        situationPlottingPanel.style.display = 'none';
        // 取消选中状态
        if (selectedIcon) {
            selectedIcon.classList.remove('selected');
            selectedIcon = null;
        }
    });

    // 选择标绘图标
    plottingIcons.forEach(icon => {
        icon.addEventListener('click', function() {
            // 如果已经选中了这个图标，则取消选中
            if (selectedIcon === this) {
                this.classList.remove('selected');
                selectedIcon = null;
                return;
            }

            // 取消之前选中的图标
            if (selectedIcon) {
                selectedIcon.classList.remove('selected');
            }

            // 选中当前图标
            this.classList.add('selected');
            selectedIcon = this;
        });
    });

    // 在地图上添加标绘标记
    mapDisplayArea.addEventListener('click', function(event) {
        // 如果没有选中图标，则不执行任何操作
        if (!selectedIcon) return;

        // 如果点击的是标绘面板或其子元素，则不执行任何操作
        if (event.target.closest('#situation-plotting-panel')) return;

        // 获取点击位置相对于地图的坐标
        const rect = mapDisplayArea.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // 获取选中的图标类型
        const iconType = selectedIcon.getAttribute('data-icon');
        const iconName = selectedIcon.querySelector('span').textContent;

        // 创建标绘标记
        const marker = document.createElement('div');
        marker.className = `plotting-marker ${iconType}-marker`;
        marker.setAttribute('data-icon-type', iconType);
        marker.setAttribute('data-marker-id', `marker-${markerCounter++}`);
        marker.style.left = `${x - 16}px`; // 16是图标宽度的一半
        marker.style.top = `${y - 16}px`; // 16是图标高度的一半
        marker.title = iconName; // 添加提示文本

        // 添加到地图上
        mapDisplayArea.appendChild(marker);

        // 添加到标记列表
        plottingMarkers.push(marker);

        // 添加拖拽功能
        makeDraggable(marker);
    });

    // 清除所有标绘标记
    clearPlottingBtn.addEventListener('click', function() {
        // 移除所有标记
        plottingMarkers.forEach(marker => {
            marker.remove();
        });

        // 清空标记列表
        plottingMarkers = [];
    });

    // 保存标绘（这里只是模拟，实际应该发送到服务器）
    savePlottingBtn.addEventListener('click', function() {
        // 收集所有标记的信息
        const markersData = plottingMarkers.map(marker => {
            return {
                id: marker.getAttribute('data-marker-id'),
                type: marker.getAttribute('data-icon-type'),
                x: parseInt(marker.style.left),
                y: parseInt(marker.style.top)
            };
        });

        // 在实际应用中，这里应该发送到服务器保存
        console.log('保存标绘数据:', markersData);

        // 显示保存成功提示
        alert('标绘数据保存成功！');
    });

    // 使元素可拖拽
    function makeDraggable(element) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;

        element.onmousedown = dragMouseDown;

        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            // 获取鼠标初始位置
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            // 计算新位置
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            // 设置元素的新位置
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
        }

        function closeDragElement() {
            // 停止移动
            document.onmouseup = null;
            document.onmousemove = null;
        }
    }
});
