// 应急演练页面专用JavaScript

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('应急演练页面JavaScript已加载');

    // 初始化页面功能
    initializeEmergencyDrillPage();
});

// 初始化应急演练页面
function initializeEmergencyDrillPage() {
    // 初始化搜索功能
    initializeSearchFunctionality();

    // 初始化按钮事件
    initializeButtonEvents();

    // 初始化分页功能
    initializePagination();

    // 初始化表格交互
    initializeTableInteractions();

    // 初始化模态框功能
    initializeModalFunctionality();

    // 更新统计数据
    updateDrillStatistics();
}

// 初始化模态框功能
function initializeModalFunctionality() {
    // 模态框关闭按钮事件
    const closeButtons = document.querySelectorAll('.drill-modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.drill-modal');
            closeDrillModal(modal.id);
        });
    });

    // 点击模态框外部关闭
    const modals = document.querySelectorAll('.drill-modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeDrillModal(this.id);
            }
        });
    });

    // 表单提交事件
    const addDrillForm = document.getElementById('add-drill-form');
    if (addDrillForm) {
        addDrillForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleAddDrillSubmit();
        });
    }

    // 文件下载按钮事件
    const downloadButtons = document.querySelectorAll('.btn-download');
    downloadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const fileName = this.previousElementSibling.textContent;
            handleFileDownload(fileName);
        });
    });
}

// 显示模态框
function showDrillModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden'; // 防止背景滚动
    }
}

// 关闭模态框
function closeDrillModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto'; // 恢复背景滚动
    }
}

// 初始化搜索功能
function initializeSearchFunctionality() {
    // 实战演练搜索
    const drillSearchInput = document.getElementById('drill-search-input');
    if (drillSearchInput) {
        drillSearchInput.addEventListener('input', function() {
            filterDrillTable('drill-plan-table', this.value);
        });
    }

    // 桌面推演搜索
    const desktopSearchInput = document.getElementById('desktop-search-input');
    if (desktopSearchInput) {
        desktopSearchInput.addEventListener('input', function() {
            filterDrillTable('desktop-drill-table', this.value);
        });
    }
}

// 表格搜索过滤功能
function filterDrillTable(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    if (!table) return;

    const tbody = table.querySelector('tbody');
    const rows = tbody.querySelectorAll('tr');

    searchTerm = searchTerm.toLowerCase().trim();

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let shouldShow = false;

        // 搜索所有单元格内容
        cells.forEach(cell => {
            const cellText = cell.textContent.toLowerCase();
            if (cellText.includes(searchTerm)) {
                shouldShow = true;
            }
        });

        // 显示或隐藏行
        row.style.display = shouldShow ? '' : 'none';
    });

    // 更新搜索结果统计
    updateSearchResults(tableId, searchTerm);
}

// 更新搜索结果统计
function updateSearchResults(tableId, searchTerm) {
    const table = document.getElementById(tableId);
    const visibleRows = table.querySelectorAll('tbody tr[style=""], tbody tr:not([style])');
    const totalRows = table.querySelectorAll('tbody tr').length;

    console.log(`${tableId} 搜索结果: ${visibleRows.length}/${totalRows} 条记录`);
}

// 初始化按钮事件
function initializeButtonEvents() {
    // 新增演练计划按钮
    const addDrillBtn = document.getElementById('add-drill-plan-btn');
    if (addDrillBtn) {
        addDrillBtn.addEventListener('click', function() {
            showAddDrillPlanModal();
        });
    }

    // 演练操作按钮
    initializeDrillActionButtons();

    // 桌面推演操作按钮
    initializeDesktopDrillButtons();
}

// 初始化演练操作按钮
function initializeDrillActionButtons() {
    // 查看演练详情按钮
    const viewDetailButtons = document.querySelectorAll('.view-drill-detail');
    viewDetailButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const drillName = row.cells[1].textContent;
            showDrillDetailModal(drillName, row);
        });
    });

    // 查看复盘报告按钮
    const viewReportButtons = document.querySelectorAll('.view-review-report');
    viewReportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const drillName = row.cells[1].textContent;
            showReviewReportModal(drillName, row);
        });
    });

    // 提交按钮
    const submitButtons = document.querySelectorAll('.submit-button');
    submitButtons.forEach(button => {
        button.addEventListener('click', function() {
            handleSubmitAction(this);
        });
    });

    // 更多操作按钮
    const moreButtons = document.querySelectorAll('.more-button');
    moreButtons.forEach(button => {
        button.addEventListener('click', function() {
            showMoreActionsMenu(this);
        });
    });
}

// 初始化桌面推演按钮
function initializeDesktopDrillButtons() {
    // 演练记录按钮
    const recordButtons = document.querySelectorAll('.record-button');
    recordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const row = this.closest('tr');
            const drillName = row.cells[1].textContent;
            showDrillRecordModal(drillName);
        });
    });
}

// 初始化分页功能
function initializePagination() {
    // 实战演练分页
    initializePaginationForTable('drill-plan-pagination');

    // 桌面推演分页
    initializePaginationForTable('desktop-drill-pagination');
}

// 为特定表格初始化分页
function initializePaginationForTable(paginationId) {
    const pagination = document.getElementById(paginationId);
    if (!pagination) return;

    const buttons = pagination.querySelectorAll('.pagination-button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.disabled) return;

            // 移除所有活动状态
            buttons.forEach(btn => btn.classList.remove('active'));

            // 处理不同类型的分页按钮
            const buttonText = this.textContent.trim();
            if (!isNaN(buttonText)) {
                // 数字页码
                this.classList.add('active');
                loadPage(paginationId, parseInt(buttonText));
            } else {
                // 导航按钮
                handlePaginationNavigation(paginationId, this);
            }
        });
    });
}

// 处理分页导航
function handlePaginationNavigation(paginationId, button) {
    const pagination = document.getElementById(paginationId);
    const currentActive = pagination.querySelector('.pagination-button.active');
    const currentPage = currentActive ? parseInt(currentActive.textContent) : 1;

    const icon = button.querySelector('i');
    if (!icon) return;

    let newPage = currentPage;

    if (icon.classList.contains('fa-angle-left')) {
        newPage = Math.max(1, currentPage - 1);
    } else if (icon.classList.contains('fa-angle-right')) {
        newPage = currentPage + 1;
    } else if (icon.classList.contains('fa-angle-double-left')) {
        newPage = 1;
    } else if (icon.classList.contains('fa-angle-double-right')) {
        newPage = 10; // 假设最大页数
    }

    if (newPage !== currentPage) {
        // 更新活动页码
        const newPageButton = pagination.querySelector(`.pagination-button:nth-child(${newPage + 2})`);
        if (newPageButton && !isNaN(newPageButton.textContent)) {
            currentActive.classList.remove('active');
            newPageButton.classList.add('active');
            loadPage(paginationId, newPage);
        }
    }
}

// 加载指定页面
function loadPage(paginationId, pageNumber) {
    console.log(`加载 ${paginationId} 的第 ${pageNumber} 页`);
    // 这里可以添加实际的数据加载逻辑
}

// 初始化表格交互
function initializeTableInteractions() {
    // 表格行悬停效果已通过CSS实现

    // 状态标签点击事件
    const statusTags = document.querySelectorAll('.status-tag');
    statusTags.forEach(tag => {
        tag.addEventListener('click', function() {
            const status = this.classList.contains('completed') ? '已演练' : '未演练';
            console.log(`点击状态标签: ${status}`);
        });
    });
}

// 更新演练统计数据
function updateDrillStatistics() {
    // 统计实战演练数据
    const drillTable = document.getElementById('drill-plan-table');
    if (drillTable) {
        const rows = drillTable.querySelectorAll('tbody tr');
        let completed = 0;
        let pending = 0;

        rows.forEach(row => {
            const statusTag = row.querySelector('.status-tag');
            if (statusTag) {
                if (statusTag.classList.contains('completed')) {
                    completed++;
                } else if (statusTag.classList.contains('pending')) {
                    pending++;
                }
            }
        });

        // 更新统计显示
        updateStatDisplay('已完成演练', completed);
        updateStatDisplay('未计划演练', pending);
    }

    // 统计桌面推演数据
    const desktopTable = document.getElementById('desktop-drill-table');
    if (desktopTable) {
        const desktopRows = desktopTable.querySelectorAll('tbody tr');
        updateStatDisplay('桌面推演', desktopRows.length);
    }
}

// 更新统计显示
function updateStatDisplay(label, value) {
    const statItems = document.querySelectorAll('.drill-stat-item');
    statItems.forEach(item => {
        const labelElement = item.querySelector('.drill-stat-label');
        if (labelElement && labelElement.textContent === label) {
            const valueElement = item.querySelector('.drill-stat-value');
            if (valueElement) {
                valueElement.textContent = value;
            }
        }
    });
}

// 显示新增演练计划模态框
function showAddDrillPlanModal() {
    showDrillModal('add-drill-modal');
}

// 显示演练详情模态框
function showDrillDetailModal(drillName, row) {
    // 填充模态框数据
    populateDrillMaterialModal(drillName, row);
    showDrillModal('drill-material-modal');
}

// 显示复盘报告模态框
function showReviewReportModal(drillName, row) {
    // 填充模态框数据
    populateReviewReportModal(drillName, row);
    showDrillModal('review-report-modal');
}

// 填充演练资料模态框数据
function populateDrillMaterialModal(drillName, row) {
    const cells = row.cells;

    // 基本信息
    document.getElementById('material-drill-name').textContent = drillName;
    document.getElementById('material-reporting-unit').textContent = cells[2].textContent; // 组织单位
    document.getElementById('material-commander').textContent = cells[3].textContent; // 负责人
    document.getElementById('material-drill-method').textContent = cells[5].textContent; // 演练方式
    document.getElementById('material-drill-time').textContent = cells[4].textContent + ' 09:00'; // 演练时间

    // 根据不同演练设置不同的详细信息
    if (drillName.includes('G72高速公路')) {
        document.getElementById('material-drill-scale').textContent = '120人参与，涉及5个部门';
        document.getElementById('material-location').textContent = 'G72高速公路K1234+500处';
    } else if (drillName.includes('南宁市洪水')) {
        document.getElementById('material-drill-scale').textContent = '80人参与，涉及3个部门';
        document.getElementById('material-location').textContent = '南宁市邕江大桥附近';
    } else if (drillName.includes('柳州市危险品')) {
        document.getElementById('material-drill-scale').textContent = '95人参与，涉及4个部门';
        document.getElementById('material-location').textContent = '柳州市工业园区';
    } else {
        document.getElementById('material-drill-scale').textContent = '100人参与，涉及4个部门';
        document.getElementById('material-location').textContent = '演练指定地点';
    }

    document.getElementById('material-reporter').textContent = '张三';
    document.getElementById('material-submit-time').textContent = '2024-05-16 14:30';
}

// 填充复盘报告模态框数据
function populateReviewReportModal(drillName, row) {
    const cells = row.cells;

    // 基本信息
    document.getElementById('review-drill-name').textContent = drillName;
    document.getElementById('review-reporting-unit').textContent = cells[2].textContent; // 组织单位
    document.getElementById('review-commander').textContent = cells[3].textContent; // 负责人
    document.getElementById('review-drill-method').textContent = cells[5].textContent; // 演练方式
    document.getElementById('review-drill-time').textContent = cells[4].textContent + ' 09:00'; // 演练时间

    // 根据不同演练设置不同的详细信息
    if (drillName.includes('G72高速公路')) {
        document.getElementById('review-drill-scale').textContent = '120人参与，涉及5个部门';
        document.getElementById('review-meeting-room').textContent = '应急指挥中心会议室';
    } else if (drillName.includes('柳州市危险品')) {
        document.getElementById('review-drill-scale').textContent = '95人参与，涉及4个部门';
        document.getElementById('review-meeting-room').textContent = '柳州市应急管理局会议室';
    } else if (drillName.includes('梧州市桥梁')) {
        document.getElementById('review-drill-scale').textContent = '85人参与，涉及3个部门';
        document.getElementById('review-meeting-room').textContent = '梧州市交通局会议室';
    } else {
        document.getElementById('review-drill-scale').textContent = '100人参与，涉及4个部门';
        document.getElementById('review-meeting-room').textContent = '应急指挥中心会议室';
    }

    document.getElementById('review-reporter').textContent = '王五';
    document.getElementById('review-submit-time').textContent = '2024-05-20 16:45';
}

// 显示演练记录模态框
function showDrillRecordModal(drillName) {
    alert(`查看演练记录\n\n演练名称：${drillName}\n\n这里将显示桌面推演的历史记录，包括：\n- 演练时间\n- 参演人员\n- 演练过程\n- 决策记录\n- 结果评估等信息。`);
}

// 处理新增演练计划表单提交
function handleAddDrillSubmit() {
    const form = document.getElementById('add-drill-form');
    const formData = new FormData(form);

    // 验证必填字段
    const requiredFields = ['reportingUnit', 'drillName', 'drillMethod', 'drillContent', 'drillTime', 'responsiblePerson', 'reporter'];
    let isValid = true;

    requiredFields.forEach(field => {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            isValid = false;
            const input = form.querySelector(`[name="${field}"]`);
            if (input) {
                input.style.borderColor = '#e74c3c';
                setTimeout(() => {
                    input.style.borderColor = '#34495e';
                }, 3000);
            }
        }
    });

    if (!isValid) {
        alert('请填写所有必填字段！');
        return;
    }

    // 模拟提交成功
    alert('演练计划提交成功！\n\n系统将自动保存您的演练计划，并通知相关人员。');

    // 重置表单并关闭模态框
    form.reset();
    closeDrillModal('add-drill-modal');

    // 这里可以添加实际的数据提交逻辑
    console.log('演练计划数据:', Object.fromEntries(formData));
}

// 处理提交操作
function handleSubmitAction(button) {
    const buttonText = button.textContent.trim();
    const row = button.closest('tr');
    const drillName = row.cells[1].textContent;

    // 检查是否是演练资料提交按钮
    const parentCell = button.closest('td');
    const cellIndex = Array.from(parentCell.parentNode.children).indexOf(parentCell);

    // 如果是演练资料列（第8列，索引为8）的提交按钮，直接打开提交模态框
    if (cellIndex === 8) {
        // 直接调用演练资料提交模态框
        if (typeof openSubmitMaterialModal === 'function') {
            openSubmitMaterialModal();
        } else {
            // 如果函数不存在，直接打开模态框
            const modal = document.getElementById('submit-drill-material-modal');
            if (modal) {
                modal.style.display = 'block';
                // 设置当前日期
                const today = new Date().toISOString().split('T')[0];
                const submitDateField = document.getElementById('material-submit-date');
                if (submitDateField) {
                    submitDateField.value = today;
                }
            }
        }
        return;
    }

    // 如果是复盘报告列（第9列，索引为9）的提交按钮，直接打开复盘报告提交模态框
    if (cellIndex === 9) {
        // 直接调用复盘报告提交模态框
        if (typeof openSubmitReviewReportModal === 'function') {
            openSubmitReviewReportModal();
        } else {
            // 如果函数不存在，直接打开模态框
            const modal = document.getElementById('submit-review-report-modal');
            if (modal) {
                modal.style.display = 'block';
                // 设置当前日期
                const today = new Date().toISOString().split('T')[0];
                const submitDateField = document.getElementById('review-submit-date');
                if (submitDateField) {
                    submitDateField.value = today;
                }
            }
        }
        return;
    }

    // 其他提交操作保持原有逻辑
    alert(`${buttonText}操作\n\n演练名称：${drillName}\n\n这里将处理相应的提交操作，如：\n- 提交演练资料\n- 提交复盘报告\n- 更新演练状态等。`);
}

// 显示更多操作菜单
function showMoreActionsMenu(button) {
    const row = button.closest('tr');
    const drillName = row.cells[1].textContent;

    alert(`更多操作菜单\n\n演练名称：${drillName}\n\n可用操作：\n- 编辑演练信息\n- 删除演练计划\n- 复制演练计划\n- 导出演练数据\n- 分享演练链接等。`);
}

// 处理文件下载
function handleFileDownload(fileName) {
    alert(`下载文件：${fileName}\n\n这里将启动文件下载功能。`);
    console.log(`下载文件: ${fileName}`);
}

// 导出函数供全局使用
window.filterDrillTable = filterDrillTable;
window.showAddDrillPlanModal = showAddDrillPlanModal;
window.showDrillDetailModal = showDrillDetailModal;
window.showReviewReportModal = showReviewReportModal;
window.showDrillRecordModal = showDrillRecordModal;
window.closeDrillModal = closeDrillModal;
window.handleFileDownload = handleFileDownload;