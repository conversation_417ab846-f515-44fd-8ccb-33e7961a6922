document.addEventListener("DOMContentLoaded", function() {
  // 获取占位符元素
  const navbarPlaceholder = document.getElementById('navbar-placeholder');
  const sidebarPlaceholder = document.getElementById('sidebar-placeholder');

  // 检查变量是否存在以及占位符是否存在，然后注入 HTML
  if (navbarPlaceholder && typeof navbarHTML !== 'undefined') {
    navbarPlaceholder.innerHTML = navbarHTML;
  } else {
    if (!navbarPlaceholder) console.warn("Navbar placeholder not found.");
    if (typeof navbarHTML === 'undefined') console.warn("navbarHTML variable is not defined. Make sure navbarComponent.js is loaded before loadComponents.js");
    // 可选：显示错误信息
     if(navbarPlaceholder) navbarPlaceholder.innerHTML = `<p style="color: red; background: #ffebeb; padding: 10px;">Error loading navbar. Check console.</p>`;
  }

  if (sidebarPlaceholder && typeof sidebarHTML !== 'undefined') {
    sidebarPlaceholder.innerHTML = sidebarHTML;
  } else {
    if (!sidebarPlaceholder) console.warn("Sidebar placeholder not found.");
     if (typeof sidebarHTML === 'undefined') console.warn("sidebarHTML variable is not defined. Make sure sidebarComponent.js is loaded before loadComponents.js");
     // 可选：显示错误信息
     if(sidebarPlaceholder) sidebarPlaceholder.innerHTML = `<p style="color: red; background: #ffebeb; padding: 10px;">Error loading sidebar. Check console.</p>`;
  }

  // DOM 更新后，高亮当前链接
  // 使用 requestAnimationFrame 确保在浏览器渲染 HTML 后执行高亮
  requestAnimationFrame(() => {
     highlightActiveLink();
  });

});

// 函数：高亮当前页面的导航链接
function highlightActiveLink() {
  // 获取当前页面的文件名，例如 "plan_list.html"
  const currentPage = window.location.pathname.split('/').pop();
  if (!currentPage) return; // 如果无法获取当前页面，则退出

  // 查找侧边栏和导航栏中的所有链接
  const sidebarLinks = document.querySelectorAll('#sidebar-placeholder a.sidebar-menu-item');
  // 注意: navbar 可能没有特定的类，需要根据实际情况调整选择器
  // const navbarLinks = document.querySelectorAll('#navbar-placeholder a'); 

  sidebarLinks.forEach(link => {
    const linkHref = link.getAttribute('href');
    if (!linkHref) return; // 跳过没有 href 的链接

    const linkPage = linkHref.split('/').pop();
    const icon = link.querySelector('i'); // Get the icon element
    const span = link.querySelector('span'); // Get the span element

    // Reset styles first
    link.classList.remove('active');
    if (icon) {
      icon.classList.remove('text-blue-600'); // Ensure active color is removed
      icon.classList.add('text-gray-600');   // Add default gray back
    }
     if (span) {
         span.classList.remove('text-blue-600', 'font-medium'); // Ensure active styles are removed
         span.classList.add('text-gray-700'); // Add default gray back
     }

    // 检查链接指向的文件名是否与当前页面文件名匹配
    if (linkPage === currentPage) {
      link.classList.add('active'); // 添加 'active' 类 for background/border
      
      // Explicitly set active styles for icon and text
      if (icon) {
        icon.classList.remove('text-gray-600'); // Remove default gray
        icon.classList.add('text-blue-600');   // Add active blue
      }
       if (span) {
           span.classList.remove('text-gray-700'); // Remove default gray
           span.classList.add('text-blue-600', 'font-medium'); // Add active blue and medium weight
       }
      // 可选：如果侧边栏有折叠子菜单，这里可以添加逻辑展开父菜单
    } 
    // Removed the else block as reset happens at the beginning
  });

  // 如果需要，为 navbar 中的链接添加类似的高亮逻辑
  // navbarLinks.forEach(link => { ... });
}

// 添加一些基本的 CSS 来配合高亮和布局（可选，最好放在 CSS 文件中）
const style = document.createElement('style');
style.textContent = `
  .sidebar-menu-item {
      padding: 0.75rem 1.25rem; /* 12px 20px */
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s;
      text-decoration: none;
      color: #4a5568; /* Equivalent to text-gray-700 */
  }
  .sidebar-menu-item:hover {
      background-color: rgba(59, 130, 246, 0.1);
  }
  .sidebar-menu-item.active {
      background-color: rgba(59, 130, 246, 0.1); /* 淡蓝色背景 */
      border-left: 4px solid #2563eb; /* 左侧蓝色边框 */
  }
  .sidebar-menu-item.active span,
  .sidebar-menu-item.active i {
      color: #2563eb; 
      font-weight: 500;
  }

  /* 树形菜单样式 */
  .sidebar-menu-header {
      display: flex;
      align-items: center;
      padding: 0.75rem 1.25rem;
      cursor: pointer;
      transition: all 0.2s;
  }
  
  .sidebar-menu-header:hover {
      background-color: rgba(59, 130, 246, 0.1);
  }

  .submenu {
      overflow: hidden;
      transition: max-height 0.3s ease-out;
      max-height: 1000px; /* 设置一个足够大的高度以确保内容显示 */
  }

  .submenu.collapsed {
      max-height: 0;
  }

  .submenu .sidebar-menu-item {
      padding-left: 1rem;
      font-size: 0.95rem;
  }

  .fa-chevron-down {
      transition: transform 0.3s ease;
  }

  .fa-chevron-down.collapsed {
      transform: rotate(0deg);
  }

  .fa-chevron-down:not(.collapsed) {
      transform: rotate(180deg);
  }

  /* 基本布局 */
  body > .flex-container {
    display: flex;
  }
  #sidebar-placeholder {
      flex-shrink: 0;
  }
   #main-content {
      flex-grow: 1;
      padding-top: 64px;
   }
`;
document.head.appendChild(style); 