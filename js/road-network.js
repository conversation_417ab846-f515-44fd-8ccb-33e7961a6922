// 路网运行页面专用JavaScript

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('路网运行页面JavaScript已加载');
    
    // 初始化页面功能
    initializeRoadNetworkPage();
});

// 初始化路网运行页面
function initializeRoadNetworkPage() {
    // 初始化树形结构
    initializeTreeStructure();
    
    // 初始化筛选功能
    initializeFilters();
    
    // 初始化地图标点
    initializeMapMarkers();
    
    // 初始化告警标签页
    initializeAlertTabs();
    
    // 初始化统计数据
    updateStatistics();
}

// 初始化树形结构
function initializeTreeStructure() {
    const treeTogglers = document.querySelectorAll('.tree-toggler');
    
    treeTogglers.forEach(toggler => {
        toggler.addEventListener('click', function() {
            const parentLi = this.parentElement;
            const childUl = parentLi.querySelector('ul');
            
            if (childUl) {
                if (childUl.classList.contains('expanded')) {
                    childUl.classList.remove('expanded');
                    this.textContent = '+';
                    this.classList.remove('expanded');
                } else {
                    childUl.classList.add('expanded');
                    this.textContent = '-';
                    this.classList.add('expanded');
                }
            }
        });
    });
    
    // 初始化复选框联动
    initializeCheckboxes();
}

// 初始化复选框联动
function initializeCheckboxes() {
    const checkboxes = document.querySelectorAll('.collapsible-tree input[type="checkbox"]');
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const parentLi = this.closest('li');
            const childCheckboxes = parentLi.querySelectorAll('ul input[type="checkbox"]');
            
            // 如果是父级复选框，控制子级
            if (childCheckboxes.length > 0) {
                childCheckboxes.forEach(child => {
                    child.checked = this.checked;
                });
            }
            
            // 检查父级状态
            updateParentCheckboxState(this);
        });
    });
}

// 更新父级复选框状态
function updateParentCheckboxState(checkbox) {
    const parentLi = checkbox.closest('ul')?.parentElement;
    if (parentLi) {
        const parentCheckbox = parentLi.querySelector('input[type="checkbox"]');
        if (parentCheckbox) {
            const siblingCheckboxes = parentLi.querySelectorAll('ul > li > input[type="checkbox"]');
            const checkedSiblings = parentLi.querySelectorAll('ul > li > input[type="checkbox"]:checked');
            
            if (checkedSiblings.length === 0) {
                parentCheckbox.checked = false;
                parentCheckbox.indeterminate = false;
            } else if (checkedSiblings.length === siblingCheckboxes.length) {
                parentCheckbox.checked = true;
                parentCheckbox.indeterminate = false;
            } else {
                parentCheckbox.checked = false;
                parentCheckbox.indeterminate = true;
            }
            
            // 递归更新上级
            updateParentCheckboxState(parentCheckbox);
        }
    }
}

// 初始化筛选功能
function initializeFilters() {
    // 全选复选框
    const selectAllCheckbox = document.getElementById('select-all-congestion');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const allCheckboxes = document.querySelectorAll('.collapsible-tree input[type="checkbox"]');
            allCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }
    
    // 筛选下拉框
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            applyFilters();
        });
    });
}

// 应用筛选条件
function applyFilters() {
    const congestionStatus = document.getElementById('congestion-status')?.value || 'all';
    const congestionTrend = document.getElementById('congestion-trend')?.value || 'all';
    const congestionTime = document.getElementById('congestion-time')?.value || 'all';
    const congestionDuration = document.getElementById('congestion-duration')?.value || 'all';
    
    console.log('应用筛选条件:', {
        status: congestionStatus,
        trend: congestionTrend,
        time: congestionTime,
        duration: congestionDuration
    });
    
    // 这里可以添加实际的筛选逻辑
    filterAlertItems(congestionStatus, congestionTrend, congestionTime, congestionDuration);
}

// 筛选告警项目
function filterAlertItems(status, trend, time, duration) {
    const alertItems = document.querySelectorAll('.alert-item');
    
    alertItems.forEach(item => {
        let shouldShow = true;
        
        // 根据筛选条件判断是否显示
        if (status !== 'all') {
            const alertLevel = item.querySelector('.alert-level')?.textContent || '';
            shouldShow = shouldShow && matchesStatus(alertLevel, status);
        }
        
        if (trend !== 'all') {
            const alertLevel = item.querySelector('.alert-level')?.textContent || '';
            shouldShow = shouldShow && matchesTrend(alertLevel, trend);
        }
        
        // 显示或隐藏项目
        item.style.display = shouldShow ? 'block' : 'none';
    });
}

// 匹配状态
function matchesStatus(alertLevel, status) {
    const statusMap = {
        'severe': ['严重拥堵', '交通中断', '交通管制'],
        'moderate': ['中度拥堵'],
        'light': ['轻度拥堵'],
        'smooth': []
    };
    
    return statusMap[status]?.includes(alertLevel) || false;
}

// 匹配趋势
function matchesTrend(alertLevel, trend) {
    const trendMap = {
        'severe-soon': ['即将严重拥堵'],
        'moderate-soon': ['即将中度拥堵'],
        'none': []
    };
    
    return trendMap[trend]?.includes(alertLevel) || false;
}

// 切换筛选标签页
function switchFilterTab(button, tabType) {
    // 移除所有活动状态
    document.querySelectorAll('.filter-tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelectorAll('.filter-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 激活当前标签页
    button.classList.add('active');
    document.getElementById(`filter-by-${tabType}-content`).classList.add('active');
}

// 初始化地图标点
function initializeMapMarkers() {
    const mapImage = document.getElementById('road-network-map-image');
    if (!mapImage) return;
    
    // 创建标点容器
    const markersContainer = document.createElement('div');
    markersContainer.style.position = 'relative';
    markersContainer.style.display = 'inline-block';
    
    // 包装地图图片
    mapImage.parentNode.insertBefore(markersContainer, mapImage);
    markersContainer.appendChild(mapImage);
    
    // 添加拥堵标点（包含详细数据）
    const congestionPoints = [
        { 
            x: 45, y: 35, type: 'severe', 
            info: 'G72泉南高速吴家屯隧道',
            data: {
                sectionCode: 'G72-K1500',
                range: 'K1500+200至K1501+500',
                level: '严重拥堵',
                startTime: '2023-04-20 10:30',
                duration: '2小时30分钟',
                flow: '0辆/小时',
                speed: '0 km/h',
                detourRoutes: [
                    {
                        title: '绕行方案一：经S20省道',
                        description: '从桂林南收费站下高速，经S20省道至柳江收费站重新上高速',
                        distance: '增加距离：25公里',
                        time: '预计用时：45分钟',
                        status: '道路畅通'
                    },
                    {
                        title: '绕行方案二：经国道G322',
                        description: '从桂林市区经G322国道至柳州市区',
                        distance: '增加距离：18公里',
                        time: '预计用时：35分钟',
                        status: '轻度拥堵'
                    }
                ],
                videos: [
                    { title: '吴家屯隧道入口', location: 'K1500+100', status: 'online' },
                    { title: '吴家屯隧道出口', location: 'K1501+600', status: 'online' },
                    { title: '桂林南收费站', location: 'K1490+000', status: 'online' }
                ]
            }
        },
        { 
            x: 42, y: 38, type: 'severe', 
            info: 'G72泉南高速K1500+200段',
            data: {
                sectionCode: 'G72-K1500-2',
                range: 'K1500+200至K1500+800',
                level: '严重拥堵',
                startTime: '2023-04-20 10:35',
                duration: '2小时25分钟',
                flow: '0辆/小时',
                speed: '0 km/h',
                detourRoutes: [
                    {
                        title: '绕行方案：经县道X015',
                        description: '从桂林南收费站下高速，经县道X015绕行至柳江',
                        distance: '增加距离：32公里',
                        time: '预计用时：55分钟',
                        status: '道路畅通'
                    }
                ],
                videos: [
                    { title: '事故现场监控', location: 'K1500+500', status: 'online' },
                    { title: '应急车道监控', location: 'K1500+300', status: 'online' }
                ]
            }
        },
        { 
            x: 40, y: 32, type: 'moderate', 
            info: 'G72泉南高速桂林南收费站',
            data: {
                sectionCode: 'G72-GLNSFZ',
                range: 'K1490+000收费站区域',
                level: '中度拥堵',
                startTime: '2023-04-20 11:00',
                duration: '1小时',
                flow: '850辆/小时',
                speed: '25 km/h',
                detourRoutes: [
                    {
                        title: '建议：错峰出行',
                        description: '建议延后30分钟出行，或选择桂林北收费站',
                        distance: '无增加距离',
                        time: '节省时间：20分钟',
                        status: '推荐方案'
                    }
                ],
                videos: [
                    { title: '收费站广场', location: '收费站入口', status: 'online' },
                    { title: '收费站出口', location: '收费站出口', status: 'online' }
                ]
            }
        },
        { 
            x: 48, y: 40, type: 'moderate', 
            info: 'G72泉南高速柳江收费站',
            data: {
                sectionCode: 'G72-LJSFZ',
                range: 'K1510+000收费站区域',
                level: '中度拥堵',
                startTime: '2023-04-20 11:15',
                duration: '45分钟',
                flow: '920辆/小时',
                speed: '30 km/h',
                detourRoutes: [
                    {
                        title: '建议：使用ETC通道',
                        description: '建议使用ETC通道快速通过，避开人工收费通道',
                        distance: '无增加距离',
                        time: '节省时间：10分钟',
                        status: '快速通行'
                    }
                ],
                videos: [
                    { title: 'ETC通道监控', location: 'ETC车道', status: 'online' },
                    { title: '人工收费通道', location: '人工车道', status: 'online' }
                ]
            }
        },
        { 
            x: 35, y: 45, type: 'light', 
            info: 'S20省道桂林段',
            data: {
                sectionCode: 'S20-GL',
                range: 'K30+000至K35+000',
                level: '轻度拥堵',
                startTime: '2023-04-20 12:00',
                duration: '30分钟',
                flow: '1200辆/小时',
                speed: '45 km/h',
                detourRoutes: [
                    {
                        title: '正常通行',
                        description: '当前路段通行正常，预计10分钟内恢复畅通',
                        distance: '无需绕行',
                        time: '正常通行时间',
                        status: '即将恢复'
                    }
                ],
                videos: [
                    { title: 'S20省道K32监控', location: 'K32+000', status: 'online' },
                    { title: 'S20省道K34监控', location: 'K34+000', status: 'offline' }
                ]
            }
        },
        { 
            x: 50, y: 30, type: 'severe-soon', 
            info: 'S20省道预警路段',
            data: {
                sectionCode: 'S20-YJ',
                range: 'K40+000至K60+000',
                level: '即将严重拥堵',
                startTime: '预计2023-04-20 13:30',
                duration: '预计持续2小时',
                flow: '1800辆/小时（接近饱和）',
                speed: '55 km/h（正在下降）',
                detourRoutes: [
                    {
                        title: '预防性绕行：经G322国道',
                        description: '建议提前绕行G322国道，避开即将到来的拥堵',
                        distance: '增加距离：12公里',
                        time: '预计用时：25分钟',
                        status: '强烈推荐'
                    }
                ],
                videos: [
                    { title: 'S20预警点监控', location: 'K45+000', status: 'online' },
                    { title: 'G322绕行路口', location: '绕行入口', status: 'online' }
                ]
            }
        },
        { 
            x: 38, y: 42, type: 'moderate-soon', 
            info: '桂林机场高速预警',
            data: {
                sectionCode: 'GLJCGS-YJ',
                range: '机场高速全线',
                level: '即将中度拥堵',
                startTime: '预计2023-04-20 14:00',
                duration: '预计持续1小时',
                flow: '1400辆/小时（持续增长）',
                speed: '65 km/h（正在下降）',
                detourRoutes: [
                    {
                        title: '建议：错峰出行',
                        description: '建议提前或延后1小时出行，避开航班集中时段',
                        distance: '无增加距离',
                        time: '节省时间：30分钟',
                        status: '时间调整'
                    }
                ],
                videos: [
                    { title: '机场高速入口', location: '市区入口', status: 'online' },
                    { title: '机场高速出口', location: '机场出口', status: 'online' }
                ]
            }
        }
    ];
    
    congestionPoints.forEach(point => {
        const marker = document.createElement('div');
        marker.className = `congestion-marker ${point.type}`;
        marker.style.left = `${point.x}%`;
        marker.style.top = `${point.y}%`;
        marker.title = point.info;
        
        // 添加点击事件
        marker.addEventListener('click', function() {
            showCongestionDetailModal(point);
        });
        
        markersContainer.appendChild(marker);
    });
}

// 显示拥堵详情模态框
function showCongestionDetailModal(point) {
    const modal = document.getElementById('congestion-detail-modal');
    const data = point.data;
    
    // 设置标题
    document.getElementById('congestion-modal-title').textContent = `${point.info} - 拥堵详情`;
    
    // 填充基本信息
    document.getElementById('road-section-code').textContent = data.sectionCode;
    document.getElementById('road-section-range').textContent = data.range;
    document.getElementById('congestion-start-time').textContent = data.startTime;
    document.getElementById('congestion-duration').textContent = data.duration;
    document.getElementById('traffic-flow').textContent = data.flow;
    document.getElementById('average-speed').textContent = data.speed;
    
    // 设置拥堵等级和颜色
    const levelElement = document.getElementById('congestion-level');
    levelElement.textContent = data.level;
    levelElement.className = `info-value congestion-level ${point.type}`;
    
    // 填充绕行方案
    const detourContainer = document.getElementById('detour-routes');
    detourContainer.innerHTML = '';
    data.detourRoutes.forEach(route => {
        const routeDiv = document.createElement('div');
        routeDiv.className = 'detour-route';
        routeDiv.innerHTML = `
            <div class="route-title">${route.title}</div>
            <div class="route-description">${route.description}</div>
            <div class="route-info">
                <span class="route-distance">${route.distance}</span>
                <span class="route-time">${route.time}</span>
                <span class="route-status">${route.status}</span>
            </div>
        `;
        detourContainer.appendChild(routeDiv);
    });
    
    // 填充监控视频
    const videoContainer = document.getElementById('monitoring-videos');
    videoContainer.innerHTML = '';
    data.videos.forEach(video => {
        const videoDiv = document.createElement('div');
        videoDiv.className = 'video-item';
        videoDiv.innerHTML = `
            <div class="video-thumbnail" onclick="playVideo('${video.title}')">
                <i class="fas fa-video"></i>
            </div>
            <div class="video-info">
                <div class="video-title">${video.title}</div>
                <div class="video-location">${video.location}</div>
                <span class="video-status ${video.status}">${video.status === 'online' ? '在线' : '离线'}</span>
            </div>
        `;
        videoContainer.appendChild(videoDiv);
    });
    
    // 显示模态框
    modal.style.display = 'flex';
    
    // 添加点击外部关闭功能
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeCongestionModal();
        }
    });
}

// 关闭拥堵详情模态框
function closeCongestionModal() {
    const modal = document.getElementById('congestion-detail-modal');
    modal.style.display = 'none';
}

// 播放视频（模拟功能）
function playVideo(videoTitle) {
    alert(`正在播放视频：${videoTitle}\n\n注：这是演示功能，实际应用中会打开视频播放器或跳转到视频监控系统。`);
}

// 显示拥堵信息（保留原有简单版本作为备用）
function showCongestionInfo(point) {
    alert(`拥堵信息：\n位置：${point.info}\n等级：${getTypeLabel(point.type)}`);
}

// 获取类型标签
function getTypeLabel(type) {
    const typeMap = {
        'severe': '严重拥堵',
        'moderate': '中度拥堵',
        'light': '轻度拥堵',
        'severe-soon': '即将严重拥堵',
        'moderate-soon': '即将中度拥堵'
    };
    return typeMap[type] || '未知';
}

// 初始化告警标签页
function initializeAlertTabs() {
    // 标签页已在HTML中设置了onclick事件
    console.log('告警标签页已初始化');
}

// 切换路网告警标签页
function switchRoadNetworkAlertTab(button, tabType) {
    // 移除所有活动状态
    document.querySelectorAll('.alert-tab-button').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelectorAll('.alert-tab-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 激活当前标签页
    button.classList.add('active');
    document.getElementById(`alert-${tabType}-content`).classList.add('active');
}

// 更新统计数据
function updateStatistics() {
    // 统计各类拥堵数量
    const alertItems = document.querySelectorAll('.alert-item');
    const stats = {
        severe: 0,
        moderate: 0,
        light: 0,
        soon: 0
    };
    
    alertItems.forEach(item => {
        const alertLevel = item.querySelector('.alert-level')?.textContent || '';
        
        if (alertLevel.includes('交通中断') || alertLevel.includes('交通管制')) {
            stats.severe++;
        } else if (alertLevel.includes('严重拥堵')) {
            stats.severe++;
        } else if (alertLevel.includes('中度拥堵')) {
            stats.moderate++;
        } else if (alertLevel.includes('轻度拥堵')) {
            stats.light++;
        } else if (alertLevel.includes('即将')) {
            stats.soon++;
        }
    });
    
    // 更新显示
    const severeCount = document.getElementById('severe-congestion-count');
    const moderateCount = document.getElementById('moderate-congestion-count');
    const lightCount = document.getElementById('light-congestion-count');
    const soonCount = document.getElementById('soon-congestion-count');
    
    if (severeCount) severeCount.textContent = stats.severe;
    if (moderateCount) moderateCount.textContent = stats.moderate;
    if (lightCount) lightCount.textContent = stats.light;
    if (soonCount) soonCount.textContent = stats.soon;
}

// 导出函数供全局使用
window.switchFilterTab = switchFilterTab;
window.switchRoadNetworkAlertTab = switchRoadNetworkAlertTab;
window.closeCongestionModal = closeCongestionModal;
window.playVideo = playVideo; 