/**
 * 指挥调度系统主要功能
 */

// 模拟数据
const mockEvents = [
    {
        id: 'EVT001',
        title: 'G75高速多车相撞事故',
        reporter: '南宁市交通运输局',
        reportTime: '2024-12-19 14:30',
        eventTime: '2024-12-19 14:25',
        location: 'G75兰海高速南宁至柳州段K1205+300处',
        level: '重大事故',
        status: 'processing',
        responseStatus: 'activated',
        activatedPlan: '广西壮族自治区公路交通突发事件应急预案',
        activatedTime: '2024-12-19 14:35',
        activatedBy: '李明华厅长',
        responseLevel: 'Ⅱ级响应',
        casualties: '3人轻伤，无死亡',
        description: '雨天路滑，5辆车连环相撞'
    },
    {
        id: 'EVT002',
        title: '桂林隧道火灾事故',
        reporter: '桂林市交通运输局',
        reportTime: '2024-12-19 16:20',
        eventTime: '2024-12-19 16:00',
        location: '桂林市象山隧道',
        level: '较大事故',
        status: 'pending',
        responseStatus: 'recommended',
        recommendedPlan: '广西隧道火灾应急预案',
        recommendedLevel: 'Ⅲ级响应',
        recommendedReason: '事件类型匹配，影响范围评估',
        casualties: '暂无人员伤亡',
        description: '隧道内货车起火，烟雾较大'
    },
    {
        id: 'EVT003',
        title: '南宁绕城高速积水事件',
        reporter: '南宁市交通运输局',
        reportTime: '2024-12-18 09:15',
        eventTime: '2024-12-18 09:00',
        location: '南宁绕城高速东段',
        level: '一般事故',
        status: 'completed',
        responseStatus: 'completed',
        activatedPlan: '广西公路防汛应急预案',
        processingTime: '2小时30分钟',
        result: '积水排除，交通恢复',
        casualties: '无人员伤亡',
        description: '暴雨导致路面积水严重'
    },
    {
        id: 'EVT004',
        title: '柳州大桥交通管制',
        reporter: '柳州市交通运输局',
        reportTime: '2024-12-19 10:15',
        eventTime: '2024-12-19 10:00',
        location: '柳州大桥',
        level: '一般事故',
        status: 'confirmed',
        responseStatus: 'recommended',
        recommendedPlan: '广西桥梁交通管制应急预案',
        recommendedLevel: 'Ⅳ级响应',
        recommendedReason: '桥梁结构检查需要',
        casualties: '无人员伤亡',
        description: '桥梁例行检查，实施临时交通管制'
    },
    {
        id: 'EVT005',
        title: '北海港危化品泄漏',
        reporter: '北海市交通运输局',
        reportTime: '2024-12-19 08:45',
        eventTime: '2024-12-19 08:30',
        location: '北海港码头区域',
        level: '重大事故',
        status: 'processing',
        responseStatus: 'activated',
        activatedPlan: '广西港口危化品应急预案',
        activatedTime: '2024-12-19 08:50',
        activatedBy: '张德国副厅长',
        responseLevel: 'Ⅱ级响应',
        casualties: '1人中毒，已送医',
        description: '码头装卸过程中化学品泄漏'
    },
    {
        id: 'EVT006',
        title: '梧州货车侧翻事故',
        reporter: '梧州市交通运输局',
        reportTime: '2024-12-18 15:20',
        eventTime: '2024-12-18 15:10',
        location: 'G321国道梧州段K125+400',
        level: '较大事故',
        status: 'completed',
        responseStatus: 'completed',
        activatedPlan: '广西国道交通事故应急预案',
        processingTime: '3小时45分钟',
        result: '车辆清除，道路恢复通行',
        casualties: '驾驶员轻伤',
        description: '超载货车转弯时侧翻，占用部分车道'
    }
];

// 指挥调度系统主对象
const CommandDispatch = {
  currentTab: "event-report",
  currentSubTab: "event-submit",
  organizationTree: null, // 存储组织架构数据
  divisionTree: null, // 存储行政区划数据
  markerTree: null, // 存储道路标识数据
  addressTimer: null, // 防抖定时器
  searchTimers: new Map(), // 存储各个树形选择器的搜索防抖定时器
  // 当前事件列表状态
  currentEventFilter: "all",
  currentEventPage: 1,
  eventPageSize: 10,
  eventList:[],

  // 添加分页相关属性
  totalEvents: 0, // 总事件数
  totalPages: 0,  // 总页数

  // 初始化
  init() {
    this.initEventListeners();
    this.initDateTime();
    this.loadReceivedEvents();
    this.initTreeSelects();
    this.initAddressGeocode(); // 初始化地址解析功能
  },

  // 初始化树形下拉框
  async initTreeSelects() {
    try {
      console.log("开始初始化树形下拉框...");

      // 获取组织架构数据
      await this.loadOrganizationTree();

      // 获取行政区划数据
      await this.loadDivisionTree();

      // 获取道路标识数据
      await this.loadMarkerTree();

      // 确保DOM元素存在后再初始化
      setTimeout(() => {
        // 初始化上报人选择器
        this.initTreeSelect("submitter", "submitterValue", "organization");

        // 初始化路段管辖单位负责人选择器
        this.initTreeSelect(
          "managementUnit",
          "managementUnitValue",
          "organization"
        );

        // 初始化行政辖区选择器
        this.initTreeSelect(
          "administrativeRegion",
          "administrativeRegionValue",
          "division"
        );

        // 初始化路段编号选择器
        this.initTreeSelect(
          "roadSectionCode",
          "roadSectionCodeValue",
          "marker"
        );

        console.log("树形下拉框初始化完成");
      }, 100);
    } catch (error) {
      console.error("初始化树形下拉框失败:", error);
      this.showErrorMessage("加载数据失败，请刷新页面重试");
    }
  },

  // 加载组织架构数据
  async loadOrganizationTree() {
    try {
      console.log("开始加载组织架构数据...");
      const response = await window.Http.get("/system/organization/tree");
      console.log("组织架构数据加载成功:", response);
      this.organizationTree = response.data || [];
      console.log("组织架构数据加载成功:", this.organizationTree);
    } catch (error) {
      console.error("加载组织架构数据失败:", error);
      throw error;
    }
  },

  // 加载行政区划数据
  async loadDivisionTree() {
    try {
      console.log("开始加载行政区划数据...");
      const response = await window.Http.get("/system/division/tree");
      console.log("行政区划数据加载成功:", response);
      this.divisionTree = response.data || [];
      console.log("行政区划数据:", this.divisionTree);
    } catch (error) {
      console.error("加载行政区划数据失败:", error);
      throw error;
    }
  },

  // 加载道路标识数据
  async loadMarkerTree() {
    try {
      console.log("开始加载道路标识数据...");
      const response = await window.Http.get("/system/marker/tree");
      console.log("道路标识数据加载成功:", response);
      this.markerTree = response.data || [];
      console.log("道路标识数据:", this.markerTree);
    } catch (error) {
      console.error("加载道路标识数据失败:", error);
      throw error;
    }
  },

  // 初始化单个树形下拉框
  initTreeSelect(selectId, hiddenInputId, dataType = "organization") {
    console.log(`初始化树形下拉框: ${selectId}, 数据类型: ${dataType}`);

    const treeSelect = document.getElementById(selectId);
    const hiddenInput = document.getElementById(hiddenInputId);

    if (!treeSelect) {
      console.error(`找不到元素: ${selectId}`);
      return;
    }

    if (!hiddenInput) {
      console.error(`找不到隐藏输入框: ${hiddenInputId}`);
      return;
    }

    const display = treeSelect.querySelector(".tree-select-display");
    const dropdown = treeSelect.querySelector(".tree-select-dropdown");
    const searchInput = treeSelect.querySelector(".tree-search-input");
    const optionsContainer = treeSelect.querySelector(".tree-options");

    if (!display || !dropdown || !searchInput || !optionsContainer) {
      console.error(`树形下拉框 ${selectId} 的子元素不完整`);
      return;
    }

    // 根据数据类型选择对应的数据源
    let treeData;
    switch (dataType) {
      case "division":
        treeData = this.divisionTree;
        break;
      case "marker":
        treeData = this.markerTree;
        break;
      default:
        treeData = this.organizationTree;
    }

    if (!treeData || !Array.isArray(treeData)) {
      console.warn(`${dataType}数据未加载或格式错误:`, treeData);
      return;
    }

    console.log(`开始渲染 ${selectId} 的选项...`);
    // 渲染树形结构
    this.renderTreeOptions(optionsContainer, treeData, 0, dataType);

    // 移除之前的事件监听器（避免重复绑定）
    const newDisplay = display.cloneNode(true);
    display.parentNode.replaceChild(newDisplay, display);

    // 点击显示区域切换下拉框
    newDisplay.addEventListener("click", (e) => {
      console.log(`点击了 ${selectId} 的显示区域`);
      e.stopPropagation();
      this.toggleTreeSelect(treeSelect);
    });

    // 重新获取搜索输入框和选项容器的引用
    const currentSearchInput = treeSelect.querySelector(".tree-search-input");
    const currentOptionsContainer = treeSelect.querySelector(".tree-options");

    // 清空并重新渲染选项
    currentOptionsContainer.innerHTML = "";
    this.renderTreeOptions(currentOptionsContainer, treeData, 0, dataType);

    // 重新绑定搜索功能 - 添加防抖
    if (currentSearchInput) {
      // 移除旧的事件监听器
      const newSearchInput = currentSearchInput.cloneNode(true);
      currentSearchInput.parentNode.replaceChild(
        newSearchInput,
        currentSearchInput
      );

      // 绑定新的搜索事件 - 使用防抖
      newSearchInput.addEventListener("input", (e) => {
        const searchText = e.target.value;
        console.log(`搜索输入 (${selectId}): ${searchText}`);

        // 清除之前的搜索定时器
        if (this.searchTimers.has(selectId)) {
          clearTimeout(this.searchTimers.get(selectId));
        }

        // 设置新的防抖定时器
        const timer = setTimeout(() => {
          console.log(`执行防抖搜索 (${selectId}): ${searchText}`);
          this.filterTreeOptions(currentOptionsContainer, searchText);
          this.searchTimers.delete(selectId); // 清理已完成的定时器
        }, 500); // 500ms 防抖延迟

        this.searchTimers.set(selectId, timer);
      });

      // 键盘导航支持
      newSearchInput.addEventListener("keydown", (e) => {
        if (e.key === "Escape") {
          this.closeAllTreeSelects();
        } else if (e.key === "Enter") {
          // 回车键立即执行搜索
          e.preventDefault();
          const searchText = e.target.value;

          // 清除防抖定时器
          if (this.searchTimers.has(selectId)) {
            clearTimeout(this.searchTimers.get(selectId));
            this.searchTimers.delete(selectId);
          }

          // 立即执行搜索
          console.log(`回车立即搜索 (${selectId}): ${searchText}`);
          this.filterTreeOptions(currentOptionsContainer, searchText);
        }
      });

      // 失焦时立即执行搜索（如果有待执行的搜索）
      newSearchInput.addEventListener("blur", (e) => {
        const searchText = e.target.value;

        // 如果有待执行的搜索定时器，立即执行
        if (this.searchTimers.has(selectId)) {
          clearTimeout(this.searchTimers.get(selectId));
          this.searchTimers.delete(selectId);

          console.log(`失焦立即搜索 (${selectId}): ${searchText}`);
          this.filterTreeOptions(currentOptionsContainer, searchText);
        }
      });
    }

    // 统一的点击事件处理
    currentOptionsContainer.addEventListener("click", (e) => {
      e.stopPropagation();

      // 检查是否点击的是展开/收起图标
      const expandIcon = e.target.closest(".tree-node-expand");
      if (expandIcon) {
        console.log("点击了展开/收起图标");
        const treeNode = expandIcon.closest(".tree-node");
        if (treeNode) {
          this.toggleTreeNode(treeNode);
        }
        return;
      }

      // 检查是否点击的是节点内容
      const nodeContent = e.target.closest(".tree-node-content");
      if (nodeContent) {
        const nodeType = nodeContent.dataset.type;
        const nodeId = nodeContent.dataset.id;
        const nodeName =
          nodeContent.querySelector(".tree-node-text").textContent;
        console.log(
          `点击了节点: ${nodeId}, 类型: ${nodeType}, 名称: ${nodeName}`
        );

        // 根据数据类型决定选择逻辑
        if (dataType === "organization") {
          // 组织架构只能选择用户
          if (nodeType === "user") {
            this.selectTreeNode(
              nodeContent,
              currentOptionsContainer,
              newDisplay,
              hiddenInput
            );
          } else {
            // 点击部门或岗位节点，展开/收起
            const treeNode = nodeContent.closest(".tree-node");
            if (treeNode && treeNode.querySelector(".tree-node-children")) {
              console.log(`展开/收起部门或岗位节点: ${nodeId}`);
              this.toggleTreeNode(treeNode);
            }
          }
        } else if (dataType === "division") {
          // 行政区划只能选择最后一级
          if (nodeContent.dataset.isleaf === "true") {
            this.selectTreeNode(
              nodeContent,
              currentOptionsContainer,
              newDisplay,
              hiddenInput
            );
          } else {
            const treeNode = nodeContent.closest(".tree-node");
            if (treeNode && treeNode.querySelector(".tree-node-children")) {
              console.log(`展开/收起行政区划节点: ${nodeId}`);
              this.toggleTreeNode(treeNode);
            }
          }
        } else if (dataType === "marker") {
          // 道路标识只能选择marker类型的叶子节点
          if (nodeType === "marker" && nodeContent.dataset.isleaf === "true") {
            this.selectTreeNode(
              nodeContent,
              currentOptionsContainer,
              newDisplay,
              hiddenInput
            );
          } else {
            const treeNode = nodeContent.closest(".tree-node");
            if (treeNode && treeNode.querySelector(".tree-node-children")) {
              console.log(`展开/收起路段节点: ${nodeId}`);
              this.toggleTreeNode(treeNode);
            }
          }
        }
      }
    });

    console.log(`${selectId} 初始化完成`);
  },

  // 选择树节点
  selectTreeNode(nodeContent, optionsContainer, display, hiddenInput) {
    const nodeName = nodeContent.querySelector(".tree-node-text").textContent;
    const nodeId = nodeContent.dataset.id;
    const nodeType = nodeContent.dataset.type;
    const deptId = nodeContent.dataset.deptid; // 获取部门ID

    // 移除之前选中的状态
    optionsContainer
      .querySelectorAll(".tree-node-content.selected")
      .forEach((node) => {
        node.classList.remove("selected");
      });

    // 添加选中状态
    nodeContent.classList.add("selected");

    // 更新显示
    const selectedText = display.querySelector(".selected-text");
    selectedText.textContent = nodeName;
    selectedText.classList.remove("placeholder");

    // 更新隐藏字段值
    hiddenInput.value = nodeId;

    // 如果是路段管辖单位负责人选择器，还需要存储部门ID
    const selectElement = optionsContainer.closest(".tree-select");
    if (
      selectElement &&
      selectElement.id === "managementUnit" &&
      nodeType === "user" &&
      deptId
    ) {
      // 创建或更新额外的隐藏字段来存储部门ID
      let deptIdInput = document.getElementById("roadManagerUnitId");
      if (!deptIdInput) {
        deptIdInput = document.createElement("input");
        deptIdInput.type = "hidden";
        deptIdInput.id = "roadManagerUnitId";
        deptIdInput.name = "roadManagerUnitId";
        hiddenInput.parentElement.appendChild(deptIdInput);
      }
      deptIdInput.value = deptId;
      console.log(`设置路段管辖单位部门ID: ${deptId}`);
    }

    // 关闭下拉框
    this.closeAllTreeSelects();

    // 触发change事件
    hiddenInput.dispatchEvent(new Event("change"));

    console.log(
      `选择了节点: ${nodeName} (${nodeId})${
        deptId ? ", 部门ID: " + deptId : ""
      }`
    );
  },

  // 添加清空选择的功能
  clearTreeSelect(selectId) {
    const treeSelect = document.getElementById(selectId);
    const hiddenInput = document.getElementById(selectId + "Value");
    const display = treeSelect.querySelector(".tree-select-display");
    const selectedText = display.querySelector(".selected-text");

    // 清空选择
    selectedText.textContent = selectedText.classList.contains("submitter")
      ? "请选择上报人"
      : "请选择负责人";
    selectedText.classList.add("placeholder");
    hiddenInput.value = "";

    // 如果是路段管辖单位负责人选择器，也清空部门ID
    if (selectId === "managementUnit") {
      const roadManagerUnitIdInput =
        document.getElementById("roadManagerUnitId");
      if (roadManagerUnitIdInput) {
        roadManagerUnitIdInput.value = "";
      }
    }

    // 移除选中状态
    const optionsContainer = treeSelect.querySelector(".tree-options");
    optionsContainer
      .querySelectorAll(".tree-node-content.selected")
      .forEach((node) => {
        node.classList.remove("selected");
      });
  },

  // 渲染树形选项
  renderTreeOptions(container, data, level = 0, dataType = "organization") {
    if (!data || !Array.isArray(data)) {
      console.warn("无效的树形数据:", data);
      return;
    }

    // 清空容器
    container.innerHTML = "";

    data.forEach((item) => {
      const nodeElement = this.createTreeNode(item, level, dataType);
      container.appendChild(nodeElement);

      if (item.children && item.children.length > 0) {
        const childrenContainer = nodeElement.querySelector(
          ".tree-node-children"
        );
        this.renderTreeOptions(
          childrenContainer,
          item.children,
          level + 1,
          dataType
        );
      }
    });

    // console.log(`渲染了 ${data.length} 个节点`);
  },

  // 创建树节点 - 更简洁的动态样式
  createTreeNode(item, level, dataType = "organization") {
    const nodeDiv = document.createElement("div");

    // 根据数据类型设置节点类型
    let nodeType, displayName, isLeaf, deptId, nodeId;

    const hasChildren = item.children && item.children.length > 0;
    if (dataType === "division") {
      nodeType = "division";
      displayName = item.name || item.extName || "";
      isLeaf = !hasChildren;
      nodeId = item.id;
    } else if (dataType === "marker") {
      nodeType = "marker";
      displayName = item.label || item.name || item.allName || "";
      isLeaf = item.isLeaf;
      nodeId = item?.code || item?.id;
    } else {
      nodeType = item.type || "dept";
      displayName = item.name;
      deptId = item?.deptId;
      nodeId = item?.userId || item.id;
      if (item.type === "user" && item.nickName) {
        displayName = `${item.nickName} (${item.userName || ""})`;
      }
    }

    nodeDiv.className = `tree-node ${nodeType}`;
    nodeDiv.dataset.id = nodeId;
    nodeDiv.dataset.type = nodeType;
    nodeDiv.dataset.level = level;

    nodeDiv.innerHTML = `
            <div class="tree-node-content" data-id="${nodeId}" data-type="${nodeType}" data-isleaf="${isLeaf}" ${
      dataType === "organization" ? `data-deptid=${deptId}` : ""
    }>
                ${
                  hasChildren
                    ? `<span class="tree-node-expand"><i class="fas fa-chevron-right"></i></span>`
                    : '<span class="tree-node-expand tree-node-expand-empty"></span>'
                }
                <i class="tree-node-icon ${this.getNodeIcon(
                  nodeType,
                  dataType
                )}"></i>
                <span class="tree-node-text">${displayName}</span>
            </div>
            ${hasChildren ? '<div class="tree-node-children"></div>' : ""}
        `;

    // 设置缩进
    const content = nodeDiv.querySelector(".tree-node-content");
    content.style.paddingLeft = `${level * 20 + 10}px`;

    return nodeDiv;
  },

  // 获取节点图标
  getNodeIcon(nodeType, dataType = "organization") {
    if (dataType === "division") {
      return "fas fa-map-marker-alt";
    }

    switch (nodeType) {
      case "dept":
        return "fas fa-building";
      case "post":
        return "fas fa-user-tag";
      case "user":
        return "fas fa-user";
      default:
        return "fas fa-folder";
    }
  },

  // 切换树形下拉框
  toggleTreeSelect(treeSelect) {
    console.log("切换树形下拉框状态");
    const isOpen = treeSelect.classList.contains("open");

    // 先关闭所有其他下拉框
    this.closeAllTreeSelects();

    if (!isOpen) {
      console.log("打开树形下拉框");
      treeSelect.classList.add("open");
      // 聚焦搜索框
      const searchInput = treeSelect.querySelector(".tree-search-input");
      setTimeout(() => searchInput.focus(), 100);
    }
  },

  // 关闭所有树形下拉框
  closeAllTreeSelects() {
    // 清理所有搜索定时器
    this.searchTimers.forEach((timer, selectId) => {
      clearTimeout(timer);
      console.log(`清理搜索定时器: ${selectId}`);
    });
    this.searchTimers.clear();

    document.querySelectorAll(".tree-select.open").forEach((select) => {
      select.classList.remove("open");
    });
  },

  // 切换树节点展开/收起
  toggleTreeNode(node) {
    console.log("切换节点展开状态:", node.dataset.id);
    const isExpanded = node.classList.contains("expanded");

    if (isExpanded) {
      node.classList.remove("expanded");
      console.log("收起节点");
    } else {
      node.classList.add("expanded");
      console.log("展开节点");
    }
  },

  // 过滤树形选项 - 展开匹配节点及其所有上级
  filterTreeOptions(container, searchText) {
    const selectElement = container.closest(".tree-select");
    const selectId = selectElement ? selectElement.id : "unknown";

    console.log(`过滤选项 (${selectId})，搜索文本: "${searchText}"`);

    // 显示搜索状态指示器
    this.showSearchStatus(container, "searching");

    const allNodes = container.querySelectorAll(".tree-node");
    const lowerSearchText = searchText.toLowerCase().trim();

    if (!lowerSearchText) {
      // 显示所有节点，恢复正常状态
      allNodes.forEach((node) => {
        node.classList.remove("hidden", "search-hidden");
        node.style.display = "";
        // 收起所有节点
        node.classList.remove("expanded");
      });
      this.hideSearchStatus(container);
      console.log(`搜索文本为空 (${selectId})，显示所有节点`);
      return;
    }

    // 第一步：找到所有匹配的节点
    let matchingNodes = [];
    let nodesToShow = new Set(); // 用Set避免重复

    allNodes.forEach((node) => {
      const textElement = node.querySelector(".tree-node-text");
      if (!textElement) return;

      const text = textElement.textContent.toLowerCase();
      const matches = text.includes(lowerSearchText);

      if (matches) {
        matchingNodes.push(node);
        nodesToShow.add(node); // 匹配的节点要显示
        console.log(`匹配节点 (${selectId}): ${text}`);
      }
    });

    // 第二步：添加所有匹配节点的父节点路径
    matchingNodes.forEach((node) => {
      this.addParentNodesToShow(node, nodesToShow);
    });

    // 第三步：添加所有匹配节点的子孙节点（使用递归方法）
    matchingNodes.forEach((node) => {
      this.addChildNodesToShow(node, nodesToShow);
    });

    // 第四步：显示/隐藏节点
    let showCount = 0;
    allNodes.forEach((node) => {
      if (nodesToShow.has(node)) {
        node.classList.remove("hidden", "search-hidden");
        node.style.display = "";
        showCount++;
      } else {
        node.classList.add("search-hidden");
        node.style.display = "none";
      }
    });

    // 第五步：先收起所有节点
    allNodes.forEach((node) => {
      node.classList.remove("expanded");
    });

    // 第六步：展开匹配节点及其所有上级节点
    matchingNodes.forEach((node) => {
      // 展开匹配节点本身
      if (node.querySelector(".tree-node-children")) {
        node.classList.add("expanded");
        console.log(`展开匹配节点: ${node.dataset.id}`);
      }

      // 展开匹配节点的所有父节点
      this.expandParentNodes(node);
    });

    // 显示搜索结果状态
    if (matchingNodes.length > 0) {
      this.showSearchStatus(
        container,
        "success",
        `找到 ${matchingNodes.length} 个匹配项`
      );
    } else {
      this.showSearchStatus(container, "empty", "未找到匹配项");
    }

    console.log(
      `搜索完成 (${selectId})，匹配到 ${matchingNodes.length} 个节点，显示 ${showCount} 个节点`
    );
  },

  // 展开父节点 - 用于展开匹配节点的完整路径
  expandParentNodes(node) {
    let current = node;

    // 向上遍历找到所有父节点并展开
    while (current) {
      const parentChildren = current.parentElement;
      if (
        parentChildren &&
        parentChildren.classList.contains("tree-node-children")
      ) {
        const parentNode = parentChildren.parentElement;
        if (parentNode && parentNode.classList.contains("tree-node")) {
          parentNode.classList.add("expanded");
          console.log(`展开父节点: ${parentNode.dataset.id}`);
          current = parentNode;
        } else {
          break;
        }
      } else {
        break;
      }
    }
  },

  // 添加子节点到显示列表（递归方法，获取所有子孙节点）
  addChildNodesToShow(node, nodesToShow) {
    const childrenContainer = node.querySelector(".tree-node-children");
    if (!childrenContainer) return;

    const childNodes = childrenContainer.querySelectorAll(".tree-node");
    childNodes.forEach((childNode) => {
      nodesToShow.add(childNode);
      console.log(`添加子节点到显示列表: ${childNode.dataset.id}`);

      // 递归添加子节点的子节点
      this.addChildNodesToShow(childNode, nodesToShow);
    });
  },

  // 添加父节点到显示列表
  addParentNodesToShow(node, nodesToShow) {
    let current = node;

    // 向上遍历找到所有父节点并添加到显示列表
    while (current) {
      const parentChildren = current.parentElement;
      if (
        parentChildren &&
        parentChildren.classList.contains("tree-node-children")
      ) {
        const parentNode = parentChildren.parentElement;
        if (parentNode && parentNode.classList.contains("tree-node")) {
          nodesToShow.add(parentNode);
          console.log(`添加父节点到显示列表: ${parentNode.dataset.id}`);
          current = parentNode;
        } else {
          break;
        }
      } else {
        break;
      }
    }
  },

  // 显示搜索状态
  showSearchStatus(container, status, message = "") {
    const dropdown = container.closest(".tree-select-dropdown");
    if (!dropdown) return;

    let statusElement = dropdown.querySelector(".search-status");

    if (!statusElement) {
      statusElement = document.createElement("div");
      statusElement.className = "search-status";
      statusElement.style.cssText = `
                padding: 8px 12px;
                font-size: 12px;
                text-align: center;
                border-bottom: 1px solid #e9ecef;
                background: #f8f9fa;
            `;

      // 插入到搜索框后面
      const searchInput = dropdown.querySelector(".tree-search-input");
      if (searchInput && searchInput.parentNode) {
        searchInput.parentNode.insertBefore(
          statusElement,
          searchInput.nextSibling
        );
      }
    }

    // 根据状态设置样式和内容
    switch (status) {
      case "searching":
        statusElement.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> 搜索中...';
        statusElement.style.color = "#6c757d";
        break;
      case "success":
        statusElement.innerHTML = `<i class="fas fa-check"></i> ${message}`;
        statusElement.style.color = "#28a745";
        break;
      case "empty":
        statusElement.innerHTML =
          '<i class="fas fa-exclamation-circle"></i> 未找到匹配项';
        statusElement.style.color = "#dc3545";
        break;
    }

    statusElement.style.display = "block";

    // 搜索状态3秒后自动隐藏
    if (status !== "searching") {
      setTimeout(() => {
        this.hideSearchStatus(container);
      }, 3000);
    }
  },

  // 隐藏搜索状态
  hideSearchStatus(container) {
    const dropdown = container.closest(".tree-select-dropdown");
    if (!dropdown) return;

    const statusElement = dropdown.querySelector(".search-status");
    if (statusElement) {
      statusElement.style.display = "none";
    }
  },

  // 初始化事件监听器
  initEventListeners() {
    // 主标签页切换
    document.querySelectorAll(".sidebar-tab").forEach((tab) => {
      tab.addEventListener("click", (e) => {
        const tabId = e.currentTarget.dataset.tab;
        this.switchTab(tabId);
      });
    });

    // 子标签页切换
    document.querySelectorAll(".sub-tab").forEach((tab) => {
      tab.addEventListener("click", (e) => {
        const subTabId = e.currentTarget.dataset.subtab;
        this.switchSubTab(subTabId);
      });
    });

    // 事件筛选
    document.querySelectorAll(".filter-btn").forEach((btn) => {
      btn.addEventListener("click", (e) => {
        const filter = e.currentTarget.dataset.filter;
        this.filterEvents(filter);
      });
    });

    // 表单提交
    document.getElementById("eventForm").addEventListener("submit", (e) => {
      e.preventDefault();
      this.submitEvent();
    });

    // 事件类型切换监听
    const eventTypeSelect = document.getElementById("eventType");
    if (eventTypeSelect) {
      eventTypeSelect.addEventListener("change", () => {
        this.toggleAccidentFields();
      });
    }
  },

  // 初始化日期时间
  initDateTime() {
    const now = new Date();
    const formatDateTime = (date) => {
      return date.toISOString().slice(0, 16);
    };

    // 只有当reportTime元素存在时才设置值
    const reportTimeInput = document.getElementById("reportTime");
    if (reportTimeInput) {
      reportTimeInput.value = formatDateTime(now);
    }
  },

  // 切换主标签页
  switchTab(tabId) {
    // 更新标签按钮状态
    document.querySelectorAll(".sidebar-tab").forEach((tab) => {
      tab.classList.remove("active");
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add("active");

    // 更新内容显示
    document.querySelectorAll(".tab-pane").forEach((pane) => {
      pane.classList.remove("active");
    });
    document.getElementById(tabId).classList.add("active");

    this.currentTab = tabId;
  },

  // 切换子标签页
  switchSubTab(subTabId) {
    console.log("切换子标签页到:", subTabId);

    // 更新子标签按钮状态
    document.querySelectorAll(".sub-tab").forEach((tab) => {
      tab.classList.remove("active");
    });
    document
      .querySelector(`[data-subtab="${subTabId}"]`)
      .classList.add("active");

    // 更新子内容显示
    document.querySelectorAll(".sub-pane").forEach((pane) => {
      pane.classList.remove("active");
    });
    document.getElementById(subTabId).classList.add("active");

    this.currentSubTab = subTabId;

    // 当切换到收到事件标签时，重新加载事件列表
    if (subTabId === "received-events") {
      console.log("切换到收到事件标签，重新加载事件列表");
      setTimeout(() => {
        this.loadReceivedEvents();
      }, 100);
    }
  },

  // 提交事件 - 使用正确的接口和数据格式
  async submitEvent() {
    const submitBtn = document.querySelector(
      '.btn-submit, button[type="submit"]'
    );
    const originalBtnText = submitBtn?.innerHTML || "上报上级";
    try {
      // 验证必填的树形选择框
      const submitterValue = document.getElementById("submitterValue").value;
      if (!submitterValue) {
        this.showErrorMessage("请选择上报人");
        return;
      }

      // 验证必填字段
      const requiredFields = [
        { id: "eventTitle", name: "事件标题" },
        { id: "eventType", name: "事件类型" },
        { id: "eventTime", name: "发生时间" },
        { id: "eventLevel", name: "事件等级" },
        { id: "eventLocation", name: "事故详细地址" },
        { id: "impactScope", name: "影响范围" },
        { id: "eventDescription", name: "事件描述" },
        { id: "eventCause", name: "事件原因" },
        { id: "measures", name: "已采取的应急处置措施" },
        { id: "forces", name: "投入的应急力量" },
      ];

      for (let field of requiredFields) {
        const element = document.getElementById(field.id);
        if (!element || !element.value.trim()) {
          this.showErrorMessage(`请填写${field.name}`);
          return;
        }
      }

      // 禁用提交按钮
      if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> 上报中...';
      }

      // 获取表单数据
      const formData = this.collectFormData();

      console.log("提交的事件数据:", formData);

      // 更新上报状态
    //   document.getElementById("reportStatus").style.display = "block";
    //   document.getElementById("reportSent").innerHTML = "⏳ 正在上报...";
    //   document.getElementById("superiorConfirm").innerHTML = "⏸️ 等待中";

      // 调用后台接口
      const result = await window.Http.post("/emergency/event/add", formData);

      console.log("事件提交成功:", result);

      // 更新上报状态
    //   document.getElementById("reportSent").innerHTML = "✅ 上报成功";

      if (result.eventId || result.id) {
        // 如果后台返回了事件ID，显示出来
        const eventId = result.eventId || result.id;
        const eventIdInfo = document.createElement("div");
        eventIdInfo.className = "status-item";
        eventIdInfo.innerHTML = `
                    <span class="status-label">事件编号：</span>
                    <span class="status-value">${eventId}</span>
                `;
        // document.getElementById("reportStatus").appendChild(eventIdInfo);

        // 模拟上级确认过程
        setTimeout(() => {
          this.checkEventStatus(eventId);
        }, 3000);
      }

      this.showSuccessMessage("事件已成功上报！");
    } catch (error) {
      console.error("事件提交失败:", error);

      // 更新错误状态
    //   document.getElementById("reportSent").innerHTML = "❌ 上报失败";
    //   document.getElementById("superiorConfirm").innerHTML = "⏸️ 已中止";

      // 显示错误信息
      let errorMessage = "事件上报失败";
      if (error.message) {
        if (error.message.includes("网络")) {
          errorMessage = "网络连接错误，请检查网络后重试";
        } else if (error.message.includes("404")) {
          errorMessage = "上报接口不存在，请联系系统管理员";
        } else if (error.message.includes("500")) {
          errorMessage = "服务器内部错误，请稍后重试";
        } else {
          errorMessage = `上报失败：${error.message}`;
        }
      }

      this.showErrorMessage(errorMessage);
    } finally {
      // 恢复提交按钮状态
      const submitBtn = document.querySelector(
        '.btn-submit, button[type="submit"]'
      );
      if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalBtnText;
      }
    }
  },

  // 收集并转换表单数据
  collectFormData() {
    // 基础信息
    const eventData = {
      // 基本事件信息
      eventTitle: document.getElementById("eventTitle").value.trim(),
      eventType: this.convertEventType(
        document.getElementById("eventType").value
      ),
      accidentType: this.convertAccidentType(
        document.getElementById("accidentType").value
      ),
      occurTime: this.convertToTimestamp(
        document.getElementById("eventTime").value
      ),

      // 地理位置信息
      administrativeArea: this.getSelectedAdministrativeAreaName(),
      administrativeAreaId:
        document.getElementById("administrativeRegionValue").value || "",
      detailedAddress: document.getElementById("eventLocation").value.trim(),
      longitude: parseFloat(document.getElementById("longitude").value) || null,
      latitude: parseFloat(document.getElementById("latitude").value) || null,

      // 事件等级和责任人
      eventLevel: this.convertEventLevel(
        document.getElementById("eventLevel").value
      ),
      submitterId: document.getElementById("submitterValue").value,
      roadManagerLeaderId:
        document.getElementById("managementUnitValue").value || "",
      roadManagerUnitId:
        document.getElementById("roadManagerUnitId").value || "",

      // 事件描述信息
      impactScope: document.getElementById("impactScope").value.trim(),
      eventDescription: document
        .getElementById("eventDescription")
        .value.trim(),
      eventCause: document.getElementById("eventCause").value.trim(),
      emergencyMeasures: document.getElementById("measures").value.trim(),
      emergencyForces: document.getElementById("forces").value.trim(),
      supportNeeded: document.getElementById("support").value.trim() || "",
      remark: document.getElementById("remark").value.trim() || "",
    };

    // 如果是道路交通事故，添加专项信息
    if (document.getElementById("eventType").value === "traffic-accident") {
      const trafficData = this.collectTrafficAccidentData();
      Object.assign(eventData, trafficData);
    }

    // 移除空值
    Object.keys(eventData).forEach((key) => {
      if (eventData[key] === null || eventData[key] === "") {
        delete eventData[key];
      }
    });

    return eventData;
  },

  // 转换事件类型
  convertEventType(value) {
    const typeMap = {
      "traffic-accident": "1", // 道路交通事故
      "water-accident": "2", // 水路交通事故
      // 'construction-accident': '3', // 工程建设事故
      // 'natural-disaster': '4',     // 自然灾害
      // 'public-health': '5',        // 公共卫生事件
      // 'social-security': '6',      // 社会安全事件
      // 'other': '7'                 // 其他事件
    };
    return typeMap[value] || "1";
  },

  // 转换事故类型
  convertAccidentType(value) {
    const typeMap = {
      collision: "1", // 碰撞事故
      rollover: "2", // 翻车事故
      fire: "3", // 火灾事故
      // 'explosion': '4',    // 爆炸事故
      hazmat: "4", // 危化品事故
      // 'collapse': '6',     // 坍塌事故
      // 'other': '7'         // 其他
    };
    return typeMap[value] || "1";
  },

  // 转换事件等级
  convertEventLevel(value) {
    const levelMap = {
      level1: "1", // Ⅰ级（特别重大）
      level2: "2", // Ⅱ级（重大）
      level3: "3", // Ⅲ级（较大）
      level4: "4", // Ⅳ级（一般）
    };
    return levelMap[value] || "4";
  },

  // 转换时间为时间戳（秒）
  convertToTimestamp(datetimeLocal) {
    if (!datetimeLocal) return Math.floor(Date.now() / 1000);

    const date = new Date(datetimeLocal);
    return Math.floor(date.getTime() / 1000);
  },

  // 获取选中的行政区名称
  getSelectedAdministrativeAreaName() {
    const selectedElement = document.querySelector(
      "#administrativeRegion .tree-node-content.selected .tree-node-text"
    );
    return selectedElement ? selectedElement.textContent.trim() : "";
  },

  // 收集道路交通事故专项数据
  collectTrafficAccidentData() {
    const roadSectionCode =
      document.getElementById("roadSectionCodeValue")?.value?.trim() || "";

    return {
      // 道路交通事故专项信息
      roadSectionCode: roadSectionCode, // 默认路段编号
      startStakeNumber:
        document.getElementById("startStakeNumber").value.trim() || "",
      endStakeNumber:
        document.getElementById("endStakeNumber").value.trim() || "",
      direction: this.convertDirection(
        document.getElementById("direction")?.value
      ),
      trafficAffected: document.getElementById("trafficAffected")?.value,
      vehicleType: document.getElementById("vehicleType")?.value.trim() || "",
      estimatedRecoveryTime: this.convertToTimestamp(
        document.getElementById("estimatedRecoveryTime")?.value
      ),
      roadCasualtySituation:
        document.getElementById("roadCasualtySituation")?.value.trim() || "",
      impactTrend: document.getElementById("impactTrend")?.value.trim() || "",
    };
  },

  // 转换方向
  convertDirection(value) {
    const directionMap = {
      up: "1", // 上行
      down: "2", // 下行
      both: "3", // 双向
    };
    return directionMap[value] || "3";
  },

  // 检查事件状态
  async checkEventStatus(eventId) {
    try {
      const response = await fetch(`/api/events/${eventId}/status`);
      if (response.ok) {
        const statusData = await response.json();

        if (statusData.confirmed) {
        //   document.getElementById("superiorConfirm").innerHTML = "✅ 已确认";

          if (statusData.comment) {
            // document.getElementById("superiorComment").style.display = "block";
            // document.getElementById("commentText").innerHTML =
            //   statusData.comment;
          }

          this.showSuccessMessage("上级已确认事件信息");
        } else {
          // 如果还未确认，可以继续轮询或显示等待状态
        //   document.getElementById("superiorConfirm").innerHTML =
        //     "⏰ 等待确认中...";
        }
      }
    } catch (error) {
      console.warn("检查事件状态失败:", error);
      // 状态检查失败不影响主流程，只记录警告
    }
  },

  // 提交附件文件（如果有的话）
  async submitAttachments(eventId, files) {
    if (!files || files.length === 0) return;

    const attachmentFormData = new FormData();
    attachmentFormData.append("eventId", eventId);

    for (let i = 0; i < files.length; i++) {
      attachmentFormData.append("attachments", files[i]);
    }

    try {
      const response = await fetch("/api/events/attachments", {
        method: "POST",
        body: attachmentFormData,
      });

      if (response.ok) {
        console.log("附件上传成功");
        this.showSuccessMessage("附件上传成功");
      } else {
        throw new Error("附件上传失败");
      }
    } catch (error) {
      console.error("附件上传错误:", error);
      this.showErrorMessage("附件上传失败，但事件信息已提交成功");
    }
  },

  // 显示成功消息
  showSuccessMessage(message) {
    this.showMessage(message, "success");
  },

  // 显示错误消息
  showErrorMessage(message) {
    // 简单的错误提示，您可以根据需要替换为更好的UI组件
    alert(message);
  },

  // 显示消息的通用方法
  showMessage(message, type = "info") {
    // 创建消息提示
    const messageDiv = document.createElement("div");
    messageDiv.className = `message-toast message-${type}`;

    let icon = "fas fa-info-circle";
    let bgColor = "#3498db";

    switch (type) {
      case "success":
        icon = "fas fa-check-circle";
        bgColor = "#27ae60";
        break;
      case "error":
        icon = "fas fa-exclamation-circle";
        bgColor = "#e74c3c";
        break;
      case "warning":
        icon = "fas fa-exclamation-triangle";
        bgColor = "#f39c12";
        break;
    }

    messageDiv.innerHTML = `
            <i class="${icon}"></i>
            ${message}
        `;

    messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            max-width: 400px;
            animation: slideInRight 0.3s ease;
        `;

    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
      if (messageDiv.parentNode) {
        messageDiv.style.animation = "slideOutRight 0.3s ease";
        setTimeout(() => {
          if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
          }
        }, 300);
      }
    }, 3000);
  },

  // 重置表单
  resetForm() {
    const form = document.getElementById("eventForm");
    form.reset();
    // document.getElementById("reportStatus").style.display = "none";
    // document.getElementById("superiorComment").style.display = "none";
    this.initDateTime();
    this.showSuccessMessage("表单已重置");
  },

  // 加载收到的事件列表
  async loadReceivedEvents(filter = null, page = 1) {
    console.log("开始加载收到事件列表");
    const eventsList = document.getElementById("eventsList");

    if (!eventsList) {
      console.error("未找到eventsList元素，ID为: eventsList");
      return;
    }

    console.log("找到eventsList元素，清空现有内容");
    eventsList.innerHTML = "";

    try {
      // 如果指定了筛选条件，则更新当前筛选状态
      if (filter !== null) {
        this.currentEventFilter = filter;
      }
      this.currentEventPage = page;

      // 构建请求参数
      const params = {
        pageNum: this.currentEventPage,
        pageSize: this.eventPageSize
      };

      // 根据筛选条件添加status参数
      if (this.currentEventFilter !== "all") {
        const statusMap = {
          pending: "0", // 待确认
          confirmed: "1", // 已确认
          completed: "2", // 已完成
        };
        params.status = statusMap[this.currentEventFilter];
      }

      // 发送请求
      const result = await window.Http.get("/emergency/event/list", params);

      if (result.code === 200) {
        // 更新分页信息
        this.totalEvents = result.total || 0;
        this.totalPages = Math.ceil(this.totalEvents / this.eventPageSize);
        
        if (result.rows?.length > 0) {
          this.eventList = result.rows;
          for (let index = 0; index < result.rows.length; index++) {
            const item = result.rows[index];
            const eventCard = this.createEventCard(item);
            eventsList.appendChild(eventCard);
          }
        } else {
          // 显示空状态
          this.showEmptyState();
        }
        
        // 更新分页控件
        this.updatePagination();
      } else {
        console.error("加载事件列表失败:", result.msg);
        this.showEventError("加载事件列表失败: " + result.msg);
      }
    } catch (error) {
      console.error("加载事件列表时发生错误:", error);
      this.showEventError("网络错误，请检查连接后重试");
    }
  },

  // 显示空状态 - 增强版本
  showEmptyState(type = 'no-events', customMessage = null) {
    const eventsList = document.getElementById("eventsList");
    
    const emptyStates = {
      'no-events': {
        icon: 'fas fa-inbox',
        title: '暂无事件',
        message: customMessage || '当前筛选条件下没有找到相关事件'
      },
      'no-search-results': {
        icon: 'fas fa-search',
        title: '未找到搜索结果',
        message: customMessage || '尝试使用不同的关键词或调整筛选条件'
      },
      'error': {
        icon: 'fas fa-exclamation-triangle',
        title: '加载失败',
        message: customMessage || '网络连接异常，请检查网络后重试'
      },
      'loading': {
        icon: 'fas fa-spinner',
        title: '正在加载',
        message: customMessage || '请稍候，正在获取最新数据...'
      }
    };

    const state = emptyStates[type] || emptyStates['no-events'];
    
    eventsList.innerHTML = `
      <div class="empty-state ${type}">
        <i class="${state.icon}"></i>
        <h3>${state.title}</h3>
        <p>${state.message}</p>
      </div>
    `;
  },

  // 更新分页控件
  updatePagination() {
    const paginationContainer = document.getElementById("eventsPagination");
    const paginationInfo = document.getElementById("paginationInfo");
    const paginationNumbers = document.getElementById("paginationNumbers");
    const prevBtn = document.getElementById("prevPageBtn");
    const nextBtn = document.getElementById("nextPageBtn");

    if (!paginationContainer) return;

    // 如果总页数小于等于1，隐藏分页控件
    if (this.totalPages <= 1) {
      paginationContainer.style.display = "none";
      return;
    }

    // 显示分页控件
    paginationContainer.style.display = "flex";

    // 更新分页信息
    const startItem = (this.currentEventPage - 1) * this.eventPageSize + 1;
    const endItem = Math.min(this.currentEventPage * this.eventPageSize, this.totalEvents);
    paginationInfo.textContent = `显示第 ${startItem}-${endItem} 条，共 ${this.totalEvents} 条记录`;

    // 更新上一页/下一页按钮状态
    prevBtn.disabled = this.currentEventPage <= 1;
    nextBtn.disabled = this.currentEventPage >= this.totalPages;

    // 生成页码按钮
    this.renderPageNumbers();
  },

  // 渲染页码按钮
  renderPageNumbers() {
    const paginationNumbers = document.getElementById("paginationNumbers");
    if (!paginationNumbers) return;

    paginationNumbers.innerHTML = "";

    const currentPage = this.currentEventPage;
    const totalPages = this.totalPages;
    
    // 计算显示的页码范围
    let startPage = 1;
    let endPage = totalPages;
    
    if (totalPages > 7) {
      if (currentPage <= 4) {
        endPage = 5;
      } else if (currentPage >= totalPages - 3) {
        startPage = totalPages - 4;
      } else {
        startPage = currentPage - 2;
        endPage = currentPage + 2;
      }
    }

    // 添加第一页
    if (startPage > 1) {
      this.addPageButton(1);
      if (startPage > 2) {
        this.addEllipsis();
      }
    }

    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
      this.addPageButton(i);
    }

    // 添加最后一页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        this.addEllipsis();
      }
      this.addPageButton(totalPages);
    }
  },

  // 添加页码按钮
  addPageButton(pageNum) {
    const paginationNumbers = document.getElementById("paginationNumbers");
    const button = document.createElement("button");
    button.className = `page-number ${pageNum === this.currentEventPage ? 'active' : ''}`;
    button.textContent = pageNum;
    button.onclick = () => this.goToPage(pageNum);
    paginationNumbers.appendChild(button);
  },

  // 添加省略号
  addEllipsis() {
    const paginationNumbers = document.getElementById("paginationNumbers");
    const ellipsis = document.createElement("span");
    ellipsis.className = "page-ellipsis";
    ellipsis.textContent = "...";
    paginationNumbers.appendChild(ellipsis);
  },

  // 切换页面
  changePage(direction) {
    if (direction === 'prev' && this.currentEventPage > 1) {
      this.goToPage(this.currentEventPage - 1);
    } else if (direction === 'next' && this.currentEventPage < this.totalPages) {
      this.goToPage(this.currentEventPage + 1);
    }
  },

  // 跳转到指定页面
  goToPage(pageNum) {
    if (pageNum < 1 || pageNum > this.totalPages || pageNum === this.currentEventPage) {
      return;
    }
    
    this.loadReceivedEvents(null, pageNum);
  },

  // 创建事件卡片
  createEventCard(event) {
    console.log("创建事件卡片，事件ID:", event.id, "标题:", event.title);
    const card = document.createElement("div");
    card.className = "event-card";
    card.dataset.status = event.status;

    // 格式化时间
    const occurTime = new Date(event.occurTime * 1000).toLocaleString("zh-CN");

    // 格式化事件等级
    const getLevelText = (level) => {
      const levelMap = {
        1: "Ⅰ级（特别重大）",
        2: "Ⅱ级（重大）",
        3: "Ⅲ级（较大）",
        4: "Ⅳ级（一般）",
      };
      return levelMap[level] || "";
    };

    // 格式化状态
    const getStatusClass = (status) => {
      const statusMap = {
        0: "status-pending",
        1: "status-confirmed",
        2: "status-completed",
      };
      return statusMap[status] || "status-pending";
    };

    const getStatusText = (status) => {
      const statusMap = {
        0: "待确认",
        1: "已确认",
        2: "已完成",
      };
      return statusMap[status] || "待确认";
    };

    const statusClass = getStatusClass(event.status);
    const statusText = getStatusText(event.status);
    const levelText = getLevelText(event.eventLevel);

    let responseStatusHTML = "";
    if (event.status === "1") {
      event.activatedPlan = "广西壮族自治区公路交通突发事件应急预案";
      event.activatedTime = "2024-12-19 14:35";
      event.activatedBy = "李明华厅长";
      event.responseLevel = "Ⅱ级响应";
      responseStatusHTML = `
                <div class="response-status">
                    <h5><i class="fas fa-check-circle"></i> 应急响应状态</h5>
                    <div class="response-info">
                        • 预案状态：✅ 已启动<br>
                        • 启动预案：${event.activatedPlan}<br>
                        • 启动时间：${event.activatedTime}<br>
                        • 启动人员：${event.activatedBy}<br>
                        • 响应等级：${event.responseLevel}
                    </div>
                </div>
            `;
    } else if (event.status === "0") {
      event.recommendedPlan = "广西隧道火灾应急预案";
      event.recommendedLevel = "Ⅲ级响应";
      event.recommendedReason = "事件类型匹配，影响范围评估";

      responseStatusHTML = `
                <div class="response-status">
                    <h5><i class="fas fa-lightbulb"></i> 应急响应状态</h5>
                    <div class="response-info">
                        • 预案状态：💡 推荐预案<br>
                        • 推荐预案：${event.recommendedPlan}<br>
                        • 推荐等级：${event.recommendedLevel}<br>
                        • 推荐依据：${event.recommendedReason}
                    </div>
                </div>
            `;
    } else if (event.responseStatus === "2") {
      event.activatedPlan = "广西公路防汛应急预案";
      event.processingTime = "2小时30分钟";
      event.result = "积水排除，交通恢复";
      responseStatusHTML = `
                <div class="response-status">
                    <h5><i class="fas fa-check-circle"></i> 应急响应状态</h5>
                    <div class="response-info">
                        • 预案状态：✅ 已启动并完成<br>
                        • 启动预案：${event.activatedPlan}<br>
                        • 处置时长：${event.processingTime}<br>
                        • 处置结果：${event.result}
                    </div>
                </div>
            `;
    }

    let actionsHTML = "";
    if (event.status === "0") {
        // actionsHTML = `
        // <div class="event-actions">
        //     <button class="btn btn-primary btn-sm" onclick="CommandDispatch.viewDetails('${event.eventId}')">
        //         <i class="fas fa-eye"></i> 查看详情
        //     </button>
        //     <button class="btn btn-primary btn-sm" onclick="CommandDispatch.confirmEvent('${event.id}')">
        //         <i class="fas fa-check"></i> 确认事件
        //     </button>
        //     <button class="btn btn-secondary btn-sm" onclick="CommandDispatch.startResponse('${event.id}')">
        //         <i class="fas fa-rocket"></i> 启动响应
        //     </button>
        // </div>
        // `; 
        actionsHTML = `
            <div class="event-actions">
                <button class="btn btn-primary btn-sm" onclick="CommandDispatch.viewDetails('${event.eventId}')">
                    <i class="fas fa-eye"></i> 查看详情
                </button>
            </div>
        `;
    } else if (event.status === "1") {
      actionsHTML = `
                <div class="event-actions">
                    <button class="btn btn-primary btn-sm" onclick="CommandDispatch.viewDetails('${event.eventId}')">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                    <button class="btn btn-success btn-sm" onclick="CommandDispatch.enterCommand('${event.id}')">
                        <i class="fas fa-headset"></i> 进入指挥
                    </button>
                </div>
            `;
    } else if (event.status === "2") {
      actionsHTML = `
                <div class="event-actions">
                    <button class="btn btn-primary btn-sm" onclick="CommandDispatch.viewDetails('${event.eventId}')">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="CommandDispatch.viewEvaluation('${event.id}')">
                        <i class="fas fa-chart-line"></i> 查看评估
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="CommandDispatch.viewSummary('${event.id}')">
                        <i class="fas fa-book"></i> 经验总结
                    </button>
                </div>
            `;
    }

    card.innerHTML = `
            <div class="event-header">
                <div>
                    <div class="event-title">${event.eventTitle}</div>
                    <div class="event-meta">
                        <span>上报人：${event.reporterName}</span>
                        <span>时间：${occurTime}</span>
                        <span>等级：${levelText}</span>
                    </div>
                </div>
                <div class="event-status ${statusClass}">${statusText}</div>
            </div>
            ${responseStatusHTML}
            ${actionsHTML}
        `;

    return card;
  },

  // 筛选事件
  filterEvents(filter) {
    // 更新筛选按钮状态
    document.querySelectorAll(".filter-btn").forEach((btn) => {
      btn.classList.remove("active");
    });
    document.querySelector(`[data-filter="${filter}"]`).classList.add("active");

    // 筛选事件卡片
    // document.querySelectorAll(".event-card").forEach((card) => {
    //   if (filter === "all" || card.dataset.status === filter) {
    //     card.style.display = "block";
    //   } else {
    //     card.style.display = "none";
    //   }
    // });

    this.loadReceivedEvents(filter);
  },

  // 确认事件
  confirmEvent(eventId) {
    // 更新事件状态
    const eventIndex = mockEvents.findIndex((e) => e.id === eventId);
    if (eventIndex !== -1) {
      mockEvents[eventIndex].status = "confirmed";
    }

    // 重新加载事件列表
    this.loadReceivedEvents();

    // 显示成功消息
    this.showSuccessMessage(`事件已确认：${eventId}`);
  },

  // 启动响应
  startResponse(eventId) {
    const event = mockEvents.find((e) => e.id === eventId);
    if (event) {
      this.showResponseModal(event);
    }
  },

  // 显示响应启动模态框
  showResponseModal(event) {
    const modal = document.getElementById("responseModal");
    const content = document.getElementById("responseModalContent");

    content.innerHTML = `
            <div class="response-form">
                <div class="event-info">
                    <h4>📊 事件信息</h4>
                    <p><strong>事件：</strong>${event.title}</p>
                    <p><strong>上报单位：</strong>${event.reporter}</p>
                    <p><strong>发生时间：</strong>${event.eventTime}</p>
                </div>

                <div class="plan-selection">
                    <div class="plan-header">
                        <h4>📋 预案选择</h4>
                        <button class="btn btn-info btn-sm" onclick="CommandDispatch.viewOtherPlans('${event.id}')">
                            <i class="fas fa-list"></i> 查看其他预案
                        </button>
                    </div>
                    <div class="plan-option">
                        <input type="radio" id="recommended" name="plan" value="recommended" checked>
                        <label for="recommended">
                            <strong>${event.recommendedPlan}</strong> (推荐)<br>
                            <small>适用范围：隧道火灾突发事件<br>
                            响应等级：${event.recommendedLevel}<br>
                            推荐依据：${event.recommendedReason}</small>
                        </label>
                    </div>
                </div>

                <div class="confirmation">
                    <h4>🎯 启动确认</h4>
                    <p><strong>选择预案：</strong>${event.recommendedPlan}</p>
                    <p><strong>响应等级：</strong>${event.recommendedLevel}</p>
                    <p><strong>启动人员：</strong>[当前用户] 李明华厅长</p>
                </div>

                <div class="modal-actions">
                    <button class="btn btn-primary" onclick="CommandDispatch.confirmStartResponse('${event.id}')">
                        <i class="fas fa-rocket"></i> 确认启动
                    </button>
                    <button class="btn btn-secondary" onclick="CommandDispatch.closeResponseModal()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        `;

    modal.style.display = "flex";
  },

  // 确认启动响应
  confirmStartResponse(eventId) {
    // 获取选中的预案
    const selectedPlan = document.querySelector(
      'input[name="plan"]:checked'
    ).value;

    // 更新事件状态
    const eventIndex = mockEvents.findIndex((e) => e.id === eventId);
    if (eventIndex !== -1) {
      mockEvents[eventIndex].status = "processing";
      mockEvents[eventIndex].responseStatus = "activated";
      mockEvents[eventIndex].activatedPlan =
        selectedPlan === "recommended"
          ? mockEvents[eventIndex].recommendedPlan
          : "广西壮族自治区公路交通突发事件应急预案";
      mockEvents[eventIndex].activatedTime = new Date().toLocaleString("zh-CN");
      mockEvents[eventIndex].activatedBy = "李明华厅长";
      mockEvents[eventIndex].responseLevel =
        selectedPlan === "recommended"
          ? mockEvents[eventIndex].recommendedLevel
          : "Ⅱ级响应";
    }

    // 重新加载事件列表
    this.loadReceivedEvents();

    // 显示成功消息
    this.showSuccessMessage(`应急响应已启动！事件：${eventId}`);
    this.closeResponseModal();
  },

  // 关闭响应模态框
  closeResponseModal() {
    document.getElementById("responseModal").style.display = "none";
  },

  // 查看其他预案
  viewOtherPlans(eventId) {
    const modalHTML = `
            <div class="modal-overlay" onclick="CommandDispatch.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3><i class="fas fa-list"></i> 应急预案库</h3>
                        <button class="modal-close" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="plans-list">
                            <div class="plan-category">
                                <h4><i class="fas fa-fire"></i> 火灾事故预案</h4>
                                <div class="plan-items">
                                    <div class="plan-item">
                                        <div class="plan-name">广西隧道火灾应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">III级响应</span>
                                            <span class="plan-scope">隧道火灾突发事件</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('tunnel-fire', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('tunnel-fire')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                    <div class="plan-item">
                                        <div class="plan-name">建筑火灾应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">II级响应</span>
                                            <span class="plan-scope">建筑物火灾事故</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('building-fire', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('building-fire')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="plan-category">
                                <h4><i class="fas fa-car-crash"></i> 交通事故预案</h4>
                                <div class="plan-items">
                                    <div class="plan-item">
                                        <div class="plan-name">广西壮族自治区公路交通突发事件应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">可调整</span>
                                            <span class="plan-scope">各类公路交通突发事件</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('traffic-general', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('traffic-general')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                    <div class="plan-item">
                                        <div class="plan-name">高速公路交通事故应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">III级响应</span>
                                            <span class="plan-scope">高速公路交通事故</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('highway-traffic', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('highway-traffic')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="plan-category">
                                <h4><i class="fas fa-exclamation-triangle"></i> 综合应急预案</h4>
                                <div class="plan-items">
                                    <div class="plan-item">
                                        <div class="plan-name">广西壮族自治区突发事件总体应急预案</div>
                                        <div class="plan-info">
                                            <span class="plan-level">I-IV级</span>
                                            <span class="plan-scope">各类突发事件</span>
                                        </div>
                                        <div class="plan-actions">
                                            <button class="btn btn-sm btn-primary" onclick="CommandDispatch.selectPlan('general-emergency', '${eventId}')">
                                                <i class="fas fa-check"></i> 选择
                                            </button>
                                            <button class="btn btn-sm btn-secondary" onclick="CommandDispatch.viewPlanDetails('general-emergency')">
                                                <i class="fas fa-eye"></i> 详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

    document.body.insertAdjacentHTML("beforeend", modalHTML);
  },

  // 选择预案
  selectPlan(planId, eventId) {
    this.closeModal();
    this.showSuccessMessage(`已选择预案：${planId}`);
    // 这里可以添加更新预案选择的逻辑
  },

  // 查看预案详情
  viewPlanDetails(planId) {
    alert(`查看预案详情：${planId}`);
  },

  async viewDetails(eventId) {
    let response = await window.Http.get(`/emergency/event/${eventId}`);
    if (response.code !== 200) {
      this.showErrorMessage(response.message);
      return;
    }
    let event = response.data;

    if (!event) return;

    const statusText = {
      0: "待确认",
      1: "已确认",
      2: "已完成",
    };

    const levelText = {
      I: "I级响应",
      II: "II级响应",
      III: "III级响应",
      IV: "IV级响应",
    };

    const occurTime = new Date(event.occurTime * 1000).toLocaleString("zh-CN");
    const estimatedRecoveryTime = new Date(event.estimatedRecoveryTime * 1000).toLocaleString("zh-CN");

    const modalHTML = `
            <div class="modal-overlay" onclick="CommandDispatch.closeModal()">
                <div class="modal-content" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3><i class="fas fa-info-circle"></i> 事件详情</h3>
                        <button class="modal-close" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="event-details">
                            <h4><i class="fas fa-exclamation-triangle"></i> ${
                              event.eventTitle
                            }</h4>

                            <div class="detail-section">
                                <h5><i class="fas fa-info"></i> 基本信息</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">事件编号</div>
                                        <div class="value">${event.eventId}</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">上报单位</div>
                                        <div class="value">${
                                          event.reporterDeptName || '-'
                                        }</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">发生时间</div>
                                        <div class="value">${
                                            occurTime  || '-'
                                        }</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">事件状态</div>
                                        <div class="value status ${
                                          event.status
                                        }">${statusText[event.status] || '-' }</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5><i class="fas fa-map-marker-alt"></i> 位置信息</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">发生地点</div>
                                        <div class="value">${
                                          event.detailedAddress || '-'
                                        }</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">影响范围</div>
                                        <div class="value">${
                                          event.impactScope || '-'
                                        }</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5><i class="fas fa-users"></i> 伤亡情况</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">人员伤亡</div>
                                        <div class="value">${
                                          event.roadCasualtySituation || '-'
                                        }</div>
                                    </div>
                                 
                                </div>
                            </div>

                            <div class="detail-section">
                                <h5><i class="fas fa-clipboard-list"></i> 响应情况</h5>
                                <div class="detail-grid">
                                    <div class="detail-item">
                                        <div class="label">响应等级</div>
                                        <div class="value">${
                                          levelText[event.eventLevel]  || '-'
                                        }</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">启动预案</div>
                                        <div class="value">${
                                          event.activatedPlan  || '-'
                                        }</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">预计恢复时间</div>
                                        <div class="value">${
                                          estimatedRecoveryTime  || '-'
                                        }</div>
                                    </div>
                                    <div class="detail-item">
                                        <div class="label">已投入力量</div>
                                        <div class="value">${
                                          event.emergencyForces || '-'
                                        }</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <div class="detail-description">
                                    <div class="label">事件描述</div>
                                    <div class="value">${
                                      event.eventDescription  || '-'
                                    }</div>
                                </div>
                            </div>

                            <div class="detail-section">
                                <div class="detail-description">
                                    <div class="label">已采取措施</div>
                                    <div class="value">${
                                      event.emergencyMeasures  || '-'
                                    }</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button class="btn btn-secondary" onclick="CommandDispatch.closeModal()">
                            <i class="fas fa-times"></i> 关闭
                        </button>
                    </div>
                </div>
            </div>
        `;

    document.body.insertAdjacentHTML("beforeend", modalHTML);
  },

  enterCommand(eventId) {
    // 跳转到现场指挥标签页
    this.switchTab("field-command");
    this.showSuccessMessage(`已进入事件 ${eventId} 的现场指挥`);
  },

  viewEvaluation(eventId) {
    alert(`查看评估：${eventId}`);
  },

  viewSummary(eventId) {
    alert(`经验总结：${eventId}`);
  },

  consultExpert(eventId) {
    alert(`咨询专家：${eventId}`);
  },

  closeModal() {
    const modal = document.querySelector(".modal-overlay");
    if (modal) {
      modal.remove();
    }
  },

  // 初始化地址解析功能
  initAddressGeocode() {
    const eventLocationInput = document.getElementById("eventLocation");
    if (!eventLocationInput) {
      console.warn("找不到事故详细地址输入框");
      return;
    }

    // 输入时防抖处理
    eventLocationInput.addEventListener("input", (e) => {
      const address = e.target.value.trim();

      // 清除之前的定时器
      if (this.addressTimer) {
        clearTimeout(this.addressTimer);
      }

      // 如果地址为空，清空经纬度
      if (!address) {
        this.clearCoordinates();
        return;
      }

      // 设置防抖定时器，2秒后执行查询
      this.addressTimer = setTimeout(() => {
        this.geocodeAddress(address);
      }, 2000);
    });

    // 失焦时立即查询
    eventLocationInput.addEventListener("blur", (e) => {
      const address = e.target.value.trim();
      if (address) {
        // 清除防抖定时器
        if (this.addressTimer) {
          clearTimeout(this.addressTimer);
        }
        // 立即查询
        this.geocodeAddress(address);
      }
    });

    console.log("地址解析功能初始化完成");
  },

  // 地理编码查询
  async geocodeAddress(address) {
    try {
      console.log(`开始查询地址经纬度: ${address}`);

      // 显示加载状态
      this.showGeocodingStatus("正在查询经纬度...", "loading");

      // 调用地理编码接口
      const response = await window.Http.post(
        "/map/getLonAndLatByAddress?address=" + address
      );

      console.log("地理编码查询结果:", response);

      if (response && response.code === 200) {
        // 更新经纬度字段
        this.updateCoordinates(response.data.lng, response.data.lat);
        this.showGeocodingStatus("经纬度查询成功", "success");

        // 3秒后隐藏状态提示
        setTimeout(() => {
          this.hideGeocodingStatus();
        }, 3000);
      } else {
        console.warn("地理编码查询返回数据格式错误:", response);
        this.showGeocodingStatus("无法获取该地址的经纬度", "error");
      }
    } catch (error) {
      console.error("地理编码查询失败:", error);
      this.showGeocodingStatus("经纬度查询失败，请检查网络连接", "error");
    }
  },

  // 更新经纬度字段
  updateCoordinates(lng, lat) {
    const longitudeInput = document.getElementById("longitude");
    const latitudeInput = document.getElementById("latitude");

    if (longitudeInput) {
      longitudeInput.value = lng;
      // 添加视觉反馈
      this.highlightField(longitudeInput);
    }

    if (latitudeInput) {
      latitudeInput.value = lat;
      // 添加视觉反馈
      this.highlightField(latitudeInput);
    }

    console.log(`经纬度已更新: 经度=${lng}, 纬度=${lat}`);
  },

  // 清空经纬度
  clearCoordinates() {
    const longitudeInput = document.getElementById("longitude");
    const latitudeInput = document.getElementById("latitude");

    if (longitudeInput) {
      longitudeInput.value = "";
    }

    if (latitudeInput) {
      latitudeInput.value = "";
    }

    this.hideGeocodingStatus();
  },

  // 高亮字段
  highlightField(field) {
    field.style.backgroundColor = "#e8f5e8";
    field.style.borderColor = "#28a745";

    setTimeout(() => {
      field.style.backgroundColor = "";
      field.style.borderColor = "";
    }, 2000);
  },

  // 显示地理编码状态
  showGeocodingStatus(message, type) {
    let statusElement = document.getElementById("geocoding-status");

    if (!statusElement) {
      // 创建状态提示元素
      statusElement = document.createElement("div");
      statusElement.id = "geocoding-status";
      statusElement.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                padding: 8px 12px;
                font-size: 12px;
                border-radius: 0 0 4px 4px;
                z-index: 1000;
                transition: all 0.3s ease;
            `;

      // 将状态元素添加到地址输入框的父容器
      const eventLocationGroup = document
        .getElementById("eventLocation")
        .closest(".form-group");
      if (eventLocationGroup) {
        eventLocationGroup.style.position = "relative";
        eventLocationGroup.appendChild(statusElement);
      }
    }

    // 设置状态样式和文本
    statusElement.textContent = message;
    statusElement.className = `geocoding-status ${type}`;

    // 根据类型设置样式
    switch (type) {
      case "loading":
        statusElement.style.backgroundColor = "#cce5ff";
        statusElement.style.color = "#0056b3";
        statusElement.style.borderLeft = "3px solid #0056b3";
        break;
      case "success":
        statusElement.style.backgroundColor = "#d4edda";
        statusElement.style.color = "#155724";
        statusElement.style.borderLeft = "3px solid #28a745";
        break;
      case "error":
        statusElement.style.backgroundColor = "#f8d7da";
        statusElement.style.color = "#721c24";
        statusElement.style.borderLeft = "3px solid #dc3545";
        break;
    }

    statusElement.style.display = "block";
  },

  // 隐藏地理编码状态
  hideGeocodingStatus() {
    const statusElement = document.getElementById("geocoding-status");
    if (statusElement) {
      statusElement.style.display = "none";
    }
  },

  // 手动刷新经纬度
  refreshCoordinates() {
    const eventLocationInput = document.getElementById("eventLocation");
    const address = eventLocationInput.value.trim();

    if (!address) {
      this.showGeocodingStatus("请先输入详细地址", "error");
      return;
    }

    this.geocodeAddress(address);
  },

  // 切换事故专项字段显示
  toggleAccidentFields() {
    const eventType = document.getElementById("eventType").value;
    const trafficFields = document.getElementById("trafficAccidentFields");
    const constructionFields = document.getElementById(
      "constructionAccidentFields"
    );

    // 隐藏所有专项字段
    if (trafficFields) trafficFields.style.display = "none";
    if (constructionFields) constructionFields.style.display = "none";

    // 根据事件类型显示对应的专项字段
    if (eventType === "traffic-accident" && trafficFields) {
      trafficFields.style.display = "block";
      console.log("显示道路交通事故专项字段");
    } else if (eventType === "construction-accident" && constructionFields) {
      constructionFields.style.display = "block";
      console.log("显示工程建设事故专项字段");
    }
  },

  // 构建树形节点HTML
  buildTreeNodeHTML(node) {
    const hasChildren = node.children && node.children.length > 0;
    const nodeType = node.type || "dept";
    const isLeaf = node.isLeaf || false;
    const deptId = node.deptId || ""; // 获取部门ID

    let html = `
            <div class="tree-node ${nodeType}" data-id="${node.id}">
                <div class="tree-node-content" data-id="${node.id}" data-type="${nodeType}" data-isleaf="${isLeaf}" data-deptid="${deptId}">
        `;

    if (hasChildren) {
      html += `<span class="tree-node-expand"><i class="fas fa-chevron-right"></i></span>`;
    } else {
      html += `<span class="tree-node-expand"></span>`;
    }

    // 根据节点类型添加图标
    if (nodeType === "dept") {
      html += `<i class="fas fa-building tree-node-icon"></i>`;
    } else if (nodeType === "post") {
      html += `<i class="fas fa-briefcase tree-node-icon"></i>`;
    } else if (nodeType === "user") {
      html += `<i class="fas fa-user tree-node-icon"></i>`;
    } else if (nodeType === "marker") {
      html += `<i class="fas fa-map-marker-alt tree-node-icon"></i>`;
    } else if (nodeType === "road") {
      html += `<i class="fas fa-road tree-node-icon"></i>`;
    } else {
      html += `<i class="fas fa-folder tree-node-icon"></i>`;
    }

    html += `<span class="tree-node-text">${node.name}</span>`;
    html += `</div>`;

    if (hasChildren) {
      html += `<div class="tree-node-children">`;
      node.children.forEach((child) => {
        html += this.buildTreeNodeHTML(child);
      });
      html += `</div>`;
    }

    html += `</div>`;
    return html;
  },

  // 清除筛选条件
  clearFilters() {
    this.currentEventFilter = 'all';
    // 更新筛选按钮状态
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.remove('active');
      if (btn.dataset.filter === 'all') {
        btn.classList.add('active');
      }
    });
    // 重新加载数据
    this.loadReceivedEvents();
  },
};

// 添加全局点击事件来关闭下拉框
document.addEventListener('click', (e) => {
    if (!e.target.closest('.tree-select')) {
        if (window.CommandDispatch && typeof CommandDispatch.closeAllTreeSelects === 'function') {
            CommandDispatch.closeAllTreeSelects();
        }
    }
});

// 添加CSS动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(30px);
        }
    }
`;
document.head.appendChild(style);

// 全局函数，供HTML调用
function resetForm() {
    CommandDispatch.resetForm();
}

// 现场指挥协调功能
const FieldCommand = {
    // 初始化
    init() {
        this.initSubTabs();
        this.initContactSections();
        this.initRecording();
    },

    // 初始化子标签页
    initSubTabs() {
        const subTabs = document.querySelectorAll('.sub-tab[data-subtab]');
        subTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const targetTab = e.currentTarget.dataset.subtab;
                this.switchSubTab(targetTab);
            });
        });
    },

    // 切换子标签页
    switchSubTab(tabName) {
        // 更新标签页状态
        document.querySelectorAll('.sub-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-subtab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.sub-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
    },

    // 初始化联系人分组
    initContactSections() {
        // 默认展开第一个分组
        const firstSection = document.querySelector('.contact-list');
        if (firstSection) {
            firstSection.style.display = 'block';
        }
    },

    // 切换联系人分组
    toggleGroup(groupId) {
        const group = document.getElementById(groupId);
        const icon = event.currentTarget.querySelector('.toggle-icon');

        if (group.style.display === 'none' || !group.style.display) {
            group.style.display = 'block';
            icon.classList.add('rotated');
        } else {
            group.style.display = 'none';
            icon.classList.remove('rotated');
        }
    },

    // 视频会商功能
    startConference() {
        this.showSuccessMessage('正在启动视频会商...');
        // 模拟启动会商
        setTimeout(() => {
            this.updateConferenceStatus('active');
            this.showSuccessMessage('视频会商已启动！');
        }, 2000);
    },

    joinConference() {
        this.showSuccessMessage('正在加入会商...');
        // 模拟加入会商
        setTimeout(() => {
            this.updateParticipantStatus('online');
            this.showSuccessMessage('已成功加入会商！');
        }, 1500);
    },

    updateConferenceStatus(status) {
        const mainVideo = document.querySelector('.main-video .video-status');
        const videoPlaceholder = document.querySelector('.main-video .video-placeholder');

        if (status === 'active') {
            mainVideo.textContent = '在线';
            mainVideo.style.color = '#27ae60';
            videoPlaceholder.innerHTML = `
                <i class="fas fa-video"></i>
                <p>会商进行中...</p>
            `;
        }
    },

    updateParticipantStatus(status) {
        const participantCount = document.querySelector('.participants-list h4');
        if (status === 'online') {
            participantCount.textContent = '参会人员 (4/8)';

            // 更新参与者状态
            const participants = document.querySelectorAll('.participant-status');
            participants.forEach((status, index) => {
                if (index < 4) {
                    status.classList.remove('offline');
                    status.classList.add('online');
                }
            });

            // 更新视频网格中的状态
            const videoStatuses = document.querySelectorAll('.participant-info .status');
            videoStatuses.forEach((status, index) => {
                if (index < 4) {
                    status.textContent = '在线';
                    status.classList.remove('offline');
                    status.classList.add('online');
                }
            });
        }
    },

    // 会商工具功能
    toggleMute() {
        this.showSuccessMessage('麦克风状态已切换');
    },

    toggleVideo() {
        this.showSuccessMessage('视频状态已切换');
    },

    shareScreen() {
        this.showSuccessMessage('屏幕共享已启动');
    },

    // 通讯联系功能
    makeCall(phone, name) {
        this.showSuccessMessage(`正在呼叫 ${name} (${phone})...`, 'info');
        // 模拟呼叫过程
        setTimeout(() => {
            this.showSuccessMessage(`与 ${name} 通话已接通`);
        }, 2000);
    },

    // AI录音功能
    initRecording() {
        this.isRecording = false;
        this.recordingTime = 0;
        this.recordingTimer = null;
    },

    startRecording() {
        if (this.isRecording) return;

        this.isRecording = true;
        this.recordingTime = 0;

        // 更新按钮状态
        document.getElementById('recordBtn').disabled = true;
        document.getElementById('pauseBtn').disabled = false;
        document.getElementById('stopBtn').disabled = false;

        // 开始计时
        this.recordingTimer = setInterval(() => {
            this.recordingTime++;
            this.updateRecordingTime();
            this.simulateTranscription();
        }, 1000);

        this.showSuccessMessage('AI录音已开始');
    },

    pauseRecording() {
        if (!this.isRecording) return;

        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
            document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-play"></i> 继续';
            this.showSuccessMessage('录音已暂停');
        } else {
            this.recordingTimer = setInterval(() => {
                this.recordingTime++;
                this.updateRecordingTime();
                this.simulateTranscription();
            }, 1000);
            document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-pause"></i> 暂停';
            this.showSuccessMessage('录音已继续');
        }
    },

    stopRecording() {
        if (!this.isRecording) return;

        this.isRecording = false;

        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }

        // 更新按钮状态
        document.getElementById('recordBtn').disabled = false;
        document.getElementById('pauseBtn').disabled = true;
        document.getElementById('stopBtn').disabled = true;
        document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-pause"></i> 暂停';

        this.showSuccessMessage('录音已停止，正在生成完整纪要...');

        // 模拟生成完整纪要
        setTimeout(() => {
            this.generateFinalSummary();
        }, 2000);
    },

    updateRecordingTime() {
        const minutes = Math.floor(this.recordingTime / 60);
        const seconds = this.recordingTime % 60;
        const timeStr = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const timeDisplay = document.querySelector('.recording-time');
        if (timeDisplay) {
            timeDisplay.textContent = `00:${timeStr}`;
        }
    },

    simulateTranscription() {
        // 模拟实时转录
        if (this.recordingTime % 10 === 0) {
            this.addTranscriptionItem();
        }

        // 模拟音频电平
        this.updateAudioLevel();
    },

    addTranscriptionItem() {
        const speakers = ['张德国副厅长', '赵安全主任', '陈军组长', '王医生'];
        const contents = [
            '根据现场反馈，交通管制措施已经到位。',
            '医疗救护队伍准备就绪，随时待命。',
            '请各部门加强信息沟通，确保协调一致。',
            '伤员救治情况良好，无生命危险。'
        ];

        const speaker = speakers[Math.floor(Math.random() * speakers.length)];
        const content = contents[Math.floor(Math.random() * contents.length)];
        const time = new Date().toLocaleTimeString('zh-CN', { hour12: false, hour: '2-digit', minute: '2-digit' });

        const transcriptionDisplay = document.getElementById('transcriptionDisplay');
        const currentItem = transcriptionDisplay.querySelector('.transcription-item.current');

        if (currentItem) {
            currentItem.classList.remove('current');
        }

        const newItem = document.createElement('div');
        newItem.className = 'transcription-item';
        newItem.innerHTML = `
            <span class="speaker">${speaker}</span>
            <span class="time">${time}</span>
            <div class="content">${content}</div>
        `;

        transcriptionDisplay.appendChild(newItem);
        transcriptionDisplay.scrollTop = transcriptionDisplay.scrollHeight;

        // 更新AI纪要
        this.updateAISummary();
    },

    updateAudioLevel() {
        const levelBars = document.querySelectorAll('.level-bar');
        levelBars.forEach((bar, index) => {
            bar.classList.remove('active');
            if (Math.random() > 0.5 && index < 3) {
                bar.classList.add('active');
            }
        });
    },

    updateAISummary() {
        // 模拟AI实时更新纪要内容
        const summaryContent = document.getElementById('summaryContent');
        const participantCount = document.querySelectorAll('.transcription-item').length;

        // 更新参会人员信息
        const basicInfo = summaryContent.querySelector('.summary-section:first-child p:last-child');
        if (basicInfo) {
            basicInfo.innerHTML = `<strong>参会人员：</strong>张德国、王强、李队长、赵安全等 (${participantCount}人发言)`;
        }
    },

    generateFinalSummary() {
        const summaryContent = document.getElementById('summaryContent');
        summaryContent.innerHTML = `
            <div class="summary-section">
                <h5>📋 会议基本信息</h5>
                <p><strong>会议时间：</strong>2024-12-19 15:42-16:15 (33分钟)</p>
                <p><strong>主持人：</strong>李明华 (自治区交通运输厅厅长)</p>
                <p><strong>参会人员：</strong>张德国、王强、李队长、赵安全、陈军、王医生等</p>
            </div>

            <div class="summary-section">
                <h5>🚨 现场情况汇报</h5>
                <ul>
                    <li>事故涉及5辆车辆相撞，现场清障基本完成</li>
                    <li>3人轻伤，已送医治疗，生命体征稳定</li>
                    <li>交通管制措施到位，绕行方案执行顺利</li>
                    <li>消防救援力量协助清障，现场安全可控</li>
                </ul>
            </div>

            <div class="summary-section">
                <h5>🤝 部门协调分工</h5>
                <ul>
                    <li>应急指挥组：负责现场统一指挥，技术指导到位</li>
                    <li>消防救援：协助清障作业，确保现场安全</li>
                    <li>医疗救护：伤员救治及时，医疗保障充分</li>
                    <li>交警部门：交通管制有效，绕行组织有序</li>
                </ul>
            </div>

            <div class="summary-section">
                <h5>📝 主要决策事项</h5>
                <ul>
                    <li>启动Ⅱ级应急响应，成立现场指挥部</li>
                    <li>优先完成人员救援，确保生命安全</li>
                    <li>组织交通绕行，减少拥堵影响</li>
                    <li>加强信息发布，及时通报处置进展</li>
                </ul>
            </div>

            <div class="summary-section">
                <h5>⏰ 下次汇报安排</h5>
                <p><strong>汇报时间：</strong>16:30</p>
                <p><strong>汇报内容：</strong>各工作组汇报最新进展情况</p>
            </div>
        `;

        this.showSuccessMessage('AI智能纪要生成完成！');
    },

    // 纪要编辑功能
    editSummary() {
        this.showSuccessMessage('正在打开纪要编辑器...');
        // 这里可以打开一个富文本编辑器
    },

    exportSummary() {
        this.showSuccessMessage('会议纪要已导出到本地');
        // 这里可以实现导出功能
    },

    // 音频文件功能
    playAudio(fileId) {
        this.showSuccessMessage('正在播放录音文件...');
    },

    downloadAudio(fileId) {
        this.showSuccessMessage('录音文件下载已开始');
    },

    // 显示成功消息
    showSuccessMessage(message) {
        // 创建成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            ${message}
        `;
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d4edda;
            color: #155724;
            padding: 15px 20px;
            border-radius: 6px;
            border: 1px solid #c3e6cb;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
        `;

        document.body.appendChild(successDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    }
};

// ==================== 事件回溯分析模块 ====================
const EventReview = {
    // 查看事件详情
    viewEventDetail(eventId) {
        const eventData = this.getEventData(eventId);
        this.showEventDetailModal(eventData);
    },

    // 获取事件数据（模拟数据）
    getEventData(eventId) {
        // 模拟详细的事件数据
        const eventDataMap = {
            'event-001': {
                id: 'event-001',
                title: '泉南高速吴家屯隧道山体塌方事故',
                type: '交通事故',
                level: 'Ⅱ级',
                eventTime: '2023-04-20 10:03',
                reportTime: '2023-04-20 10:08',
                location: '泉南高速广西桂林至柳州段改扩建工程吴家屯隧道出口（柳州端）',
                reporter: '广西壮族自治区交通运输厅',
                casualties: {
                    total: 10,
                    dead: 0,
                    injured: 10,
                    missing: 0,
                    trapped: 10,
                    details: '10人受伤被困车内（轿车和客车各4人，厢式货车2人），均已救出送医治疗'
                },
                vehicleDamage: {
                    scrapped: 1,
                    major: 3,
                    minor: 0,
                    estimatedLoss: '约45万元',
                    details: '1辆槽罐车报废，1辆客车大修，1辆厢式货车大修，1辆小轿车大修'
                },
                roadImpact: {
                    closureTime: '6小时15分钟',
                    affectedVehicles: '约1200辆',
                    detourDistance: '25公里',
                    economicLoss: '约15万元',
                    details: '桂林至柳州方向高速公路交通中断，车辆需绕行省道，造成大面积拥堵'
                },
                totalLoss: '直接经济损失约68万元，间接损失约15万元，总计约83万元',
                description: '因多日连续强降雨导致山体塌方，槽罐车撞上塌方体后引发连环追尾，造成10人受伤被困，粗苯泄漏',
                cause: '多日连续强降雨导致隧道出口上方山体塌方，槽罐车避让不及撞上塌方体，后车连环追尾',
                impact: '桂林至柳州方向高速公路交通中断6.25小时，影响车辆约1200辆，造成拥堵15公里',
                weather: '连续强降雨，路面湿滑，能见度较差',
                economicLoss: 680000,
                processingTime: '6小时15分钟',
                responseLevel: 'Ⅱ级应急响应',
                status: '已完成',
                vehicles: '4辆车（1辆装载33t粗苯的槽罐车，1辆4.5t厢式货车，1辆载客30人客车，1辆小轿车）',
                roadCondition: '湿滑，有塌方体阻挡',
                trafficFlow: '中等，事故发生时车流密集',
                emergencyFacilities: '隧道内有应急电话、监控设备，但塌方影响部分设施',
                rescueConditions: '救援通道受阻，需清理塌方体后进入',
                timeline: [
                    { time: '10:03', event: '山体塌方发生，槽罐车撞上塌方体，连环追尾事故发生' },
                    { time: '10:08', event: '接到报警，启动Ⅱ级应急响应' },
                    { time: '10:15', event: '消防救援队伍到达现场' },
                    { time: '10:25', event: '医疗救护队伍到达，开始救治被困人员' },
                    { time: '10:45', event: '危化品处置专家到达，开始处置粗苯泄漏' },
                    { time: '11:30', event: '10名被困人员全部救出送医' },
                    { time: '13:15', event: '粗苯泄漏处置完毕，现场安全' },
                    { time: '14:30', event: '事故车辆清理完毕，开始清理塌方体' },
                    { time: '16:18', event: '道路恢复通行，应急响应结束' }
                ],
                keyDecisions: [
                    {
                        time: '10:08',
                        decision: '启动Ⅱ级应急响应',
                        decisionMaker: '李明华厅长',
                        basis: '山体塌方引发连环追尾，有人员被困，危化品泄漏',
                        execution: '立即启动，各部门快速响应',
                        effect: '有效'
                    },
                    {
                        time: '10:15',
                        decision: '优先救治被困人员',
                        decisionMaker: '现场指挥部',
                        basis: '保障人员生命安全是第一要务',
                        execution: '消防、医疗队伍同时到场救援',
                        effect: '有效'
                    },
                    {
                        time: '10:20',
                        decision: '启动危化品应急处置',
                        decisionMaker: '应急指挥中心',
                        basis: '槽罐车粗苯泄漏，存在环境污染和安全风险',
                        execution: '调派危化品处置专家和设备',
                        effect: '有效'
                    },
                    {
                        time: '10:30',
                        decision: '实施交通管制和疏导',
                        decisionMaker: '交警部门',
                        basis: '确保救援通道畅通，减少交通影响',
                        execution: '设置绕行路线，引导车辆分流',
                        effect: '有效'
                    }
                ],
                meetingRecords: [
                    {
                        id: 'meeting-001',
                        title: '山体塌方事故应急处置会议',
                        time: '10:20-11:10',
                        duration: '50分钟',
                        participants: '李明华厅长、张德国副厅长、王强组长、现场指挥员、危化品专家',
                        keyPoints: [
                            '确认山体塌方规模和连环追尾事故情况',
                            '调配救援力量、医疗资源和危化品处置设备',
                            '制定人员救援和危化品泄漏处置方案',
                            '协调消防、医疗、环保、交警等部门分工配合',
                            '建立现场安全防护区域，防止二次事故'
                        ]
                    }
                ],
                successExperiences: [
                    '应急响应速度快，5分钟内启动Ⅱ级响应',
                    '多部门协调配合良好，消防、医疗、环保、交警分工明确',
                    '危化品处置专业及时，有效控制了泄漏扩散',
                    '人员救援高效，10名被困人员全部安全救出',
                    '信息报送及时准确，决策科学合理',
                    '现场安全防护到位，未发生二次事故'
                ],
                existingProblems: [
                    '山体地质灾害监测预警不够及时',
                    '隧道出口地质灾害防护设施不完善',
                    '危化品运输车辆安全监管需要加强',
                    '恶劣天气条件下交通管制措施不够完善',
                    '应急物资储备点布局需要优化'
                ],
                improvements: [
                    '建立完善的地质灾害监测预警系统，加强雨季巡查',
                    '加强隧道出口等重点区域地质灾害防护工程建设',
                    '强化危化品运输车辆安全监管和应急处置能力',
                    '完善恶劣天气条件下的交通管制和疏导预案',
                    '优化应急物资储备布局，提高快速响应能力',
                    '加强多部门联合演练，提升协同作战能力'
                ]
            },
            'event-002': {
                id: 'event-002',
                title: '服务区餐厅厨房火灾',
                type: '火灾事故',
                level: 'Ⅲ级',
                eventTime: '2024-01-10 11:20',
                reportTime: '2024-01-10 11:25',
                location: '阳澄湖服务区',
                reporter: '服务区管理处',
                casualties: {
                    total: 0,
                    dead: 0,
                    injured: 0,
                    missing: 0,
                    trapped: 0,
                    details: '无人员伤亡'
                },
                vehicleDamage: {
                    scrapped: 0,
                    major: 0,
                    minor: 0,
                    estimatedLoss: '无',
                    details: '无车辆损失'
                },
                roadImpact: {
                    closureTime: '无',
                    affectedVehicles: '约50辆',
                    detourDistance: '无',
                    economicLoss: '约0.2万元',
                    details: '餐厅暂停营业，部分旅客需到其他服务区用餐'
                },
                totalLoss: '直接经济损失约3万元，间接损失约0.2万元，总计约3.2万元',
                description: '服务区餐厅厨房油烟机起火，火势较小，及时扑灭',
                cause: '厨房油烟机长期使用，油污积累过多，电路老化引发火灾',
                impact: '餐厅暂停营业1天，经济损失约3万元',
                weather: '晴天，无风',
                economicLoss: 30000,
                processingTime: '1小时15分钟',
                responseLevel: 'Ⅲ级应急响应',
                status: '已完成',
                timeline: [
                    { time: '11:20', event: '厨房起火，工作人员发现' },
                    { time: '11:22', event: '启动内部消防系统' },
                    { time: '11:25', event: '报告管理处，启动应急响应' },
                    { time: '11:30', event: '消防队到达现场' },
                    { time: '11:35', event: '火势扑灭，现场安全' },
                    { time: '12:35', event: '现场清理完毕，应急响应结束' }
                ],
                keyDecisions: [
                    {
                        time: '11:25',
                        decision: '启动Ⅲ级应急响应',
                        decisionMaker: '服务区主任',
                        basis: '火灾规模较小，但涉及公共场所',
                        execution: '立即启动内部应急程序',
                        effect: '有效'
                    },
                    {
                        time: '11:26',
                        decision: '疏散餐厅人员',
                        decisionMaker: '安全员',
                        basis: '确保人员安全',
                        execution: '有序疏散，无人员伤亡',
                        effect: '有效'
                    }
                ],
                meetingRecords: [],
                successExperiences: [
                    '内部消防系统发挥作用，及时控制火势',
                    '员工应急处置得当，疏散有序',
                    '与外部消防部门配合良好'
                ],
                existingProblems: [
                    '设备维护不及时，存在安全隐患',
                    '安全检查存在盲区',
                    '员工安全培训需要加强'
                ],
                improvements: [
                    '加强设备定期维护，建立完善的保养制度',
                    '完善安全检查制度，消除安全盲区',
                    '定期开展员工安全培训和应急演练',
                    '升级消防设备，提高自动化水平'
                ]
            }
        };

        return eventDataMap[eventId] || {
            id: eventId,
            title: '未知事件',
            type: '其他',
            level: 'Ⅳ级',
            status: '未知',
            casualties: { total: 0, dead: 0, injured: 0, missing: 0, trapped: 0, details: '无数据' },
            vehicleDamage: { scrapped: 0, major: 0, minor: 0, estimatedLoss: '无', details: '无数据' },
            roadImpact: { closureTime: '无', affectedVehicles: '无', detourDistance: '无', economicLoss: '无', details: '无数据' },
            totalLoss: '无数据',
            timeline: [],
            keyDecisions: [],
            meetingRecords: [],
            successExperiences: [],
            existingProblems: [],
            improvements: []
        };
    },

    // 显示事件详情模态框
    showEventDetailModal(eventData) {
        if (!eventData) return;

        const modalHTML = `
            <div class="modal-overlay" onclick="EventReview.closeModal()">
                <div class="modal-content event-detail-modal" onclick="event.stopPropagation()">
                    <div class="modal-header">
                        <h3><i class="fas fa-history"></i> ${eventData.title} - 回溯分析</h3>
                        <button class="close-button" onclick="EventReview.closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="event-detail-content">
                            ${this.generateBasicInfoSection(eventData)}
                            ${this.generateEventDetailsSection(eventData)}
                            ${this.generateResultsSection(eventData)}
                            ${this.generateTimelineSection(eventData)}
                            ${this.generateDecisionsSection(eventData)}
                            ${this.generateMeetingRecordsSection(eventData)}
                            ${this.generateLessonsSection(eventData)}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="EventReview.closeModal()">关闭</button>
                        <button class="btn btn-primary" onclick="EventReview.exportReport('${eventData.id}')">
                            <i class="fas fa-download"></i> 导出报告
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    },

    // 生成基本信息部分
    generateBasicInfoSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> 事件基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>事件名称：</label>
                        <span>${eventData.title}</span>
                    </div>
                    <div class="info-item">
                        <label>事件类型：</label>
                        <span>${eventData.type}</span>
                    </div>
                    <div class="info-item">
                        <label>发生时间：</label>
                        <span>${eventData.eventTime}</span>
                    </div>
                    <div class="info-item">
                        <label>接报时间：</label>
                        <span>${eventData.reportTime}</span>
                    </div>
                    <div class="info-item">
                        <label>处置时长：</label>
                        <span>${eventData.processingTime}</span>
                    </div>
                    <div class="info-item">
                        <label>响应等级：</label>
                        <span class="level-badge level-${eventData.level.replace('Ⅰ', '1').replace('Ⅱ', '2').replace('Ⅲ', '3').replace('Ⅳ', '4')}">${eventData.level}</span>
                    </div>
                    <div class="info-item full-width">
                        <label>事发地点：</label>
                        <span>${eventData.location}</span>
                    </div>
                    <div class="info-item full-width">
                        <label>最终结果：</label>
                        <span>应急处置成功，人员安全，道路恢复通行</span>
                    </div>
                </div>
            </div>
        `;
    },

    // 生成事件详情部分
    generateEventDetailsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-exclamation-triangle"></i> 事件详情</h4>

                <div class="result-subsection">
                    <h5><i class="fas fa-car-crash"></i> 事故概况</h5>
                    <div class="info-grid">
                        <div class="info-item full-width">
                            <label>事故原因：</label>
                            <span>${eventData.cause}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>天气情况：</label>
                            <span>${eventData.weather}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>涉及车辆：</label>
                            <span>${eventData.vehicles}</span>
                        </div>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-road"></i> 事发环境</h5>
                    <div class="info-grid">
                        <div class="info-item full-width">
                            <label>路面状况：</label>
                            <span>${eventData.roadCondition}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>交通流量：</label>
                            <span>${eventData.trafficFlow}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>应急设施：</label>
                            <span>${eventData.emergencyFacilities}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>救援条件：</label>
                            <span>${eventData.rescueConditions}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    // 生成处置结果部分
    generateResultsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-chart-bar"></i> 最终处置结果</h4>

                <div class="result-subsection">
                    <h5><i class="fas fa-user-injured"></i> 人员伤亡情况</h5>
                    <div class="casualties-grid">
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.dead}</span>
                            <span class="casualty-label">死亡</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.injured}</span>
                            <span class="casualty-label">受伤</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.missing}</span>
                            <span class="casualty-label">失踪</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.trapped}</span>
                            <span class="casualty-label">被困</span>
                        </div>
                        <div class="casualty-item">
                            <span class="casualty-number">${eventData.casualties.total}</span>
                            <span class="casualty-label">涉及总人数</span>
                        </div>
                    </div>
                    <div class="casualty-details">
                        <p><strong>详细情况：</strong>${eventData.casualties.details}</p>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-car-crash"></i> 车辆损失情况</h5>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>报废车辆：</label>
                            <span>${eventData.vehicleDamage.scrapped}辆</span>
                        </div>
                        <div class="info-item">
                            <label>大修车辆：</label>
                            <span>${eventData.vehicleDamage.major}辆</span>
                        </div>
                        <div class="info-item">
                            <label>轻微损坏：</label>
                            <span>${eventData.vehicleDamage.minor}辆</span>
                        </div>
                        <div class="info-item">
                            <label>预估损失：</label>
                            <span>${eventData.vehicleDamage.estimatedLoss}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>损失详情：</label>
                            <span>${eventData.vehicleDamage.details}</span>
                        </div>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-road"></i> 道路影响情况</h5>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>封闭时长：</label>
                            <span>${eventData.roadImpact.closureTime}</span>
                        </div>
                        <div class="info-item">
                            <label>影响车辆：</label>
                            <span>${eventData.roadImpact.affectedVehicles}</span>
                        </div>
                        <div class="info-item">
                            <label>绕行距离：</label>
                            <span>${eventData.roadImpact.detourDistance}</span>
                        </div>
                        <div class="info-item">
                            <label>经济损失：</label>
                            <span>${eventData.roadImpact.economicLoss}</span>
                        </div>
                        <div class="info-item full-width">
                            <label>影响详情：</label>
                            <span>${eventData.roadImpact.details}</span>
                        </div>
                    </div>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-calculator"></i> 总体损失评估</h5>
                    <div class="total-loss">
                        <p>${eventData.totalLoss}</p>
                    </div>
                </div>
            </div>
        `;
    },

    // 生成时间轴部分
    generateTimelineSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-clock"></i> 处置时间轴记录</h4>
                <div class="timeline">
                    ${eventData.timeline.map(item => `
                        <div class="timeline-item">
                            <div class="timeline-time">${item.time}</div>
                            <div class="timeline-content">${item.event}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    },

    // 生成关键决策部分
    generateDecisionsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-gavel"></i> 关键决策回顾</h4>
                ${eventData.keyDecisions.map(decision => `
                    <div class="result-subsection">
                        <h5><i class="fas fa-lightbulb"></i> ${decision.time} - ${decision.decision}</h5>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>决策人：</label>
                                <span>${decision.decisionMaker}</span>
                            </div>
                            <div class="info-item">
                                <label>决策依据：</label>
                                <span>${decision.basis}</span>
                            </div>
                            <div class="info-item">
                                <label>执行情况：</label>
                                <span>${decision.execution}</span>
                            </div>
                            <div class="info-item">
                                <label>效果评价：</label>
                                <span class="decision-effect">${decision.effect}</span>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // 生成会议记录部分
    generateMeetingRecordsSection(eventData) {
        if (!eventData.meetingRecords || eventData.meetingRecords.length === 0) {
            return `
                <div class="detail-section">
                    <h4><i class="fas fa-microphone"></i> AI会议记录回顾</h4>
                    <p class="no-data">本次事件处置过程中无正式会议记录</p>
                </div>
            `;
        }

        return `
            <div class="detail-section">
                <h4><i class="fas fa-microphone"></i> AI会议记录回顾</h4>
                ${eventData.meetingRecords.map(meeting => `
                    <div class="result-subsection">
                        <h5><i class="fas fa-users"></i> ${meeting.title}</h5>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>会议时间：</label>
                                <span>${meeting.time}</span>
                            </div>
                            <div class="info-item">
                                <label>会议时长：</label>
                                <span>${meeting.duration}</span>
                            </div>
                            <div class="info-item full-width">
                                <label>参会人员：</label>
                                <span>${meeting.participants}</span>
                            </div>
                        </div>
                        <div class="meeting-content">
                            <div class="meeting-points">
                                <strong>关键要点：</strong>
                                <ul>
                                    ${meeting.keyPoints.map(point => `<li>${point}</li>`).join('')}
                                </ul>
                            </div>
                            <div class="meeting-audio">
                                <button class="btn btn-sm btn-secondary" onclick="EventReview.playMeetingAudio('${meeting.id}')">
                                    <i class="fas fa-play"></i> 播放录音
                                </button>
                                <button class="btn btn-sm btn-secondary" onclick="EventReview.downloadMeetingAudio('${meeting.id}')">
                                    <i class="fas fa-download"></i> 下载录音
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    },

    // 生成经验教训部分
    generateLessonsSection(eventData) {
        return `
            <div class="detail-section">
                <h4><i class="fas fa-lightbulb"></i> 经验教训总结</h4>

                <div class="result-subsection">
                    <h5><i class="fas fa-check-circle"></i> 成功经验</h5>
                    <ul class="lessons-list">
                        ${eventData.successExperiences.map(experience => `<li>${experience}</li>`).join('')}
                    </ul>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-exclamation-triangle"></i> 存在问题</h5>
                    <ul class="problems-list">
                        ${eventData.existingProblems.map(problem => `<li>${problem}</li>`).join('')}
                    </ul>
                </div>

                <div class="result-subsection">
                    <h5><i class="fas fa-arrow-up"></i> 改进建议</h5>
                    <ul class="improvements-list">
                        ${eventData.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    },

    // 播放会议录音
    playMeetingAudio(meetingId) {
        CommandDispatch.showSuccessMessage('正在播放会议录音...');
    },

    // 下载会议录音
    downloadMeetingAudio(meetingId) {
        CommandDispatch.showSuccessMessage('会议录音下载已开始');
    },

    // 导出报告
    exportReport(eventId) {
        CommandDispatch.showSuccessMessage('事件回溯分析报告导出成功！');
    },

    // 关闭模态框
    closeModal() {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            modal.remove();
        }
    }
};

// 应急评估模块
const EmergencyEvaluation = {
    // 初始化
    init() {
        this.initEventSelect();
    },

    // 初始化事件选择
    initEventSelect() {
        const eventSelect = document.getElementById('evaluationEventSelect');
        if (eventSelect) {
            eventSelect.addEventListener('change', () => {
                this.loadEventEvaluation();
            });

            // 自动选择第一个事件并显示内容
            if (eventSelect.options.length > 1) {
                eventSelect.selectedIndex = 1; // 选择第一个实际事件（跳过"请选择"选项）
                this.loadEventEvaluation();
            }
        }
    },

    // 加载事件评估
    loadEventEvaluation() {
        const eventSelect = document.getElementById('evaluationEventSelect');
        const selectedEventId = eventSelect.value;

        if (selectedEventId) {
            // 显示评估内容
            const evaluationContent = document.querySelector('.evaluation-main-content');
            if (evaluationContent) {
                evaluationContent.style.display = 'flex';
            }

            // 隐藏空状态
            const noEvaluation = document.querySelector('.no-evaluation-selected');
            if (noEvaluation) {
                noEvaluation.style.display = 'none';
            }

            // 更新基本信息
            this.updateBasicInfo(selectedEventId);

            CommandDispatch.showSuccessMessage(`已加载事件评估：${eventSelect.options[eventSelect.selectedIndex].text}`);
        }
    },

    // 更新基本信息
    updateBasicInfo(eventId) {
        // 从EventReview模块获取事件数据
        const eventData = EventReview.getEventData(eventId);
        if (!eventData) return;

        // 更新基本信息网格中的内容
        const basicInfoGrid = document.querySelector('.basic-info-grid');
        if (basicInfoGrid) {
            basicInfoGrid.innerHTML = `
                <div class="info-item">
                    <label>事件名称</label>
                    <span>${eventData.title}</span>
                </div>
                <div class="info-item">
                    <label>评估时间</label>
                    <span>2023-04-20 18:30</span>
                </div>
                <div class="info-item">
                    <label>评估人员</label>
                    <span>张德国副厅长</span>
                </div>
                <div class="info-item">
                    <label>处置时长</label>
                    <span>${eventData.processingTime}</span>
                </div>
                <div class="info-item">
                    <label>响应等级</label>
                    <span>${eventData.responseLevel}</span>
                </div>
                <div class="info-item">
                    <label>最终结果</label>
                    <span>成功处置，10名被困人员全部救出，粗苯泄漏得到有效控制，道路恢复通行</span>
                </div>
            `;
        }
    },

    // 创建新评估
    createNewEvaluation() {
        CommandDispatch.showSuccessMessage('创建新评估功能开发中...');
    },

    // 保存草稿
    saveDraft() {
        CommandDispatch.showSuccessMessage('评估草稿已保存！');
    },

    // 提交评估
    submitEvaluation() {
        // 简单的表单验证
        const requiredFields = ['responseSpeed', 'processingEffect', 'coordination', 'resourceAllocation', 'mainProblems', 'improvements', 'overallScore'];
        let isValid = true;

        for (let field of requiredFields) {
            const element = document.getElementById(field);
            if (element && !element.value.trim()) {
                isValid = false;
                element.style.borderColor = '#dc3545';
                break;
            } else if (element) {
                element.style.borderColor = '#dee2e6';
            }
        }

        if (isValid) {
            CommandDispatch.showSuccessMessage('应急评估报告已提交！');
        } else {
            CommandDispatch.showErrorMessage('请填写所有必填字段！');
        }
    },

    // 生成AI建议
    generateSuggestions() {
        CommandDispatch.showSuccessMessage('AI正在分析评估内容，生成预案修改建议...');

        // 模拟AI生成过程
        setTimeout(() => {
            CommandDispatch.showSuccessMessage('AI预案修改建议已生成！');
        }, 2000);
    },

    // 编辑建议
    editSuggestion(suggestionId) {
        CommandDispatch.showSuccessMessage(`编辑建议 ${suggestionId}`);
    },

    // 采纳建议
    applySuggestion(suggestionId) {
        CommandDispatch.showSuccessMessage(`已采纳建议 ${suggestionId}`);
    },

    // 提交AI建议
    submitSuggestions() {
        CommandDispatch.showSuccessMessage('AI预案修改建议已提交至预案管理部门！');
    },

    // 编辑建议（新的统一编辑功能）
    editSuggestions() {
        const textarea = document.getElementById('suggestionText');
        if (textarea) {
            if (textarea.readOnly) {
                textarea.readOnly = false;
                textarea.style.background = 'white';
                textarea.style.borderColor = '#0056b3';
                CommandDispatch.showSuccessMessage('现在可以编辑预案修改建议了');
            } else {
                textarea.readOnly = true;
                textarea.style.background = '#f8f9fa';
                textarea.style.borderColor = '#e9ecef';
                CommandDispatch.showSuccessMessage('预案修改建议已保存');
            }
        }
    },

    // 采纳建议（新的统一采纳功能）
    adoptSuggestions() {
        const textarea = document.getElementById('suggestionText');
        if (textarea && textarea.value.trim()) {
            CommandDispatch.showSuccessMessage('预案修改建议已采纳并提交至预案管理部门！');
        } else {
            CommandDispatch.showErrorMessage('请先生成或编辑预案修改建议');
        }
    },

    // 预览报告
    previewReport() {
        CommandDispatch.showSuccessMessage('预览评估报告功能开发中...');
    },

    // 导出评估
    exportEvaluation() {
        CommandDispatch.showSuccessMessage('评估报告导出成功！');
    }
};

// ==================== 通话管理模块 ====================
const CallManager = {
    workbench: null,
    isInitialized: false,
    currentCall: null,
    callTimer: null,
    callStartTime: null,
    isMuted: false,
    isOnHold: false,
    isMinimized: false,

    // 初始化通话管理器
    init() {
        this.initSDK();
        this.bindEvents();
    },

    // 初始化阿里云SDK
    async initSDK() {
        let token=window.localStorage.getItem('token')
        console.log('token',token)  
        let Authorization ='Bearer ' + token;
        try {
            // 检查SDK是否加载
            if (typeof window.CCCWorkbenchSDK === 'undefined') {
                throw new Error('阿里云SDK未加载');
            }

            // 初始化SDK实例
            this.workbench = new window.CCCWorkbenchSDK({
                instanceId: 'cloud_call_test', // 需要替换为实际的实例ID
                exportErrorOfApi: true,
                afterCallRule: 15,
                ajaxOrigin:'http://10.20.104.215:8380',
                ajaxPath: '/tel/api', // 需要根据实际后端接口调整
                ajaxHeaders: {
                  'Authorization': Authorization
                },
                logger: {
                    builtinEnabled: false,
                },
                
                // SDK初始化完成
                onInit: async () => {
                    console.log('SDK初始化完成');
                    this.showMessage('通话系统初始化完成', 'success');
                    // 自动注册和登录
                    await this.registerAndLogin();
                },

                // 注册完成
                onRegister: (event) => {
                    console.log('注册完成', event);
                    this.isInitialized = true;
                    this.showMessage('通话系统注册成功', 'success');
                },

                // 登录完成
                onLogIn: (event) => {
                    console.log('登录完成', event);
                    this.showMessage('已成功上线', 'success');
                },

                // 准备就绪
                onReady: () => {
                    console.log('系统准备就绪');
                    this.showMessage('通话系统就绪', 'success');
                },

                // 来电事件
                onCallComing: (event) => {
                    console.log('来电', event);
                    this.handleIncomingCall(event);
                },

                // 拨号事件
                onCallDialing: (event) => {
                    console.log('拨号中', event);
                    this.handleDialing(event);
                },

                // 通话建立
                onCallEstablish: (event) => {
                    console.log('通话建立', event);
                    this.handleCallEstablished(event);
                },

                // 通话结束
                onCallRelease: (event) => {
                    console.log('通话结束', event);
                    this.handleCallEnded(event);
                },

                // 状态变化
                onStatusChange: (event) => {
                    console.log('状态变化', event);
                    this.handleStatusChange(event);
                },

                // 错误通知
                onErrorNotify: (event) => {
                    console.error('通话错误', event);
                    // this.showMessage(`通话错误: ${event.errorMsgTip || event.errorMsg}`, 'error');
                    if (event.errorCode == 7001) {
                        this.workbench.forceToPrepareSignIn()
                      }
                },

                // 挂断事件
                onHangUp: (event) => {
                    console.log('挂断', event);
                    this.handleCallEnded(event);
                }
            });


        } catch (error) {
            console.error('SDK初始化失败:', error);
            this.showMessage(`通话系统初始化失败: ${error.message}`, 'error');
        }
    },

    // 注册和登录
    async registerAndLogin() {
        try {
            // 获取技能组列表
            let skillGroups= await this.workbench.getSkillGroups();
            console.log('技能组列表:', skillGroups);
            // 注册
            await this.workbench.register({
                currentSkillGroups:[skillGroups.allSkillGroupList[0]]
            });

            // // 登录
            // await this.workbench.logIn({
            //     // mode: 'ready' // 登录后直接进入就绪状态
            // });

        } catch (error) {
            console.error('注册登录失败:', error);
            // this.showMessage(`注册登录失败: ${error.message}`, 'error');
        }
    },

    // 绑定事件
    bindEvents() {
        // 添加外呼按钮到联系人列表
        this.addCallButtonsToContacts();
    },

    // 为联系人添加通话按钮
    addCallButtonsToContacts() {
        const contacts = document.querySelectorAll('.contact-item');
        contacts.forEach(contact => {
            const phone = contact.querySelector('.contact-info p')?.textContent;
            if (phone && phone.includes('联系电话：')) {
                const phoneNumber = phone.replace('联系电话：', '').trim();
                
                // 添加通话按钮
                if (!contact.querySelector('.call-contact-btn')) {
                    const callButton = document.createElement('button');
                    callButton.className = 'call-contact-btn';
                    callButton.innerHTML = '<i class="fas fa-phone"></i>';
                    callButton.title = '拨打电话';
                    callButton.onclick = (e) => {
                        e.stopPropagation();
                        this.quickCall(phoneNumber);
                    };
                    contact.appendChild(callButton);
                }
            }
        });
    },

    // 快速拨打
    quickCall(phoneNumber) {
        if (!this.isInitialized) {
            this.showMessage('通话系统未初始化，请稍后再试', 'warning');
            return;
        }

        this.makeCallToNumber(phoneNumber);
    },

    // 显示外呼模态框
    showDialModal() {
        document.getElementById('dialModal').style.display = 'flex';
        document.getElementById('dialNumber').focus();
    },

    // 关闭外呼模态框
    closeDialModal() {
        document.getElementById('dialModal').style.display = 'none';
        document.getElementById('dialNumber').value = '';
    },

    // 添加数字到拨号
    addDigit(digit) {
        const input = document.getElementById('dialNumber');
        input.value += digit;
        input.focus();
    },

    // 清除号码
    clearNumber() {
        const input = document.getElementById('dialNumber');
        if (input.value.length > 0) {
            input.value = input.value.slice(0, -1);
        }
        input.focus();
    },

    // 拨打电话
    makeCall() {
        const phoneNumber = document.getElementById('dialNumber').value.trim();
        if (!phoneNumber) {
            this.showMessage('请输入电话号码', 'warning');
            return;
        }

        this.closeDialModal();
        this.makeCallToNumber(phoneNumber);
    },

    // 实际拨打电话
    async makeCallToNumber(phoneNumber) {
        if (!this.workbench) {
            this.showMessage('通话系统未初始化', 'error');
            return;
        }

        try {
            this.showMessage(`正在拨打 ${phoneNumber}...`, 'info');
            
            await this.workbench.call({
                callee: phoneNumber,
                // caller: 'your-caller-number' // 如果需要指定主叫号码
            });

        } catch (error) {
            console.error('拨打电话失败:', error);
            this.showMessage(`拨打失败: ${error.message}`, 'error');
        }
    },

    // 处理来电
    handleIncomingCall(event) {
        this.currentCall = {
            type: 'incoming',
            number: event.caller || event.customerNumber || '未知号码',
            startTime: new Date(),
            callId: event.callId
        };

        document.getElementById('incomingNumber').textContent = this.currentCall.number;
        document.getElementById('incomingCallModal').style.display = 'flex';
        
        this.showMessage(`来电: ${this.currentCall.number}`, 'info');
    },

    // 接听电话
    async answerCall() {
        if (!this.workbench) return;

        try {
            await this.workbench.answer();
            document.getElementById('incomingCallModal').style.display = 'none';
            this.showMessage('已接听电话', 'success');
        } catch (error) {
            console.error('接听失败:', error);
            this.showMessage(`接听失败: ${error.message}`, 'error');
        }
    },

    // 拒接电话
    async rejectCall() {
        if (!this.workbench) return;

        try {
            await this.workbench.hangUp();
            document.getElementById('incomingCallModal').style.display = 'none';
            this.currentCall = null;
            this.showMessage('已拒接电话', 'info');
        } catch (error) {
            console.error('拒接失败:', error);
            this.showMessage(`拒接失败: ${error.message}`, 'error');
        }
    },

    // 处理拨号中
    handleDialing(event) {
        if (!this.currentCall) {
            this.currentCall = {
                type: 'outgoing',
                number: event.callee || event.customerNumber || '未知号码',
                startTime: new Date(),
                callId: event.callId
            };
        }

        this.showCallWindow();
        this.updateCallStatus('拨号中');
        this.updateCallNumber(this.currentCall.number);
    },

    // 处理通话建立
    handleCallEstablished(event) {
        if (!this.currentCall) {
            this.currentCall = {
                type: event.caller ? 'incoming' : 'outgoing',
                number: event.caller || event.callee || event.customerNumber || '未知号码',
                startTime: new Date(),
                callId: event.callId
            };
        }

        this.showCallWindow();
        this.updateCallStatus('通话中');
        this.updateCallNumber(this.currentCall.number);
        this.startCallTimer();
        
        this.showMessage('通话已建立', 'success');
    },

    // 处理通话结束
    handleCallEnded(event) {
        this.hideCallWindow();
        this.stopCallTimer();
        
        if (this.currentCall) {
            const duration = Math.floor((new Date() - this.currentCall.startTime) / 1000);
            this.showMessage(`通话结束，时长: ${this.formatTime(duration)}`, 'info');
        }
        
        this.currentCall = null;
        this.isMuted = false;
        this.isOnHold = false;
    },

    // 处理状态变化
    handleStatusChange(event) {
        console.log('坐席状态:', event.agentStatus, '用户状态:', event.userStatus);
        
        // 根据状态更新UI
        switch (event.userStatus) {
            case 6: // 振铃
                this.updateCallStatus('振铃中');
                break;
            case 8: // 拨号
                this.updateCallStatus('拨号中');
                break;
            case 9: // 呼入通话
            case 10: // 呼出通话
                this.updateCallStatus('通话中');
                break;
            case 12: // 通话保持
                this.updateCallStatus('通话保持');
                break;
        }
    },

    // 显示通话窗口
    showCallWindow() {
        document.getElementById('callWindow').style.display = 'block';
        document.getElementById('callWindowMinimized').style.display = 'none';
        this.isMinimized = false;
    },

    // 隐藏通话窗口
    hideCallWindow() {
        document.getElementById('callWindow').style.display = 'none';
        document.getElementById('callWindowMinimized').style.display = 'none';
        this.isMinimized = false;
    },

    // 最小化通话窗口
    minimizeCallWindow() {
        document.getElementById('callWindow').style.display = 'none';
        document.getElementById('callWindowMinimized').style.display = 'flex';
        this.isMinimized = true;
    },

    // 恢复通话窗口
    restoreCallWindow() {
        document.getElementById('callWindow').style.display = 'block';
        document.getElementById('callWindowMinimized').style.display = 'none';
        this.isMinimized = false;
    },

    // 更新通话状态
    updateCallStatus(status) {
        document.getElementById('callStatus').textContent = status;
    },

    // 更新通话号码
    updateCallNumber(number) {
        document.getElementById('callNumber').textContent = number;
    },

    // 开始通话计时
    startCallTimer() {
        this.callStartTime = new Date();
        this.callTimer = setInterval(() => {
            const elapsed = Math.floor((new Date() - this.callStartTime) / 1000);
            const timeStr = this.formatTime(elapsed);
            document.getElementById('callTimer').textContent = timeStr;
            if (this.isMinimized) {
                document.getElementById('callTimerMini').textContent = timeStr;
            }
        }, 1000);
    },

    // 停止通话计时
    stopCallTimer() {
        if (this.callTimer) {
            clearInterval(this.callTimer);
            this.callTimer = null;
        }
        document.getElementById('callTimer').textContent = '00:00:00';
        document.getElementById('callTimerMini').textContent = '00:00:00';
    },

    // 格式化时间
    formatTime(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    },

    // 挂断电话
    async hangUp() {
        if (!this.workbench || !this.currentCall) return;

        try {
            await this.workbench.hangUp();
            this.showMessage('已挂断电话', 'info');
        } catch (error) {
            console.error('挂断失败:', error);
            this.showMessage(`挂断失败: ${error.message}`, 'error');
        }
    },

    // 切换静音
    async toggleMute() {
        if (!this.workbench || !this.currentCall) return;

        try {
            if (this.isMuted) {
                await this.workbench.unmuteAgent();
                document.getElementById('muteIcon').className = 'fas fa-microphone';
                this.showMessage('已取消静音', 'info');
            } else {
                await this.workbench.muteAgent();
                document.getElementById('muteIcon').className = 'fas fa-microphone-slash';
                this.showMessage('已静音', 'info');
            }
            this.isMuted = !this.isMuted;
        } catch (error) {
            console.error('静音操作失败:', error);
            this.showMessage(`静音操作失败: ${error.message}`, 'error');
        }
    },

    // 切换保持
    async toggleHold() {
        if (!this.workbench || !this.currentCall) return;

        try {
            if (this.isOnHold) {
                await this.workbench.callRetrieve();
                document.getElementById('holdIcon').className = 'fas fa-pause';
                this.updateCallStatus('通话中');
                this.showMessage('已取消保持', 'info');
            } else {
                await this.workbench.callHold();
                document.getElementById('holdIcon').className = 'fas fa-play';
                this.updateCallStatus('通话保持');
                this.showMessage('通话已保持', 'info');
            }
            this.isOnHold = !this.isOnHold;
        } catch (error) {
            console.error('保持操作失败:', error);
            this.showMessage(`保持操作失败: ${error.message}`, 'error');
        }
    },

    // 显示消息
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `call-message call-message-${type}`;
        
        let icon = 'fas fa-info-circle';
        switch (type) {
            case 'success': icon = 'fas fa-check-circle'; break;
            case 'error': icon = 'fas fa-exclamation-circle'; break;
            case 'warning': icon = 'fas fa-exclamation-triangle'; break;
        }
        
        messageDiv.innerHTML = `
            <i class="${icon}"></i>
            ${message}
        `;
        
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-weight: 500;
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            background: ${type === 'success' ? '#27ae60' : 
                       type === 'error' ? '#e74c3c' : 
                       type === 'warning' ? '#f39c12' : '#3498db'};
        `;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 4000);
    }
};

// 在现有的 FieldCommand 对象中添加通话功能
FieldCommand.makeCall = function(phone, name) {
    if (name) {
        CallManager.showMessage(`正在呼叫 ${name} (${phone})...`, 'info');
    }
    CallManager.makeCallToNumber(phone);
};

// 在现有的 CommandDispatch 对象中添加外呼功能
CommandDispatch.showDialPad = function() {
    CallManager.showDialModal();
};
