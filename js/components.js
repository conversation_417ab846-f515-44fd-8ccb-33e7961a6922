/**
 * 组件加载与管理脚本
 */
document.addEventListener('DOMContentLoaded', function() {
    // 初始化侧边栏状态
    initSidebar();
    
    // 设置活动菜单项
    const pathname = window.location.pathname;
    const pageName = pathname.split('/').pop();
    setActiveMenuItem(pageName);
    
    // 设置侧边栏切换功能
    setupSidebarToggle();
});

/**
 * 设置活动菜单项
 * @param {string} pageName - 当前页面名称
 */
function setActiveMenuItem(pageName) {
    // 移除所有菜单项的活动状态
    const menuItems = document.querySelectorAll('.sidebar-menu-item');
    menuItems.forEach(item => {
        item.classList.remove('active');
        const icon = item.querySelector('i');
        const text = item.querySelector('span');
        
        if (icon) icon.classList.remove('text-blue-600');
        if (text) {
            text.classList.remove('text-blue-600', 'font-medium');
            text.classList.add('text-gray-700');
        }
    });
    
    // 设置匹配当前页面的菜单项为活动状态
    let activeMenuItem = null;
    
    // 根据页面名称确定要激活的菜单项
    if (pageName.includes('dashboard')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="dashboard"]');
    } else if (pageName.includes('plan_list') || pageName.includes('plan_detail') || pageName.includes('plan_version_history')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="plan"]');
    } else if (pageName.includes('edit_plan')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="edit-plan"]');
    } else if (pageName.includes('plan_approval')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="plan-approval"]');
    } else if (pageName.includes('plan_statistics')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="plan-statistics"]');
    } else if (pageName.includes('material_list')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="material"]');
    } else if (pageName.includes('resource_alert')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="resource-alert"]');
    } else if (pageName.includes('warehouse_list')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="warehouse"]');
    } else if (pageName.includes('vehicle_list')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="vehicle"]');
    } else if (pageName.includes('org_structure')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="org_structure"]');
    } else if (pageName.includes('personnel_list')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="personnel_list"]');
    } else if (pageName.includes('expert_list')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="expert_list"]');
    } else if (pageName.includes('third_party_list')) {
        activeMenuItem = document.querySelector('.sidebar-menu-item[data-page="third_party_list"]');
    }
    
    // 设置活动菜单项
    if (activeMenuItem) {
        activeMenuItem.classList.add('active');
        const icon = activeMenuItem.querySelector('i');
        const text = activeMenuItem.querySelector('span');
        
        if (icon) icon.classList.add('text-blue-600');
        if (text) {
            text.classList.remove('text-gray-700');
            text.classList.add('text-blue-600', 'font-medium');
        }
    }
}

/**
 * 设置侧边栏切换功能
 */
function setupSidebarToggle() {
    const toggleButton = document.querySelector('.sidebar-toggle');
    if (toggleButton) {
        toggleButton.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('collapsed');
            
            // 调整main-content的margin
            const mainContent = document.querySelector('.main-content');
            if (sidebar.classList.contains('collapsed')) {
                mainContent.style.marginLeft = '70px';
            } else {
                mainContent.style.marginLeft = '250px';
            }
            
            // 保存侧边栏状态到 localStorage
            const sidebarCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', sidebarCollapsed);
        });
    }
}

/**
 * 初始化侧边栏状态
 */
function initSidebar() {
    // 从localStorage获取侧边栏状态
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    // 根据保存的状态设置侧边栏
    if (sidebarCollapsed && sidebar) {
        sidebar.classList.add('collapsed');
        if (mainContent) {
            mainContent.style.marginLeft = '70px';
        }
    } else if (sidebar) {
        sidebar.classList.remove('collapsed');
        if (mainContent) {
            mainContent.style.marginLeft = '250px';
        }
    }
} 