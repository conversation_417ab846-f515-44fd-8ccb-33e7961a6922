const sidebarHTML = `
    <!-- 风险隐患管理 -->
    <div class="sidebar-menu-group">
        <div class="sidebar-menu-header block px-4 py-2 cursor-pointer" onclick="toggleSubmenu(event, this)">
            <i class="fas fa-shield-alt text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700 font-medium">风险隐患管理</span>
            <i class="fas fa-chevron-down text-gray-500 ml-auto"></i>
        </div>
        
        <div class="submenu pl-6">
            <a href="my_check_tasks.html" class="sidebar-menu-item block">
                <i class="fas fa-shield-virus text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">检查任务</span>
            </a>

            <a href="construction_projects.html" class="sidebar-menu-item block">
                <i class="fas fa-building text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">在建项目</span>
            </a>
            
            <a href="hazard_rectification.html" class="sidebar-menu-item block">
                <i class="fas fa-shield-alt text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">隐患与整改</span>
            </a>

            <a href="hazard_approval.html" class="sidebar-menu-item block">
                <i class="fas fa-clipboard-check text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">检查审批</span>
            </a>
            
            <a href="check_template_list.html" class="sidebar-menu-item block">
                <i class="fas fa-list-alt text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">检查下发与模板管理</span>
            </a>
        </div>
    </div>

    <!-- 应急处置 -->
    <div class="sidebar-menu-group mt-4">
        <div class="sidebar-menu-header block px-4 py-2 cursor-pointer" onclick="toggleSubmenu(event, this)">
            <i class="fas fa-ambulance text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700 font-medium">应急处置</span>
            <i class="fas fa-chevron-down text-gray-500 ml-auto"></i>
        </div>
        
        <div class="submenu pl-6">
            <a href="plan_list.html" class="sidebar-menu-item block">
                <i class="fas fa-book text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">预案库</span>
            </a>

            <a href="emergency_resources.html" class="sidebar-menu-item block">
                <i class="fas fa-warehouse text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">应急物资</span>
            </a>

            <a href="emergency_contacts.html" class="sidebar-menu-item block">
                <i class="fas fa-users text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">应急通讯录</span>
            </a>

            <a href="org_structure.html" class="sidebar-menu-item block">
                <i class="fas fa-sitemap text-gray-600 w-6"></i>
                <span class="ml-3 text-gray-700">组织机构</span>
            </a>
        </div>
    </div>
`;

// 添加展开/收起子菜单的函数
function toggleSubmenu(event, header) {
    event.stopPropagation();
    
    const submenu = header.nextElementSibling;
    const chevron = header.querySelector('.fa-chevron-down');
    
    submenu.classList.toggle('collapsed');
    chevron.classList.toggle('collapsed');
}

// 在页面加载时初始化菜单状态
document.addEventListener('DOMContentLoaded', function() {
    // 高亮当前页面的菜单项
    const currentPage = window.location.pathname.split('/').pop();
    if (!currentPage) return;

    const submenu = document.querySelector('.submenu');
    if (submenu) {
        const links = submenu.querySelectorAll('a');
        links.forEach(link => {
            if (link.getAttribute('href') === currentPage) {
                link.classList.add('active');
            }
        });
    }
});
