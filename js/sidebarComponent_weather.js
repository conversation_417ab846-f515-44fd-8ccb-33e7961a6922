const sidebarHTML = `<!-- 左侧菜单-气象预警 -->
<!-- Note: 'sidebar' class and fixed positioning might need adjustment in main CSS -->
<aside class="sidebar w-64 flex-shrink-0 bg-white shadow-md overflow-y-auto h-full">
    <nav class="py-4">
        
        <a href="weather_alerts.html" class="sidebar-menu-item block active">
            <i class="fas fa-cloud-sun-rain text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">气象预警</span>
        </a>
        
    </nav>
</aside>`;
