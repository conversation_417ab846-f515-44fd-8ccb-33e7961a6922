/**
 * 动态内容加载器
 * 负责加载、解析和渲染页面内容
 */

class ContentLoader {
    static currentContent = null;
    static loadedScripts = new Set();
    static loadedStyles = new Set();
    static vueApps = new Map(); // 存储Vue应用实例

    /**
     * 加载内容
     * @param {string} contentUrl - 内容URL
     * @param {Array} breadcrumb - 面包屑路径
     * @returns {Promise<void>}
     */
    static async loadContent(contentUrl, breadcrumb = []) {
        try {
            // 显示加载指示器
            this.showLoading();

            // 获取内容 - 支持本地文件系统
            let htmlContent;
            if (window.location.protocol === 'file:') {
                // 本地文件系统，使用预加载的内容
                htmlContent = await this.loadLocalContent(contentUrl);
            } else {
                // HTTP/HTTPS，使用fetch
                const response = await fetch(contentUrl);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                htmlContent = await response.text();
            }
            
            // 解析内容
            const parsedContent = this.parseContent(htmlContent);
            
            // 清理当前内容
            this.cleanupCurrentContent();
            
            // 加载新内容
            await this.renderContent(parsedContent);
            
            // 更新面包屑
            this.updateBreadcrumb(breadcrumb);
            
            // 隐藏加载指示器
            this.hideLoading();
            
            this.currentContent = {
                url: contentUrl,
                content: parsedContent
            };
            
        } catch (error) {
            console.error('Content loading failed:', error);
            this.showError('内容加载失败，请稍后重试。', () => {
                this.loadContent(contentUrl, breadcrumb);
            });
            this.hideLoading();
        }
    }

    /**
     * 加载本地内容（用于file://协议）
     * @param {string} contentUrl - 内容URL
     * @returns {Promise<string>} HTML内容
     */
    static async loadLocalContent(contentUrl) {
        // 从预定义的内容映射中获取内容
        const contentMap = window.LOCAL_CONTENT_MAP || {};

        // 提取文件名作为key
        const fileName = contentUrl.split('/').pop().replace('.html', '');

        if (contentMap[fileName]) {
            return contentMap[fileName];
        }

        // 如果没有找到预定义内容，返回默认内容
        return this.getDefaultContent(fileName);
    }

    /**
     * 获取默认内容
     * @param {string} fileName - 文件名
     * @returns {string} 默认HTML内容
     */
    static getDefaultContent(fileName) {
        return `
            <div class="py-6">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-exclamation-triangle text-yellow-500 text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">内容加载中...</h3>
                    <p class="text-gray-500">正在加载 ${fileName} 页面内容</p>
                    <div class="mt-4">
                        <button onclick="location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
                            刷新页面
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 解析HTML内容
     * @param {string} htmlString - HTML字符串
     * @returns {Object} 解析后的内容对象
     */
    static parseContent(htmlString) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(htmlString, 'text/html');
        
        return {
            html: doc.body.innerHTML,
            styles: Array.from(doc.querySelectorAll('style')).map(style => style.textContent),
            scripts: Array.from(doc.querySelectorAll('script')).map(script => script.textContent),
            title: doc.title || ''
        };
    }

    /**
     * 渲染内容
     * @param {Object} parsedContent - 解析后的内容
     * @returns {Promise<void>}
     */
    static async renderContent(parsedContent) {
        const container = document.getElementById('dynamic-content');
        
        // 插入HTML内容
        container.innerHTML = parsedContent.html;
        
        // 加载样式
        for (const styleContent of parsedContent.styles) {
            if (!this.loadedStyles.has(styleContent)) {
                this.injectStyle(styleContent);
                this.loadedStyles.add(styleContent);
            }
        }
        
        // 执行脚本
        for (const scriptContent of parsedContent.scripts) {
            if (scriptContent.trim()) {
                this.executeScript(scriptContent);
            }
        }
        
        // 初始化Vue组件
        await this.initializeVueComponents();
    }

    /**
     * 注入样式
     * @param {string} styleContent - 样式内容
     */
    static injectStyle(styleContent) {
        const styleElement = document.createElement('style');
        styleElement.textContent = styleContent;
        styleElement.setAttribute('data-dynamic-style', 'true');
        document.head.appendChild(styleElement);
    }

    /**
     * 执行脚本
     * @param {string} scriptContent - 脚本内容
     */
    static executeScript(scriptContent) {
        try {
            // 创建新的脚本上下文
            const scriptFunction = new Function(scriptContent);
            scriptFunction.call(window);
        } catch (error) {
            console.error('Script execution failed:', error);
        }
    }

    /**
     * 初始化Vue组件
     * @returns {Promise<void>}
     */
    static async initializeVueComponents() {
        // 等待Vue和Element Plus加载完成
        if (typeof Vue === 'undefined' || typeof ElementPlus === 'undefined') {
            console.warn('Vue or Element Plus not loaded');
            return;
        }

        // 查找Vue应用容器
        const vueContainers = document.querySelectorAll('[id$="-app"], [id$="App"], [id*="app"]');
        
        for (const container of vueContainers) {
            if (!container._vueApp && container.id) {
                try {
                    // 创建基础Vue应用
                    const app = Vue.createApp({
                        data() {
                            return {
                                // 基础数据
                            };
                        },
                        mounted() {
                            console.log(`Vue app mounted: ${container.id}`);
                        }
                    });
                    
                    // 使用Element Plus
                    app.use(ElementPlus);
                    
                    // 挂载应用
                    app.mount(`#${container.id}`);
                    
                    // 存储应用实例
                    this.vueApps.set(container.id, app);
                    container._vueApp = app;
                    
                } catch (error) {
                    console.error(`Failed to initialize Vue app for ${container.id}:`, error);
                }
            }
        }
    }

    /**
     * 清理当前内容
     */
    static cleanupCurrentContent() {
        // 销毁Vue应用实例
        this.vueApps.forEach((app, containerId) => {
            try {
                app.unmount();
            } catch (error) {
                console.warn(`Failed to unmount Vue app ${containerId}:`, error);
            }
        });
        this.vueApps.clear();

        // 清理动态样式
        document.querySelectorAll('style[data-dynamic-style]').forEach(style => {
            style.remove();
        });
        this.loadedStyles.clear();
    }

    /**
     * 更新面包屑导航
     * @param {Array} breadcrumb - 面包屑路径
     */
    static updateBreadcrumb(breadcrumb) {
        const breadcrumbElement = document.getElementById('breadcrumb-path');
        if (breadcrumbElement && breadcrumb.length > 0) {
            breadcrumbElement.textContent = breadcrumb.join(' > ');
        }
    }

    /**
     * 显示加载指示器
     */
    static showLoading() {
        const loadingElement = document.getElementById('loading-indicator');
        if (loadingElement) {
            loadingElement.classList.remove('hidden');
        }
    }

    /**
     * 隐藏加载指示器
     */
    static hideLoading() {
        const loadingElement = document.getElementById('loading-indicator');
        if (loadingElement) {
            loadingElement.classList.add('hidden');
        }
    }

    /**
     * 显示错误信息
     * @param {string} message - 错误信息
     * @param {Function} retryCallback - 重试回调函数
     */
    static showError(message, retryCallback = null) {
        const errorModal = document.getElementById('error-modal');
        const errorMessage = document.getElementById('error-message');
        const retryButton = document.getElementById('error-retry');
        const closeButton = document.getElementById('error-close');

        if (errorModal && errorMessage) {
            errorMessage.textContent = message;
            errorModal.classList.remove('hidden');

            // 绑定重试按钮
            if (retryButton && retryCallback) {
                retryButton.onclick = () => {
                    errorModal.classList.add('hidden');
                    retryCallback();
                };
            }

            // 绑定关闭按钮
            if (closeButton) {
                closeButton.onclick = () => {
                    errorModal.classList.add('hidden');
                };
            }
        }
    }

    /**
     * 加载系统概览页面
     */
    static loadSystemOverview() {
        const container = document.getElementById('dynamic-content');
        const overviewElement = document.getElementById('system-overview');
        
        if (container && overviewElement) {
            container.innerHTML = overviewElement.outerHTML;
            this.updateBreadcrumb(['系统管理', '概览']);
        }
    }
}

// 导出到全局
window.ContentLoader = ContentLoader;
