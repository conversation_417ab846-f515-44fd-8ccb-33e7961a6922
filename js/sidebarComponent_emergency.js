const sidebarHTML = `<!-- 左侧菜单-应急处置 -->
<!-- Note: 'sidebar' class and fixed positioning might need adjustment in main CSS -->
<aside class="sidebar w-64 flex-shrink-0 bg-white shadow-md overflow-y-auto h-full">
    <nav class="py-4">
        
        <a href="plan_list.html" class="sidebar-menu-item block">
            <i class="fas fa-book text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">预案库</span>
        </a>

        <a href="emergency_resources.html" class="sidebar-menu-item block">
            <i class="fas fa-warehouse text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">应急物资</span>
        </a>

        <a href="emergency_contacts.html" class="sidebar-menu-item block">
            <i class="fas fa-users text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">应急通讯录</span>
        </a>

        <a href="org_structure.html" class="sidebar-menu-item block">
            <i class="fas fa-sitemap text-gray-600 w-6"></i>
            <span class="ml-3 text-gray-700">组织机构</span>
        </a>

        <!-- System settings removed for brevity, can be added back if needed -->
        <!-- <div class="px-4 mt-6 mb-2"> ... </div> -->
        
    </nav>
</aside>`;
