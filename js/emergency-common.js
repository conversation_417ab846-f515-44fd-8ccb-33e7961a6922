/* 应急系统公共JavaScript文件 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化导航高亮
    initializeNavigation();
    
    // 初始化公共事件监听
    initializeCommonEvents();
    
    console.log('应急系统公共模块已加载');
});

/**
 * 初始化导航栏高亮
 */
function initializeNavigation() {
    // 根据当前页面URL高亮对应的导航按钮
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    const currentButton = document.querySelector(`[data-page="${currentPage}"]`);
    
    if (currentButton) {
        // 移除所有active类
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // 添加当前页面的active类
        currentButton.classList.add('active');
        console.log(`导航高亮已设置: ${currentPage}`);
    }
}

/**
 * 开发中提示弹框函数
 */
function showDevelopmentModal(tabName) {
    // 创建弹框元素（如果不存在）
    let modal = document.getElementById('development-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'development-modal';
        modal.className = 'development-modal';

        const modalContent = `
            <div class="development-modal-content">
                <div class="development-modal-icon">
                    <i class="fas fa-code"></i>
                </div>
                <div class="development-modal-title">功能开发中</div>
                <div class="development-modal-message">
                    <span id="development-modal-tab-name"></span>功能正在开发中，敬请期待！
                </div>
                <button class="development-modal-button" onclick="closeDevelopmentModal()">
                    我知道了
                </button>
            </div>
        `;

        modal.innerHTML = modalContent;
        document.body.appendChild(modal);
        
        // 添加样式
        addDevelopmentModalStyles();
    }

    // 设置标签页名称
    document.getElementById('development-modal-tab-name').textContent = tabName;

    // 显示弹框
    modal.style.display = 'flex';
}

/**
 * 关闭开发中提示弹框
 */
function closeDevelopmentModal() {
    const modal = document.getElementById('development-modal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * 添加开发中提示弹框样式
 */
function addDevelopmentModalStyles() {
    if (document.getElementById('development-modal-styles')) {
        return; // 样式已存在
    }
    
    const style = document.createElement('style');
    style.id = 'development-modal-styles';
    style.textContent = `
        .development-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .development-modal-content {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            width: 90%;
            text-align: center;
            position: relative;
        }

        .development-modal-icon {
            font-size: 48px;
            color: #007bff;
            margin-bottom: 20px;
        }

        .development-modal-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .development-modal-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .development-modal-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .development-modal-button:hover {
            background-color: #0056b3;
        }
    `;
    document.head.appendChild(style);
}

/**
 * 初始化公共事件监听
 */
function initializeCommonEvents() {
    // 模态框关闭事件
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('close-button')) {
            const modal = e.target.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const visibleModals = document.querySelectorAll('.modal[style*="display: flex"], .modal[style*="display: block"]');
            visibleModals.forEach(modal => {
                modal.style.display = 'none';
            });
            
            // 关闭开发中提示弹框
            closeDevelopmentModal();
        }
    });
}

/**
 * 显示模态框
 * @param {string} modalId - 模态框ID
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        // 添加动画效果
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.style.opacity = '1';
        }, 10);
    }
}

/**
 * 隐藏模态框
 * @param {string} modalId - 模态框ID
 */
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.opacity = '0';
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300);
    }
}

/**
 * 切换标签页内容
 * @param {Element} button - 点击的标签按钮
 * @param {string} tabType - 标签类型
 */
function switchTab(button, tabType) {
    // 移除所有按钮的active类
    const parentContainer = button.closest('.filter-tabs, .resource-type-tabs, .alert-tabs');
    if (parentContainer) {
        const siblingButtons = parentContainer.querySelectorAll('.filter-tab-button, .resource-tab-button, .alert-tab-button');
        siblingButtons.forEach(btn => btn.classList.remove('active'));
    }

    // 添加当前按钮的active类
    button.classList.add('active');

    // 获取目标内容ID
    const targetTabContentId = `${tabType}-content`;

    // 获取相关的内容元素
    const parentSection = button.closest('.resource-filter-container, .alert-list-container');
    if (parentSection) {
        const contentElements = parentSection.querySelectorAll('.filter-tab-content, .resource-tab-content, .alert-tab-content');

        contentElements.forEach(content => {
            if (content.id === targetTabContentId) {
                content.classList.add('active');
            } else {
                content.classList.remove('active');
            }
        });
    }
}

/**
 * 切换筛选标签页
 * @param {Element} button - 点击的按钮
 * @param {string} tabType - 标签类型
 */
function switchFilterTab(button, tabType) {
    switchTab(button, `filter-by-${tabType}`);
}

/**
 * 切换资源标签页
 * @param {Element} button - 点击的按钮
 * @param {string} tabType - 标签类型
 */
function switchResourceTab(button, tabType) {
    switchTab(button, tabType);
    
    // 获取标点类型
    const markerType = button.getAttribute('data-marker-type');
    
    // 根据标签卡类型显示/隐藏标点
    if (markerType) {
        const allMarkers = document.querySelectorAll('.map-marker');
        allMarkers.forEach(marker => {
            const type = marker.getAttribute('data-type');
            if (type === markerType) {
                marker.style.display = ''; // 显示
            } else {
                marker.style.display = 'none'; // 隐藏
            }
        });
    }
}

/**
 * 切换告警标签页
 * @param {Element} button - 点击的按钮
 * @param {string} tabType - 标签类型
 */
function switchAlertTab(button, tabType) {
    switchTab(button, `alert-${tabType}`);
}

/**
 * 树形结构展开/收起功能
 */
function initializeTreeToggle() {
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('tree-toggler')) {
            const li = e.target.parentElement;
            const ul = li.querySelector('ul');
            
            if (ul) {
                if (li.classList.contains('open')) {
                    li.classList.remove('open');
                    e.target.textContent = '+';
                    ul.style.display = 'none';
                } else {
                    li.classList.add('open');
                    e.target.textContent = '-';
                    ul.style.display = 'block';
                }
            }
        }
    });
}

/**
 * 全选/全不选功能
 * @param {Element} checkbox - 全选复选框
 * @param {string} targetSelector - 目标复选框选择器
 */
function toggleAllCheckboxes(checkbox, targetSelector) {
    const isChecked = checkbox.checked;
    const targetCheckboxes = document.querySelectorAll(targetSelector);
    
    targetCheckboxes.forEach(cb => {
        cb.checked = isChecked;
    });
}

/**
 * 显示/隐藏地图标点
 * @param {string} markerSelector - 标点选择器
 * @param {boolean} show - 是否显示
 */
function toggleMapMarkers(markerSelector, show) {
    const markers = document.querySelectorAll(markerSelector);
    markers.forEach(marker => {
        marker.style.display = show ? '' : 'none';
        marker.style.visibility = show ? 'visible' : 'hidden';
        marker.style.opacity = show ? '1' : '0';
    });
}

/**
 * 格式化日期时间
 * @param {Date|string} date - 日期对象或字符串
 * @param {string} format - 格式化模式
 * @returns {string} 格式化后的日期字符串
 */
function formatDateTime(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 显示加载状态
 * @param {string} message - 加载消息
 */
function showLoading(message = '加载中...') {
    let loadingEl = document.getElementById('loading-overlay');
    if (!loadingEl) {
        loadingEl = document.createElement('div');
        loadingEl.id = 'loading-overlay';
        loadingEl.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-message">${message}</div>
            </div>
        `;
        document.body.appendChild(loadingEl);
        
        // 添加加载样式
        addLoadingStyles();
    }
    
    loadingEl.querySelector('.loading-message').textContent = message;
    loadingEl.style.display = 'flex';
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
    const loadingEl = document.getElementById('loading-overlay');
    if (loadingEl) {
        loadingEl.style.display = 'none';
    }
}

/**
 * 添加加载样式
 */
function addLoadingStyles() {
    if (document.getElementById('loading-styles')) {
        return; // 样式已存在
    }
    
    const style = document.createElement('style');
    style.id = 'loading-styles';
    style.textContent = `
        #loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #0056b3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        .loading-message {
            color: #333;
            font-size: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
}

/**
 * 显示提示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, warning, info)
 * @param {number} duration - 显示时长（毫秒）
 */
function showMessage(message, type = 'info', duration = 3000) {
    const messageEl = document.createElement('div');
    messageEl.className = `message-toast message-${type}`;
    messageEl.textContent = message;
    
    // 添加到页面
    document.body.appendChild(messageEl);
    
    // 添加样式（如果不存在）
    addMessageStyles();
    
    // 显示动画
    setTimeout(() => {
        messageEl.classList.add('show');
    }, 10);
    
    // 自动隐藏
    setTimeout(() => {
        messageEl.classList.remove('show');
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, duration);
}

/**
 * 添加消息提示样式
 */
function addMessageStyles() {
    if (document.getElementById('message-styles')) {
        return; // 样式已存在
    }
    
    const style = document.createElement('style');
    style.id = 'message-styles';
    style.textContent = `
        .message-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        }
        
        .message-toast.show {
            transform: translateX(0);
        }
        
        .message-success {
            background-color: #28a745;
        }
        
        .message-error {
            background-color: #dc3545;
        }
        
        .message-warning {
            background-color: #ffc107;
            color: #333;
        }
        
        .message-info {
            background-color: #17a2b8;
        }
    `;
    document.head.appendChild(style);
}

// 初始化树形结构功能
document.addEventListener('DOMContentLoaded', function() {
    initializeTreeToggle();
});

// 导出公共函数（如果使用模块化）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showDevelopmentModal,
        closeDevelopmentModal,
        showModal,
        hideModal,
        switchTab,
        switchFilterTab,
        switchResourceTab,
        switchAlertTab,
        toggleAllCheckboxes,
        toggleMapMarkers,
        formatDateTime,
        debounce,
        throttle,
        showLoading,
        hideLoading,
        showMessage
    };
}

/**
 * 页面导航函数
 * @param {string} url - 目标页面URL
 */
function navigateToPage(url) {
    console.log(`导航到页面: ${url}`);
    window.location.href = url;
} 