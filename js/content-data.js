/**
 * 页面内容数据
 * 包含所有页面的实际内容，避免CORS问题
 */

// 我的检查任务内容
const MY_CHECK_TASKS_CONTENT = `
<!-- 检查任务页面内容 -->
<div class="py-6">
    <!-- 页面标题部分 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">我的检查任务</h2>
            <p class="text-gray-600 mt-1">查看分配给您的检查任务并进行填报</p>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Example Task 1: Pending -->
                    <tr class="hover:bg-gray-50 task-row" data-task-id="mytask1">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">2024年第三季度风险路段专项检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">风险路段</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自治区公路发展中心</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-09-30 17:00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">待处理</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-report-hazard" title="填报风险隐患" data-task-id="mytask1" data-task-name="2024年第三季度风险路段专项检查">
                                <i class="fas fa-edit mr-1"></i>填报
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-complete-task" title="直接完成任务（无隐患）">
                                <i class="fas fa-check-circle mr-1"></i>直接完结
                            </button>
                        </td>
                    </tr>
                    <!-- Example Task 2: Completed -->
                    <tr class="hover:bg-gray-50 task-row" data-task-id="mytask2">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">汛期前桥梁安全检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自治区交通运输厅</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-31 17:00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完结</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="text-gray-400">-</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 检查任务页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 任务列表按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const row = button.closest('tr.task-row');
        const taskId = row ? row.dataset.taskId : null;
        const taskName = row ? row.querySelector('.task-name')?.textContent.trim() : '未知任务';

        if (button.classList.contains('btn-report-hazard')) {
            if (taskId) {
                alert('开始填报任务: ' + taskName + ' (ID: ' + taskId + ') - 模拟');
            }
        } else if (button.classList.contains('btn-complete-task')) {
            if (taskId) {
                if (confirm('确认直接完结任务 "' + taskName + '" 吗？')) {
                    alert('任务 "' + taskName + '" 已直接完结 - 模拟');
                    
                    // 更新UI状态
                    const statusCell = row.querySelector('.task-status');
                    const actionCell = button.closest('td');
                    if (statusCell) {
                        statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完结</span>';
                    }
                    if (actionCell) {
                        actionCell.innerHTML = '<span class="text-gray-400">-</span>';
                    }
                }
            }
        }
    });
});
</script>
`;

// 在建项目内容
const CONSTRUCTION_PROJECTS_CONTENT = `
<!-- 在建项目管理页面内容 -->
<div class="py-6">
    <!-- 页面标题部分 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">在建项目管理</h2>
            <p class="text-gray-600 mt-1">管理和查看在建项目的信息</p>
        </div>
        <div>
            <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 btn-add-project">
                <i class="fas fa-plus mr-2"></i>添加项目
            </button>
        </div>
    </div>

    <!-- 项目列表 -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">施工单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目负责人</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开工日期</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预计完工日期</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Example Project Row 1 -->
                    <tr class="hover:bg-gray-50 project-row" data-project-id="proj001">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">G324国道扩建工程A标段</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广西路桥建设集团有限公司</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王明</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-15</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-09-30</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">在建</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-project" title="查看项目详情">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                            <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2 btn-edit-project" title="编辑项目">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-project" title="删除项目">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// 在建项目管理页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 添加项目按钮
    document.querySelector('.btn-add-project')?.addEventListener('click', () => {
        alert('添加项目功能 - 模拟');
    });

    // 项目列表按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const row = button.closest('tr.project-row');
        const projectId = row ? row.dataset.projectId : null;

        if (button.classList.contains('btn-view-project')) {
            if (projectId) {
                alert('查看项目详情 (ID: ' + projectId + ') - 模拟');
            }
        } else if (button.classList.contains('btn-edit-project')) {
            if (projectId) {
                alert('编辑项目 (ID: ' + projectId + ') - 模拟');
            }
        } else if (button.classList.contains('btn-delete-project')) {
            if (projectId) {
                if (confirm('确认删除项目 (ID: ' + projectId + ') 吗？')) {
                    alert('项目 (ID: ' + projectId + ') 已删除 - 模拟');
                    row.remove();
                }
            }
        }
    });
});
</script>
`;

// 隐患列表内容
const HAZARD_LIST_CONTENT = `
<!-- 隐患列表页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">隐患列表</h2>
        <p class="text-sm text-gray-500 mt-1">查看和管理风险隐患检查记录</p>
    </div>

    <!-- 过滤栏 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入市名称">
            </div>
            <div>
                <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入区/县名称">
            </div>
            <div>
                <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                <select id="filterOrgUnit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择单位</option>
                    <option value="1.1.1">自治区公路发展中心</option>
                    <option value="1.1.2">自治区高速公路发展中心</option>
                    <option value="1.2.1">钦州市交通运输局</option>
                    <option value="1.2.2">南宁市交通运输局</option>
                    <option value="1.2.3">玉林市交通运输局</option>
                </select>
            </div>
            <div>
                <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                <select id="filterCategory" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择检查类别</option>
                    <option value="risk_section_flood">山洪淹没区风险路段</option>
                    <option value="risk_section_geology">地质灾害风险路段</option>
                    <option value="management_mechanism">工作管理机制隐患</option>
                    <option value="basic_facilities_sign">防洪标识</option>
                    <option value="basic_facilities_trail">检查步道</option>
                    <option value="basic_facilities_hazard">涉灾隐患点</option>
                    <option value="hazard_points_slope">边坡</option>
                    <option value="hazard_points_drainage">防洪排水设施</option>
                    <option value="hazard_points_bridge">桥梁</option>
                    <option value="hazard_points_tunnel">隧道</option>
                </select>
            </div>
            <div>
                <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">全部</option>
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                    <option value="none">无风险/已整改</option>
                </select>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
            <div>
                <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
            </div>
            <div class="col-start-4 md:col-start-5 flex items-end space-x-2 justify-end">
                <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-search mr-1"></i> 查询
                </button>
                <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                  <i class="fas fa-undo mr-1"></i> 重置
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody id="hazardListTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- 示例数据行 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">青秀区</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段-地质灾害风险</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">G324</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">高</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200处边坡有落石风险</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="1" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none mr-2 btn-delete" data-id="1" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-rectify" data-id="1" title="生成整改任务">
                                <i class="fas fa-clipboard-list"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">灵山县</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患-防洪标识</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">S211</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">低</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="2" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="2" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none mr-2 btn-delete" data-id="2" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-rectify" data-id="2" title="生成整改任务">
                                <i class="fas fa-clipboard-list"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- 分页 -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 隐患列表页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 筛选功能
    document.getElementById('btnFilter')?.addEventListener('click', () => {
        const filters = {
            city: document.getElementById('filterCity').value,
            county: document.getElementById('filterCounty').value,
            orgUnit: document.getElementById('filterOrgUnit').value,
            category: document.getElementById('filterCategory').value,
            riskLevel: document.getElementById('filterRiskLevel').value,
            roadNumber: document.getElementById('filterRoadNumber').value
        };

        console.log('应用筛选条件:', filters);
        alert('筛选功能 (模拟)');
    });

    // 重置筛选
    document.getElementById('btnResetFilter')?.addEventListener('click', () => {
        document.getElementById('filterCity').value = '';
        document.getElementById('filterCounty').value = '';
        document.getElementById('filterOrgUnit').value = '';
        document.getElementById('filterCategory').value = '';
        document.getElementById('filterRiskLevel').value = '';
        document.getElementById('filterRoadNumber').value = '';

        console.log('重置筛选条件');
        alert('筛选条件已重置 (模拟)');
    });

    // 表格操作按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const id = button.dataset.id;
        if (!id) return;

        if (button.classList.contains('btn-view')) {
            console.log('查看隐患详情 ID:', id);
            alert('查看隐患详情 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-edit')) {
            console.log('编辑隐患 ID:', id);
            alert('编辑隐患 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-delete')) {
            if (confirm('确认删除隐患记录 (ID: ' + id + ') 吗？')) {
                console.log('删除隐患 ID:', id);
                alert('隐患记录 (ID: ' + id + ') 已删除 - 模拟');
                button.closest('tr').remove();
            }
        } else if (button.classList.contains('btn-rectify')) {
            console.log('生成整改任务 ID:', id);
            alert('为隐患 (ID: ' + id + ') 生成整改任务 - 模拟');
        }
    });
});
</script>
`;

// 整改任务列表内容
const RECTIFICATION_TASKS_CONTENT = `
<!-- 整改任务列表页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">整改任务列表</h2>
            <p class="text-sm text-gray-500 mt-1">跟踪和管理隐患整改任务的进度</p>
        </div>
        <button id="btnAddRectificationTask" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
            <i class="fas fa-plus mr-2"></i> 添加任务
        </button>
    </div>

    <!-- 过滤栏 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="filterTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态</label>
                <select id="filterTaskStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">全部</option>
                    <option value="pending">待处理</option>
                    <option value="progress">整改中</option>
                    <option value="completed">已完成</option>
                    <option value="overdue">已逾期</option>
                </select>
            </div>
            <div>
                <label for="filterRectificationRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位</label>
                <select id="filterRectificationRespUnit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择责任单位</option>
                    <option value="1.1.1">自治区公路发展中心</option>
                    <option value="1.1.2">自治区高速公路发展中心</option>
                    <option value="1.2.1">钦州市交通运输局</option>
                    <option value="1.2.2">南宁市交通运输局</option>
                    <option value="1.2.3">玉林市交通运输局</option>
                </select>
            </div>
            <div>
                <label for="filterRectificationDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限</label>
                <input type="date" id="filterRectificationDeadline" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            <div class="flex items-end space-x-2 justify-end">
                <button id="btnRectificationFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-search mr-1"></i> 查询
                </button>
                <button id="btnRectificationResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                  <i class="fas fa-undo mr-1"></i> 重置
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联隐患</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任人</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">整改期限</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody id="rectificationListTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- 示例数据行 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK001</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="1" title="ID:1, K1500+200处边坡有落石风险">隐患ID:1</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李工</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-28</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-10</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">待处理</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK001" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK001" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK001" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK001" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK002</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="2" title="ID:2, 桥梁限高标识不清">隐患ID:2</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王工</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-05</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">整改中</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK002" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK002" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK002" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK002" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK003</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="3" title="ID:3, XX桥梁伸缩缝堵塞">隐患ID:3</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张工</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-20</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已逾期</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK003" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK003" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK003" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK003" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- 分页 -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 整改任务列表页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 添加任务按钮
    document.getElementById('btnAddRectificationTask')?.addEventListener('click', () => {
        console.log('添加整改任务');
        alert('添加整改任务 - 模拟');
    });

    // 筛选功能
    document.getElementById('btnRectificationFilter')?.addEventListener('click', () => {
        const filters = {
            status: document.getElementById('filterTaskStatus').value,
            respUnit: document.getElementById('filterRectificationRespUnit').value,
            deadline: document.getElementById('filterRectificationDeadline').value
        };

        console.log('应用整改任务筛选条件:', filters);
        alert('整改任务筛选功能 (模拟)');
    });

    // 重置筛选
    document.getElementById('btnRectificationResetFilter')?.addEventListener('click', () => {
        document.getElementById('filterTaskStatus').value = '';
        document.getElementById('filterRectificationRespUnit').value = '';
        document.getElementById('filterRectificationDeadline').value = '';

        console.log('重置整改任务筛选条件');
        alert('整改任务筛选条件已重置 (模拟)');
    });

    // 表格操作按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const id = button.dataset.id;
        const hazardId = button.dataset.hazardId;

        if (button.classList.contains('btn-view-associated-hazard')) {
            console.log('查看关联隐患 ID:', hazardId);
            alert('查看关联隐患 (ID: ' + hazardId + ') - 模拟');
        } else if (button.classList.contains('btn-view-rectification')) {
            console.log('查看整改任务 ID:', id);
            alert('查看整改任务 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-edit-rectification')) {
            console.log('编辑整改任务 ID:', id);
            alert('编辑整改任务 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-complete-rectification')) {
            if (confirm('确认标记整改任务 (ID: ' + id + ') 为已完成吗？')) {
                console.log('完成整改任务 ID:', id);
                alert('整改任务 (ID: ' + id + ') 已标记为完成 - 模拟');

                // 更新状态显示
                const statusCell = button.closest('tr').querySelector('td:nth-child(7)');
                if (statusCell) {
                    statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span>';
                }
            }
        } else if (button.classList.contains('btn-delete-rectification')) {
            if (confirm('确认删除整改任务 (ID: ' + id + ') 吗？')) {
                console.log('删除整改任务 ID:', id);
                alert('整改任务 (ID: ' + id + ') 已删除 - 模拟');
                button.closest('tr').remove();
            }
        }
    });
});
</script>
`;

// 检查下发内容
const CHECK_DISPATCH_CONTENT = `
<!-- 检查下发页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">检查下发</h2>
            <p class="text-sm text-gray-500 mt-1">创建和管理检查任务的下发</p>
        </div>
        <button id="btnCreateCheckTask" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <i class="fas fa-plus mr-2"></i> 创建检查任务
        </button>
    </div>

    <!-- 过滤栏 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="filterDispatchStatus" class="block text-sm font-medium text-gray-700 mb-1">下发状态</label>
                <select id="filterDispatchStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">全部</option>
                    <option value="draft">草稿</option>
                    <option value="dispatched">已下发</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                </select>
            </div>
            <div>
                <label for="filterTargetUnit" class="block text-sm font-medium text-gray-700 mb-1">目标单位</label>
                <select id="filterTargetUnit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择目标单位</option>
                    <option value="1.1.1">自治区公路发展中心</option>
                    <option value="1.1.2">自治区高速公路发展中心</option>
                    <option value="1.2.1">钦州市交通运输局</option>
                    <option value="1.2.2">南宁市交通运输局</option>
                    <option value="1.2.3">玉林市交通运输局</option>
                </select>
            </div>
            <div>
                <label for="filterCheckCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                <select id="filterCheckCategory" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择检查类别</option>
                    <option value="risk_section_flood">山洪淹没区风险路段</option>
                    <option value="risk_section_geology">地质灾害风险路段</option>
                    <option value="management_mechanism">工作管理机制隐患</option>
                    <option value="basic_facilities">基础保障设施隐患</option>
                    <option value="hazard_points">涉灾隐患点</option>
                </select>
            </div>
            <div class="flex items-end space-x-2 justify-end">
                <button id="btnDispatchFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-search mr-1"></i> 查询
                </button>
                <button id="btnDispatchResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                  <i class="fas fa-undo mr-1"></i> 重置
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody id="dispatchListTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- 示例数据行 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CHK001</td>
                        <td class="px-6 py-4 text-sm text-gray-900 font-medium">2024年第三季度风险路段专项检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-15</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-09-30</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已下发</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-dispatch" data-id="CHK001" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-dispatch" data-id="CHK001" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-orange-600 hover:text-orange-800 focus:outline-none mr-2 btn-resend-dispatch" data-id="CHK001" title="重新下发">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-cancel-dispatch" data-id="CHK001" title="取消">
                                <i class="fas fa-times-circle"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CHK002</td>
                        <td class="px-6 py-4 text-sm text-gray-900 font-medium">汛期前桥梁安全检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">涉灾隐患点-桥梁</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-04-20</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-31</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已完成</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-dispatch" data-id="CHK002" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-gray-400 cursor-not-allowed mr-2" title="已完成，不可编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-gray-400 cursor-not-allowed mr-2" title="已完成，不可重发">
                                <i class="fas fa-redo"></i>
                            </button>
                            <button class="text-gray-400 cursor-not-allowed" title="已完成，不可取消">
                                <i class="fas fa-times-circle"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CHK003</td>
                        <td class="px-6 py-4 text-sm text-gray-900 font-medium">基础设施隐患排查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-01</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-31</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">草稿</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-dispatch" data-id="CHK003" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-dispatch" data-id="CHK003" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-send-dispatch" data-id="CHK003" title="下发">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-dispatch" data-id="CHK003" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- 分页 -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 检查下发页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 创建检查任务按钮
    document.getElementById('btnCreateCheckTask')?.addEventListener('click', () => {
        console.log('创建检查任务');
        alert('创建检查任务 - 模拟');
    });

    // 筛选功能
    document.getElementById('btnDispatchFilter')?.addEventListener('click', () => {
        const filters = {
            status: document.getElementById('filterDispatchStatus').value,
            targetUnit: document.getElementById('filterTargetUnit').value,
            category: document.getElementById('filterCheckCategory').value
        };

        console.log('应用检查下发筛选条件:', filters);
        alert('检查下发筛选功能 (模拟)');
    });

    // 重置筛选
    document.getElementById('btnDispatchResetFilter')?.addEventListener('click', () => {
        document.getElementById('filterDispatchStatus').value = '';
        document.getElementById('filterTargetUnit').value = '';
        document.getElementById('filterCheckCategory').value = '';

        console.log('重置检查下发筛选条件');
        alert('检查下发筛选条件已重置 (模拟)');
    });

    // 表格操作按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const id = button.dataset.id;
        if (!id) return;

        if (button.classList.contains('btn-view-dispatch')) {
            console.log('查看检查任务 ID:', id);
            alert('查看检查任务 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-edit-dispatch')) {
            console.log('编辑检查任务 ID:', id);
            alert('编辑检查任务 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-send-dispatch')) {
            if (confirm('确认下发检查任务 (ID: ' + id + ') 吗？')) {
                console.log('下发检查任务 ID:', id);
                alert('检查任务 (ID: ' + id + ') 已下发 - 模拟');

                // 更新状态显示
                const statusCell = button.closest('tr').querySelector('td:nth-child(7)');
                if (statusCell) {
                    statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已下发</span>';
                }
            }
        } else if (button.classList.contains('btn-resend-dispatch')) {
            if (confirm('确认重新下发检查任务 (ID: ' + id + ') 吗？')) {
                console.log('重新下发检查任务 ID:', id);
                alert('检查任务 (ID: ' + id + ') 已重新下发 - 模拟');
            }
        } else if (button.classList.contains('btn-cancel-dispatch')) {
            if (confirm('确认取消检查任务 (ID: ' + id + ') 吗？')) {
                console.log('取消检查任务 ID:', id);
                alert('检查任务 (ID: ' + id + ') 已取消 - 模拟');

                // 更新状态显示
                const statusCell = button.closest('tr').querySelector('td:nth-child(7)');
                if (statusCell) {
                    statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已取消</span>';
                }
            }
        } else if (button.classList.contains('btn-delete-dispatch')) {
            if (confirm('确认删除检查任务 (ID: ' + id + ') 吗？')) {
                console.log('删除检查任务 ID:', id);
                alert('检查任务 (ID: ' + id + ') 已删除 - 模拟');
                button.closest('tr').remove();
            }
        }
    });
});
</script>
`;

// 检查审批内容（风险隐患审批）- 使用原始文件内容的简化版本
const HAZARD_APPROVAL_CONTENT = `
<!-- 风险隐患审批页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">风险隐患审批</h2>
            <p class="text-gray-600 mt-1">管理和审批风险隐患检查记录</p>
        </div>
    </div>

    <!-- 选项卡导航 -->
    <div class="bg-white rounded-t-lg shadow-sm mb-0">
        <div class="p-4 sm:px-6">
            <nav class="flex space-x-4 overflow-x-auto pb-1">
                <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="pending">
                    待审批 <span class="ml-1 px-2 py-0.5 bg-red-100 text-red-800 rounded-full text-xs">3</span>
                </button>
                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="approved">
                    已通过
                </button>
                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="rejected">
                    已驳回
                </button>
            </nav>
        </div>
    </div>

    <!-- 风险隐患审批列表区域 -->
    <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
        <!-- 待审批列表 -->
        <div id="pending" class="tab-content active">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交人</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 示例待审批记录 1 -->
                        <tr>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">青秀区</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市交通运输局</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">风险路段-地质灾害风险</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">G324</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">高</span>
                            </td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                            <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200边坡有落石风险</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">张检查员</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25 10:30</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                <button class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h1">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h1">通过</button>
                                <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h1">驳回</button>
                            </td>
                        </tr>
                        <!-- 示例待审批记录 2 -->
                        <tr>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">钦州市</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">灵山县</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">钦州市交通运输局</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">基础保障设施隐患-防洪标识</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">S211</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">低</span>
                            </td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                            <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">李检查员</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-24 15:00</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                <button class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h2">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h2">通过</button>
                                <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h2">驳回</button>
                            </td>
                        </tr>
                        <!-- 示例待审批记录 3 -->
                        <tr>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">玉林市</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">福绵区</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">玉林市交通运输局</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">X456</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">中</span>
                            </td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                            <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">王检查员</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-23 09:00</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                <button class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h3">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h3">通过</button>
                                <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h3">驳回</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 已通过列表 -->
        <div id="approved" class="tab-content" style="display: none;">
            <div class="p-6 text-center text-gray-500">
                已通过的风险隐患记录列表（结构类似，可能包含审批时间、审批人等信息）
            </div>
        </div>

        <!-- 已驳回列表 -->
        <div id="rejected" class="tab-content" style="display: none;">
            <div class="p-6 text-center text-gray-500">
                已驳回的风险隐患记录列表（结构类似，应包含驳回原因和操作）
            </div>
        </div>
    </div>
</div>

<style>
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}
</style>

<script>
// 风险隐患审批页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 选项卡切换逻辑
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 取消激活所有选项卡
            tabButtons.forEach(btn => {
                btn.classList.remove('text-blue-600', 'border-blue-600');
                btn.classList.add('text-gray-500', 'border-transparent');
            });
            // 激活点击的选项卡
            button.classList.remove('text-gray-500', 'border-transparent');
            button.classList.add('text-blue-600', 'border-blue-600');
            // 隐藏所有内容
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            // 显示目标内容
            const tabId = button.getAttribute('data-tab');
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
              targetContent.classList.add('active');
            }
        });
    });

    // 操作按钮事件监听
    document.addEventListener('click', (event) => {
        const target = event.target.closest('button');
        if (!target) return;

        const id = target.getAttribute('data-id');
        if (!id) return;

        if (target.classList.contains('btn-view-hazard')) {
            console.log('查看隐患详情 ID:', id);
            alert('查看隐患详情 (ID: ' + id + ') - 模拟');
        } else if (target.classList.contains('btn-approve')) {
            console.log('通过审批 ID:', id);
            if(confirm('确定要通过风险隐患记录 ' + id + ' 吗？')){
                alert('风险隐患 ' + id + ' 已通过审批 - 模拟');
                target.closest('tr').remove();
            }
        } else if (target.classList.contains('btn-reject')) {
            console.log('驳回审批 ID:', id);
            const reason = prompt('请输入驳回风险隐患记录 ' + id + ' 的原因:');
            if(reason !== null && reason.trim() !== ''){
                alert('风险隐患 ' + id + ' 已驳回，原因: ' + reason + ' - 模拟');
                target.closest('tr').remove();
            }
        }
    });
});
</script>
`;

// 检查模版内容
const CHECK_TEMPLATE_CONTENT = `
<!-- 检查模版页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">检查模版管理</h2>
            <p class="text-sm text-gray-500 mt-1">创建和管理检查任务的模版</p>
        </div>
        <button id="btnCreateTemplate" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <i class="fas fa-plus mr-2"></i> 创建模版
        </button>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模版ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">模版名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查项数量</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- 示例模版数据行 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TPL001</td>
                        <td class="px-6 py-4 text-sm text-gray-900 font-medium">风险路段检查模版</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">地质灾害风险路段</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">15项</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">系统管理员</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-15</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-template" data-id="TPL001" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-template" data-id="TPL001" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-copy-template" data-id="TPL001" title="复制">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-template" data-id="TPL001" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TPL002</td>
                        <td class="px-6 py-4 text-sm text-gray-900 font-medium">桥梁安全检查模版</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">涉灾隐患点-桥梁</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12项</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张工程师</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-20</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-template" data-id="TPL002" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-template" data-id="TPL002" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-copy-template" data-id="TPL002" title="复制">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-template" data-id="TPL002" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// 检查模版页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    // 创建模版按钮
    document.getElementById('btnCreateTemplate')?.addEventListener('click', () => {
        console.log('创建检查模版');
        alert('创建检查模版 - 模拟');
    });

    // 表格操作按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const id = button.dataset.id;
        if (!id) return;

        if (button.classList.contains('btn-view-template')) {
            console.log('查看模版 ID:', id);
            alert('查看模版 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-edit-template')) {
            console.log('编辑模版 ID:', id);
            alert('编辑模版 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-copy-template')) {
            console.log('复制模版 ID:', id);
            alert('复制模版 (ID: ' + id + ') - 模拟');
        } else if (button.classList.contains('btn-delete-template')) {
            if (confirm('确认删除模版 (ID: ' + id + ') 吗？')) {
                console.log('删除模版 ID:', id);
                alert('模版 (ID: ' + id + ') 已删除 - 模拟');
                button.closest('tr').remove();
            }
        }
    });
});
</script>
`;

// 默认内容生成函数
function getDefaultContentForKey(key) {
    const titleMap = {
        'hazard_list_content': '隐患列表',
        'rectification_tasks_content': '整改任务列表',
        'hazard_approval_content': '风险隐患审批',
        'check_template_content': '检查下发与模版管理',
        'plan_list_content': '预案库',
        'emergency_resources_content': '应急物资',
        'emergency_contacts_content': '应急通讯录',
        'user_management_content': '用户管理',
        'role_management_content': '角色管理',
        'org_structure_content': '部门管理',
        'position_management_content': '职位管理',
        'dictionary_management_content': '字典管理'
    };

    const title = titleMap[key] || '页面内容';
    
    return `
        <div class="py-6">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-cog fa-spin text-blue-500 text-4xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">${title}</h3>
                <p class="text-gray-500">页面内容正在开发中...</p>
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md max-w-md mx-auto">
                    <p class="text-sm text-blue-700">
                        <i class="fas fa-info-circle mr-2"></i>
                        此页面功能正在完善中，敬请期待！
                    </p>
                </div>
            </div>
        </div>
    `;
}

// 内容映射
window.LOCAL_CONTENT_MAP = {
    'my_check_tasks_content': MY_CHECK_TASKS_CONTENT,
    'construction_projects_content': CONSTRUCTION_PROJECTS_CONTENT,
    'hazard_list_content': HAZARD_LIST_CONTENT,
    'rectification_tasks_content': RECTIFICATION_TASKS_CONTENT,
    'hazard_approval_content': HAZARD_APPROVAL_CONTENT,
    'check_dispatch_content': CHECK_DISPATCH_CONTENT,
    'check_template_content': CHECK_TEMPLATE_CONTENT,
    'plan_list_content': getDefaultContentForKey('plan_list_content'),
    'emergency_resources_content': getDefaultContentForKey('emergency_resources_content'),
    'emergency_contacts_content': getDefaultContentForKey('emergency_contacts_content'),
    'user_management_content': getDefaultContentForKey('user_management_content'),
    'role_management_content': getDefaultContentForKey('role_management_content'),
    'org_structure_content': getDefaultContentForKey('org_structure_content'),
    'position_management_content': getDefaultContentForKey('position_management_content'),
    'dictionary_management_content': getDefaultContentForKey('dictionary_management_content')
};

console.log('Content data loaded with keys:', Object.keys(window.LOCAL_CONTENT_MAP));
