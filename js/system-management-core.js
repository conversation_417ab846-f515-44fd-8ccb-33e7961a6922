/**
 * 系统管理核心控制器
 * 统一管理菜单渲染、内容加载和用户交互
 */

class SystemManagementCore {
    constructor() {
        this.menuRenderer = null;
        this.isInitialized = false;
        this.currentRoute = null;
    }

    /**
     * 初始化系统管理
     */
    async init() {
        try {
            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 等待一小段时间确保所有脚本都加载完成
            await new Promise(resolve => setTimeout(resolve, 100));

            // 检查依赖是否加载完成
            if (typeof SYSTEM_MENU_CONFIG === 'undefined') {
                console.error('SYSTEM_MENU_CONFIG not loaded');
                return;
            }

            if (typeof SystemMenuRenderer === 'undefined') {
                console.error('SystemMenuRenderer not loaded');
                return;
            }

            // 初始化菜单渲染器
            this.menuRenderer = new SystemMenuRenderer(SYSTEM_MENU_CONFIG);
            this.menuRenderer.init('system-sidebar');

            // 绑定全局事件
            this.bindGlobalEvents();

            // 处理初始路由
            this.handleInitialRoute();

            this.isInitialized = true;
            console.log('System Management initialized successfully');

        } catch (error) {
            console.error('Failed to initialize System Management:', error);
            this.showInitializationError();
        }
    }

    /**
     * 绑定全局事件
     */
    bindGlobalEvents() {
        // 绑定键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 绑定窗口大小变化事件
        window.addEventListener('resize', () => {
            this.handleWindowResize();
        });

        // 绑定浏览器前进后退事件
        window.addEventListener('popstate', (e) => {
            this.handlePopState(e);
        });

        // 绑定错误处理
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });

        // 绑定未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
        });
    }

    /**
     * 处理初始路由
     */
    handleInitialRoute() {
        // 检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const module = urlParams.get('module');
        const page = urlParams.get('page');

        if (module && page) {
            // 根据URL参数加载对应内容
            const menuKey = `${module}-${page}`;
            this.loadMenuContent(menuKey);
        } else {
            // 默认加载检查任务页面
            this.loadMenuContent('my_check_tasks_content');
        }
    }

    /**
     * 加载菜单内容
     * @param {string} menuKey - 菜单项key
     */
    loadMenuContent(menuKey) {
        if (this.menuRenderer) {
            this.menuRenderer.loadContent(menuKey);
            this.updateURL(menuKey);
        }
    }

    /**
     * 更新URL
     * @param {string} menuKey - 菜单项key
     */
    updateURL(menuKey) {
        const menuItem = MenuUtils.findMenuItem(menuKey);
        if (menuItem) {
            const url = new URL(window.location);
            url.searchParams.set('module', menuKey.split('-')[0]);
            url.searchParams.set('page', menuKey.split('-').slice(1).join('-'));
            
            // 更新浏览器历史记录
            window.history.pushState(
                { menuKey }, 
                menuItem.title, 
                url.toString()
            );
            
            this.currentRoute = menuKey;
        }
    }

    /**
     * 处理浏览器前进后退
     * @param {PopStateEvent} event - 事件对象
     */
    handlePopState(event) {
        if (event.state && event.state.menuKey) {
            this.loadMenuContent(event.state.menuKey);
        } else {
            this.loadMenuContent('my_check_tasks_content');
        }
    }

    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} event - 键盘事件
     */
    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K: 搜索菜单
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            this.showMenuSearch();
        }

        // ESC: 关闭模态框
        if (event.key === 'Escape') {
            this.closeAllModals();
        }

        // Alt + 1-9: 快速导航到主菜单
        if (event.altKey && event.key >= '1' && event.key <= '9') {
            event.preventDefault();
            this.navigateToMainMenu(parseInt(event.key) - 1);
        }
    }

    /**
     * 处理窗口大小变化
     */
    handleWindowResize() {
        // 在小屏幕上自动收起菜单
        if (window.innerWidth < 768) {
            this.collapseSidebar();
        } else {
            this.expandSidebar();
        }
    }

    /**
     * 显示菜单搜索
     */
    showMenuSearch() {
        // TODO: 实现菜单搜索功能
        console.log('Menu search not implemented yet');
    }

    /**
     * 关闭所有模态框
     */
    closeAllModals() {
        const modals = document.querySelectorAll('.fixed.inset-0:not(.hidden)');
        modals.forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    /**
     * 导航到主菜单
     * @param {number} index - 菜单索引
     */
    navigateToMainMenu(index) {
        const mainMenuKeys = Object.keys(SYSTEM_MENU_CONFIG);
        if (index < mainMenuKeys.length) {
            const menuKey = mainMenuKeys[index];
            if (this.menuRenderer) {
                this.menuRenderer.toggleGroup(menuKey);
            }
        }
    }

    /**
     * 收起侧边栏
     */
    collapseSidebar() {
        const sidebar = document.getElementById('system-sidebar');
        if (sidebar) {
            sidebar.classList.add('hidden', 'md:block');
        }
    }

    /**
     * 展开侧边栏
     */
    expandSidebar() {
        const sidebar = document.getElementById('system-sidebar');
        if (sidebar) {
            sidebar.classList.remove('hidden');
        }
    }

    /**
     * 显示初始化错误
     */
    showInitializationError() {
        const container = document.getElementById('dynamic-content');
        if (container) {
            container.innerHTML = `
                <div class="flex items-center justify-center h-full">
                    <div class="text-center">
                        <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">系统初始化失败</h3>
                        <p class="text-gray-600 mb-4">系统管理模块初始化时发生错误，请刷新页面重试。</p>
                        <button onclick="window.location.reload()" 
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            刷新页面
                        </button>
                    </div>
                </div>
            `;
        }
    }

    /**
     * 获取当前状态
     * @returns {Object} 当前状态信息
     */
    getState() {
        return {
            isInitialized: this.isInitialized,
            currentRoute: this.currentRoute,
            activeMenuItem: this.menuRenderer ? this.menuRenderer.getActiveMenuItem() : null
        };
    }

    /**
     * 重置系统状态
     */
    reset() {
        if (this.menuRenderer) {
            this.menuRenderer.reset();
        }
        ContentLoader.loadSystemOverview();
        this.currentRoute = null;
        
        // 清理URL参数
        const url = new URL(window.location);
        url.search = '';
        window.history.replaceState({}, document.title, url.toString());
    }

    /**
     * 销毁实例
     */
    destroy() {
        // 清理事件监听器
        document.removeEventListener('keydown', this.handleKeyboardShortcuts);
        window.removeEventListener('resize', this.handleWindowResize);
        window.removeEventListener('popstate', this.handlePopState);
        
        // 清理内容
        ContentLoader.cleanupCurrentContent();
        
        this.isInitialized = false;
        this.menuRenderer = null;
        this.currentRoute = null;
    }
}

// 创建全局实例
const systemManagement = new SystemManagementCore();

// 自动初始化
systemManagement.init();

// 导出到全局
window.SystemManagement = systemManagement;
