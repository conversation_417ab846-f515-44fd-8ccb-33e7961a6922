/**
 * 三级菜单渲染器
 * 负责生成和管理系统管理的三级菜单
 */

class SystemMenuRenderer {
    constructor(menuConfig) {
        this.menuConfig = menuConfig;
        this.expandedGroups = new Set();
        this.activeMenuItem = null;
        this.container = null;
    }

    /**
     * 初始化菜单
     * @param {string} containerId - 容器ID
     */
    init(containerId = 'system-sidebar') {
        this.container = document.getElementById(containerId);
        if (!this.container) {
            console.error(`Menu container ${containerId} not found`);
            return;
        }

        // 默认展开第一级菜单
        for (const key of Object.keys(this.menuConfig)) {
            this.expandedGroups.add(key);
        }

        console.log('Menu initialized with expanded groups:', Array.from(this.expandedGroups));
        console.log('Menu config keys:', Object.keys(this.menuConfig));
        this.render();
        this.bindEvents();

        // 添加调试信息
        setTimeout(() => {
            const toggleButtons = this.container.querySelectorAll('[data-toggle]');
            console.log('Found toggle buttons:', toggleButtons.length);
            toggleButtons.forEach((btn, index) => {
                console.log(`Toggle button ${index}:`, btn.dataset.toggle);
            });
        }, 100);
    }

    /**
     * 渲染菜单
     */
    render() {
        if (!this.container) return;

        let menuHTML = '<nav class="py-4">';
        
        for (const [groupKey, groupConfig] of Object.entries(this.menuConfig)) {
            menuHTML += this.renderMenuGroup(groupKey, groupConfig, 0);
        }
        
        menuHTML += '</nav>';
        this.container.innerHTML = menuHTML;
    }

    /**
     * 渲染菜单组
     * @param {string} key - 菜单项key
     * @param {Object} config - 菜单配置
     * @param {number} level - 层级
     * @returns {string} HTML字符串
     */
    renderMenuGroup(key, config, level) {
        const isExpanded = this.expandedGroups.has(key);
        const hasChildren = config.children && Object.keys(config.children).length > 0;
        const isActive = this.activeMenuItem === key;

        let html = `<div class="menu-group level-${level}" data-key="${key}">`;

        if (hasChildren) {
            // 有子菜单的组
            html += `
                <button class="menu-header ${isActive ? 'active' : ''}" data-toggle="${key}">
                    <div class="flex">
                        <i class="${config.icon}"></i>
                        <span>${config.title}</span>
                    </div>
                    <i class="fas fa-chevron-${isExpanded ? 'down' : 'right'}"></i>
                </button>
            `;

            if (isExpanded) {
                html += `<div class="menu-children">`;
                for (const [childKey, childConfig] of Object.entries(config.children)) {
                    html += this.renderMenuGroup(childKey, childConfig, level + 1);
                }
                html += `</div>`;
            }
        } else {
            // 叶子节点
            const isLeafActive = this.activeMenuItem === key;
            html += `
                <button class="menu-item ${isLeafActive ? 'active' : ''}" data-content="${key}">
                    <i class="${config.icon || 'fas fa-circle'}"></i>
                    <span>${config.title}</span>
                </button>
            `;
        }

        html += `</div>`;
        return html;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        if (!this.container) return;

        // 移除之前的事件监听器
        this.container.removeEventListener('click', this.handleClick);

        // 绑定新的事件监听器
        this.handleClick = (e) => {
            e.preventDefault();
            e.stopPropagation();

            const toggleElement = e.target.closest('[data-toggle]');
            if (toggleElement) {
                const key = toggleElement.dataset.toggle;
                console.log('Toggle clicked:', key);
                this.toggleGroup(key);
                return;
            }

            // 绑定内容加载事件
            const contentElement = e.target.closest('[data-content]');
            if (contentElement) {
                const key = contentElement.dataset.content;
                console.log('Content clicked:', key);
                this.loadContent(key);
                return;
            }
        };

        this.container.addEventListener('click', this.handleClick);
    }

    /**
     * 切换菜单组展开状态
     * @param {string} key - 菜单组key
     */
    toggleGroup(key) {
        if (this.expandedGroups.has(key)) {
            this.expandedGroups.delete(key);
            console.log(`Collapsed group: ${key}`);
        } else {
            this.expandedGroups.add(key);
            console.log(`Expanded group: ${key}`);
        }
        console.log('Current expanded groups:', Array.from(this.expandedGroups));
        this.render();
        this.bindEvents(); // 重新绑定事件
    }

    /**
     * 加载内容
     * @param {string} key - 菜单项key
     */
    loadContent(key) {
        const menuItem = MenuUtils.findMenuItem(key);
        if (menuItem && menuItem.contentUrl) {
            // 设置活动菜单项
            this.setActiveMenuItem(key);
            
            // 确保父级菜单展开
            this.expandParentGroups(key);
            
            // 加载内容
            ContentLoader.loadContent(menuItem.contentUrl, menuItem.breadcrumb);
        } else {
            console.warn(`Menu item ${key} not found or has no content URL`);
        }
    }

    /**
     * 展开父级菜单组
     * @param {string} key - 菜单项key
     */
    expandParentGroups(key) {
        const path = MenuUtils.getMenuPath(key);
        if (path) {
            // 展开路径上的所有父级组
            for (let i = 0; i < path.length - 1; i++) {
                this.expandedGroups.add(path[i]);
            }
            this.render();
            this.bindEvents();
        }
    }

    /**
     * 设置活动菜单项
     * @param {string} key - 菜单项key
     */
    setActiveMenuItem(key) {
        this.activeMenuItem = key;
        this.render();
        this.bindEvents();
    }

    /**
     * 根据URL设置活动菜单项
     * @param {string} url - 内容URL
     */
    setActiveMenuByUrl(url) {
        const leaves = MenuUtils.getLeafNodes();
        const matchedItem = leaves.find(item => item.contentUrl === url);
        
        if (matchedItem) {
            this.setActiveMenuItem(matchedItem.key);
        }
    }

    /**
     * 获取当前活动菜单项
     * @returns {string|null} 活动菜单项key
     */
    getActiveMenuItem() {
        return this.activeMenuItem;
    }

    /**
     * 重置菜单状态
     */
    reset() {
        this.expandedGroups.clear();
        this.activeMenuItem = null;
        this.render();
        this.bindEvents();
    }

    /**
     * 展开所有菜单组
     */
    expandAll() {
        const collectKeys = (config) => {
            const keys = [];
            for (const [key, item] of Object.entries(config)) {
                if (item.children) {
                    keys.push(key);
                    keys.push(...collectKeys(item.children));
                }
            }
            return keys;
        };

        const allKeys = collectKeys(this.menuConfig);
        allKeys.forEach(key => this.expandedGroups.add(key));
        this.render();
        this.bindEvents();
    }

    /**
     * 收起所有菜单组
     */
    collapseAll() {
        this.expandedGroups.clear();
        this.render();
        this.bindEvents();
    }

    /**
     * 搜索菜单项
     * @param {string} searchTerm - 搜索词
     * @returns {Array} 匹配的菜单项
     */
    searchMenuItems(searchTerm) {
        const results = [];
        const search = (config, path = []) => {
            for (const [key, item] of Object.entries(config)) {
                const currentPath = [...path, key];
                
                if (item.title.toLowerCase().includes(searchTerm.toLowerCase())) {
                    results.push({
                        key,
                        title: item.title,
                        path: currentPath,
                        contentUrl: item.contentUrl
                    });
                }
                
                if (item.children) {
                    search(item.children, currentPath);
                }
            }
        };

        search(this.menuConfig);
        return results;
    }
}

// 导出到全局
window.SystemMenuRenderer = SystemMenuRenderer;
