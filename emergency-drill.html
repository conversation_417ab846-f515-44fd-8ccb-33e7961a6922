<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急演练 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    <link rel="stylesheet" href="alert-styles.css">
    <link rel="stylesheet" href="css/emergency-drill.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 应急演练内容 -->
                <div id="emergency-drill-content" class="tab-content emergency-drill-container" style="display: block;">
                    <!-- 数据分析和演练评估部分 -->
                    <div class="drill-statistics-section">
                        <h3>演练数据分析</h3>
                        <div class="drill-statistics-grid">
                            <div class="drill-stat-item">
                                <div class="drill-stat-icon">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="drill-stat-content">
                                    <div class="drill-stat-value">24</div>
                                    <div class="drill-stat-label">已完成演练</div>
                                </div>
                            </div>
                            <div class="drill-stat-item">
                                <div class="drill-stat-icon">
                                    <i class="fas fa-clipboard-list"></i>
                                </div>
                                <div class="drill-stat-content">
                                    <div class="drill-stat-value">12</div>
                                    <div class="drill-stat-label">计划中演练</div>
                                </div>
                            </div>
                            <div class="drill-stat-item">
                                <div class="drill-stat-icon">
                                    <i class="fas fa-exclamation-circle"></i>
                                </div>
                                <div class="drill-stat-content">
                                    <div class="drill-stat-value">8</div>
                                    <div class="drill-stat-label">未计划演练</div>
                                </div>
                            </div>
                            <div class="drill-stat-item">
                                <div class="drill-stat-icon">
                                    <i class="fas fa-desktop"></i>
                                </div>
                                <div class="drill-stat-content">
                                    <div class="drill-stat-value">18</div>
                                    <div class="drill-stat-label">桌面推演</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 演练计划列表部分 -->
                    <div class="drill-plan-section">
                        <div class="section-header">
                            <h3>实战演练列表</h3>
                            <div class="section-actions">
                                <button class="action-button add-button" id="add-drill-plan-btn">
                                    <i class="fas fa-plus"></i> 新增演练计划
                                </button>
                                <div class="search-box">
                                    <input type="text" placeholder="搜索演练计划..." id="drill-search-input">
                                    <button><i class="fas fa-search"></i></button>
                                </div>
                            </div>
                        </div>

                        <div class="drill-table-container">
                            <table class="drill-table" id="drill-plan-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>演练名称</th>
                                        <th>组织单位</th>
                                        <th>负责人</th>
                                        <th>演练时间</th>
                                        <th>演练方式</th>
                                        <th>演练场景</th>
                                        <th>是否已演练</th>
                                        <th>演练资料</th>
                                        <th>复盘报告</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>G72高速公路交通事故应急演练</td>
                                        <td>广西交通运输厅</td>
                                        <td>李明</td>
                                        <td>2024-05-15</td>
                                        <td>实战演练</td>
                                        <td>多车追尾事故</td>
                                        <td><span class="status-tag completed">已演练</span></td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'material')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-drill-detail"><i class="fas fa-file-pdf"></i> 查看</button>
                                        </td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'review')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-review-report"><i class="fas fa-file-alt"></i> 查看</button>
                                        </td>
                                        <td><button class="drill-action-button view-button view-drill-detail">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>南宁市洪水灾害应急演练</td>
                                        <td>南宁市交通运输局</td>
                                        <td>王芳</td>
                                        <td>2024-05-20</td>
                                        <td>桌面推演</td>
                                        <td>洪水冲毁道路</td>
                                        <td><span class="status-tag pending">未演练</span></td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'material')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-drill-detail"><i class="fas fa-file-pdf"></i> 查看</button>
                                        </td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'review')"><i class="fas fa-bell"></i> 提醒</button>
                                            <span class="no-report">-</span>
                                        </td>
                                        <td><button class="drill-action-button view-button view-drill-detail">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>柳州市危险品运输事故应急演练</td>
                                        <td>柳州市交通运输局</td>
                                        <td>张伟</td>
                                        <td>2024-04-25</td>
                                        <td>实战演练</td>
                                        <td>危险品泄漏</td>
                                        <td><span class="status-tag completed">已演练</span></td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'material')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-drill-detail"><i class="fas fa-file-pdf"></i> 查看</button>
                                        </td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'review')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-review-report"><i class="fas fa-file-alt"></i> 查看</button>
                                        </td>
                                        <td><button class="drill-action-button view-button view-drill-detail">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>桂林市隧道火灾应急演练</td>
                                        <td>桂林市交通运输局</td>
                                        <td>赵强</td>
                                        <td>2024-06-10</td>
                                        <td>联合演练</td>
                                        <td>隧道内车辆起火</td>
                                        <td><span class="status-tag pending">未演练</span></td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'material')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-drill-detail"><i class="fas fa-file-pdf"></i> 查看</button>
                                        </td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'review')"><i class="fas fa-bell"></i> 提醒</button>
                                            <span class="no-report">-</span>
                                        </td>
                                        <td><button class="drill-action-button view-button view-drill-detail">查看</button></td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>梧州市桥梁坍塌应急演练</td>
                                        <td>梧州市交通运输局</td>
                                        <td>陈静</td>
                                        <td>2024-04-15</td>
                                        <td>桌面推演</td>
                                        <td>桥梁结构损坏</td>
                                        <td><span class="status-tag completed">已演练</span></td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'material')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-drill-detail"><i class="fas fa-file-pdf"></i> 查看</button>
                                        </td>
                                        <td>
                                            <button class="drill-action-button submit-button">提交</button>
                                            <button class="drill-action-button reminder-button" onclick="openReminderModal(this, 'review')"><i class="fas fa-bell"></i> 提醒</button>
                                            <button class="drill-action-button view-button view-review-report"><i class="fas fa-file-alt"></i> 查看</button>
                                        </td>
                                        <td><button class="drill-action-button view-button view-drill-detail">查看</button></td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="pagination" id="drill-plan-pagination">
                                <button class="pagination-button"><i class="fas fa-angle-double-left"></i></button>
                                <button class="pagination-button"><i class="fas fa-angle-left"></i></button>
                                <button class="pagination-button active">1</button>
                                <button class="pagination-button">2</button>
                                <button class="pagination-button">3</button>
                                <button class="pagination-button"><i class="fas fa-angle-right"></i></button>
                                <button class="pagination-button"><i class="fas fa-angle-double-right"></i></button>
                            </div>
                        </div>
                    </div>

                    <!-- 桌面推演列表部分 -->
                    <div class="desktop-drill-section">
                        <div class="section-header">
                            <h3>桌面推演列表</h3>
                            <div class="section-actions">
                                <div class="search-box">
                                    <input type="text" placeholder="搜索桌面推演..." id="desktop-search-input">
                                    <button><i class="fas fa-search"></i></button>
                                </div>
                            </div>
                        </div>

                        <div class="drill-table-container">
                            <table class="drill-table" id="desktop-drill-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>演练名称</th>
                                        <th>演练类别</th>
                                        <th>事件类型</th>
                                        <th>参演岗位</th>
                                        <th>参演人数</th>
                                        <th>演练描述</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>G72高速公路交通事故应急演练</td>
                                        <td>防汛应急</td>
                                        <td>洪水灾害</td>
                                        <td>指挥长、信息员、抢险组</td>
                                        <td>12</td>
                                        <td>因连续强降雨引发高速隧道口山体塌方，致多车连环相撞及槽罐车粗苯泄漏，人员被困、道路中断的综合应急处置演练</td>
                                        <td>
                                            <a href="http://localhost:3000/ue-screen" target="_blank" class="action-button start-button">发起演练</a>
                                            <button class="action-button record-button">演练记录</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>桂林市隧道火灾桌面推演</td>
                                        <td>消防应急</td>
                                        <td>火灾事故</td>
                                        <td>指挥长、消防组、疏散组</td>
                                        <td>15</td>
                                        <td>模拟隧道内车辆起火情景下的应急处置流程</td>
                                        <td>
                                            <a href="http://localhost:3000/ue-screen" target="_blank" class="action-button start-button">发起演练</a>
                                            <button class="action-button record-button">演练记录</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>柳州市危险品泄漏桌面推演</td>
                                        <td>危化品应急</td>
                                        <td>危险品泄漏</td>
                                        <td>指挥长、处置组、警戒组</td>
                                        <td>10</td>
                                        <td>模拟危险品运输车辆泄漏情景下的应急处置流程</td>
                                        <td>
                                            <a href="http://localhost:3000/ue-screen" target="_blank" class="action-button start-button">发起演练</a>
                                            <button class="action-button record-button">演练记录</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>梧州市桥梁坍塌桌面推演</td>
                                        <td>结构安全应急</td>
                                        <td>桥梁坍塌</td>
                                        <td>指挥长、抢险组、救援组</td>
                                        <td>14</td>
                                        <td>模拟桥梁结构损坏情景下的应急处置流程</td>
                                        <td>
                                            <a href="http://localhost:3000/ue-screen" target="_blank" class="action-button start-button">发起演练</a>
                                            <button class="action-button record-button">演练记录</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>北海市台风灾害桌面推演</td>
                                        <td>防台应急</td>
                                        <td>台风灾害</td>
                                        <td>指挥长、预警组、抢险组</td>
                                        <td>11</td>
                                        <td>模拟台风来袭情景下的应急处置流程</td>
                                        <td>
                                            <a href="http://localhost:3000/ue-screen" target="_blank" class="action-button start-button">发起演练</a>
                                            <button class="action-button record-button">演练记录</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="pagination" id="desktop-drill-pagination">
                                <button class="pagination-button"><i class="fas fa-angle-double-left"></i></button>
                                <button class="pagination-button"><i class="fas fa-angle-left"></i></button>
                                <button class="pagination-button active">1</button>
                                <button class="pagination-button">2</button>
                                <button class="pagination-button">3</button>
                                <button class="pagination-button"><i class="fas fa-angle-right"></i></button>
                                <button class="pagination-button"><i class="fas fa-angle-double-right"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入公共JavaScript -->
    <script src="js/emergency-common.js"></script>

    <!-- 应急演练专用JavaScript -->
    <script src="js/emergency-drill.js"></script>

    <!-- 新增演练计划模态框 -->
    <div id="add-drill-modal" class="drill-modal">
        <div class="drill-modal-content">
            <div class="drill-modal-header">
                <h3>新增演练计划</h3>
                <span class="drill-modal-close">&times;</span>
            </div>
            <div class="drill-modal-body">
                <form id="add-drill-form" class="drill-form">
                    <!-- 基本信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="reporting-unit">填报单位 <span class="required">*</span></label>
                                <select id="reporting-unit" name="reportingUnit" required>
                                    <option value="">请选择填报单位</option>
                                    <option value="广西交通运输厅">广西交通运输厅</option>
                                    <option value="南宁市交通运输局">南宁市交通运输局</option>
                                    <option value="柳州市交通运输局">柳州市交通运输局</option>
                                    <option value="桂林市交通运输局">桂林市交通运输局</option>
                                    <option value="梧州市交通运输局">梧州市交通运输局</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="drill-method">演练方式 <span class="required">*</span></label>
                                <select id="drill-method" name="drillMethod" required>
                                    <option value="">请选择演练方式</option>
                                    <option value="实战演练">实战演练</option>
                                    <option value="桌面推演">桌面推演</option>
                                    <option value="联合演练">联合演练</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label for="drill-name">演练名称 <span class="required">*</span></label>
                                <input type="text" id="drill-name" name="drillName" required placeholder="请输入演练名称">
                            </div>
                            <div class="form-group full-width">
                                <label for="drill-content">演练内容 <span class="required">*</span></label>
                                <textarea id="drill-content" name="drillContent" required placeholder="请详细描述演练的主要内容、目标和要求"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 时间和规模区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-calendar-alt"></i>
                            时间与规模
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="drill-time">演练时间 <span class="required">*</span></label>
                                <input type="datetime-local" id="drill-time" name="drillTime" required>
                            </div>
                            <div class="form-group">
                                <label for="drill-scale">演练规模</label>
                                <input type="text" id="drill-scale" name="drillScale" placeholder="如：参与人数、涉及部门等">
                            </div>
                        </div>
                    </div>

                    <!-- 组织单位区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-building"></i>
                            组织单位
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="host-unit">主办单位</label>
                                <input type="text" id="host-unit" name="hostUnit" placeholder="请输入主办单位">
                            </div>
                            <div class="form-group">
                                <label for="organizer-unit">承办单位</label>
                                <input type="text" id="organizer-unit" name="organizerUnit" placeholder="请输入承办单位">
                            </div>
                            <div class="form-group">
                                <label for="responsible-person">负责人 <span class="required">*</span></label>
                                <input type="text" id="responsible-person" name="responsiblePerson" required placeholder="请输入负责人姓名">
                            </div>
                            <div class="form-group">
                                <label for="contact-info">联络方式</label>
                                <input type="text" id="contact-info" name="contactInfo" placeholder="请输入联系电话或邮箱">
                            </div>
                        </div>
                    </div>

                    <!-- 演练场景区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-map-marked-alt"></i>
                            演练场景
                        </h4>
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="drill-scenario">演练场景描述</label>
                                <textarea id="drill-scenario" name="drillScenario" placeholder="请详细描述演练的场景设定、假想事故情况等" rows="4"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 填报信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-user-edit"></i>
                            填报信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="reporter">填报人 <span class="required">*</span></label>
                                <input type="text" id="reporter" name="reporter" required placeholder="请输入填报人姓名">
                            </div>
                            <div class="form-group">
                                <label for="report-date">填报日期</label>
                                <input type="date" id="report-date" name="reportDate" value="" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeDrillModal('add-drill-modal')">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-check"></i>
                            提交
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 查看演练资料模态框 -->
    <div id="drill-material-modal" class="drill-modal">
        <div class="drill-modal-content large-modal">
            <div class="drill-modal-header">
                <h3>演练资料详情</h3>
                <span class="drill-modal-close">&times;</span>
            </div>
            <div class="drill-modal-body">
                <div class="detail-content">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>填报单位：</label>
                            <span id="material-reporting-unit">广西交通运输厅</span>
                        </div>
                        <div class="detail-item">
                            <label>演练名称：</label>
                            <span id="material-drill-name">G72高速公路交通事故应急演练</span>
                        </div>
                        <div class="detail-item">
                            <label>演练总指挥：</label>
                            <span id="material-commander">李明</span>
                        </div>
                        <div class="detail-item">
                            <label>演练方式：</label>
                            <span id="material-drill-method">实战演练</span>
                        </div>
                        <div class="detail-item">
                            <label>演练时间：</label>
                            <span id="material-drill-time">2024-05-15 09:00</span>
                        </div>
                        <div class="detail-item">
                            <label>演练规模：</label>
                            <span id="material-drill-scale">120人参与，涉及5个部门</span>
                        </div>
                        <div class="detail-item full-width">
                            <label>事故过程的情景描述：</label>
                            <div id="material-scenario-desc" class="detail-text">
                                因连续强降雨引发高速隧道口山体塌方，致多车连环相撞及槽罐车粗苯泄漏，人员被困、道路中断。现场情况复杂，需要多部门协调处置。
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>演练目标达成情况：</label>
                            <div id="material-goal-achievement" class="detail-text">
                                1. 应急响应时间达到预期目标（15分钟内启动响应）<br>
                                2. 人员疏散和救援程序执行到位<br>
                                3. 部门间协调配合良好<br>
                                4. 应急物资调配及时有效
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>暴露的问题和薄弱环节：</label>
                            <div id="material-problems" class="detail-text">
                                1. 通信设备在恶劣天气下信号不稳定<br>
                                2. 部分参演人员对应急预案不够熟悉<br>
                                3. 现场指挥协调还需进一步优化<br>
                                4. 应急物资储备点分布需要调整
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>改进措施：</label>
                            <div id="material-improvements" class="detail-text">
                                1. 加强通信设备维护和备用方案<br>
                                2. 定期组织应急预案培训<br>
                                3. 完善现场指挥体系<br>
                                4. 优化应急物资配置方案
                            </div>
                        </div>
                        <div class="detail-item">
                            <label>演练地点：</label>
                            <span id="material-location">G72高速公路K1234+500处</span>
                        </div>
                        <div class="detail-item">
                            <label>填报人：</label>
                            <span id="material-reporter">张三</span>
                        </div>
                        <div class="detail-item">
                            <label>提交时间：</label>
                            <span id="material-submit-time">2024-05-16 14:30</span>
                        </div>
                    </div>

                    <div class="attachment-section">
                        <h4>演练签到表</h4>
                        <div class="file-list">
                            <div class="file-item">
                                <i class="fas fa-file-excel"></i>
                                <span>演练签到表.xlsx</span>
                                <button class="btn-download">下载</button>
                            </div>
                        </div>
                    </div>

                    <div class="attachment-section">
                        <h4>演练照片</h4>
                        <div class="photo-gallery">
                            <div class="photo-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+演练现场1</dGV4dD48L3N2Zz4=" alt="演练现场1">
                                <span>演练现场1</span>
                            </div>
                            <div class="photo-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+演练现场2</dGV4dD48L3N2Zz4=" alt="演练现场2">
                                <span>演练现场2</span>
                            </div>
                            <div class="photo-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+指挥现场</dGV4dD48L3N2Zz4=" alt="指挥现场">
                                <span>指挥现场</span>
                            </div>
                        </div>
                    </div>

                    <div class="attachment-section">
                        <h4>附件（方案、总结等）</h4>
                        <div class="file-list">
                            <div class="file-item">
                                <i class="fas fa-file-pdf"></i>
                                <span>应急演练方案.pdf</span>
                                <button class="btn-download">下载</button>
                            </div>
                            <div class="file-item">
                                <i class="fas fa-file-word"></i>
                                <span>演练总结报告.docx</span>
                                <button class="btn-download">下载</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看复盘报告模态框 -->
    <div id="review-report-modal" class="drill-modal">
        <div class="drill-modal-content large-modal">
            <div class="drill-modal-header">
                <h3>复盘报告详情</h3>
                <span class="drill-modal-close">&times;</span>
            </div>
            <div class="drill-modal-body">
                <div class="detail-content">
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>填报单位：</label>
                            <span id="review-reporting-unit">广西交通运输厅</span>
                        </div>
                        <div class="detail-item">
                            <label>演练名称：</label>
                            <span id="review-drill-name">G72高速公路交通事故应急演练</span>
                        </div>
                        <div class="detail-item">
                            <label>演练总指挥：</label>
                            <span id="review-commander">李明</span>
                        </div>
                        <div class="detail-item">
                            <label>演练方式：</label>
                            <span id="review-drill-method">实战演练</span>
                        </div>
                        <div class="detail-item">
                            <label>演练时间：</label>
                            <span id="review-drill-time">2024-05-15 09:00</span>
                        </div>
                        <div class="detail-item">
                            <label>演练规模：</label>
                            <span id="review-drill-scale">120人参与，涉及5个部门</span>
                        </div>
                        <div class="detail-item full-width">
                            <label>演练情况分析：</label>
                            <div id="review-analysis" class="detail-text">
                                本次演练按照预定方案顺利进行，各参演单位积极配合，演练过程中应急响应流程基本符合预案要求。整体演练效果良好，达到了预期目标。参演人员对应急处置流程有了更深入的理解和掌握。
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>演练目标的实现：</label>
                            <div id="review-goal-realization" class="detail-text">
                                1. 检验应急预案的可操作性和实用性 - 已实现<br>
                                2. 提高应急队伍的快速反应能力 - 已实现<br>
                                3. 增强各部门间的协调配合能力 - 基本实现<br>
                                4. 完善应急处置流程 - 已实现<br>
                                5. 提升应急物资调配效率 - 基本实现
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>改进的意见和建议：</label>
                            <div id="review-suggestions" class="detail-text">
                                1. 建议加强日常培训，提高参演人员的应急处置技能<br>
                                2. 完善通信保障体系，确保恶劣天气下的通信畅通<br>
                                3. 优化现场指挥体系，明确各级指挥员职责<br>
                                4. 加强与相关部门的协调联动机制<br>
                                5. 定期更新和完善应急预案内容
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>应急处置预案修改情况：</label>
                            <div id="review-plan-modifications" class="detail-text">
                                根据演练中发现的问题，对应急预案进行了以下修改：<br>
                                1. 调整了应急响应时间要求<br>
                                2. 完善了部门间协调机制<br>
                                3. 更新了应急联系人信息<br>
                                4. 优化了应急物资调配流程
                            </div>
                        </div>
                        <div class="detail-item full-width">
                            <label>综合评估：</label>
                            <div id="review-comprehensive-assessment" class="detail-text">
                                本次演练总体评估为"良好"。演练达到了预期目标，参演人员表现积极，各部门配合到位。通过演练发现了一些问题和不足，为今后的应急管理工作提供了宝贵经验。建议继续加强日常训练和预案完善工作。
                            </div>
                        </div>
                        <div class="detail-item">
                            <label>复盘会议室：</label>
                            <span id="review-meeting-room">应急指挥中心会议室</span>
                        </div>
                        <div class="detail-item">
                            <label>填报人：</label>
                            <span id="review-reporter">王五</span>
                        </div>
                        <div class="detail-item">
                            <label>提交时间：</label>
                            <span id="review-submit-time">2024-05-20 16:45</span>
                        </div>
                    </div>

                    <div class="attachment-section">
                        <h4>复盘签到表</h4>
                        <div class="file-list">
                            <div class="file-item">
                                <i class="fas fa-file-excel"></i>
                                <span>复盘会议签到表.xlsx</span>
                                <button class="btn-download">下载</button>
                            </div>
                        </div>
                    </div>

                    <div class="attachment-section">
                        <h4>复盘会议照片</h4>
                        <div class="photo-gallery">
                            <div class="photo-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+复盘会议1</dGV4dD48L3N2Zz4=" alt="复盘会议1">
                                <span>复盘会议1</span>
                            </div>
                            <div class="photo-item">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgZm9udC1zaXplPSIxMiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+复盘会议2</dGV4dD48L3N2Zz4=" alt="复盘会议2">
                                <span>复盘会议2</span>
                            </div>
                        </div>
                    </div>

                    <div class="attachment-section">
                        <h4>复盘报告</h4>
                        <div class="file-list">
                            <div class="file-item">
                                <i class="fas fa-file-pdf"></i>
                                <span>演练复盘报告.pdf</span>
                                <button class="btn-download">下载</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提交演练资料模态框 -->
    <div id="submit-drill-material-modal" class="drill-modal">
        <div class="drill-modal-content large-modal">
            <div class="drill-modal-header">
                <h3>提交演练资料</h3>
                <span class="drill-modal-close">&times;</span>
            </div>
            <div class="drill-modal-body">
                <form id="submit-drill-material-form" class="drill-form">
                    <!-- 基本信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="material-reporting-unit-input">填报单位 <span class="required">*</span></label>
                                <select id="material-reporting-unit-input" name="reportingUnit" required>
                                    <option value="">请选择填报单位</option>
                                    <option value="广西交通运输厅">广西交通运输厅</option>
                                    <option value="南宁市交通运输局">南宁市交通运输局</option>
                                    <option value="柳州市交通运输局">柳州市交通运输局</option>
                                    <option value="桂林市交通运输局">桂林市交通运输局</option>
                                    <option value="梧州市交通运输局">梧州市交通运输局</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="material-drill-method-input">演练方式 <span class="required">*</span></label>
                                <select id="material-drill-method-input" name="drillMethod" required>
                                    <option value="">请选择演练方式</option>
                                    <option value="实战演练">实战演练</option>
                                    <option value="桌面推演">桌面推演</option>
                                    <option value="联合演练">联合演练</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label for="material-drill-name-input">演练名称 <span class="required">*</span></label>
                                <input type="text" id="material-drill-name-input" name="drillName" required placeholder="请输入演练名称">
                            </div>
                            <div class="form-group">
                                <label for="material-commander-input">演练总指挥 <span class="required">*</span></label>
                                <input type="text" id="material-commander-input" name="commander" required placeholder="请输入演练总指挥姓名">
                            </div>
                            <div class="form-group">
                                <label for="material-drill-time-input">演练时间 <span class="required">*</span></label>
                                <input type="datetime-local" id="material-drill-time-input" name="drillTime" required>
                            </div>
                            <div class="form-group">
                                <label for="material-drill-scale-input">演练规模</label>
                                <input type="text" id="material-drill-scale-input" name="drillScale" placeholder="如：120人参与，涉及5个部门">
                            </div>
                            <div class="form-group">
                                <label for="material-location-input">演练地点 <span class="required">*</span></label>
                                <input type="text" id="material-location-input" name="location" required placeholder="请输入演练地点">
                            </div>
                        </div>
                    </div>

                    <!-- 演练情况描述区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-clipboard-list"></i>
                            演练情况描述
                        </h4>
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="material-scenario-desc-input">事故过程的情景描述 <span class="required">*</span></label>
                                <textarea id="material-scenario-desc-input" name="scenarioDesc" required placeholder="请详细描述演练的事故情景、过程等" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="material-goal-achievement-input">演练目标达成情况 <span class="required">*</span></label>
                                <textarea id="material-goal-achievement-input" name="goalAchievement" required placeholder="请描述演练目标的达成情况，可分点列出" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="material-problems-input">暴露的问题和薄弱环节</label>
                                <textarea id="material-problems-input" name="problems" placeholder="请描述演练中发现的问题和薄弱环节" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="material-improvements-input">改进措施</label>
                                <textarea id="material-improvements-input" name="improvements" placeholder="请提出针对问题的改进措施和建议" rows="4"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 附件上传区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-upload"></i>
                            附件上传
                        </h4>
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="material-signin-file">演练签到表</label>
                                <input type="file" id="material-signin-file" name="signinFile" accept=".xlsx,.xls,.pdf" multiple>
                                <small class="file-hint">支持上传Excel或PDF格式文件</small>
                            </div>
                            <div class="form-group full-width">
                                <label for="material-photos">演练照片</label>
                                <input type="file" id="material-photos" name="photos" accept="image/*" multiple>
                                <small class="file-hint">支持上传多张图片，格式：JPG、PNG、GIF等</small>
                            </div>
                            <div class="form-group full-width">
                                <label for="material-attachments">附件（方案、总结等）</label>
                                <input type="file" id="material-attachments" name="attachments" accept=".pdf,.doc,.docx,.xls,.xlsx" multiple>
                                <small class="file-hint">支持上传PDF、Word、Excel等格式文件</small>
                            </div>
                        </div>
                    </div>

                    <!-- 填报信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-user-edit"></i>
                            填报信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="material-reporter-input">填报人 <span class="required">*</span></label>
                                <input type="text" id="material-reporter-input" name="reporter" required placeholder="请输入填报人姓名">
                            </div>
                            <div class="form-group">
                                <label for="material-submit-date">提交日期</label>
                                <input type="date" id="material-submit-date" name="submitDate" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeDrillModal('submit-drill-material-modal')">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-upload"></i>
                            提交资料
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 提交复盘报告模态框 -->
    <div id="submit-review-report-modal" class="drill-modal">
        <div class="drill-modal-content large-modal">
            <div class="drill-modal-header">
                <h3>提交复盘报告</h3>
                <span class="drill-modal-close">&times;</span>
            </div>
            <div class="drill-modal-body">
                <form id="submit-review-report-form" class="drill-form">
                    <!-- 基本信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            基本信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="review-reporting-unit-input">填报单位 <span class="required">*</span></label>
                                <select id="review-reporting-unit-input" name="reportingUnit" required>
                                    <option value="">请选择填报单位</option>
                                    <option value="广西交通运输厅">广西交通运输厅</option>
                                    <option value="南宁市交通运输局">南宁市交通运输局</option>
                                    <option value="柳州市交通运输局">柳州市交通运输局</option>
                                    <option value="桂林市交通运输局">桂林市交通运输局</option>
                                    <option value="梧州市交通运输局">梧州市交通运输局</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="review-drill-method-input">演练方式 <span class="required">*</span></label>
                                <select id="review-drill-method-input" name="drillMethod" required>
                                    <option value="">请选择演练方式</option>
                                    <option value="实战演练">实战演练</option>
                                    <option value="桌面推演">桌面推演</option>
                                    <option value="联合演练">联合演练</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-drill-name-input">演练名称 <span class="required">*</span></label>
                                <input type="text" id="review-drill-name-input" name="drillName" required placeholder="请输入演练名称">
                            </div>
                            <div class="form-group">
                                <label for="review-commander-input">演练总指挥 <span class="required">*</span></label>
                                <input type="text" id="review-commander-input" name="commander" required placeholder="请输入演练总指挥姓名">
                            </div>
                            <div class="form-group">
                                <label for="review-drill-time-input">演练时间 <span class="required">*</span></label>
                                <input type="datetime-local" id="review-drill-time-input" name="drillTime" required>
                            </div>
                            <div class="form-group">
                                <label for="review-drill-scale-input">演练规模</label>
                                <input type="text" id="review-drill-scale-input" name="drillScale" placeholder="如：120人参与，涉及5个部门">
                            </div>
                            <div class="form-group">
                                <label for="review-meeting-room-input">复盘会议室 <span class="required">*</span></label>
                                <input type="text" id="review-meeting-room-input" name="meetingRoom" required placeholder="请输入复盘会议室">
                            </div>
                        </div>
                    </div>

                    <!-- 复盘分析区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-chart-line"></i>
                            复盘分析
                        </h4>
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="review-analysis-input">演练情况分析 <span class="required">*</span></label>
                                <textarea id="review-analysis-input" name="analysis" required placeholder="请详细分析本次演练的整体情况、参演单位配合情况、演练效果等" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-goal-realization-input">演练目标的实现 <span class="required">*</span></label>
                                <textarea id="review-goal-realization-input" name="goalRealization" required placeholder="请逐项分析演练目标的实现情况，可分点列出" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-suggestions-input">改进的意见和建议 <span class="required">*</span></label>
                                <textarea id="review-suggestions-input" name="suggestions" required placeholder="请提出针对演练中发现问题的改进意见和建议" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-plan-modifications-input">应急处置预案修改情况</label>
                                <textarea id="review-plan-modifications-input" name="planModifications" placeholder="请描述根据演练情况对应急预案的修改内容" rows="4"></textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-comprehensive-assessment-input">综合评估 <span class="required">*</span></label>
                                <textarea id="review-comprehensive-assessment-input" name="comprehensiveAssessment" required placeholder="请对本次演练进行综合评估，包括总体评价、经验总结等" rows="4"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 附件上传区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-upload"></i>
                            附件上传
                        </h4>
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label for="review-signin-file">复盘签到表</label>
                                <input type="file" id="review-signin-file" name="signinFile" accept=".xlsx,.xls,.pdf" multiple>
                                <small class="file-hint">支持上传Excel或PDF格式文件</small>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-photos">复盘会议照片</label>
                                <input type="file" id="review-photos" name="photos" accept="image/*" multiple>
                                <small class="file-hint">支持上传多张图片，格式：JPG、PNG、GIF等</small>
                            </div>
                            <div class="form-group full-width">
                                <label for="review-report-file">复盘报告</label>
                                <input type="file" id="review-report-file" name="reportFile" accept=".pdf,.doc,.docx" multiple>
                                <small class="file-hint">支持上传PDF、Word格式的复盘报告文件</small>
                            </div>
                        </div>
                    </div>

                    <!-- 填报信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-user-edit"></i>
                            填报信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="review-reporter-input">填报人 <span class="required">*</span></label>
                                <input type="text" id="review-reporter-input" name="reporter" required placeholder="请输入填报人姓名">
                            </div>
                            <div class="form-group">
                                <label for="review-submit-date">提交日期</label>
                                <input type="date" id="review-submit-date" name="submitDate" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeDrillModal('submit-review-report-modal')">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-upload"></i>
                            提交报告
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 一键提醒模态框 -->
    <div id="reminder-modal" class="drill-modal">
        <div class="drill-modal-content">
            <div class="drill-modal-header">
                <h3>一键提醒上交资料</h3>
                <span class="drill-modal-close">&times;</span>
            </div>
            <div class="drill-modal-body">
                <form id="reminder-form" class="drill-form">
                    <!-- 演练信息区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            演练信息
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label>演练名称：</label>
                                <span id="reminder-drill-name" class="form-display-value">G72高速公路交通事故应急演练</span>
                            </div>
                            <div class="form-group">
                                <label>组织单位：</label>
                                <span id="reminder-organization" class="form-display-value">广西交通运输厅</span>
                            </div>
                            <div class="form-group">
                                <label>负责人：</label>
                                <span id="reminder-responsible-person" class="form-display-value">李明</span>
                            </div>
                            <div class="form-group">
                                <label>演练时间：</label>
                                <span id="reminder-drill-time" class="form-display-value">2024-05-15</span>
                            </div>
                        </div>
                    </div>

                    <!-- 提醒设置区域 -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-bell"></i>
                            提醒设置
                        </h4>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="reminder-type">提醒类型 <span class="required">*</span></label>
                                <div class="checkbox-group">
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="reminderType" value="material" checked>
                                        <span class="checkmark"></span>
                                        提醒上交演练资料
                                    </label>
                                    <label class="checkbox-item">
                                        <input type="checkbox" name="reminderType" value="review">
                                        <span class="checkmark"></span>
                                        提醒上交复盘报告
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="reminder-deadline">截止时间 <span class="required">*</span></label>
                                <input type="datetime-local" id="reminder-deadline" name="deadline" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn-cancel" onclick="closeDrillModal('reminder-modal')">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                        <button type="submit" class="btn-submit">
                            <i class="fas fa-paper-plane"></i>
                            发送提醒
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('应急演练页面已加载');

            // 初始化导航高亮
            if (typeof initializeNavigation === 'function') {
                initializeNavigation('emergency-drill');
            }

            // 设置当前日期
            const today = new Date().toISOString().split('T')[0];
            const reportDateInput = document.getElementById('report-date');
            if (reportDateInput) {
                reportDateInput.value = today;
            }

            // 设置演练资料提交日期
            const materialSubmitDateInput = document.getElementById('material-submit-date');
            if (materialSubmitDateInput) {
                materialSubmitDateInput.value = today;
            }

            // 设置复盘报告提交日期
            const reviewSubmitDateInput = document.getElementById('review-submit-date');
            if (reviewSubmitDateInput) {
                reviewSubmitDateInput.value = today;
            }

            // 设置提醒截止时间默认值（3天后）
            const reminderDeadlineInput = document.getElementById('reminder-deadline');
            if (reminderDeadlineInput) {
                const threeDaysLater = new Date();
                threeDaysLater.setDate(threeDaysLater.getDate() + 3);
                reminderDeadlineInput.value = threeDaysLater.toISOString().slice(0, 16);
            }

            // 新增演练计划按钮事件
            const addDrillBtn = document.getElementById('add-drill-plan-btn');
            if (addDrillBtn) {
                addDrillBtn.addEventListener('click', function() {
                    const modal = document.getElementById('add-drill-modal');
                    if (modal) {
                        modal.style.display = 'block';
                        // 重新设置当前日期（防止页面长时间打开）
                        const currentDate = new Date().toISOString().split('T')[0];
                        const reportDateField = document.getElementById('report-date');
                        if (reportDateField) {
                            reportDateField.value = currentDate;
                        }
                    }
                });
            }

            // 演练资料提交按钮事件已在 emergency-drill.js 中处理
        });

        // 关闭模态框函数
        function closeDrillModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                // 清空表单
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                    // 重新设置当前日期
                    const today = new Date().toISOString().split('T')[0];
                    const reportDateField = document.getElementById('report-date');
                    if (reportDateField) {
                        reportDateField.value = today;
                    }
                    // 重新设置演练资料提交日期
                    const materialSubmitDateField = document.getElementById('material-submit-date');
                    if (materialSubmitDateField) {
                        materialSubmitDateField.value = today;
                    }
                    // 重新设置复盘报告提交日期
                    const reviewSubmitDateField = document.getElementById('review-submit-date');
                    if (reviewSubmitDateField) {
                        reviewSubmitDateField.value = today;
                    }
                }
            }
        }

        // 打开演练资料提交模态框（直接打开，无确认弹窗）
        function openSubmitMaterialModal(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const modal = document.getElementById('submit-drill-material-modal');
            if (modal) {
                modal.style.display = 'block';
                // 设置当前日期
                const today = new Date().toISOString().split('T')[0];
                const submitDateField = document.getElementById('material-submit-date');
                if (submitDateField) {
                    submitDateField.value = today;
                }
            }
        }

        // 打开复盘报告提交模态框（直接打开，无确认弹窗）
        function openSubmitReviewReportModal(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }

            const modal = document.getElementById('submit-review-report-modal');
            if (modal) {
                modal.style.display = 'block';
                // 设置当前日期
                const today = new Date().toISOString().split('T')[0];
                const submitDateField = document.getElementById('review-submit-date');
                if (submitDateField) {
                    submitDateField.value = today;
                }
            }
        }

        // 打开一键提醒模态框
        function openReminderModal(button, reminderType) {
            const row = button.closest('tr');
            const cells = row.cells;

            // 填充演练信息
            document.getElementById('reminder-drill-name').textContent = cells[1].textContent;
            document.getElementById('reminder-organization').textContent = cells[2].textContent;
            document.getElementById('reminder-responsible-person').textContent = cells[3].textContent;
            document.getElementById('reminder-drill-time').textContent = cells[4].textContent;

            // 重置复选框
            const materialCheckbox = document.querySelector('input[name="reminderType"][value="material"]');
            const reviewCheckbox = document.querySelector('input[name="reminderType"][value="review"]');

            // 根据传入的提醒类型参数设置复选框
            if (reminderType === 'material') {
                // 演练资料提醒
                materialCheckbox.checked = true;
                reviewCheckbox.checked = false;
            } else if (reminderType === 'review') {
                // 复盘报告提醒
                materialCheckbox.checked = false;
                reviewCheckbox.checked = true;
            } else {
                // 如果没有指定类型，根据演练状态智能设置
                const statusTag = row.querySelector('.status-tag');
                const isCompleted = statusTag && statusTag.classList.contains('completed');

                if (isCompleted) {
                    // 已演练的，默认提醒复盘报告
                    materialCheckbox.checked = false;
                    reviewCheckbox.checked = true;
                } else {
                    // 未演练的，默认提醒演练资料
                    materialCheckbox.checked = true;
                    reviewCheckbox.checked = false;
                }
            }

            // 生成默认提醒内容
            generateReminderContent();

            // 显示模态框
            const modal = document.getElementById('reminder-modal');
            if (modal) {
                modal.style.display = 'block';
            }
        }

        // 简化的提醒内容生成（仅用于显示，不需要实际生成内容）
        function generateReminderContent() {
            // 由于简化了表单，这个函数现在主要用于保持兼容性
            // 实际的提醒内容将由系统后台自动生成
            console.log('提醒类型已更新');
        }

        // 提交演练资料表单处理
        document.getElementById('submit-drill-material-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);

            // 这里可以添加表单验证逻辑
            const requiredFields = ['reportingUnit', 'drillMethod', 'drillName', 'commander', 'drillTime', 'location', 'scenarioDesc', 'goalAchievement', 'reporter'];
            let isValid = true;

            for (let field of requiredFields) {
                if (!formData.get(field)) {
                    isValid = false;
                    break;
                }
            }

            if (!isValid) {
                alert('请填写所有必填字段！');
                return;
            }

            // 模拟提交成功
            alert('演练资料提交成功！');
            closeDrillModal('submit-drill-material-modal');
        });

        // 提交复盘报告表单处理
        document.getElementById('submit-review-report-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);

            // 这里可以添加表单验证逻辑
            const requiredFields = ['reportingUnit', 'drillMethod', 'drillName', 'commander', 'drillTime', 'meetingRoom', 'analysis', 'goalRealization', 'suggestions', 'comprehensiveAssessment', 'reporter'];
            let isValid = true;

            for (let field of requiredFields) {
                if (!formData.get(field)) {
                    isValid = false;
                    break;
                }
            }

            if (!isValid) {
                alert('请填写所有必填字段！');
                return;
            }

            // 模拟提交成功
            alert('复盘报告提交成功！');
            closeDrillModal('submit-review-report-modal');
        });

        // 提交提醒表单处理
        document.getElementById('reminder-form').addEventListener('submit', function(e) {
            e.preventDefault();

            // 获取表单数据
            const formData = new FormData(this);

            // 验证必填字段
            const reminderTypes = formData.getAll('reminderType');

            if (reminderTypes.length === 0) {
                alert('请选择至少一种提醒类型！');
                return;
            }

            if (!formData.get('deadline')) {
                alert('请设置截止时间！');
                return;
            }

            // 模拟提交成功
            const typeText = reminderTypes.map(type => type === 'material' ? '演练资料' : '复盘报告').join('和');
            const drillName = document.getElementById('reminder-drill-name').textContent;

            alert(`提醒发送成功！\n\n演练名称：${drillName}\n提醒类型：${typeText}\n截止时间：${formData.get('deadline')}\n\n系统将自动发送提醒通知给相关负责人。`);
            closeDrillModal('reminder-modal');
        });

        // 提醒类型复选框变化事件
        document.addEventListener('change', function(e) {
            if (e.target.name === 'reminderType') {
                generateReminderContent();
            }
        });

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modals = document.querySelectorAll('.drill-modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        });

        // 初始化导航栏
        NavigationComponent.init('emergency-drill');
    </script>
</body>
</html>