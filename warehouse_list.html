<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库管理 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        .sidebar-menu-item { /* Keep for reference */
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #4b5563;
            transition: all 0.2s;
            text-decoration: none;
        }
        .sidebar-menu-item:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        /* Active state handled by JS */
        
        /* 自定义 Tree Select 样式 */
        .el-tree-select {
            width: 100% !important;
        }
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">

     <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
        <div class="py-6">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800">仓库管理</h2>
                    <p class="text-gray-600 mt-1">管理应急物资存放仓库的基础信息</p>
                </div>
                <div>
                        <button id="btnAddWarehouse" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 新增仓库
                    </button>
                </div>
            </div>
            
            <!-- 搜索栏 -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                    <div>
                        <label for="warehouse_name" class="block text-sm font-medium text-gray-700 mb-1">仓库名称</label>
                        <input type="text" id="warehouse_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入仓库名称">
                    </div>
                    <div>
                        <label for="org_unit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                        <div id="app">
                            <el-tree-select
                                    v-model="selectedUnit"
                                :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                placeholder="请选择单位"
                                class="block w-full"
                                    clearable
                                @change="handleChange"
                            />
                        </div>
                    </div>
                    <div>
                            <label for="warehouse_type" class="block text-sm font-medium text-gray-700 mb-1">仓库类型</label>
                            <select id="warehouse_type" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">全部</option>
                                <option value="1">中心库</option>
                                <option value="2">区域库</option>
                                <option value="3">前置库</option>
                            </select>
                        </div>
                        <div>
                            <button type="button" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-search mr-1"></i> 搜索
                            </button>
                        </div>
                </div>
            </div>
            
                <!-- 仓库列表表格 -->
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库地址</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 表格行示例 -->
                                <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">南宁市应急物资中心库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">南宁市应急管理局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G7211</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K15+200</td>
                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">南宁市青秀区民族大道100号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">中心库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">王主任</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">138xxxxxxxx</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <!-- <button class="text-indigo-600 hover:text-indigo-900 btn-view-warehouse" data-id="1" title="查看"><i class="fas fa-eye"></i></button> -->
                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="1" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="1" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">钦州港应急物资前置库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">钦州市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G75</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K2100+000</td>
                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">钦州市钦南区港口路1号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">前置库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">李科长</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">139xxxxxxxx</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <!-- <button class="text-indigo-600 hover:text-indigo-900 btn-view-warehouse" data-id="2" title="查看"><i class="fas fa-eye"></i></button> -->
                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="2" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="2" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">百色市区域应急物资库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">百色市应急管理局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G80</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K550+800</td>
                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">百色市右江区城东路20号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">区域库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">陈处长</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">137xxxxxxxx</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <!-- <button class="text-indigo-600 hover:text-indigo-900 btn-view-warehouse" data-id="3" title="查看"><i class="fas fa-eye"></i></button> -->
                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="3" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="3" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                <!-- 更多行... -->
                        </tbody>
                    </table>
                </div>
                <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">25</span> 条记录
                        </div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                    <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left h-5 w-5"></i>
                                </a>
                            <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                2
                            </a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                                3
                            </a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                    <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right h-5 w-5"></i>
                                </a>
                            </nav>
                    </div>
                </div>
            </div>
            <!-- Keep Modal outside main scrolling area but inside Vue app scope if needed -->
            <!-- 添加/编辑仓库 Modal -->
                                <div id="modal-app">
                <el-dialog
                    v-model="dialogVisible"
                    :title="isEditMode ? '编辑仓库信息' : '新增仓库'"
                    width="50%"
                    @closed="resetForm"
                    :close-on-click-modal="false"
                >
                    <el-form :model="warehouseForm" ref="warehouseFormRef" label-width="100px" label-position="right">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="仓库名称" required prop="name">
                                    <el-input v-model="warehouseForm.name" placeholder="请输入仓库名称"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="仓库类型" required prop="type">
                                    <el-select v-model="warehouseForm.type" placeholder="请选择仓库类型">
                                        <el-option label="中心库" value="1"></el-option>
                                        <el-option label="区域库" value="2"></el-option>
                                        <el-option label="前置库" value="3"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="所属单位" required prop="orgUnit">
                                    <el-tree-select
                                v-model="warehouseForm.orgUnit"
                                        :data="unitOptions"
                                :multiple="false"
                                :check-strictly="true"
                                        placeholder="请选择所属单位"
                                        class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                clearable
                             />
                        </el-form-item>
                        <el-form-item label="仓库地址" required prop="address">
                            <el-input v-model="warehouseForm.address" placeholder="请输入仓库详细地址"></el-input>
                        </el-form-item>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="负责人" required prop="manager">
                                    <el-input v-model="warehouseForm.manager" placeholder="请输入负责人姓名"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="联系电话" required prop="phone">
                                    <el-input v-model="warehouseForm.phone" placeholder="请输入负责人联系电话"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="路段编号" required prop="roadSection">
                                    <el-select v-model="warehouseForm.roadSection" placeholder="请选择路段编号">
                                        <!-- 这里需要填充实际的路段编号选项 -->
                                        <el-option label="G7211" value="G7211"></el-option>
                                        <el-option label="G75" value="G75"></el-option>
                                        <el-option label="G80" value="G80"></el-option>
                                        <el-option label="其他" value="other"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="起始桩号" required prop="startStake">
                                    <el-input v-model="warehouseForm.startStake" placeholder="例如: K15+200"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item label="备注" prop="remarks">
                             <el-input type="textarea" :rows="3" v-model="warehouseForm.remarks" placeholder="请输入备注信息"></el-input>
                        </el-form-item>
                    </el-form>
                    <template #footer>
                        <span class="dialog-footer">
                            <el-button @click="dialogVisible = false">取消</el-button>
                            <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                        </span>
                    </template>
                </el-dialog>
            </div>
        </main>
    </div>

    <!-- 引入 Vue 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- 页面特定脚本 (保留 Vue 实例和交互逻辑) -->
    <script>
        // Define unit options globally for reuse
        const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
            // Add other top-level orgs if needed
        ];

        const App = {
            data() {
                return {
                    selectedUnit: null,
                    unitOptions: standardUnitOptions
                };
            },
            methods: {
                handleChange(value) {
                    console.log('选中的值 (Filter):', value);
                },
                handleModalChange(value) {
                    console.log('选中的单位 (Modal):', value);
                }
            }
        };

        const app = Vue.createApp(App);
        app.use(ElementPlus);
        app.mount('#app');
        
        // Modal App Logic
        const WarehouseModalApp = {
            data() {
                return {
                    dialogVisible: false,
                    isEditMode: false,
                    warehouseForm: {
                        id: null,
                        name: '',
                        type: '',
                        orgUnit: null,
                        address: '',
                        manager: '',
                        phone: '',
                        roadSection: '',
                        startStake: '',
                        remarks: ''
                    },
                    unitOptions: standardUnitOptions
                };
            },
            methods: {
                openModal(isEdit = false, warehouseData = null) {
                    this.isEditMode = isEdit;
                    if (isEdit && warehouseData) {
                        // Populate form with existing data
                        this.warehouseForm = { ...warehouseData }; 
                    } else {
                         // Reset form for new entry
                        this.resetForm();
                    }
                    this.dialogVisible = true;
                },
                closeModal() {
                    this.dialogVisible = false;
                },
                submitForm() {
                    this.$refs.warehouseFormRef.validate((valid) => {
                        if (valid) {
                            console.log('Form Submitted:', this.warehouseForm);
                            // TODO: Add actual save/update logic here (e.g., AJAX call)
                            this.closeModal();
                            // Optionally refresh the table
                        } else {
                            console.log('Error submitting form');
                            return false;
                        }
                    });
                },
                resetForm() {
                     // Reset form fields to default/empty
                    this.warehouseForm = {
                        id: null, name: '', type: '', orgUnit: null, address: '',
                        manager: '', phone: '', roadSection: '', startStake: '', remarks: ''
                    };
                     if (this.$refs.warehouseFormRef) {
                        this.$refs.warehouseFormRef.clearValidate(); // Clear validation errors
                    }
                }
            }
        };

        const warehouseModalVm = Vue.createApp(WarehouseModalApp);
        warehouseModalVm.use(ElementPlus);
        const modalComponentInstance = warehouseModalVm.mount('#modal-app'); // Capture the component instance

        // Event Listeners for buttons (outside Vue for simplicity here)
        document.addEventListener('click', function(event) {
            // Open Add Modal
            if (event.target.closest('#btnAddWarehouse')) {
                 modalComponentInstance.openModal(false); // Use the component instance
            }
            
            // Open Edit Modal (Example - needs data fetching)
            const editButton = event.target.closest('.btn-edit-warehouse');
            if (editButton) {
                const warehouseId = editButton.dataset.id;
                console.log("Edit warehouse ID:", warehouseId);
                // TODO: Fetch warehouse data by ID
                const mockData = { id: warehouseId, name: 'Example Warehouse', type: '1', orgUnit: '1', address: '123 Street', manager: 'John Doe', phone: '1234567890', roadSection: 'G7211', startStake: 'K15+200', remarks: 'Main warehouse'}; 
                modalComponentInstance.openModal(true, mockData); // Use the component instance
            }

            // Handle View (Example)
             const viewButton = event.target.closest('.btn-view-warehouse');
             if (viewButton) {
                const warehouseId = viewButton.dataset.id;
                console.log("View warehouse ID:", warehouseId);
                // TODO: Implement view logic (e.g., redirect or show details)
                 alert('Viewing Warehouse ID: ' + warehouseId);
            }

            // Handle Delete (Example)
            const deleteButton = event.target.closest('.btn-delete-warehouse');
            if (deleteButton) {
                const warehouseId = deleteButton.dataset.id;
                console.log("Delete warehouse ID:", warehouseId);
                if (confirm(`确定要删除仓库 ID ${warehouseId} 吗？`)) {
                    // TODO: Implement delete logic (e.g., AJAX call)
                     alert('Deleting Warehouse ID: ' + warehouseId);
                     // Optionally remove row from table
                }
            }
        });

    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 