<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端预览 - 广西公路水路安全畅通与应急处置系统</title>
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
            background-color: #f3f4f6; /* Light gray background */
            display: flex;
            flex-direction: column; /* Stack header and phone */
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 2rem;
        }
        /* Styles for the phone mockup (copied and potentially adjusted from index.html's previous version) */
        .phone-mockup {
            width: 375px; /* More standard mobile width */
            height: 750px; /* Adjust height for a common aspect ratio */
            background-color: #111827; /* Darker phone body */
            border-radius: 40px; /* More pronounced rounding */
            padding: 18px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4), inset 0 0 0 2px #374151; /* Updated shadow and inner border */
            position: relative;
        }
        .phone-screen {
            background-color: #fff;
            width: 100%;
            height: 100%;
            border-radius: 22px; /* Adjust inner rounding */
            overflow: hidden;
            position: relative; /* Needed for potential notch/camera simulation */
        }
        .phone-screen iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        /* Optional: Simulate a notch or camera */
        .phone-mockup::before {
            content: '';
            position: absolute;
            top: 18px; /* Align with padding */
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 8px;
            background-color: #111827; /* Match body */
            border-radius: 0 0 8px 8px;
            z-index: 10; /* Above screen */
        }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-semibold text-gray-700">移动端应用预览</h1>
        <p class="text-gray-500 text-sm mt-1">模拟手机端风险隐患上报流程</p>
         <a href="index.html" class="text-sm text-blue-600 hover:underline mt-2 inline-block"><i class="fas fa-arrow-left mr-1"></i>返回首页</a>
    </div>

    <!-- Phone Mockup -->
    <div class="phone-mockup">
        <div class="phone-screen">
            <iframe src="mobile_check_tasks.html" title="移动端应用模拟"></iframe>
        </div>
    </div>

</body>
</html> 