<!-- 风险隐患审批页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">风险隐患审批</h2>
            <p class="text-gray-600 mt-1">管理和审批风险隐患检查记录</p>
        </div>
    </div>

    <!-- 选项卡导航 -->
    <div class="bg-white rounded-t-lg shadow-sm mb-0">
        <div class="p-4 sm:px-6">
            <nav class="flex space-x-4 overflow-x-auto pb-1">
                <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="pending">
                    待审批 <span class="ml-1 px-2 py-0.5 bg-red-100 text-red-800 rounded-full text-xs">3</span>
                </button>
                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="approved">
                    已通过
                </button>
                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="rejected">
                    已驳回
                </button>
            </nav>
        </div>

        <!-- 过滤区域 -->
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                    <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入市名称">
                </div>
                <div>
                    <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                    <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入区/县名称">
                </div>
                <div>
                    <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                    <select id="filterOrgUnit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">请选择单位</option>
                        <option value="1.1.1">自治区公路发展中心</option>
                        <option value="1.1.2">自治区高速公路发展中心</option>
                        <option value="1.2.1">钦州市交通运输局</option>
                        <option value="1.2.2">南宁市交通运输局</option>
                        <option value="1.2.3">玉林市交通运输局</option>
                    </select>
                </div>
                <div>
                    <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                    <select id="filterCategory" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">请选择检查类别</option>
                        <option value="risk_section_flood">山洪淹没区风险路段</option>
                        <option value="risk_section_geology">地质灾害风险路段</option>
                        <option value="management_mechanism">工作管理机制隐患</option>
                        <option value="basic_facilities_sign">防洪标识</option>
                        <option value="basic_facilities_trail">检查步道</option>
                        <option value="basic_facilities_hazard">涉灾隐患点</option>
                        <option value="hazard_points_slope">边坡</option>
                        <option value="hazard_points_drainage">防洪排水设施</option>
                        <option value="hazard_points_bridge">桥梁</option>
                        <option value="hazard_points_tunnel">隧道</option>
                    </select>
                </div>
                <div>
                    <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                    <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">全部</option>
                        <option value="high">高</option>
                        <option value="medium">中</option>
                        <option value="low">低</option>
                        <option value="none">无风险/已整改</option>
                    </select>
                </div>
                <div>
                    <label for="filterIsHazard" class="block text-sm font-medium text-gray-700 mb-1">是否为隐患点</label>
                    <select id="filterIsHazard" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <option value="">全部</option>
                        <option value="yes">是</option>
                        <option value="no">否</option>
                    </select>
                </div>
                <div>
                    <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                    <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入路段编号">
                </div>
                <div>
                    <label for="date_range" class="block text-sm font-medium text-gray-700 mb-1">检查时间</label>
                    <div class="flex items-center space-x-2">
                        <input type="date" id="filter_check_date_from" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        <span class="text-gray-500">至</span>
                        <input type="date" id="filter_check_date_to" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <button id="btnResetFilter" class="bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-3">
                    <i class="fas fa-undo mr-1"></i> 重置
                </button>
                <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <i class="fas fa-search mr-1"></i> 搜索
                </button>
            </div>
        </div>
    </div>

    <!-- 风险隐患审批列表区域 -->
    <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
        <!-- 待审批列表 -->
        <div id="pending" class="tab-content active">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交人</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                            <th scope="col" class="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 示例待审批记录 1 -->
                        <tr>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">青秀区</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">南宁市交通运输局</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">风险路段-地质灾害风险</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">G324</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">高</span>
                            </td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                            <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200边坡有落石风险</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">张检查员</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25 10:30</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                <button class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h1">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h1">通过</button>
                                <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h1">驳回</button>
                            </td>
                        </tr>
                        <!-- 示例待审批记录 2 -->
                        <tr>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">钦州市</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">灵山县</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">钦州市交通运输局</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">基础保障设施隐患-防洪标识</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">S211</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">低</span>
                            </td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                            <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">李检查员</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-24 15:00</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                <button class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h2">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h2">通过</button>
                                <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h2">驳回</button>
                            </td>
                        </tr>
                        <!-- 示例待审批记录 3 -->
                        <tr>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">玉林市</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">福绵区</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">玉林市交通运输局</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">X456</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">中</span>
                            </td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                            <td class="px-3 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">王检查员</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-23 09:00</td>
                            <td class="px-3 py-4 whitespace-nowrap text-sm text-center">
                                <button class="text-blue-600 hover:text-blue-900 mr-3 btn-view-hazard" data-id="h3">查看</button>
                                <button class="text-green-600 hover:text-green-900 mr-3 btn-approve" data-id="h3">通过</button>
                                <button class="text-red-600 hover:text-red-900 mr-3 btn-reject" data-id="h3">驳回</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left text-xs"></i>
                            </a>
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-50">1</a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right text-xs"></i>
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- 已通过列表 -->
        <div id="approved" class="tab-content">
            <div class="p-6 text-center text-gray-500">
                已通过的风险隐患记录列表（结构类似，可能包含审批时间、审批人等信息）
            </div>
        </div>

        <!-- 已驳回列表 -->
        <div id="rejected" class="tab-content">
            <div class="p-6 text-center text-gray-500">
                已驳回的风险隐患记录列表（结构类似，应包含驳回原因和操作）
            </div>
        </div>
    </div>
</div>

<script>
// 风险隐患审批页面的JavaScript逻辑 - 仅在审批页面加载时执行
if (typeof window.hazardApprovalLoaded === 'undefined') {
    window.hazardApprovalLoaded = true;
    
    document.addEventListener('DOMContentLoaded', function() {
        // 选项卡切换逻辑
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        // 默认激活第一个选项卡
        if (tabButtons.length > 0 && tabContents.length > 0) {
            tabButtons[0].classList.add('text-blue-600', 'border-blue-600');
            tabButtons[0].classList.remove('text-gray-500', 'border-transparent');
            tabContents[0].classList.add('active');
        }

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 取消激活所有选项卡
                tabButtons.forEach(btn => {
                    btn.classList.remove('text-blue-600', 'border-blue-600');
                    btn.classList.add('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                });
                // 激活点击的选项卡
                button.classList.remove('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                button.classList.add('text-blue-600', 'border-blue-600');
                // 隐藏所有内容
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                // 显示目标内容
                const tabId = button.getAttribute('data-tab');
                const targetContent = document.getElementById(tabId);
                if (targetContent) {
                  targetContent.classList.add('active');
                }
            });
        });

        // 操作按钮事件监听
        document.addEventListener('click', (event) => {
            const target = event.target.closest('button');
            if (!target) return;
            
            const id = target.getAttribute('data-id');
            if (!id) return;

            if (target.classList.contains('btn-view-hazard')) {
                console.log('查看隐患详情 ID:', id);
                alert(`查看隐患详情 (ID: ${id}) - 模拟`);
            } else if (target.classList.contains('btn-approve')) {
                console.log('通过审批 ID:', id);
                if(confirm(`确定要通过风险隐患记录 ${id} 吗？`)){
                    alert(`风险隐患 ${id} 已通过审批 - 模拟`);
                    // 可以在这里移除表格行或更新状态
                    target.closest('tr').remove();
                }
            } else if (target.classList.contains('btn-reject')) {
                console.log('驳回审批 ID:', id);
                const reason = prompt(`请输入驳回风险隐患记录 ${id} 的原因:`);
                if(reason !== null && reason.trim() !== ''){
                    alert(`风险隐患 ${id} 已驳回，原因: ${reason} - 模拟`);
                    // 可以在这里移除表格行或更新状态
                    target.closest('tr').remove();
                }
            }
        });

        // 筛选功能
        document.getElementById('btnFilter')?.addEventListener('click', () => {
            const filters = {
                city: document.getElementById('filterCity').value,
                county: document.getElementById('filterCounty').value,
                orgUnit: document.getElementById('filterOrgUnit').value,
                category: document.getElementById('filterCategory').value,
                riskLevel: document.getElementById('filterRiskLevel').value,
                isHazard: document.getElementById('filterIsHazard').value,
                roadNumber: document.getElementById('filterRoadNumber').value,
                dateFrom: document.getElementById('filter_check_date_from').value,
                dateTo: document.getElementById('filter_check_date_to').value
            };
            
            console.log('应用审批筛选条件:', filters);
            alert('审批筛选功能 (模拟)');
        });

        // 重置筛选
        document.getElementById('btnResetFilter')?.addEventListener('click', () => {
            document.getElementById('filterCity').value = '';
            document.getElementById('filterCounty').value = '';
            document.getElementById('filterOrgUnit').value = '';
            document.getElementById('filterCategory').value = '';
            document.getElementById('filterRiskLevel').value = '';
            document.getElementById('filterIsHazard').value = '';
            document.getElementById('filterRoadNumber').value = '';
            document.getElementById('filter_check_date_from').value = '';
            document.getElementById('filter_check_date_to').value = '';
            
            console.log('重置审批筛选条件');
            alert('审批筛选条件已重置 (模拟)');
        });
    });
}
</script>
