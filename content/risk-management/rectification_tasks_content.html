<!-- 整改任务列表页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-semibold text-gray-800">整改任务列表</h2>
            <p class="text-sm text-gray-500 mt-1">跟踪和管理隐患整改任务的进度</p>
        </div>
        <button id="btnAddRectificationTask" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
            <i class="fas fa-plus mr-2"></i> 添加任务
        </button>
    </div>

    <!-- 过滤栏 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="filterTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态</label>
                <select id="filterTaskStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">全部</option>
                    <option value="pending">待处理</option>
                    <option value="progress">整改中</option>
                    <option value="completed">已完成</option>
                    <option value="overdue">已逾期</option>
                </select>
            </div>
            <div>
                <label for="filterRectificationRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位</label>
                <select id="filterRectificationRespUnit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择责任单位</option>
                    <option value="1.1.1">自治区公路发展中心</option>
                    <option value="1.1.2">自治区高速公路发展中心</option>
                    <option value="1.2.1">钦州市交通运输局</option>
                    <option value="1.2.2">南宁市交通运输局</option>
                    <option value="1.2.3">玉林市交通运输局</option>
                </select>
            </div>
            <div>
                <label for="filterRectificationDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限</label>
                <input type="date" id="filterRectificationDeadline" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            <div class="flex items-end space-x-2 justify-end">
                <button id="btnRectificationFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-search mr-1"></i> 查询
                </button>
                <button id="btnRectificationResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                  <i class="fas fa-undo mr-1"></i> 重置
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联隐患</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任人</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">整改期限</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody id="rectificationListTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- 示例数据行 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK001</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="1" title="ID:1, K1500+200处边坡有落石风险">隐患ID:1</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李工</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-28</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-10</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">待处理</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK001" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK001" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK001" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK001" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK002</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="2" title="ID:2, 桥梁限高标识不清">隐患ID:2</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王工</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-05</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">整改中</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK002" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK002" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK002" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK002" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK003</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="3" title="ID:3, XX桥梁伸缩缝堵塞">隐患ID:3</a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张工</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-20</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已逾期</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK003" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK003" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK003" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK003" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- 分页 -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 整改任务列表页面的JavaScript逻辑 - 仅在整改任务页面加载时执行
if (typeof window.rectificationTasksLoaded === 'undefined') {
    window.rectificationTasksLoaded = true;
    
    document.addEventListener('DOMContentLoaded', function() {
        // 添加任务按钮
        document.getElementById('btnAddRectificationTask')?.addEventListener('click', () => {
            console.log('添加整改任务');
            alert('添加整改任务 - 模拟');
        });

        // 筛选功能
        document.getElementById('btnRectificationFilter')?.addEventListener('click', () => {
            const filters = {
                status: document.getElementById('filterTaskStatus').value,
                respUnit: document.getElementById('filterRectificationRespUnit').value,
                deadline: document.getElementById('filterRectificationDeadline').value
            };
            
            console.log('应用整改任务筛选条件:', filters);
            alert('整改任务筛选功能 (模拟)');
        });

        // 重置筛选
        document.getElementById('btnRectificationResetFilter')?.addEventListener('click', () => {
            document.getElementById('filterTaskStatus').value = '';
            document.getElementById('filterRectificationRespUnit').value = '';
            document.getElementById('filterRectificationDeadline').value = '';
            
            console.log('重置整改任务筛选条件');
            alert('整改任务筛选条件已重置 (模拟)');
        });

        // 表格操作按钮事件
        document.addEventListener('click', (event) => {
            const button = event.target.closest('button');
            if (!button) return;

            const id = button.dataset.id;
            const hazardId = button.dataset.hazardId;

            if (button.classList.contains('btn-view-associated-hazard')) {
                console.log('查看关联隐患 ID:', hazardId);
                alert(`查看关联隐患 (ID: ${hazardId}) - 模拟`);
            } else if (button.classList.contains('btn-view-rectification')) {
                console.log('查看整改任务 ID:', id);
                alert(`查看整改任务 (ID: ${id}) - 模拟`);
            } else if (button.classList.contains('btn-edit-rectification')) {
                console.log('编辑整改任务 ID:', id);
                alert(`编辑整改任务 (ID: ${id}) - 模拟`);
            } else if (button.classList.contains('btn-complete-rectification')) {
                if (confirm(`确认标记整改任务 (ID: ${id}) 为已完成吗？`)) {
                    console.log('完成整改任务 ID:', id);
                    alert(`整改任务 (ID: ${id}) 已标记为完成 - 模拟`);
                    
                    // 更新状态显示
                    const statusCell = button.closest('tr').querySelector('td:nth-child(7)');
                    if (statusCell) {
                        statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span>';
                    }
                }
            } else if (button.classList.contains('btn-delete-rectification')) {
                if (confirm(`确认删除整改任务 (ID: ${id}) 吗？`)) {
                    console.log('删除整改任务 ID:', id);
                    alert(`整改任务 (ID: ${id}) 已删除 - 模拟`);
                    // 可以在这里移除表格行
                    button.closest('tr').remove();
                }
            }
        });
    });
}
</script>
