<!-- 在建项目管理页面内容 -->
<div class="py-6">
    <!-- 页面标题部分 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">在建项目管理</h2>
            <p class="text-gray-600 mt-1">管理和查看在建项目的信息</p>
        </div>
        <div>
            <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 btn-add-project">
                <i class="fas fa-plus mr-2"></i>添加项目
            </button>
        </div>
    </div>

    <!-- 项目列表 -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">施工单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目负责人</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开工日期</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预计完工日期</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Example Project Row 1 -->
                    <tr class="hover:bg-gray-50 project-row" data-project-id="proj001">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">G324国道扩建工程A标段</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广西路桥建设集团有限公司</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王明</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-15</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-09-30</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">在建</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-project" title="查看项目详情">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                            <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2 btn-edit-project" title="编辑项目">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-project" title="删除项目">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </td>
                    </tr>
                    <!-- Example Project Row 2 -->
                    <tr class="hover:bg-gray-50 project-row" data-project-id="proj002">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">S211省道维修工程</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广西交通工程有限公司</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李强</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-10</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-12-20</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完工</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-project" title="查看项目详情">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                            <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2 btn-edit-project" title="编辑项目">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-project" title="删除项目">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </td>
                    </tr>
                    <!-- Example Project Row 3 -->
                    <tr class="hover:bg-gray-50 project-row" data-project-id="proj003">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">X456县道改造工程</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">广西建工集团有限责任公司</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张华</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-01</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-03-31</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">暂停</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-project" title="查看项目详情">
                                <i class="fas fa-eye mr-1"></i>查看
                            </button>
                            <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2 btn-edit-project" title="编辑项目">
                                <i class="fas fa-edit mr-1"></i>编辑
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-project" title="删除项目">
                                <i class="fas fa-trash mr-1"></i>删除
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 项目增/改 Modal -->
<div id="projectModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800" id="projectModalTitle">添加在建项目</h3>
            <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
            <form id="projectForm">
                <input type="hidden" id="modalProjectId" name="project_id">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="modalProjectName" class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="modalProjectName" name="project_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="modalConstructionUnit" class="block text-sm font-medium text-gray-700 mb-1">施工单位 <span class="text-red-500">*</span></label>
                        <input type="text" id="modalConstructionUnit" name="construction_unit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="modalProjectManager" class="block text-sm font-medium text-gray-700 mb-1">项目负责人 <span class="text-red-500">*</span></label>
                        <input type="text" id="modalProjectManager" name="project_manager" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="modalContactPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                        <input type="tel" id="modalContactPhone" name="contact_phone" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="modalStartDate" class="block text-sm font-medium text-gray-700 mb-1">开工日期 <span class="text-red-500">*</span></label>
                        <input type="date" id="modalStartDate" name="start_date" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="modalEndDate" class="block text-sm font-medium text-gray-700 mb-1">预计完工日期 <span class="text-red-500">*</span></label>
                        <input type="date" id="modalEndDate" name="end_date" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                    </div>
                    <div>
                        <label for="modalProjectStatus" class="block text-sm font-medium text-gray-700 mb-1">项目状态 <span class="text-red-500">*</span></label>
                        <select id="modalProjectStatus" name="project_status" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                            <option value="in_progress">在建</option>
                            <option value="completed">已完工</option>
                            <option value="pending">未开工</option>
                            <option value="suspended">已暂停</option>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <label for="modalProjectDescription" class="block text-sm font-medium text-gray-700 mb-1">项目描述</label>
                    <textarea id="modalProjectDescription" name="project_description" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                </div>
            </form>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
            <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
              取消
            </button>
            <button id="btnSaveProject" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              保存项目
            </button>
        </div>
    </div>
</div>

<!-- 查看项目详情 Modal -->
<div id="viewProjectModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">查看在建项目详情</h3>
            <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-4 pb-4 border-b">
                <div><strong class="text-gray-600">项目名称:</strong> <span id="view-projectName" class="text-gray-800"></span></div>
                <div><strong class="text-gray-600">施工单位:</strong> <span id="view-constructionUnit" class="text-gray-800"></span></div>
                <div><strong class="text-gray-600">项目负责人:</strong> <span id="view-projectManager" class="text-gray-800"></span></div>
                <div><strong class="text-gray-600">联系电话:</strong> <span id="view-contactPhone" class="text-gray-800"></span></div>
                <div><strong class="text-gray-600">开工日期:</strong> <span id="view-startDate" class="text-gray-800"></span></div>
                <div><strong class="text-gray-600">预计完工日期:</strong> <span id="view-endDate" class="text-gray-800"></span></div>
                <div><strong class="text-gray-600">项目状态:</strong> <span id="view-projectStatus" class="text-gray-800"></span></div>
            </div>
            <div class="mb-4">
                <strong class="text-gray-600 block mb-1">项目描述:</strong>
                <p id="view-projectDescription" class="text-gray-800 bg-gray-50 p-3 rounded min-h-[60px]"></p>
            </div>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
            <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 btn-close-modal">
                关闭
            </button>
        </div>
    </div>
</div>

<script>
// 在建项目管理页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    const projectModal = document.getElementById('projectModal');
    const projectForm = document.getElementById('projectForm');
    const projectModalTitle = document.getElementById('projectModalTitle');
    const modalProjectIdInput = document.getElementById('modalProjectId');
    const viewProjectModal = document.getElementById('viewProjectModal');
    let editingProjectId = null;

    // 模态框控制
    const openModal = (modal) => {
        if(modal) modal.classList.remove('hidden');
    }
    const closeModal = (modal) => {
        if(modal) modal.classList.add('hidden');
    }

    // 关闭模态框按钮
    document.querySelectorAll('.btn-close-modal').forEach(button => {
        button.addEventListener('click', () => {
            const modalToClose = button.closest('.fixed.inset-0');
            if (modalToClose) {
                closeModal(modalToClose);
            }
        });
    });

    // 添加项目按钮
    document.querySelector('.btn-add-project')?.addEventListener('click', () => {
        editingProjectId = null;
        projectModalTitle.textContent = '添加在建项目';
        projectForm.reset();
        modalProjectIdInput.value = '';
        openModal(projectModal);
    });

    // 项目列表按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const row = button.closest('tr.project-row');
        const projectId = row ? row.dataset.projectId : null;

        if (button.classList.contains('btn-view-project')) {
            if (projectId) {
                populateAndShowViewProjectModal(projectId, row);
            }
        } else if (button.classList.contains('btn-edit-project')) {
            if (projectId) {
                editingProjectId = projectId;
                projectModalTitle.textContent = '编辑在建项目';
                projectForm.reset();
                modalProjectIdInput.value = projectId;

                // 从表格行中提取数据填充表单
                const cells = row.querySelectorAll('td');
                document.getElementById('modalProjectName').value = cells[1].textContent;
                document.getElementById('modalConstructionUnit').value = cells[2].textContent;
                document.getElementById('modalProjectManager').value = cells[3].textContent;
                document.getElementById('modalStartDate').value = cells[4].textContent;
                document.getElementById('modalEndDate').value = cells[5].textContent;
                
                // 状态映射
                const statusText = cells[6].querySelector('span').textContent;
                if (statusText === '在建') document.getElementById('modalProjectStatus').value = 'in_progress';
                else if (statusText === '已完工') document.getElementById('modalProjectStatus').value = 'completed';
                else if (statusText === '暂停') document.getElementById('modalProjectStatus').value = 'suspended';

                openModal(projectModal);
            }
        } else if (button.classList.contains('btn-delete-project')) {
            if (projectId) {
                if (confirm(`确认删除项目 (ID: ${projectId}) 吗？`)) {
                    console.log(`Deleting project: ${projectId}`);
                    alert(`项目 (ID: ${projectId}) 已删除 (模拟)`);
                    row.remove();
                }
            }
        }
    });

    // 保存项目按钮
    document.getElementById('btnSaveProject')?.addEventListener('click', () => {
        let isValid = true;
        projectForm.querySelectorAll('[required]').forEach(el => {
            if (!el.value) {
                isValid = false;
                el.classList.add('border-red-500');
                el.addEventListener('input', () => el.classList.remove('border-red-500'), { once: true });
            }
        });
        
        if (!isValid) {
            alert('请填写所有必填项！');
            return;
        }

        const formData = new FormData(projectForm);
        const data = Object.fromEntries(formData.entries());

        if (editingProjectId) {
            data.id = editingProjectId;
            console.log('更新在建项目 (ID:', editingProjectId, ') 数据:', data);
            alert(`项目 (ID: ${editingProjectId}) 已更新 (模拟)`);
        } else {
            console.log('提交新在建项目数据:', data);
            alert('新项目已添加 (模拟)');
        }

        closeModal(projectModal);
    });

    // 填充查看项目模态框
    function populateAndShowViewProjectModal(projectId, projectRow) {
        const cells = projectRow.querySelectorAll('td');

        document.getElementById('view-projectName').textContent = cells[1]?.textContent || 'N/A';
        document.getElementById('view-constructionUnit').textContent = cells[2]?.textContent || 'N/A';
        document.getElementById('view-projectManager').textContent = cells[3]?.textContent || 'N/A';
        document.getElementById('view-contactPhone').textContent = '138****8888'; // 模拟数据
        document.getElementById('view-startDate').textContent = cells[4]?.textContent || 'N/A';
        document.getElementById('view-endDate').textContent = cells[5]?.textContent || 'N/A';

        const statusSpan = cells[6]?.querySelector('span');
        document.getElementById('view-projectStatus').textContent = statusSpan?.textContent || 'N/A';
        if (statusSpan) {
            document.getElementById('view-projectStatus').className = statusSpan.className + ' px-2 inline-flex text-xs leading-5 font-semibold rounded-full';
        }

        document.getElementById('view-projectDescription').textContent = '此项目的详细描述信息将会在这里展示。目前为模拟数据。';

        openModal(viewProjectModal);
    }
});
</script>
