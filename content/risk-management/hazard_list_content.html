<!-- 隐患列表页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="mb-6">
        <h2 class="text-2xl font-semibold text-gray-800">隐患列表</h2>
        <p class="text-sm text-gray-500 mt-1">查看和管理风险隐患检查记录</p>
    </div>

    <!-- 过滤栏 -->
    <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
                <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入市名称">
            </div>
            <div>
                <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入区/县名称">
            </div>
            <div>
                <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                <select id="filterOrgUnit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择单位</option>
                    <option value="1.1.1">自治区公路发展中心</option>
                    <option value="1.1.2">自治区高速公路发展中心</option>
                    <option value="1.2.1">钦州市交通运输局</option>
                    <option value="1.2.2">南宁市交通运输局</option>
                    <option value="1.2.3">玉林市交通运输局</option>
                </select>
            </div>
            <div>
                <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                <select id="filterCategory" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">请选择检查类别</option>
                    <option value="risk_section_flood">山洪淹没区风险路段</option>
                    <option value="risk_section_geology">地质灾害风险路段</option>
                    <option value="management_mechanism">工作管理机制隐患</option>
                    <option value="basic_facilities_sign">防洪标识</option>
                    <option value="basic_facilities_trail">检查步道</option>
                    <option value="basic_facilities_hazard">涉灾隐患点</option>
                    <option value="hazard_points_slope">边坡</option>
                    <option value="hazard_points_drainage">防洪排水设施</option>
                    <option value="hazard_points_bridge">桥梁</option>
                    <option value="hazard_points_tunnel">隧道</option>
                </select>
            </div>
            <div>
                <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">全部</option>
                    <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                    <option value="none">无风险/已整改</option>
                </select>
            </div>
            <div>
                <label for="filterIsHazard" class="block text-sm font-medium text-gray-700 mb-1">是否为隐患点</label>
                <select id="filterIsHazard" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">全部</option>
                    <option value="yes">是</option>
                    <option value="no">否</option>
                </select>
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
            <div>
                <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
            </div>
            <div class="col-start-4 md:col-start-5 flex items-end space-x-2 justify-end">
                <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <i class="fas fa-search mr-1"></i> 查询
                </button>
                <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                  <i class="fas fa-undo mr-1"></i> 重置
                </button>
            </div>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody id="hazardListTableBody" class="bg-white divide-y divide-gray-200">
                    <!-- 示例数据行 -->
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">青秀区</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段-地质灾害风险</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">G324</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">高</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200处边坡有落石风险</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="1" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="1" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none mr-2 btn-delete" data-id="1" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-rectify" data-id="1" title="生成整改任务">
                                <i class="fas fa-clipboard-list"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">灵山县</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患-防洪标识</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">S211</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">低</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="2" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="2" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none mr-2 btn-delete" data-id="2" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-rectify" data-id="2" title="生成整改任务">
                                <i class="fas fa-clipboard-list"></i>
                            </button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">福绵区</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">涉灾隐患点-桥梁</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">X456</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">中</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="3" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="3" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none mr-2 btn-delete" data-id="3" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-rectify" data-id="3" title="生成整改任务">
                                <i class="fas fa-clipboard-list"></i>
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- 分页 -->
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 隐患列表页面的JavaScript逻辑 - 仅在隐患列表页面加载时执行
if (typeof window.hazardListLoaded === 'undefined') {
    window.hazardListLoaded = true;

    document.addEventListener('DOMContentLoaded', function() {
        // 筛选功能
        document.getElementById('btnFilter')?.addEventListener('click', () => {
            const filters = {
                city: document.getElementById('filterCity').value,
                county: document.getElementById('filterCounty').value,
                orgUnit: document.getElementById('filterOrgUnit').value,
                category: document.getElementById('filterCategory').value,
                riskLevel: document.getElementById('filterRiskLevel').value,
                isHazard: document.getElementById('filterIsHazard').value,
                roadNumber: document.getElementById('filterRoadNumber').value
            };

            console.log('应用筛选条件:', filters);
            alert('筛选功能 (模拟)');
        });

        // 重置筛选
        document.getElementById('btnResetFilter')?.addEventListener('click', () => {
            document.getElementById('filterCity').value = '';
            document.getElementById('filterCounty').value = '';
            document.getElementById('filterOrgUnit').value = '';
            document.getElementById('filterCategory').value = '';
            document.getElementById('filterRiskLevel').value = '';
            document.getElementById('filterIsHazard').value = '';
            document.getElementById('filterRoadNumber').value = '';

            console.log('重置筛选条件');
            alert('筛选条件已重置 (模拟)');
        });

        // 表格操作按钮事件
        document.addEventListener('click', (event) => {
            const button = event.target.closest('button');
            if (!button) return;

            const id = button.dataset.id;
            if (!id) return;

            if (button.classList.contains('btn-view')) {
                console.log('查看隐患详情 ID:', id);
                alert(`查看隐患详情 (ID: ${id}) - 模拟`);
            } else if (button.classList.contains('btn-edit')) {
                console.log('编辑隐患 ID:', id);
                alert(`编辑隐患 (ID: ${id}) - 模拟`);
            } else if (button.classList.contains('btn-delete')) {
                if (confirm(`确认删除隐患记录 (ID: ${id}) 吗？`)) {
                    console.log('删除隐患 ID:', id);
                    alert(`隐患记录 (ID: ${id}) 已删除 - 模拟`);
                    // 可以在这里移除表格行
                    button.closest('tr').remove();
                }
            } else if (button.classList.contains('btn-rectify')) {
                console.log('生成整改任务 ID:', id);
                alert(`为隐患 (ID: ${id}) 生成整改任务 - 模拟`);
            }
        });
    });
}
</script>
