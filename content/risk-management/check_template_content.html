<!-- 检查下发与模版管理页面内容 -->
<div class="py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">检查下发与模版管理</h2>
            <p class="text-gray-600 mt-1">下发检查任务并管理检查模版</p>
        </div>
    </div>

    <!-- 选项卡导航和内容 -->
    <div class="bg-white rounded-t-lg shadow-sm mb-0">
        <div class="p-4 sm:px-6">
            <nav class="flex space-x-4 overflow-x-auto pb-1">
                <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none active" data-tab="dispatch">
                    检查下发
                </button>
                <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-tab="template">
                    检查模版
                </button>
            </nav>
        </div>
    </div>

    <div class="bg-white rounded-b-lg shadow-md pt-4">
        <!-- 检查下发选项卡 -->
        <div id="dispatch" class="tab-content active px-6 pb-6">
            <div class="flex justify-between items-center mb-4">
               <h3 class="text-xl font-semibold text-gray-800">检查任务列表</h3>
               <button id="btnDispatchCheck" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                   <i class="fas fa-paper-plane mr-2"></i> 下发检查任务
               </button>
           </div>
           <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
               <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务单位</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">任务进度</th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                         <tbody class="bg-white divide-y divide-gray-200">
                             <!-- 示例已下发检查任务 1 (进行中) -->
                             <tr class="hover:bg-gray-50 task-row" data-status="进行中">
                                 <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">2024年第三季度风险路段专项检查</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">风险路段</td>
                                 <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="南宁市交通运输局, 钦州市交通运输局">南宁市交通运输局, 钦州市交通运输局</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-09-30 17:00</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-700 font-medium task-progress">50%</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                                     <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span>
                                 </td>
                                 <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                     <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-progress" title="查看进度" data-id="task1">
                                         <i class="fas fa-chart-bar"></i>
                                     </button>
                                     <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2 btn-urge" title="催办" data-id="task1">
                                         <i class="fas fa-bell"></i>
                                     </button>
                                 </td>
                             </tr>
                             <!-- 示例已下发检查任务 2 (已完成) -->
                             <tr class="hover:bg-gray-50 task-row" data-status="已完成">
                                 <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">汛期前桥梁安全检查</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                                 <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="自治区公路发展中心, 自治区高速公路发展中心">自治区公路发展中心, ...</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-31 17:00</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-green-600 font-medium task-progress">100%</td>
                                 <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                                     <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                                 </td>
                                 <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                     <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-progress" title="查看结果" data-id="task2">
                                         <i class="fas fa-clipboard-list"></i>
                                     </button>
                                 </td>
                             </tr>
                         </tbody>
                    </table>
               </div>
                <!-- 分页 -->
                <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <p class="text-sm text-gray-700">显示第 1 到 2 条，共 2 条记录</p>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">1</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
           </div>
        </div>

        <!-- 检查模版选项卡 -->
        <div id="template" class="tab-content px-6 pb-6">
            <div class="mb-8">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">检查类别列表</h3>
                    <button id="btnAddCategory" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 添加检查类别
                    </button>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                             <thead class="bg-gray-50">
                                 <tr>
                                     <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别名称</th>
                                     <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">层级</th>
                                     <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                 </tr>
                             </thead>
                             <tbody class="bg-white divide-y divide-gray-200">
                                 <!-- 类别行 -->
                                  <tr class="hover:bg-gray-50 font-semibold">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一级</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat1" data-name="风险路段" data-parent="">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat1">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="pl-12 pr-6 py-4 whitespace-nowrap text-sm text-gray-900">山洪淹没区风险路段</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二级</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat1.1" data-name="山洪淹没区风险路段" data-parent="cat1">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat1.1">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                   <tr class="hover:bg-gray-50">
                                      <td class="pl-12 pr-6 py-4 whitespace-nowrap text-sm text-gray-900">地质灾害风险路段</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二级</td>
                                       <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat1.2" data-name="地质灾害风险路段" data-parent="cat1">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat1.2">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                   <tr class="hover:bg-gray-50 font-semibold">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">工作管理机制隐患</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一级</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                           <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat2" data-name="工作管理机制隐患" data-parent="">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat2">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50 font-semibold">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一级</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat3" data-name="基础保障设施隐患" data-parent="">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat3">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="pl-12 pr-6 py-4 whitespace-nowrap text-sm text-gray-900">防洪标识</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二级</td>
                                       <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                           <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-category" data-id="cat3.1" data-name="防洪标识" data-parent="cat3">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-category" data-id="cat3.1">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                             </tbody>
                         </table>
                    </div>
                </div>
            </div>
            <div>
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-semibold text-gray-800">填报项列表</h3>
                    <button id="btnAddField" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-plus mr-2"></i> 添加填报项
                    </button>
                </div>
                <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                             <thead class="bg-gray-50">
                                 <tr>
                                     <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">字段名称</th>
                                     <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">填写方式</th>
                                     <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">逻辑说明</th>
                                     <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                 </tr>
                             </thead>
                             <tbody class="bg-white divide-y divide-gray-200">
                                 <!-- 填报项行 -->
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">检查类别</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">主分类+子分类，如"风险路段-山洪淹没区"</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field1">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field1">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市、区/县名称</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">系统自动关联登录账号组织机构所在地</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field2">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field2">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">所属单位</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">系统自动关联登录账号组织机构</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field3">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field3">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">隐患位置</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">文本输入</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">输入详细地址/路段桩号等</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field4">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field4">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">隐患描述</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">文本域输入</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">详细描述隐患情况</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field5">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field5">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险等级</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">下拉选择</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">高/中/低</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field6">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field6">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">检查日期</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">日期选择</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">选择检查发生的日期</td>
                                       <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field7">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field7">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">现场照片</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">图片上传</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">可上传多张照片</td>
                                       <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field8">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field8">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                                  <tr class="hover:bg-gray-50">
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">整改建议</td>
                                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">文本域输入</td>
                                      <td class="px-6 py-4 text-sm text-gray-500">输入初步的整改建议（可选）</td>
                                       <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                          <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-field" data-id="field9">
                                              <i class="fas fa-edit"></i> 编辑
                                          </button>
                                          <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-field" data-id="field9">
                                              <i class="fas fa-trash-alt"></i> 删除
                                          </button>
                                      </td>
                                  </tr>
                             </tbody>
                         </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 检查下发与模版管理页面的JavaScript逻辑 - 仅在该页面加载时执行
if (typeof window.checkTemplateLoaded === 'undefined') {
    window.checkTemplateLoaded = true;
    
    document.addEventListener('DOMContentLoaded', function() {
        // 选项卡切换逻辑
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        // 默认激活第一个选项卡
        if (tabButtons.length > 0 && tabContents.length > 0) {
            tabButtons[0].classList.add('text-blue-600', 'border-blue-600');
            tabButtons[0].classList.remove('text-gray-500', 'border-transparent');
            tabContents[0].classList.add('active');
        }

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // 取消激活所有选项卡
                tabButtons.forEach(btn => {
                    btn.classList.remove('text-blue-600', 'border-blue-600');
                    btn.classList.add('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                });
                // 激活点击的选项卡
                button.classList.remove('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                button.classList.add('text-blue-600', 'border-blue-600');
                // 隐藏所有内容
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });
                // 显示目标内容
                const tabId = button.getAttribute('data-tab');
                const targetContent = document.getElementById(tabId);
                if (targetContent) {
                  targetContent.classList.add('active');
                }
            });
        });

        // 下发检查任务按钮
        document.getElementById('btnDispatchCheck')?.addEventListener('click', () => {
            console.log('下发检查任务');
            alert('下发检查任务 - 模拟');
        });

        // 添加检查类别按钮
        document.getElementById('btnAddCategory')?.addEventListener('click', () => {
            console.log('添加检查类别');
            alert('添加检查类别 - 模拟');
        });

        // 添加填报项按钮
        document.getElementById('btnAddField')?.addEventListener('click', () => {
            console.log('添加填报项');
            alert('添加填报项 - 模拟');
        });

        // 操作按钮事件监听
        document.addEventListener('click', (event) => {
            const button = event.target.closest('button');
            if (!button) return;

            const id = button.dataset.id;
            const name = button.dataset.name;

            if (button.classList.contains('btn-view-progress')) {
                console.log('查看任务进度 ID:', id);
                alert(`查看任务进度 (ID: ${id}) - 模拟`);
            } else if (button.classList.contains('btn-urge')) {
                console.log('催办任务 ID:', id);
                if (confirm(`确认催办任务 (ID: ${id}) 吗？`)) {
                    alert(`任务 (ID: ${id}) 催办通知已发送 - 模拟`);
                }
            } else if (button.classList.contains('btn-edit-category')) {
                console.log('编辑检查类别 ID:', id, '名称:', name);
                alert(`编辑检查类别 (ID: ${id}, 名称: ${name}) - 模拟`);
            } else if (button.classList.contains('btn-delete-category')) {
                console.log('删除检查类别 ID:', id);
                if (confirm(`确认删除检查类别 (ID: ${id}) 吗？`)) {
                    alert(`检查类别 (ID: ${id}) 已删除 - 模拟`);
                    button.closest('tr').remove();
                }
            } else if (button.classList.contains('btn-edit-field')) {
                console.log('编辑填报项 ID:', id);
                alert(`编辑填报项 (ID: ${id}) - 模拟`);
            } else if (button.classList.contains('btn-delete-field')) {
                console.log('删除填报项 ID:', id);
                if (confirm(`确认删除填报项 (ID: ${id}) 吗？`)) {
                    alert(`填报项 (ID: ${id}) 已删除 - 模拟`);
                    button.closest('tr').remove();
                }
            }
        });
    });
}
</script>
