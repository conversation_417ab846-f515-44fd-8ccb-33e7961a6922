<!-- 检查任务页面内容 -->
<div class="py-6">
    <!-- 页面标题部分 -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h2 class="text-2xl font-bold text-gray-800">我的检查任务</h2>
            <p class="text-gray-600 mt-1">查看分配给您的检查任务并进行填报</p>
        </div>
    </div>

    <!-- 任务列表 -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Example Task 1: Pending -->
                    <tr class="hover:bg-gray-50 task-row" data-task-id="mytask1">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">2024年第三季度风险路段专项检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">风险路段</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自治区公路发展中心</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-09-30 17:00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">待处理</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-report-hazard" title="填报风险隐患" data-task-id="mytask1" data-task-name="2024年第三季度风险路段专项检查">
                                <i class="fas fa-edit mr-1"></i>填报
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-complete-task" title="直接完成任务（无隐患）">
                                <i class="fas fa-check-circle mr-1"></i>直接完结
                            </button>
                        </td>
                    </tr>
                    <!-- Example Task 2: Completed -->
                    <tr class="hover:bg-gray-50 task-row" data-task-id="mytask2">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">汛期前桥梁安全检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-桥梁</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自治区交通运输厅</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-31 17:00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完结</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <span class="text-gray-400">-</span>
                        </td>
                    </tr>
                    <!-- Example Task 3: In Progress -->
                    <tr class="hover:bg-gray-50 task-row" data-task-id="mytask3">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">隧道安全专项检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-隧道</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自治区公路发展中心</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-10-15 17:00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-report-hazard" title="填报风险隐患" data-task-id="mytask3" data-task-name="隧道安全专项检查">
                                <i class="fas fa-edit mr-1"></i>填报
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-complete-task" title="直接完成任务（无隐患）">
                                <i class="fas fa-check-circle mr-1"></i>直接完结
                            </button>
                        </td>
                    </tr>
                    <!-- Example Task 4: Overdue -->
                    <tr class="hover:bg-gray-50 task-row" data-task-id="mytask4">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">防洪排水设施检查</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">涉灾隐患点-防洪排水设施</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市交通运输局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-31 17:00</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已逾期</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-report-hazard" title="填报风险隐患" data-task-id="mytask4" data-task-name="防洪排水设施检查">
                                <i class="fas fa-edit mr-1"></i>填报
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none btn-complete-task" title="直接完成任务（无隐患）">
                                <i class="fas fa-check-circle mr-1"></i>直接完结
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- Pagination -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">4</span> 条记录
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 风险隐患填报模态框 -->
<div id="hazardModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">风险隐患填报</h3>
            <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
            <form id="hazardForm">
                <!-- Hidden input to store task id -->
                <input type="hidden" id="modalTaskId" name="task_id">
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                   <p class="text-sm font-medium text-blue-800">当前检查任务: <span id="modalTaskName" class="font-semibold"></span></p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="modalCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别 <span class="text-red-500">*</span></label>
                        <select id="modalCategory" name="category" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                            <option value="">请选择</option>
                            <optgroup label="风险路段">
                                <option value="risk_section_flood">山洪淹没区风险路段</option>
                                <option value="risk_section_geology">地质灾害风险路段</option>
                            </optgroup>
                            <option value="management_mechanism">工作管理机制隐患</option>
                            <optgroup label="基础保障设施隐患">
                                 <option value="basic_facilities_sign">防洪标识</option>
                                 <option value="basic_facilities_trail">检查步道</option>
                                 <option value="basic_facilities_hazard">涉灾隐患点</option>
                            </optgroup>
                             <optgroup label="涉灾隐患点">
                                <option value="hazard_points_slope">边坡</option>
                                <option value="hazard_points_drainage">防洪排水设施</option>
                                <option value="hazard_points_bridge">桥梁</option>
                                <option value="hazard_points_tunnel">隧道</option>
                            </optgroup>
                        </select>
                    </div>
                     <div>
                        <label for="modalCity" class="block text-sm font-medium text-gray-700 mb-1">市、区/县名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="modalCity" name="city_county" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="系统自动关联/手动选择" required>
                    </div>
                    <div>
                        <label for="modalOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                        <select id="modalOrgUnit" name="organization_unit" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                            <option value="">请选择单位</option>
                            <option value="1.1.1">自治区公路发展中心</option>
                            <option value="1.1.2">自治区高速公路发展中心</option>
                            <option value="1.2.1">钦州市交通运输局</option>
                            <option value="1.2.2">南宁市交通运输局</option>
                            <option value="1.2.3">玉林市交通运输局</option>
                        </select>
                    </div>
                    <div>
                        <label for="modalRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">公路编号 <span class="text-red-500">*</span></label>
                        <select id="modalRoadNumber" name="road_number" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                            <option value="">请先选择市/区县</option>
                            <option value="G324">G324</option>
                            <option value="S211">S211</option>
                            <option value="X456">X456</option>
                        </select>
                    </div>
                     <div>
                        <label for="modalStartStake" class="block text-sm font-medium text-gray-700 mb-1">起点桩号</label>
                        <input type="text" id="modalStartStake" name="start_stake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+200">
                    </div>
                     <div>
                        <label for="modalEndStake" class="block text-sm font-medium text-gray-700 mb-1">止点桩号</label>
                        <input type="text" id="modalEndStake" name="end_stake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+500">
                    </div>
                </div>

                <div class="mt-4">
                  <label for="modalRiskDescription" class="block text-sm font-medium text-gray-700 mb-1">风险点描述 <span class="text-red-500">*</span></label>
                  <textarea id="modalRiskDescription" name="risk_description" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">现场照片/附件</label>
                        <input type="file" id="modalPhotos" name="photos" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <div id="uploadedFilesList" class="mt-2 text-sm text-gray-600"></div>
                    </div>
                    <div>
                        <label for="modalRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级 <span class="text-red-500">*</span></label>
                        <select id="modalRiskLevel" name="risk_level" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                            <option value="high">高</option>
                            <option value="medium">中</option>
                            <option value="low">低</option>
                            <option value="none">无风险</option>
                        </select>
                    </div>
                     <div>
                         <label class="block text-sm font-medium text-gray-700 mb-1">是否隐患点 <span class="text-red-500">*</span></label>
                         <div class="flex items-center space-x-4 mt-1">
                             <label><input type="radio" name="is_hazard" value="yes" class="mr-1"> 是</label>
                             <label><input type="radio" name="is_hazard" value="no" class="mr-1" checked> 否</label>
                         </div>
                     </div>
                     <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">是否已采取措施 <span class="text-red-500">*</span></label>
                        <div class="flex items-center space-x-4 mt-1">
                            <label><input type="radio" name="measures_taken" value="yes" class="mr-1"> 是</label>
                            <label><input type="radio" name="measures_taken" value="no" class="mr-1" checked> 否</label>
                        </div>
                    </div>
                     <div class="md:col-span-2">
                        <label for="modalMeasures" class="block text-sm font-medium text-gray-700 mb-1">已（拟）采取的措施</label>
                        <textarea id="modalMeasures" name="measures" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="可多选预设措施，或手动填写"></textarea>
                        <input type="file" id="modalMeasureFiles" name="measure_files" multiple class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100" placeholder="上传方案/报告">
                        <div id="uploadedMeasureFilesList" class="mt-1 text-sm text-gray-600"></div>
                    </div>
                </div>
                 <div class="mt-4">
                     <label for="modalCheckDate" class="block text-sm font-medium text-gray-700 mb-1">检查日期 <span class="text-red-500">*</span></label>
                     <input type="date" id="modalCheckDate" name="check_date" class="block border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                </div>
            </form>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
            <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
              取消
            </button>
            <button id="btnSave" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              提交填报
            </button>
        </div>
    </div>
</div>

<script>
// 检查任务页面的JavaScript逻辑
document.addEventListener('DOMContentLoaded', function() {
    const hazardModal = document.getElementById('hazardModal');
    const hazardForm = document.getElementById('hazardForm');
    const modalTitle = document.getElementById('modalTitle');
    const modalTaskIdInput = document.getElementById('modalTaskId');
    const modalTaskNameSpan = document.getElementById('modalTaskName');
    let currentTaskId = null;

    // 模态框控制
    const openModal = (modal) => modal.classList.remove('hidden');
    const closeModal = (modal) => modal.classList.add('hidden');

    // 关闭模态框按钮
    document.querySelectorAll('.btn-close-modal').forEach(button => {
        button.addEventListener('click', () => {
            closeModal(hazardModal);
        });
    });

    // 任务列表按钮事件
    document.addEventListener('click', (event) => {
        const button = event.target.closest('button');
        if (!button) return;

        const row = button.closest('tr.task-row');
        const taskId = row ? row.dataset.taskId : null;
        const taskName = row ? row.querySelector('.task-name')?.textContent.trim() : '未知任务';

        if (button.classList.contains('btn-report-hazard')) {
            if (taskId) {
                currentTaskId = taskId;
                modalTitle.textContent = '风险隐患填报';
                hazardForm.reset();
                modalTaskIdInput.value = taskId;
                modalTaskNameSpan.textContent = taskName;
                document.getElementById('modalCheckDate').value = new Date().toISOString().split('T')[0];
                openModal(hazardModal);
            }
        } else if (button.classList.contains('btn-complete-task')) {
            if (taskId) {
                if (confirm(`确认直接完结任务 "${taskName}" 吗？\n（请确保该任务确实没有需要填报的风险隐患）`)) {
                    console.log(`Completing task directly: ${taskName} (ID: ${taskId})`);
                    alert(`任务 "${taskName}" 已直接完结 (模拟)`);
                    
                    // 更新UI状态
                    const statusCell = row.querySelector('.task-status');
                    const actionCell = button.closest('td');
                    if (statusCell) {
                        statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完结</span>';
                    }
                    if (actionCell) {
                        actionCell.innerHTML = '<span class="text-gray-400">-</span>';
                    }
                }
            }
        }
    });

    // 文件上传显示
    document.getElementById('modalPhotos')?.addEventListener('change', function(e) {
        displayUploadedFiles(e.target.files, 'uploadedFilesList');
    });
    document.getElementById('modalMeasureFiles')?.addEventListener('change', function(e) {
        displayUploadedFiles(e.target.files, 'uploadedMeasureFilesList');
    });

    // 提交按钮
    document.getElementById('btnSave')?.addEventListener('click', () => {
        let isValid = true;
        hazardForm.querySelectorAll('[required]').forEach(el => {
            if (!el.value) {
                isValid = false;
                el.classList.add('border-red-500');
                el.addEventListener('input', () => el.classList.remove('border-red-500'), { once: true });
            }
        });
        
        if (!isValid) {
            alert('请填写所有必填项！');
            return;
        }

        const formData = new FormData(hazardForm);
        formData.set('task_id', currentTaskId || modalTaskIdInput.value);

        const data = Object.fromEntries(formData.entries());
        console.log('提交风险隐患填报 (任务ID:', data.task_id, ') 数据:', data);
        alert(`风险隐患已提交 (任务: ${modalTaskNameSpan.textContent}) - 模拟`);

        closeModal(hazardModal);
    });

    // 辅助函数
    function displayUploadedFiles(files, listElementId) {
        const listElement = document.getElementById(listElementId);
        if (!listElement) return;
        
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            const listItem = document.createElement('div');
            listItem.classList.add('upload-list-item', 'text-sm', 'text-gray-600', 'flex', 'justify-between', 'items-center', 'py-1');
            listItem.innerHTML = `
                <span>${file.name} (${(file.size / 1024).toFixed(1)} KB)</span>
                <button type="button" class="text-red-500 hover:text-red-700 text-xs ml-2" onclick="this.parentNode.remove()">删除</button>
            `;
            listElement.appendChild(listItem);
        }
    }
});
</script>
