<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新页面示例 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    <link rel="stylesheet" href="alert-styles.css">
    
    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 新页面内容 -->
                <div id="new-page-content" class="tab-content" style="display: block; padding: 40px; text-align: center;">
                    <h1 style="color: #2c3e50; margin-bottom: 30px;">
                        <i class="fas fa-star" style="color: #f39c12; margin-right: 15px;"></i>
                        新页面示例
                    </h1>
                    
                    <div style="background: #ecf0f1; padding: 30px; border-radius: 10px; margin: 20px auto; max-width: 800px;">
                        <h2 style="color: #34495e; margin-bottom: 20px;">🎉 恭喜！导航栏组件化成功！</h2>
                        
                        <p style="font-size: 18px; line-height: 1.8; color: #2c3e50; margin-bottom: 20px;">
                            这是一个演示页面，展示如何轻松添加新的标签页到导航栏中。
                        </p>
                        
                        <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
                            <h3 style="color: #e74c3c; margin-bottom: 15px;">📝 添加新页面的步骤：</h3>
                            <ol style="font-size: 16px; line-height: 1.8; color: #2c3e50;">
                                <li><strong>创建新的HTML页面</strong> - 复制现有页面模板</li>
                                <li><strong>引入导航栏组件</strong> - 添加 <code>navigation-component.js</code></li>
                                <li><strong>添加导航容器</strong> - 使用 <code>&lt;div id="navigation-container"&gt;&lt;/div&gt;</code></li>
                                <li><strong>初始化导航栏</strong> - 调用 <code>NavigationComponent.init('页面ID')</code></li>
                                <li><strong>更新配置</strong> - 在 <code>navigation-component.js</code> 中添加页面配置</li>
                            </ol>
                        </div>
                        
                        <div style="background: #d5f4e6; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
                            <h3 style="color: #27ae60; margin-bottom: 15px;">✨ 组件化的优势：</h3>
                            <ul style="font-size: 16px; line-height: 1.8; color: #2c3e50;">
                                <li>✅ <strong>统一管理</strong> - 所有导航栏在一个文件中维护</li>
                                <li>✅ <strong>易于扩展</strong> - 添加新页面只需修改配置</li>
                                <li>✅ <strong>自动高亮</strong> - 当前页面自动显示激活状态</li>
                                <li>✅ <strong>代码复用</strong> - 避免重复编写相同的HTML</li>
                                <li>✅ <strong>维护简单</strong> - 修改导航栏只需改一个地方</li>
                            </ul>
                        </div>
                        
                        <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
                            <h3 style="color: #856404; margin-bottom: 15px;">🔧 动态添加页面示例：</h3>
                            <p style="font-size: 16px; line-height: 1.8; color: #2c3e50; margin-bottom: 15px;">
                                您可以通过JavaScript动态添加新的导航页面：
                            </p>
                            <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-size: 14px; overflow-x: auto;"><code>// 添加新页面到导航栏
NavigationComponent.addPage({
    id: 'new-feature',
    title: '新功能',
    url: 'new-feature.html'
});

// 重新渲染导航栏
NavigationComponent.render('navigation-container', 'current-page-id');</code></pre>
                        </div>
                        
                        <button onclick="demonstrateAddPage()" 
                                style="background: #3498db; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer; margin: 10px;">
                            <i class="fas fa-plus"></i> 演示动态添加页面
                        </button>
                        
                        <button onclick="demonstrateRemovePage()" 
                                style="background: #e74c3c; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer; margin: 10px;">
                            <i class="fas fa-minus"></i> 演示移除页面
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 演示动态添加页面
        function demonstrateAddPage() {
            NavigationComponent.addPage({
                id: 'demo-page',
                title: '演示页面',
                url: 'demo-page.html'
            });
            
            // 重新渲染导航栏
            NavigationComponent.render('navigation-container', 'example-new-page');
            
            alert('成功添加"演示页面"到导航栏！\n\n请查看导航栏的变化。');
        }
        
        // 演示移除页面
        function demonstrateRemovePage() {
            NavigationComponent.removePage('demo-page');
            
            // 重新渲染导航栏
            NavigationComponent.render('navigation-container', 'example-new-page');
            
            alert('成功移除"演示页面"！\n\n请查看导航栏的变化。');
        }
        
        // 初始化导航栏
        // 注意：这里我们需要先添加这个示例页面到配置中
        document.addEventListener('DOMContentLoaded', function() {
            // 添加当前示例页面到导航配置
            NavigationComponent.addPage({
                id: 'example-new-page',
                title: '新页面示例',
                url: 'example-new-page.html'
            });
            
            // 初始化导航栏
            NavigationComponent.init('example-new-page');
        });
    </script>
</body>
</html>
