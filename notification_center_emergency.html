<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知中心</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="stylesheet" href="css/style.css"> <!-- 假设您有一个全局的style.css -->
    <style>
        /* Added from dashboard_emergency.html for consistent sidebar styling */
        .sidebar-menu-item {
            padding: 0.75rem 1.25rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #4a5568; /* Default link color */
        }
        .sidebar-menu-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
        body {
            /* Ensuring body font from style.css is not overridden if it exists, 
               or providing a default similar to dashboard_emergency.html if not. 
               It's better if this is harmonized in style.css */
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif; 
        }
        /* Tab styles from plan_list.html */
        .tab-btn.active {
             color: #2563eb; /* blue-600 */
             border-bottom: 2px solid #2563eb;
         }
         .tab-content:not(.active) {
            display: none;
         }
    </style>
</head>
<body class="flex h-screen bg-gray-100">

    <!-- 侧边栏容器 -->
    <div id="sidebar-container"></div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 顶部导航栏 -->
        <header class="bg-white shadow-md p-4">
            <h1 class="text-xl font-semibold text-gray-800">通知中心</h1>
            <p class="text-sm text-gray-600 mt-1">查看预警信息和待办任务</p>
        </header>

        <!-- 内容区域 -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
            <div class="container mx-auto">
                <!-- 选项卡导航 -->
                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <nav class="flex space-x-4 p-4 sm:px-6 border-b border-gray-200">
                        <button class="tab-btn px-3 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none active" data-tab="alerts-content">
                            <i class="fas fa-bell mr-1"></i>预警信息
                        </button>
                        <button class="tab-btn px-3 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none" data-tab="todo-content">
                            <i class="fas fa-clipboard-list mr-1"></i>待办任务
                        </button>
                    </nav>
                            </div>

                <!-- 标签页内容区域 -->
                <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
                    <!-- 预警信息内容 -->
                    <section id="alerts-content" class="tab-content active p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">预警信息列表</h2>
                        <div id="alert-list" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">序号</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警类型</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警内容</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">整改超时</span>
                                        </td>
                                        <td class="px-4 py-4 text-sm text-gray-700">隐患点 '办公楼消防通道堵塞' 的整改任务已于 2024-07-25 到期</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-26 08:00</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-center font-medium">
                                            <a href="#" class="text-blue-600 hover:text-blue-900">查看详情</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">整改超时</span>
                                        </td>
                                        <td class="px-4 py-4 text-sm text-gray-700">隐患点 '仓库货物堆放超高' 的整改任务已于 2024-07-28 到期</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29 09:30</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-center font-medium">
                                            <a href="#" class="text-blue-600 hover:text-blue-900">查看详情</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">物资有效期</span>
                                        </td>
                                        <td class="px-4 py-4 text-sm text-gray-700">仓库 B - 某批次药品 (ID: M0123) 有效期 (2024-09-01) 即将到期</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-28 15:30</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-center font-medium">
                                            <a href="#" class="text-blue-600 hover:text-blue-900">处理</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="py-3 px-4 text-sm text-gray-500 border-t border-gray-200">
                            预警信息分页占位符
                    </div>
                </section>

                    <!-- 待办任务内容 -->
                    <section id="todo-content" class="tab-content p-6">
                        <h2 class="text-xl font-semibold text-gray-800 mb-4">待办任务列表</h2>
                        <div id="todo-list" class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">序号</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务类型</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务名称/摘要</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发起人</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                                        <th scope="col" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">预案审批</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">审批：XX区洪涝灾害应急预案 (V2.0)</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">张三</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-red-600">2024-08-05 17:00</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-center font-medium">
                                            <a href="#" class="text-blue-600 hover:text-blue-900">立即处理</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-red-600">隐患审批</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">审批隐患：G324 K1500+200处边坡落石</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-center font-medium">
                                            <a href="#" class="text-blue-600 hover:text-blue-900">立即处理</a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-700">隐患整改</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">整改任务：桥梁X456伸缩缝堵塞</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-red-600">2024-08-10 17:00</td>
                                        <td class="px-4 py-4 whitespace-nowrap text-sm text-center font-medium">
                                            <a href="#" class="text-blue-600 hover:text-blue-900">立即处理</a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="py-3 px-4 text-sm text-gray-500 border-t border-gray-200">
                            待办任务分页占位符
                        </div>
                    </section>
                    </div>
            </div>
        </main>
    </div>

    <script src="js/sidebarComponent_emergency.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 加载侧边栏
            const sidebarContainer = document.getElementById('sidebar-container');
            if (sidebarContainer && typeof sidebarHTML !== 'undefined') {
                sidebarContainer.innerHTML = sidebarHTML;
                // 侧边栏高亮逻辑 (保持不变)
                const currentPath = window.location.pathname.split('/').pop();
                const menuItems = sidebarContainer.querySelectorAll('.sidebar-menu-item');
                menuItems.forEach(item => {
                    const href = item.getAttribute('href');
                    if (href && href.includes(currentPath)) {
                        menuItems.forEach(i => {
                            i.classList.remove('bg-blue-100');
                            i.querySelector('i')?.classList.replace('text-blue-600', 'text-gray-600');
                            i.querySelector('span')?.classList.replace('text-blue-600', 'text-gray-700');
                            i.querySelector('span')?.classList.remove('font-medium');
                        });
                        item.classList.add('bg-blue-100');
                        const icon = item.querySelector('i');
                        const span = item.querySelector('span');
                        if (icon) icon.classList.replace('text-gray-600', 'text-blue-600');
                        if (span) {
                            span.classList.replace('text-gray-700', 'text-blue-600');
                            span.classList.add('font-medium');
                        }
                    } else if (href && href === 'dashboard_emergency.html' && (currentPath === '' || currentPath === 'notification_center_emergency.html')) {
                        //  Correct highlighting for main page and notification page if dashboard is home
                        //  However, direct match is better. This 'else if' might need to be specific to dashboard_emergency.html
                        //  For now, let's assume currentPath for notification will be 'notification_center_emergency.html'
                    }
                });

                // Specifically highlight '通知中心' if it's the current page and not covered above
                if (currentPath === 'notification_center_emergency.html') {
                    let notificationMenuItem = null;
                    menuItems.forEach(item => {
                        // Attempt to find the notification center menu item if it exists
                        // This assumes 'notification_center_emergency.html' will be a href
                        if (item.getAttribute('href') === 'notification_center_emergency.html') {
                            notificationMenuItem = item;
                        }
                    });

                    if (!notificationMenuItem) { // If no direct link, create and append it to the sidebar
                        const newMenuItem = document.createElement('a');
                        newMenuItem.href = 'notification_center_emergency.html';
                        newMenuItem.className = 'sidebar-menu-item block bg-blue-100'; // Active style
                        newMenuItem.innerHTML = `
                            <i class="fas fa-bell text-blue-600 w-6"></i>
                            <span class="ml-3 text-blue-600 font-medium">通知中心</span>
                        `;
                        // Find a suitable place to insert, e.g., after '首页'
                        const homeMenuItem = sidebarContainer.querySelector('a[href="dashboard_emergency.html"]');
                        if (homeMenuItem && homeMenuItem.parentNode) {
                            homeMenuItem.parentNode.insertBefore(newMenuItem, homeMenuItem.nextSibling.nextSibling); // Adjust insertion as needed
                        }
                         // Ensure other items are not highlighted if this is the active page
                        menuItems.forEach(i => {
                            if(i !== newMenuItem) {
                                i.classList.remove('bg-blue-100');
                                i.querySelector('i')?.classList.replace('text-blue-600', 'text-gray-600');
                                i.querySelector('span')?.classList.replace('text-blue-600', 'text-gray-700');
                                i.querySelector('span')?.classList.remove('font-medium');
                    }
                });
                    }
                }

            } else {
                console.error('Sidebar container or sidebarHTML not found.');
            }

            // 选项卡切换逻辑
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const tabId = button.getAttribute('data-tab');
                    
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600', 'active');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    });
                    
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    button.classList.remove('text-gray-500', 'border-transparent');
                    button.classList.add('text-blue-600', 'border-blue-600', 'active');
                    
                    const activeTab = document.getElementById(tabId);
                    if (activeTab) {
                        activeTab.classList.add('active');
                    }
                });
            });

            // Ensure the first tab is active on load if no other logic dictates otherwise
            // The image shows '预警信息' as active by default, and '待办任务' as inactive initially.
            // My previous logic clicked the first button. Now, I'll specifically activate alerts and deactivate todos initially.
            const alertTabButton = document.querySelector('.tab-btn[data-tab="alerts-content"]');
            const todoTabButton = document.querySelector('.tab-btn[data-tab="todo-content"]');
            const alertTabContent = document.getElementById('alerts-content');
            const todoTabContent = document.getElementById('todo-content');

            if (alertTabButton && todoTabButton && alertTabContent && todoTabContent) {
                // Set Alerts as active
                alertTabButton.classList.add('text-blue-600', 'border-blue-600', 'active');
                alertTabButton.classList.remove('text-gray-500', 'border-transparent');
                alertTabContent.classList.add('active');

                // Set Todos as inactive
                todoTabButton.classList.remove('text-blue-600', 'border-blue-600', 'active');
                todoTabButton.classList.add('text-gray-500', 'border-transparent');
                todoTabContent.classList.remove('active');
            }
        });
    </script>
</body>
</html>
