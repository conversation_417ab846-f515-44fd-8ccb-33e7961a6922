// 添加CSS样式
const customStyles = document.createElement('style');
customStyles.textContent = `
    /* 两列布局样式 */
    .medical-columns-grid, .vehicle-columns-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        width: 100%;
        margin-top: 20px;
    }

    .medical-column, .vehicle-column {
        display: flex;
        flex-direction: column;
    }

    /* 基本信息面板样式 */
    .basic-info-panel {
        background-color: rgba(40, 44, 52, 0.8);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .info-table {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .info-row {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 10px;
    }

    .info-field {
        flex: 1;
        min-width: 200px;
        margin-right: 15px;
        margin-bottom: 5px;
    }

    .info-field.full-width {
        flex: 0 0 100%;
        width: 100%;
    }

    .info-label {
        font-weight: bold;
        color: #a8b2d1;
        margin-right: 5px;
    }

    .info-value {
        color: #e6e6e6;
    }

    /* 表格样式 - 参考应急物资的表格样式 */
    .supply-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
        background-color: rgba(40, 44, 52, 0.8);
        color: #e6e6e6;
    }

    .supply-table th, .supply-table td {
        padding: 8px 12px;
        text-align: left;
        border: 1px solid #3a3f4b;
    }

    .supply-table th {
        background-color: rgba(30, 60, 120, 0.8);
        color: white;
    }

    .supply-table tr:nth-child(even) {
        background-color: rgba(50, 54, 62, 0.8);
    }

    .supply-table tr:hover {
        background-color: rgba(60, 64, 72, 0.9);
    }

    /* 医疗点、救援车辆和气象预警模态框样式 */
    .medical-section, .vehicle-section, .warning-section {
        background-color: rgba(40, 44, 52, 0.8);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }

    .medical-section h5, .vehicle-section h5, .warning-section h5 {
        color: #61dafb;
        margin-top: 0;
        margin-bottom: 15px;
        border-bottom: 1px solid #3a3f4b;
        padding-bottom: 8px;
    }

    /* 气象预警模态框特有样式 */
    .warning-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .warning-name {
        margin: 0;
        color: #e6e6e6;
        display: inline-block;
        margin-right: 10px;
    }

    .warning-level {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        font-weight: bold;
    }

    .warning-level.红色 {
        background-color: rgba(255, 0, 0, 0.7);
        color: white;
    }

    .warning-level.橙色 {
        background-color: rgba(255, 165, 0, 0.7);
        color: white;
    }

    .warning-level.黄色 {
        background-color: rgba(255, 255, 0, 0.7);
        color: black;
    }

    .warning-level.蓝色 {
        background-color: rgba(0, 0, 255, 0.7);
        color: white;
    }

    .notify-button {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
    }

    .notify-button:hover {
        background-color: #45a049;
    }

    .warning-columns-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        width: 100%;
        margin-top: 20px;
    }

    .warning-column {
        display: flex;
        flex-direction: column;
    }

    .impact-list, .precautions-list {
        margin: 0;
        padding-left: 20px;
    }

    .impact-list li, .precautions-list li {
        margin-bottom: 8px;
    }

    /* 状态颜色 */
    .status-good {
        color: #4caf50;
    }

    .status-warning {
        color: #ff9800;
    }

    .status-danger {
        color: #f44336;
    }
`;

document.head.appendChild(customStyles);

document.addEventListener('DOMContentLoaded', () => {
    const tabButtons = document.querySelectorAll('.tab-navigation .tab-button');
    const tabContents = document.querySelectorAll('.main-content .tab-content');
    const modal = document.getElementById('details-modal');
    const closeModalButton = modal.querySelector('.close-button');
    const mapMarkers = document.querySelectorAll('.map-marker'); // Used for marker interactions AND visibility

    // 风险隐患点和在建项目标签卡切换
    const resourceTabButtons = document.querySelectorAll('.resource-tab-button');
    const resourceTabContents = document.querySelectorAll('.resource-tab-content');

    // 应急事件模态框元素
    const emergencyEventModal = document.getElementById('emergency-event-modal');
    console.log('应急事件模态框元素:', emergencyEventModal);
    const closeEmergencyEventButton = emergencyEventModal ? emergencyEventModal.querySelector('.close-button') : null;
    console.log('应急事件模态框关闭按钮:', closeEmergencyEventButton);

    // 应急物资模态框元素
    const emergencySuppliesModal = document.getElementById('emergency-supplies-modal');
    const closeEmergencySuppliesButton = emergencySuppliesModal ? emergencySuppliesModal.querySelector('.close-button') : null;

    // 救援力量模态框元素
    const rescueForcesModal = document.getElementById('rescue-forces-modal');
    const closeRescueForcesButton = rescueForcesModal ? rescueForcesModal.querySelector('.close-button') : null;

    // 检查URL中是否有锚点，如果有则切换到对应的标签页
    if (window.location.hash) {
        const hash = window.location.hash.substring(1); // 去掉#号
        const targetTabButton = document.querySelector(`.tab-button[data-tab="${hash}"]`);
        if (targetTabButton) {
            targetTabButton.click();
        }
    }

    // 通知模态框元素
    const notifyModal = document.getElementById('notify-modal');
    const notifyCloseButton = notifyModal.querySelector('.notify-close-button');
    const notifyWarningName = notifyModal.querySelector('.notify-warning-name');
    const notifyWarningLevel = notifyModal.querySelector('.notify-warning-level');
    const notifyUnitsList = notifyModal.querySelector('.notify-units-list');
    const notifyMessage = document.getElementById('notify-message');
    const selectAllUnitsButton = document.getElementById('select-all-units');
    const deselectAllUnitsButton = document.getElementById('deselect-all-units');
    const sendNotifyButton = document.getElementById('send-notify');
    const cancelNotifyButton = document.getElementById('cancel-notify');

    // --- 标签页切换逻辑 ---
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            tabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            tabContents.forEach(content => content.classList.remove('active'));
            const targetTab = button.getAttribute('data-tab');
            const activeContent = document.getElementById(`${targetTab}-content`);
            if (activeContent) {
                activeContent.classList.add('active');

                // 重新初始化树形结构
                if (targetTab === 'risk-map') {
                    // 初始化所有树形结构
                    document.querySelectorAll('.collapsible-tree ul').forEach(ul => {
                        ul.style.display = 'none';
                    });
                }
            }
        });
    });

    // --- 风险隐患点和在建项目标签卡切换逻辑 ---
    resourceTabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 切换按钮激活状态
            resourceTabButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // 切换内容显示
            resourceTabContents.forEach(content => content.classList.remove('active'));
            const targetTab = button.getAttribute('data-tab');
            const activeContent = document.getElementById(`${targetTab}-content`);
            if (activeContent) {
                activeContent.classList.add('active');

                // 如果切换到风险隐患点标签，重新初始化树形结构
                if (targetTab === 'risk-hazards') {
                    // 初始化树形结构
                    document.querySelectorAll('.risk-type-tree ul').forEach(ul => {
                        ul.style.display = 'none';
                    });
                }
            }

            // 根据选择的标签卡显示/隐藏对应的标记点
            updateMarkerVisibility();
        });
    });

    // --- 风险隐患点和在建项目筛选逻辑 ---
    // 直接添加树形结构展开/折叠功能
    document.addEventListener('click', function(event) {
        // 检查点击的是否是树形结构的展开/折叠按钮
        if (event.target.classList.contains('tree-toggler')) {
            // 阻止默认行为和事件冒泡
            event.preventDefault();
            event.stopPropagation();

            // 获取父级li元素和子级ul元素
            const parentLi = event.target.parentElement;
            const childUl = parentLi.querySelector('ul');

            if (childUl) {
                // 切换显示状态
                if (childUl.style.display === 'none' || childUl.style.display === '') {
                    childUl.style.display = 'block';
                    event.target.textContent = '-';
                } else {
                    childUl.style.display = 'none';
                    event.target.textContent = '+';
                }
            }
        }
    });

    // 初始化所有树形结构
    document.querySelectorAll('.collapsible-tree ul').forEach(ul => {
        ul.style.display = 'none';
    });

    // 初始化风险类型树形结构
    document.querySelectorAll('.risk-type-tree ul').forEach(ul => {
        ul.style.display = 'none';
    });

    // 风险隐患类型筛选
    const riskTypeCheckboxes = document.querySelectorAll('input[name="risk-type"]');
    riskTypeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            // 处理父子级联选择
            const parentLi = checkbox.closest('li');
            if (parentLi) {
                const childCheckboxes = parentLi.querySelectorAll('ul input[type="checkbox"]');
                childCheckboxes.forEach(childCb => {
                    childCb.checked = checkbox.checked;
                });
            }

            updateMarkerVisibility();
        });
    });

    // 风险等级下拉框
    const riskLevelSelect = document.getElementById('risk-level-select');
    if (riskLevelSelect) {
        riskLevelSelect.addEventListener('change', updateMarkerVisibility);
    }

    // 项目类型下拉框
    const projectTypeSelect = document.getElementById('project-type-select');
    if (projectTypeSelect) {
        projectTypeSelect.addEventListener('change', updateMarkerVisibility);
    }

    // 项目状态下拉框
    const projectStatusSelect = document.getElementById('project-status-select');
    if (projectStatusSelect) {
        projectStatusSelect.addEventListener('change', updateMarkerVisibility);
    }

    // 项目风险状态下拉框
    const projectRiskSelect = document.getElementById('project-risk-select');
    if (projectRiskSelect) {
        projectRiskSelect.addEventListener('change', updateMarkerVisibility);
    }

    // 项目风险等级下拉框
    const projectRiskLevelSelect = document.getElementById('project-risk-level-select');
    if (projectRiskLevelSelect) {
        projectRiskLevelSelect.addEventListener('change', updateMarkerVisibility);
    }

    // 项目整改措施下拉框
    const projectMeasureSelect = document.getElementById('project-measure-select');
    if (projectMeasureSelect) {
        projectMeasureSelect.addEventListener('change', updateMarkerVisibility);
    }

    // 全选按钮逻辑
    const resTypeAll = document.getElementById('res-type-all');
    if (resTypeAll) {
        resTypeAll.addEventListener('change', () => {
            const isChecked = resTypeAll.checked;

            // 更新所有风险隐患类型复选框
            riskTypeCheckboxes.forEach(cb => { cb.checked = isChecked; });

            // 更新下拉框为"所有"选项
            if (riskLevelSelect) riskLevelSelect.value = 'all';
            if (projectTypeSelect) projectTypeSelect.value = 'all';
            if (projectStatusSelect) projectStatusSelect.value = 'all';
            if (projectRiskSelect) projectRiskSelect.value = 'all';
            if (projectRiskLevelSelect) projectRiskLevelSelect.value = 'all';
            if (projectMeasureSelect) projectMeasureSelect.value = 'all';

            updateMarkerVisibility();
        });
    }

    // 项目搜索功能
    const projectSearchBtn = document.getElementById('project-search-btn');
    const projectSearchInput = document.getElementById('project-search-input');

    projectSearchBtn.addEventListener('click', () => {
        const searchTerm = projectSearchInput.value.trim().toLowerCase();

        // 根据搜索词过滤项目标记点
        const projectMarkers = document.querySelectorAll('.project-marker');
        projectMarkers.forEach(marker => {
            const markerText = marker.textContent.toLowerCase();
            if (searchTerm === '' || markerText.includes(searchTerm)) {
                marker.classList.remove('search-hidden');
            } else {
                marker.classList.add('search-hidden');
            }
        });
    });

    // 回车键触发搜索
    projectSearchInput.addEventListener('keyup', (event) => {
        if (event.key === 'Enter') {
            projectSearchBtn.click();
        }
    });

    // 清空搜索框时恢复所有项目标记点显示
    projectSearchInput.addEventListener('input', () => {
        if (projectSearchInput.value.trim() === '') {
            document.querySelectorAll('.project-marker.search-hidden').forEach(marker => {
                marker.classList.remove('search-hidden');
            });
        }
    });

    // 更新标记点可见性的函数
    function updateMarkerVisibility() {
        // 获取当前激活的资源标签卡
        const activeResourceTab = document.querySelector('.resource-tab-button.active');
        if (!activeResourceTab) return;

        const activeTabId = activeResourceTab.getAttribute('data-tab');

        // 处理风险隐患点可见性
        const riskMarkers = document.querySelectorAll('.risk-marker');
        riskMarkers.forEach(marker => {
            if (activeTabId === 'risk-hazards') {
                // 检查风险类型和等级筛选
                const riskType = marker.getAttribute('data-risk-type');
                const riskLevel = marker.getAttribute('data-risk-level');

                // 检查风险类型是否被选中
                const riskTypeCheckbox = document.getElementById(`risk-type-${riskType}`);
                const typeChecked = riskTypeCheckbox ? riskTypeCheckbox.checked : true;

                // 检查风险等级是否匹配
                const riskLevelSelect = document.getElementById('risk-level-select');
                let levelMatched = true;
                if (riskLevelSelect) {
                    const selectedLevel = riskLevelSelect.value;
                    levelMatched = selectedLevel === 'all' || selectedLevel === riskLevel;
                }

                // 检查按单位和按路段筛选
                const unitFiltered = checkUnitAndRoadFilters(marker);

                marker.style.display = (typeChecked && levelMatched && unitFiltered) ? 'flex' : 'none';
            } else {
                // 如果不是风险隐患点标签卡，则隐藏所有风险点
                marker.style.display = 'none';
            }
        });

        // 处理在建项目可见性
        const projectMarkers = document.querySelectorAll('.project-marker');
        projectMarkers.forEach(marker => {
            if (activeTabId === 'construction-projects' && !marker.classList.contains('search-hidden')) {
                // 检查项目类型、状态、风险状态等筛选
                const projectType = marker.getAttribute('data-project-type');
                const projectStatus = marker.getAttribute('data-project-status');
                const hasRisk = marker.getAttribute('data-has-risk');
                const riskLevel = marker.getAttribute('data-risk-level');

                // 检查项目类型是否匹配
                const projectTypeSelect = document.getElementById('project-type-select');
                let typeMatched = true;
                if (projectTypeSelect) {
                    const selectedType = projectTypeSelect.value;
                    typeMatched = selectedType === 'all' || selectedType === projectType;
                }

                // 检查项目状态是否匹配
                const projectStatusSelect = document.getElementById('project-status-select');
                let statusMatched = true;
                if (projectStatusSelect) {
                    const selectedStatus = projectStatusSelect.value;
                    statusMatched = selectedStatus === 'all' || selectedStatus === projectStatus;
                }

                // 检查风险状态是否匹配
                const projectRiskSelect = document.getElementById('project-risk-select');
                let riskMatched = true;
                if (projectRiskSelect) {
                    const selectedRisk = projectRiskSelect.value;
                    riskMatched = selectedRisk === 'all' ||
                                 (selectedRisk === 'yes' && hasRisk === 'yes') ||
                                 (selectedRisk === 'no' && hasRisk === 'no');
                }

                // 检查风险等级是否匹配
                let levelMatched = true;
                if (hasRisk === 'yes' && riskLevel) {
                    const projectRiskLevelSelect = document.getElementById('project-risk-level-select');
                    if (projectRiskLevelSelect) {
                        const selectedRiskLevel = projectRiskLevelSelect.value;
                        levelMatched = selectedRiskLevel === 'all' || selectedRiskLevel === riskLevel;
                    }
                }

                // 检查整改措施是否匹配
                const projectMeasureSelect = document.getElementById('project-measure-select');
                let measureMatched = true;
                if (projectMeasureSelect) {
                    const selectedMeasure = projectMeasureSelect.value;
                    measureMatched = selectedMeasure === 'all' ||
                                    (selectedMeasure === 'yes' && marker.getAttribute('data-has-measure') === 'yes') ||
                                    (selectedMeasure === 'no' && marker.getAttribute('data-has-measure') === 'no');
                }

                // 检查按单位和按路段筛选
                const unitFiltered = checkUnitAndRoadFilters(marker);

                marker.style.display = (typeMatched && statusMatched && riskMatched && levelMatched && measureMatched && unitFiltered) ? 'flex' : 'none';
            } else {
                // 如果不是在建项目标签卡，则隐藏所有项目点
                marker.style.display = 'none';
            }
        });
    }

    // 检查按单位和按路段筛选
    function checkUnitAndRoadFilters(marker) {
        // 获取当前激活的筛选标签页
        const activeFilterTab = document.querySelector('.filter-tab-button.active');
        if (!activeFilterTab) return true;

        const activeFilterTabId = activeFilterTab.getAttribute('data-tab');

        // 检查是否有任何筛选条件被选中
        let anyFilterChecked = false;

        if (activeFilterTabId === 'unit') {
            // 按单位筛选
            const unitCheckboxes = document.querySelectorAll('#filter-by-unit-content input[type="checkbox"]:checked');
            if (unitCheckboxes.length === 0) return true; // 如果没有选中任何单位，则显示所有标记点

            anyFilterChecked = true;

            // 检查标记点是否属于选中的单位
            const markerUnit = marker.getAttribute('data-unit');
            if (!markerUnit) return false;

            for (const checkbox of unitCheckboxes) {
                if (markerUnit.includes(checkbox.value)) {
                    return true;
                }
            }
        } else if (activeFilterTabId === 'road') {
            // 按路段筛选
            const roadCheckboxes = document.querySelectorAll('#filter-by-road-content input[type="checkbox"]:checked');
            if (roadCheckboxes.length === 0) return true; // 如果没有选中任何路段，则显示所有标记点

            anyFilterChecked = true;

            // 检查标记点是否属于选中的路段
            const markerRoad = marker.getAttribute('data-road');
            if (!markerRoad) return false;

            for (const checkbox of roadCheckboxes) {
                if (markerRoad.includes(checkbox.value)) {
                    return true;
                }
            }
        }

        return !anyFilterChecked; // 如果没有任何筛选条件被选中，则显示所有标记点
    }

    // 初始化标记点可见性
    updateMarkerVisibility();

    // --- 关键字搜索功能 ---
    // 应急事件搜索
    const eventSearchBtn = document.getElementById('event-search-btn');
    const eventSearchInput = document.getElementById('event-search-input');

    if (eventSearchBtn && eventSearchInput) {
        eventSearchBtn.addEventListener('click', () => {
            const searchTerm = eventSearchInput.value.trim().toLowerCase();

            // 根据搜索词过滤事件标记点
            const eventMarkers = document.querySelectorAll('.event-marker');
            eventMarkers.forEach(marker => {
                // 获取事件相关属性进行搜索
                const eventType = marker.getAttribute('data-event-type') || '';
                const eventLevel = marker.getAttribute('data-event-level') || '';
                const eventStatus = marker.getAttribute('data-event-status') || '';
                const searchText = `${eventType} ${eventLevel} ${eventStatus}`.toLowerCase();

                if (searchTerm === '' || searchText.includes(searchTerm)) {
                    marker.classList.remove('search-hidden');
                } else {
                    marker.classList.add('search-hidden');
                }
            });
        });

        // 回车键触发搜索
        eventSearchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                eventSearchBtn.click();
            }
        });

        // 清空搜索框时恢复所有事件标记点显示
        eventSearchInput.addEventListener('input', () => {
            if (eventSearchInput.value.trim() === '') {
                document.querySelectorAll('.event-marker.search-hidden').forEach(marker => {
                    marker.classList.remove('search-hidden');
                });
            }
        });
    }

    // 应急物资搜索
    const suppliesSearchBtn = document.getElementById('supplies-search-btn');
    const suppliesSearchInput = document.getElementById('supplies-search-input');

    if (suppliesSearchBtn && suppliesSearchInput) {
        suppliesSearchBtn.addEventListener('click', () => {
            const searchTerm = suppliesSearchInput.value.trim().toLowerCase();

            // 根据搜索词过滤物资标记点
            const supplyMarkers = document.querySelectorAll('.supply-marker');
            supplyMarkers.forEach(marker => {
                // 获取物资相关属性进行搜索
                const suppliesType = marker.getAttribute('data-supplies-type') || '';
                const suppliesStatus = marker.getAttribute('data-supplies-status') || '';
                const searchText = `${suppliesType} ${suppliesStatus}`.toLowerCase();

                if (searchTerm === '' || searchText.includes(searchTerm)) {
                    marker.classList.remove('search-hidden');
                } else {
                    marker.classList.add('search-hidden');
                }
            });
        });

        // 回车键触发搜索
        suppliesSearchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                suppliesSearchBtn.click();
            }
        });

        // 清空搜索框时恢复所有物资标记点显示
        suppliesSearchInput.addEventListener('input', () => {
            if (suppliesSearchInput.value.trim() === '') {
                document.querySelectorAll('.supply-marker.search-hidden').forEach(marker => {
                    marker.classList.remove('search-hidden');
                });
            }
        });
    }

    // 救援力量搜索
    const rescueSearchBtn = document.getElementById('rescue-search-btn');
    const rescueSearchInput = document.getElementById('rescue-search-input');

    if (rescueSearchBtn && rescueSearchInput) {
        rescueSearchBtn.addEventListener('click', () => {
            const searchTerm = rescueSearchInput.value.trim().toLowerCase();

            // 根据搜索词过滤救援力量标记点
            const rescueMarkers = document.querySelectorAll('.rescue-marker');
            rescueMarkers.forEach(marker => {
                // 获取救援力量相关属性进行搜索
                const rescueType = marker.getAttribute('data-rescue-type') || '';
                const rescueStatus = marker.getAttribute('data-rescue-status') || '';
                const searchText = `${rescueType} ${rescueStatus}`.toLowerCase();

                if (searchTerm === '' || searchText.includes(searchTerm)) {
                    marker.classList.remove('search-hidden');
                } else {
                    marker.classList.add('search-hidden');
                }
            });
        });

        // 回车键触发搜索
        rescueSearchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                rescueSearchBtn.click();
            }
        });

        // 清空搜索框时恢复所有救援力量标记点显示
        rescueSearchInput.addEventListener('input', () => {
            if (rescueSearchInput.value.trim() === '') {
                document.querySelectorAll('.rescue-marker.search-hidden').forEach(marker => {
                    marker.classList.remove('search-hidden');
                });
            }
        });
    }

    // --- 应急一张图标点点击事件 ---
    // 使用事件委托，确保即使是动态添加的标点也能响应点击事件
    document.addEventListener('click', (event) => {
        // 检查点击的元素是否是标点或其子元素
        const marker = event.target.closest('.map-marker');
        if (!marker) return; // 如果不是标点，直接返回

        const markerId = marker.getAttribute('data-id');
        const markerType = marker.getAttribute('data-type');

        console.log('点击了标点:', markerId, markerType); // 调试信息

        // 检查当前活动的标签页
        const monitoringWarningTab = document.querySelector('.tab-button[data-tab="monitoring-warning"]');
        const roadNetworkTab = document.querySelector('.tab-button[data-tab="road-network"]');
        const isMonitoringWarningActive = monitoringWarningTab && monitoringWarningTab.classList.contains('active');
        const isRoadNetworkActive = roadNetworkTab && roadNetworkTab.classList.contains('active');

        // 如果是路网运行标签页中的拥堵标点，使用专用的拥堵路段模态框
        if (isRoadNetworkActive && markerType === 'congestion') {
            console.log('路网运行标签页中的拥堵标点，打开拥堵路段模态框');
            const trafficJamModal = document.getElementById('traffic-jam-modal');
            if (trafficJamModal) {
                // 强制设置模态框样式
                trafficJamModal.style.cssText = `
                    display: flex !important;
                    position: fixed !important;
                    z-index: 10000 !important;
                    left: 0 !important;
                    top: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    overflow: auto !important;
                    background-color: rgba(0, 0, 0, 0.5) !important;
                    align-items: center !important;
                    justify-content: center !important;
                `;
                
                console.log('事件委托 - 模态框元素:', trafficJamModal);
                console.log('事件委托 - 模态框当前样式:', trafficJamModal.style.cssText);
                console.log('事件委托 - 模态框计算样式:', window.getComputedStyle(trafficJamModal).display);
                
                // 检查模态框内容
                const modalContent = trafficJamModal.querySelector('.modal-content');
                if (modalContent) {
                    console.log('事件委托 - 模态框内容元素:', modalContent);
                    console.log('事件委托 - 模态框内容样式:', window.getComputedStyle(modalContent).display);
                    // 强制设置模态框内容样式
                    modalContent.style.cssText = `
                        max-width: 1200px !important;
                        max-height: 90vh !important;
                        overflow-y: auto !important;
                        background-color: #2c3e50 !important;
                        color: #ecf0f1 !important;
                        margin: auto !important;
                        border-radius: 8px !important;
                        display: block !important;
                        position: relative !important;
                    `;
                    console.log('事件委托 - 已强制设置模态框内容样式');
                    
                    // 额外的调试信息
                    console.log('事件委托 - 模态框位置信息:', trafficJamModal.getBoundingClientRect());
                    console.log('事件委托 - 模态框是否可见:', trafficJamModal.offsetWidth > 0 && trafficJamModal.offsetHeight > 0);
                    console.log('事件委托 - 模态框父元素:', trafficJamModal.parentElement);
                    
                    // 检查模态框是否在正确的父元素下
                    if (trafficJamModal.parentElement !== document.body) {
                        console.log('事件委托 - 模态框不在body下，正在移动到body');
                        document.body.appendChild(trafficJamModal);
                        console.log('事件委托 - 已将模态框移动到body下');
                    }
                    
                    // 尝试滚动到模态框位置
                    trafficJamModal.scrollIntoView();
                    

                } else {
                    console.error('事件委托 - 找不到模态框内容元素 .modal-content');
                }
                
                // 填充模态框数据
                const congestionLevel = marker.getAttribute('data-congestion-level');
                let jamData = {
                    sectionCode: 'G72-K1500+200',
                    stakeRange: 'K1500+200 - K1501+500',
                    level: '严重拥堵',
                    levelColor: '#e74c3c',
                    startTime: '2024-05-26 14:30',
                    duration: '已持续2小时30分钟',
                    trafficFlow: '0辆/小时（交通中断）',
                    avgSpeed: '0km/h（交通中断）'
                };

                // 根据拥堵等级调整数据
                if (congestionLevel === 'moderate') {
                    jamData = {
                        sectionCode: 'G72-K1485+500',
                        stakeRange: 'K1485+500 - K1486+200',
                        level: '中度拥堵',
                        levelColor: '#f39c12',
                        startTime: '2024-05-26 16:15',
                        duration: '已持续45分钟',
                        trafficFlow: '850辆/小时',
                        avgSpeed: '25km/h'
                    };
                } else if (congestionLevel === 'light') {
                    jamData = {
                        sectionCode: 'G72-K1520+300',
                        stakeRange: 'K1520+300 - K1520+800',
                        level: '轻度拥堵',
                        levelColor: '#f1c40f',
                        startTime: '2024-05-26 17:00',
                        duration: '已持续20分钟',
                        trafficFlow: '1200辆/小时',
                        avgSpeed: '45km/h'
                    };
                } else if (congestionLevel === 'severe-soon') {
                    jamData = {
                        sectionCode: 'G72-K1495+100',
                        stakeRange: 'K1495+100 - K1495+600',
                        level: '即将严重拥堵',
                        levelColor: '#e67e22',
                        startTime: '2024-05-26 17:30',
                        duration: '预警发布15分钟',
                        trafficFlow: '1800辆/小时',
                        avgSpeed: '35km/h'
                    };
                }

                // 更新模态框内容
                const titleElement = document.getElementById('traffic-jam-title');
                if (titleElement) {
                    titleElement.textContent = '拥堵路段详情: G72泉南高速吴家屯隧道路段';
                }
                
                const sectionCodeElement = document.getElementById('jam-section-code');
                if (sectionCodeElement) {
                    sectionCodeElement.textContent = jamData.sectionCode;
                }
                
                const stakeRangeElement = document.getElementById('jam-stake-range');
                if (stakeRangeElement) {
                    stakeRangeElement.textContent = jamData.stakeRange;
                }
                
                const levelElement = document.getElementById('jam-level');
                if (levelElement) {
                    levelElement.textContent = jamData.level;
                    levelElement.style.backgroundColor = jamData.levelColor;
                    levelElement.style.color = 'white';
                    levelElement.style.padding = '3px 10px';
                    levelElement.style.borderRadius = '4px';
                    levelElement.style.fontSize = '14px';
                }
                
                const startTimeElement = document.getElementById('jam-start-time');
                if (startTimeElement) {
                    startTimeElement.textContent = jamData.startTime;
                }
                
                const durationElement = document.getElementById('jam-duration');
                if (durationElement) {
                    durationElement.textContent = jamData.duration;
                }
                
                const trafficFlowElement = document.getElementById('jam-traffic-flow');
                if (trafficFlowElement) {
                    trafficFlowElement.textContent = jamData.trafficFlow;
                }
                
                const avgSpeedElement = document.getElementById('jam-avg-speed');
                if (avgSpeedElement) {
                    avgSpeedElement.textContent = jamData.avgSpeed;
                }
            }
            return;
        }

        // 阻止事件冒泡，避免触发原有的事件处理器
        event.stopPropagation();

        // 如果在应急一张图中，根据标点类型使用对应的专用模态框
        if (isMonitoringWarningActive) {
            if (markerType === 'event') {
                // 应急事件标点 - 使用完整的应急事件模态框
                if (emergencyEventModal) {
                    // 设置模态框样式
                    emergencyEventModal.style.cssText = `
                        display: flex !important;
                        position: fixed !important;
                        z-index: 10000 !important;
                        left: 0 !important;
                        top: 0 !important;
                        width: 100% !important;
                        height: 100% !important;
                        overflow: auto !important;
                        background-color: rgba(0, 0, 0, 0.5) !important;
                        align-items: center !important;
                        justify-content: center !important;
                    `;
                    
                    console.log('打开完整的应急事件专用模态框');
                    
                    // 确保其他模态框不会显示
                    if (modal) modal.style.display = 'none';
                    if (emergencySuppliesModal) emergencySuppliesModal.style.display = 'none';
                    if (rescueForcesModal) rescueForcesModal.style.display = 'none';
                }
            } else if (markerType === 'supplies') {
                // 应急物资标点 - 创建动态模态框
                console.log('点击了应急物资标点，创建动态模态框');
                
                // 移除可能存在的旧模态框
                const existingModal = document.getElementById('dynamic-supplies-modal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                // 创建新的应急物资模态框
                const dynamicModal = document.createElement('div');
                dynamicModal.id = 'dynamic-supplies-modal';
                dynamicModal.style.cssText = `
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background-color: rgba(0, 0, 0, 0.5) !important;
                    z-index: 10000 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                `;
                
                // 创建模态框内容
                const modalContent = document.createElement('div');
                modalContent.style.cssText = `
                    background-color: #2c3e50 !important;
                    color: #ecf0f1 !important;
                    max-width: 800px !important;
                    width: 90% !important;
                    max-height: 80vh !important;
                    overflow-y: auto !important;
                    border-radius: 8px !important;
                    position: relative !important;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
                `;
                
                modalContent.innerHTML = `
                    <div style="background-color: #34495e; border-bottom: 1px solid #4a5f7a; display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
                        <h3 style="margin: 0; font-size: 18px; color: #ecf0f1;">应急物资详情: 南宁市西乡塘区应急物资仓库</h3>
                        <span onclick="document.getElementById('dynamic-supplies-modal').remove()" style="color: #ecf0f1; font-size: 24px; cursor: pointer; line-height: 1;">&times;</span>
                    </div>
                    <div style="padding: 20px;">
                        <!-- 基本信息 -->
                        <div style="margin-bottom: 20px;">
                            <h5 style="margin-top: 0; margin-bottom: 10px; color: #61dafb; font-size: 16px; border-bottom: 1px solid #3a3f4b; padding-bottom: 5px;">基本信息</h5>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px; color: #ecf0f1;">
                                <div><strong>仓库编号:</strong> NN-WC-001</div>
                                <div><strong>仓库类型:</strong> 综合应急物资仓库</div>
                                <div><strong>管理单位:</strong> 南宁市交通运输局</div>
                                <div><strong>联系电话:</strong> 0771-12345678</div>
                                <div style="grid-column: span 2;"><strong>仓库地址:</strong> 南宁市西乡塘区友爱北路123号</div>
                            </div>
                        </div>

                        <!-- 物资列表 -->
                        <div>
                            <h5 style="margin-bottom: 10px; color: #61dafb; font-size: 16px; border-bottom: 1px solid #3a3f4b; padding-bottom: 5px;">物资列表</h5>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; background-color: rgba(40, 44, 52, 0.8); color: #e6e6e6;">
                                <thead>
                                    <tr style="background-color: rgba(30, 60, 120, 0.8);">
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">物资类型</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">物资名称</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">型号</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">数量</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">防护用品</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">N95口罩</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">标准型</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">20000个</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                    </tr>
                                    <tr style="background-color: rgba(50, 54, 62, 0.8);">
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">防护用品</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">防护服</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">XL</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">500套</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">救援工具</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">液压剪扩器</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">型号A</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">5台</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                    </tr>
                                    <tr style="background-color: rgba(50, 54, 62, 0.8);">
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">救援工具</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">应急照明灯组</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">型号B</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">20套</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #ff9800;">待检修</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">生活保障</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">瓶装饮用水</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">500ml</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">100箱</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
                
                dynamicModal.appendChild(modalContent);
                document.body.appendChild(dynamicModal);
                
                console.log('动态应急物资模态框已创建并显示');
                
                // 确保其他模态框不会显示
                if (modal) modal.style.display = 'none';
                if (emergencyEventModal) emergencyEventModal.style.display = 'none';
                if (rescueForcesModal) rescueForcesModal.style.display = 'none';
            } else if (markerType === 'rescue') {
                // 救援力量标点 - 创建动态模态框
                console.log('点击了救援力量标点，创建动态模态框');
                
                // 移除可能存在的旧模态框
                const existingModal = document.getElementById('dynamic-rescue-modal');
                if (existingModal) {
                    existingModal.remove();
                }
                
                // 创建新的救援力量模态框
                const dynamicModal = document.createElement('div');
                dynamicModal.id = 'dynamic-rescue-modal';
                dynamicModal.style.cssText = `
                    position: fixed !important;
                    top: 0 !important;
                    left: 0 !important;
                    width: 100% !important;
                    height: 100% !important;
                    background-color: rgba(0, 0, 0, 0.5) !important;
                    z-index: 10000 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                `;
                
                // 创建模态框内容
                const modalContent = document.createElement('div');
                modalContent.style.cssText = `
                    background-color: #2c3e50 !important;
                    color: #ecf0f1 !important;
                    max-width: 800px !important;
                    width: 90% !important;
                    max-height: 80vh !important;
                    overflow-y: auto !important;
                    border-radius: 8px !important;
                    position: relative !important;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
                `;
                
                modalContent.innerHTML = `
                    <div style="background-color: #34495e; border-bottom: 1px solid #4a5f7a; display: flex; justify-content: space-between; align-items: center; padding: 15px 20px;">
                        <h3 style="margin: 0; font-size: 18px; color: #ecf0f1;">救援力量详情: 柳州市消防救援支队柳江大队</h3>
                        <span onclick="document.getElementById('dynamic-rescue-modal').remove()" style="color: #ecf0f1; font-size: 24px; cursor: pointer; line-height: 1;">&times;</span>
                    </div>
                    <div style="padding: 20px;">
                        <!-- 基本信息 -->
                        <div style="margin-bottom: 20px;">
                            <h5 style="margin-top: 0; margin-bottom: 10px; color: #61dafb; font-size: 16px; border-bottom: 1px solid #3a3f4b; padding-bottom: 5px;">基本信息</h5>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px; color: #ecf0f1;">
                                <div><strong>地址:</strong> 桂林市柳江区消防路18号</div>
                                <div><strong>距离:</strong> 8公里</div>
                                <div><strong>人数:</strong> 35人</div>
                                <div><strong>专业方向:</strong> 消防救援、危化品处置</div>
                                <div><strong>负责人:</strong> 刘大队长</div>
                                <div><strong>联系方式:</strong> 0772-7212119</div>
                            </div>
                        </div>

                        <!-- 装备配置 -->
                        <div style="margin-bottom: 20px;">
                            <h5 style="margin-bottom: 10px; color: #61dafb; font-size: 16px; border-bottom: 1px solid #3a3f4b; padding-bottom: 5px;">装备配置</h5>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; background-color: rgba(40, 44, 52, 0.8); color: #e6e6e6;">
                                <thead>
                                    <tr style="background-color: rgba(30, 60, 120, 0.8);">
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">装备名称</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">数量</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">状态</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">用途</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">挖掘机</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">2台</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">清障作业</td>
                                    </tr>
                                    <tr style="background-color: rgba(50, 54, 62, 0.8);">
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">装载机</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">1台</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">物料装载</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">排水抢险车</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">2台</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">排水抢险</td>
                                    </tr>
                                    <tr style="background-color: rgba(50, 54, 62, 0.8);">
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">运输车</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">3台</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">物资运输</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">消防车</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">5台</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">良好</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">消防救援</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 物资储备 -->
                        <div>
                            <h5 style="margin-bottom: 10px; color: #61dafb; font-size: 16px; border-bottom: 1px solid #3a3f4b; padding-bottom: 5px;">物资储备</h5>
                            <table style="width: 100%; border-collapse: collapse; margin-top: 10px; background-color: rgba(40, 44, 52, 0.8); color: #e6e6e6;">
                                <thead>
                                    <tr style="background-color: rgba(30, 60, 120, 0.8);">
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">物资名称</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">数量</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">状态</th>
                                        <th style="padding: 8px; text-align: left; border: 1px solid #3a3f4b; font-size: 14px; color: white;">用途</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">砂石料</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">50吨</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">充足</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">填筑加固</td>
                                    </tr>
                                    <tr style="background-color: rgba(50, 54, 62, 0.8);">
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">编织袋</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">2000个</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">充足</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">装土筑堤</td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">水泥</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">20吨</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">充足</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">加固修补</td>
                                    </tr>
                                    <tr style="background-color: rgba(50, 54, 62, 0.8);">
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">泡沫灭火剂</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">5吨</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px; color: #4caf50;">充足</td>
                                        <td style="padding: 8px; border: 1px solid #3a3f4b; font-size: 14px;">消防灭火</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
                
                dynamicModal.appendChild(modalContent);
                document.body.appendChild(dynamicModal);
                
                console.log('动态救援力量模态框已创建并显示');
                
                // 确保其他模态框不会显示
                if (modal) modal.style.display = 'none';
                if (emergencyEventModal) emergencyEventModal.style.display = 'none';
                if (emergencySuppliesModal) emergencySuppliesModal.style.display = 'none';
                if (rescueForcesModal) rescueForcesModal.style.display = 'none';
            } else {
                // 其他类型标点使用通用模态框
                if (modal) {
                    modal.style.display = 'block';
                    openModal(markerId);
                    console.log('打开通用详情模态框');
                    // 确保专用模态框不会显示
                    if (emergencyEventModal) emergencyEventModal.style.display = 'none';
                    if (emergencySuppliesModal) emergencySuppliesModal.style.display = 'none';
                    if (rescueForcesModal) rescueForcesModal.style.display = 'none';
                }
            }
        } else {
            // 其他标签页使用通用详情模态框
            if (modal) {
                modal.style.display = 'block';
                openModal(markerId);
                console.log('打开通用详情模态框');
                // 确保专用模态框不会显示
                if (emergencyEventModal) emergencyEventModal.style.display = 'none';
                if (emergencySuppliesModal) emergencySuppliesModal.style.display = 'none';
                if (rescueForcesModal) rescueForcesModal.style.display = 'none';
            }
        }
    });

    // 关闭应急事件模态框
    if (closeEmergencyEventButton && emergencyEventModal) {
        closeEmergencyEventButton.addEventListener('click', () => {
            emergencyEventModal.style.display = 'none';
        });
    }
    
    // 额外绑定应急事件模态框的关闭按钮（通过ID）
    const closeEmergencyEventById = document.getElementById('close-emergency-event');
    if (closeEmergencyEventById && emergencyEventModal) {
        closeEmergencyEventById.addEventListener('click', () => {
            emergencyEventModal.style.display = 'none';
        });
    }

    // 关闭应急物资模态框
    if (closeEmergencySuppliesButton && emergencySuppliesModal) {
        closeEmergencySuppliesButton.addEventListener('click', () => {
            emergencySuppliesModal.style.display = 'none';
        });
    }

    // 关闭救援力量模态框
    if (closeRescueForcesButton && rescueForcesModal) {
        closeRescueForcesButton.addEventListener('click', () => {
            rescueForcesModal.style.display = 'none';
        });
    }

    // 点击模态框外部关闭模态框
    window.addEventListener('click', (event) => {
        if (emergencyEventModal && event.target === emergencyEventModal) {
            emergencyEventModal.style.display = 'none';
        } else if (emergencySuppliesModal && event.target === emergencySuppliesModal) {
            emergencySuppliesModal.style.display = 'none';
        } else if (rescueForcesModal && event.target === rescueForcesModal) {
            rescueForcesModal.style.display = 'none';
        } else if (modal && event.target === modal) {
            modal.style.display = 'none';
        } else if (notifyModal && event.target === notifyModal) {
            notifyModal.style.display = 'none';
        }
    });

    // --- 模态框逻辑 ---
    function openModal(markerId, previousViewData = null) {
        const modalTitle = modal.querySelector('#modal-title');
        const modalBody = modal.querySelector('#modal-body');
        modalBody.innerHTML = '';

        if (previousViewData && previousViewData.id && previousViewData.name) {
            const backButton = document.createElement('button');
            backButton.className = 'back-to-previous-button';
            let buttonText = "返回";
            if (previousViewData.type === 'project') {
                buttonText = `返回在建项目: ${previousViewData.name}`;
            }
            backButton.textContent = buttonText;
            backButton.onclick = () => openModal(previousViewData.id);
            modalBody.appendChild(backButton);
        }

        function createGenericInfoItem(label, value, itemClass = '', labelClass = '', valueClass = '') {
            const item = document.createElement('div');
            item.className = `info-item ${itemClass}`.trim();
            const labelSpan = document.createElement('span');
            labelSpan.className = `info-label ${labelClass}`.trim();
            labelSpan.textContent = label ? label + ":" : "";
            const valueSpan = document.createElement('span');
            valueSpan.className = `info-value ${valueClass}`.trim();
            valueSpan.textContent = value;
            if (label) item.appendChild(labelSpan);
            item.appendChild(valueSpan);
            return item;
        }

        // 定义通用的信息字段创建函数
        function createInfoField(label, value, fullWidth = false) {
            const field = document.createElement('div');
            field.className = fullWidth ? 'info-field full-width' : 'info-field';

            const labelElement = document.createElement('span');
            labelElement.className = 'info-label';
            labelElement.textContent = label + '：';

            const valueElement = document.createElement('span');
            valueElement.className = 'info-value';
            valueElement.textContent = value;

            field.appendChild(labelElement);
            field.appendChild(valueElement);

            return field;
        }

        if (markerId.startsWith('risk')) {
            const detailedRiskData = {
                id: markerId,
                title: "泉南高速吴家屯隧道（柳州端）洞口上方山体塌方隐患",
                provincialUnit: { name: "广西高速公路发展中心", contact: "李主管 - 181xxxx1001" },
                reviewUnit: { name: "广西高速公路发展中心桂林分中心", contact: "王科长 - 181xxxx1002" },
                investigationUnit: { name: "泉南高速公路运营公司", contact: "张工程师 - 181xxxx1003" },
                checkCategory: "边坡地质灾害",
                cityDistrict: "桂林市",
                owningUnit: "广西高速公路管理局",
                roadNumber: "G72泉南高速",
                stakeNumber: "K1500+200",
                riskType: "山体滑坡、塌方",
                riskLevel: "重大（Ⅰ级）",
                isHazard: "是",
                hasTakenMeasures: "否",
                riskPointInfo: {
                    description: "吴家屯隧道出口（柳州端）洞口上方山体因多日连续强降雨，导致山体失稳，排水不畅，存在严重塌方隐患。",
                    plannedMeasures: "进行勘察与治理设计，拟设置警示桩，采用锚杆 + 防护网加固，改善排水系统。",
                    photos: [{ name: "塌方隐患点远景图", url: "#" }, { name: "塌方隐患点近景图", url: "#" }],
                    measureAttachments: "无"
                },
                rectificationInfo: {
                    isRectified: "否",
                    rectificationCompletionTime: "未记录",
                    requiredCompletionTime: "2023-06-30",
                    rectificationMeasures: "已完成地质勘察和治理方案设计。拟采用锚杆结合主动防护网进行加固，并设置警示桩，同时改善排水系统，防止雨水渗入山体。",
                    attachments: [
                        { name: "地质勘查报告.pdf", url: "#" },
                        { name: "治理设计图.pdf", url: "#" }
                    ]
                },
                nearbySupplies: {
                    warehouse: "桂林市应急物资储备库 (约15km)",
                    contactPerson: "储备库主任 - 181xxxx3001",
                    list: [
                        { item: "警示桩 (标准型)", quantity: 100, status: "良好" },
                        { item: "锚杆 (3m)", quantity: 200, status: "良好" },
                        { item: "主动防护网 (SNS)", quantity: "500平方米", status: "良好" },
                        { item: "吸油毡", quantity: "200张", status: "良好" }
                    ]
                },
                nearbyRescueTeams: [
                    { name: "桂林市应急救援支队 (约15km)", contact: "救援队长 - 181xxxx2001", equipment: "破拆工具、生命探测仪", specialization: "人员搜救、隧道事故处置" },
                    { name: "柳州市消防救援支队 (约20km)", contact: "消防队长 - 181xxxx2002", equipment: "消防车、泡沫灭火设备", specialization: "危险品处置、火灾扑救" },
                    { name: "泉南高速公路运营公司应急队 (约5km)", contact: "应急队长 - 181xxxx2004", equipment: "清障车, 吊车, 应急抢险车", specialization: "道路清障、交通事故处理" }
                ]
            };
            modalTitle.textContent = `风险隐患详情: ${detailedRiskData.title}`;
            // 参考应急物资模态框样式，创建垂直布局的三个板块
            const contentWrapper = document.createElement('div');
            contentWrapper.className = 'traffic-jam-info';
            contentWrapper.style.display = 'flex';
            contentWrapper.style.flexDirection = 'column';
            contentWrapper.style.gap = '20px';
            
            // 第一个板块：基本信息
            const basicInfoSection = document.createElement('div');
            basicInfoSection.className = 'info-section';
            basicInfoSection.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
            basicInfoSection.style.borderRadius = '8px';
            basicInfoSection.style.padding = '20px';
            
            const basicInfoTitle = document.createElement('h2');
            basicInfoTitle.className = 'section-title';
            basicInfoTitle.textContent = '基本信息';
            basicInfoTitle.style.color = '#3498db';
            basicInfoTitle.style.borderBottom = '2px solid #3498db';
            basicInfoTitle.style.paddingBottom = '5px';
            basicInfoTitle.style.marginBottom = '15px';
            basicInfoTitle.style.fontSize = '20px';
            basicInfoSection.appendChild(basicInfoTitle);

            const basicInfoGrid = document.createElement('div');
            basicInfoGrid.className = 'info-grid';
            basicInfoGrid.style.display = 'grid';
            basicInfoGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
            basicInfoGrid.style.gap = '15px';
            basicInfoGrid.style.color = '#ecf0f1';
            basicInfoGrid.style.fontSize = '16px';

            // 基本信息项（9个字段，3列布局）
            const basicInfoItems = [
                { label: '公路编号', value: 'G72泉南高速' },
                { label: '位置', value: '吴家屯隧道路段' },
                { label: '等级', value: '严重拥堵' },
                { label: '时间', value: '2024-05-26 14:30' },
                { label: '流量', value: '2500车次/小时' },
                { label: '平均速度', value: '15km/h' },
                { label: '拥堵长度', value: '约2.5公里' },
                { label: '预计缓解', value: '45分钟' },
                { label: '影响范围', value: 'K1499-K1502路段' }
            ];

            basicInfoItems.forEach(item => {
                const infoRow = document.createElement('div');
                infoRow.className = 'info-item';
                infoRow.innerHTML = `
                    <div class="info-label" style="color: #95a5a6; margin-bottom: 5px;">${item.label}：</div>
                    <div class="info-value" style="color: #ecf0f1;">${item.value}</div>
                `;
                basicInfoGrid.appendChild(infoRow);
            });

            basicInfoSection.appendChild(basicInfoGrid);
            contentWrapper.appendChild(basicInfoSection);
            const middleInfoContainer = document.createElement('div');
            middleInfoContainer.className = 'middle-info-container';
            const riskPointDiv = document.createElement('div');
            riskPointDiv.className = 'info-column risk-point-panel';
            
            // 添加标题
            const riskPointTitle = document.createElement('div');
            riskPointTitle.className = 'resource-subsection-title';
            riskPointTitle.textContent = '风险点信息';
            riskPointTitle.style.backgroundColor = '#3a4a5d';
            riskPointTitle.style.color = '#fff';
            riskPointTitle.style.padding = '5px 10px';
            riskPointTitle.style.marginBottom = '10px';
            riskPointDiv.appendChild(riskPointTitle);

            // 创建风险点信息卡片
            const riskPointCard = document.createElement('div');
            riskPointCard.className = 'resource-item';
            riskPointCard.style.marginBottom = '15px';
            riskPointCard.style.padding = '10px';
            riskPointCard.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
            riskPointCard.style.borderRadius = '5px';

            // 风险点信息内容
            const riskPointContent = document.createElement('div');
            riskPointContent.style.display = 'grid';
            riskPointContent.style.gridTemplateColumns = '1fr';
            riskPointContent.style.gap = '10px';
            riskPointContent.style.color = '#ecf0f1';
            riskPointContent.style.fontSize = '18px';

            // 描述
            const descriptionDiv = document.createElement('div');
            descriptionDiv.innerHTML = `<strong style="color: #3498db;">描述:</strong><br>${detailedRiskData.riskPointInfo.description}`;
            riskPointContent.appendChild(descriptionDiv);

            // 已（拟）采取的措施
            const riskMeasuresDiv = document.createElement('div');
            riskMeasuresDiv.innerHTML = `<strong style="color: #3498db;">已（拟）采取的措施:</strong><br>${detailedRiskData.riskPointInfo.plannedMeasures}`;
            riskPointContent.appendChild(riskMeasuresDiv);

            // 现场照片/附件
            const riskPhotosDiv = document.createElement('div');
            riskPhotosDiv.innerHTML = `<strong style="color: #3498db;">现场照片/附件:</strong><br>`;
            const riskPhotosLinks = document.createElement('div');
            riskPhotosLinks.style.marginTop = '5px';
            detailedRiskData.riskPointInfo.photos.forEach(p => { 
                const a = document.createElement('a'); 
                a.href = p.url; 
                a.textContent = p.name; 
                a.style.marginRight = '10px';
                a.style.color = '#3498db';
                a.style.textDecoration = 'underline';
                riskPhotosLinks.appendChild(a); 
            });
            riskPhotosDiv.appendChild(riskPhotosLinks);
            riskPointContent.appendChild(riskPhotosDiv);

            // 措施附件
            const riskAttachmentsDiv = document.createElement('div');
            riskAttachmentsDiv.innerHTML = `<strong style="color: #3498db;">措施附件:</strong> ${detailedRiskData.riskPointInfo.measureAttachments}`;
            riskPointContent.appendChild(riskAttachmentsDiv);

            riskPointCard.appendChild(riskPointContent);
            riskPointDiv.appendChild(riskPointCard);
            middleInfoContainer.appendChild(riskPointDiv);
            const rectificationDiv = document.createElement('div');
            rectificationDiv.className = 'info-column rectification-panel';
            
            // 添加标题
            const rectificationTitle = document.createElement('div');
            rectificationTitle.className = 'resource-subsection-title';
            rectificationTitle.textContent = '整改信息';
            rectificationTitle.style.backgroundColor = '#3a4a5d';
            rectificationTitle.style.color = '#fff';
            rectificationTitle.style.padding = '5px 10px';
            rectificationTitle.style.marginBottom = '10px';
            rectificationDiv.appendChild(rectificationTitle);

            // 创建整改信息卡片
            const rectificationCard = document.createElement('div');
            rectificationCard.className = 'resource-item';
            rectificationCard.style.marginBottom = '15px';
            rectificationCard.style.padding = '10px';
            rectificationCard.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
            rectificationCard.style.borderRadius = '5px';

            // 整改信息内容
            const rectificationContent = document.createElement('div');
            rectificationContent.style.display = 'grid';
            rectificationContent.style.gridTemplateColumns = '1fr 1fr';
            rectificationContent.style.gap = '10px';
            rectificationContent.style.color = '#ecf0f1';
            rectificationContent.style.fontSize = '18px';

            // 基本信息（2列布局）
            const isRectifiedDiv = document.createElement('div');
            isRectifiedDiv.innerHTML = `<strong style="color: #3498db;">是否整改:</strong> ${detailedRiskData.rectificationInfo.isRectified}`;
            rectificationContent.appendChild(isRectifiedDiv);

            const completionTimeDiv = document.createElement('div');
            completionTimeDiv.innerHTML = `<strong style="color: #3498db;">整改完成时间:</strong> ${detailedRiskData.rectificationInfo.rectificationCompletionTime}`;
            rectificationContent.appendChild(completionTimeDiv);

            const requiredTimeDiv = document.createElement('div');
            requiredTimeDiv.innerHTML = `<strong style="color: #3498db;">要求完成时间:</strong> ${detailedRiskData.rectificationInfo.requiredCompletionTime}`;
            rectificationContent.appendChild(requiredTimeDiv);

            // 空白占位，保持网格对齐
            const spacerDiv = document.createElement('div');
            rectificationContent.appendChild(spacerDiv);

            rectificationCard.appendChild(rectificationContent);

            // 整改措施（单独一行，全宽）
            const rectMeasuresDiv = document.createElement('div');
            rectMeasuresDiv.style.marginTop = '15px';
            rectMeasuresDiv.style.padding = '10px';
            rectMeasuresDiv.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
            rectMeasuresDiv.style.borderRadius = '5px';
            rectMeasuresDiv.innerHTML = `<strong style="color: #3498db;">整改措施:</strong><br>${detailedRiskData.rectificationInfo.rectificationMeasures}`;
            rectMeasuresDiv.style.color = '#ecf0f1';
            rectMeasuresDiv.style.fontSize = '18px';
            rectificationDiv.appendChild(rectificationCard);
            rectificationDiv.appendChild(rectMeasuresDiv);

            // 对应附件
            const rectAttachmentsDiv = document.createElement('div');
            rectAttachmentsDiv.style.marginTop = '10px';
            rectAttachmentsDiv.style.padding = '10px';
            rectAttachmentsDiv.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
            rectAttachmentsDiv.style.borderRadius = '5px';
            rectAttachmentsDiv.style.color = '#ecf0f1';
            rectAttachmentsDiv.style.fontSize = '18px';
            rectAttachmentsDiv.innerHTML = `<strong style="color: #3498db;">对应附件:</strong><br>`;
            const rectAttachmentsLinks = document.createElement('div');
            rectAttachmentsLinks.style.marginTop = '5px';
            detailedRiskData.rectificationInfo.attachments.forEach(att => { 
                const a = document.createElement('a'); 
                a.href = att.url; 
                a.textContent = att.name; 
                a.style.marginRight = '10px';
                a.style.color = '#3498db';
                a.style.textDecoration = 'underline';
                rectAttachmentsLinks.appendChild(a); 
            });
            rectAttachmentsDiv.appendChild(rectAttachmentsLinks);
            rectificationDiv.appendChild(rectAttachmentsDiv);
            middleInfoContainer.appendChild(rectificationDiv);
            contentWrapper.appendChild(middleInfoContainer);
            const bottomInfoContainer = document.createElement('div');
            bottomInfoContainer.className = 'bottom-info-container';
            const suppliesDivRisk = document.createElement('div');
            suppliesDivRisk.className = 'info-column supplies-panel';
            
            // 添加标题
            const suppliesTitle = document.createElement('div');
            suppliesTitle.className = 'resource-subsection-title';
            suppliesTitle.textContent = '附近应急物资储备';
            suppliesTitle.style.backgroundColor = '#3a4a5d';
            suppliesTitle.style.color = '#fff';
            suppliesTitle.style.padding = '5px 10px';
            suppliesTitle.style.marginBottom = '10px';
            suppliesDivRisk.appendChild(suppliesTitle);

            // 模拟应急物资数据（完整格式，包含详细物资列表）
            const nearbySuppliesData = [
                {
                    name: "桂林市应急物资储备库",
                    location: "约15km",
                    contact: "储备库主任 - 181xxxx3001",
                    items: [
                        { category: "交通设施", name: "警示桩", model: "标准型", quantity: "100个", unit: "个", status: "良好", notes: "反光材质" },
                        { category: "交通设施", name: "反光锥", model: "70cm", quantity: "200个", unit: "个", status: "良好", notes: "橙色反光" },
                        { category: "照明设备", name: "应急照明灯", model: "LED-500W", quantity: "30套", unit: "套", status: "良好", notes: "含发电机" },
                        { category: "防护用品", name: "安全帽", model: "ABS", quantity: "50个", unit: "个", status: "良好", notes: "黄色标准型" }
                    ]
                },
                {
                    name: "柳州市交通应急物资点",
                    location: "约20km", 
                    contact: "物资点负责人 - 181xxxx3002",
                    items: [
                        { category: "环保材料", name: "吸油毡", model: "标准型", quantity: "200张", unit: "张", status: "良好", notes: "化学品吸附" },
                        { category: "环保材料", name: "围油栏", model: "充气式", quantity: "100米", unit: "米", status: "良好", notes: "水面隔离" },
                        { category: "交通设施", name: "临时路障", model: "水马", quantity: "50个", unit: "个", status: "良好", notes: "可注水加重" },
                        { category: "救援工具", name: "破拆工具", model: "液压式", quantity: "5套", unit: "套", status: "良好", notes: "含剪扩器" }
                    ]
                },
                {
                    name: "泉南高速施工单位物资点",
                    location: "约5km",
                    contact: "施工单位负责人 - 181xxxx3003", 
                    items: [
                        { category: "工程机械", name: "挖掘机", model: "CAT320D", quantity: "2台", unit: "台", status: "良好", notes: "20吨级" },
                        { category: "工程机械", name: "推土机", model: "SD16", quantity: "1台", unit: "台", status: "良好", notes: "160马力" },
                        { category: "工程机械", name: "装载机", model: "ZL50", quantity: "3台", unit: "台", status: "良好", notes: "5吨装载量" },
                        { category: "运输车辆", name: "自卸车", model: "重汽豪沃", quantity: "5辆", unit: "辆", status: "良好", notes: "25吨载重" }
                    ]
                }
            ];

            // 添加应急物资信息（详细格式）
            nearbySuppliesData.forEach(supply => {
                const supplyDiv = document.createElement('div');
                supplyDiv.className = 'resource-item';
                supplyDiv.style.marginBottom = '15px';
                supplyDiv.style.padding = '10px';
                supplyDiv.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
                supplyDiv.style.borderRadius = '5px';
                
                // 物资点基本信息
                const supplyHeader = document.createElement('div');
                supplyHeader.innerHTML = `
                    <strong style="color: #3498db; font-size: 20px;">${supply.name}</strong> (${supply.location})<br>
                    <span style="color: #ecf0f1; font-size: 18px;">联系方式: ${supply.contact}</span>
                `;
                supplyDiv.appendChild(supplyHeader);

                // 物资列表标题
                const itemsTitle = document.createElement('div');
                itemsTitle.textContent = '物资列表';
                itemsTitle.style.marginTop = '10px';
                itemsTitle.style.marginBottom = '8px';
                itemsTitle.style.fontWeight = 'bold';
                itemsTitle.style.color = '#f8f9fa';
                itemsTitle.style.borderBottom = '1px solid #495057';
                itemsTitle.style.paddingBottom = '5px';
                supplyDiv.appendChild(itemsTitle);

                // 创建物资列表表格
                const table = document.createElement('table');
                table.className = 'supply-table';
                table.style.width = '100%';
                table.style.borderCollapse = 'collapse';
                table.style.marginTop = '5px';
                table.style.fontSize = '16px';

                // 表头
                const thead = table.createTHead();
                const headerRow = thead.insertRow();
                const headers = ["类别", "名称", "型号", "数量", "状态", "备注"];
                headers.forEach(headerText => {
                    const th = document.createElement('th');
                    th.textContent = headerText;
                    th.style.backgroundColor = '#34495e';
                    th.style.color = '#3498db';
                    th.style.padding = '6px 8px';
                    th.style.border = '1px solid #3a3f4b';
                    th.style.fontWeight = 'bold';
                    headerRow.appendChild(th);
                });

                // 表格内容
                const tbody = table.createTBody();
                supply.items.forEach(item => {
                    const row = tbody.insertRow();
                    row.style.backgroundColor = 'rgba(50, 54, 62, 0.8)';
                    
                    const cells = [item.category, item.name, item.model, item.quantity, item.status, item.notes];
                    cells.forEach(cellText => {
                        const cell = row.insertCell();
                        cell.textContent = cellText;
                        cell.style.padding = '6px 8px';
                        cell.style.border = '1px solid #3a3f4b';
                        cell.style.color = '#e6e6e6';
                        
                        // 状态列特殊颜色
                        if (cellText === '良好') {
                            cell.style.color = '#4caf50';
                        } else if (cellText === '待检修') {
                            cell.style.color = '#ff9800';
                        }
                    });
                });

                supplyDiv.appendChild(table);
                suppliesDivRisk.appendChild(supplyDiv);
            });
            
            bottomInfoContainer.appendChild(suppliesDivRisk);
            const rescueDivRisk = document.createElement('div');
            rescueDivRisk.className = 'info-column rescue-panel';
            
            // 添加标题
            const rescueTitle = document.createElement('div');
            rescueTitle.className = 'resource-subsection-title';
            rescueTitle.textContent = '附近救援力量';
            rescueTitle.style.backgroundColor = '#3a4a5d';
            rescueTitle.style.color = '#fff';
            rescueTitle.style.padding = '5px 10px';
            rescueTitle.style.marginBottom = '10px';
            rescueDivRisk.appendChild(rescueTitle);

            // 模拟救援队伍数据（完整格式，包含详细装备和物资信息）
            const nearbyRescueTeamsData = [
                {
                    name: "柳州市消防救援支队柳江大队",
                    address: "柳州市柳江区拉堡镇建都大道88号",
                    distance: "约8km",
                    personnel: "30人",
                    specialization: "火灾扑救、应急救援、危化品处置",
                    leader: "张队长",
                    contact: "181xxxx4001",
                    equipment: {
                        "挖掘机": "2台",
                        "装载机": "1台", 
                        "推土机": "1台",
                        "排水抢险车": "2台",
                        "运输车": "3辆",
                        "吊车": "1台",
                        "应急照明车": "2台",
                        "其它机械": "发电机5台"
                    },
                    supplies: {
                        "砂石料": "500吨",
                        "编织袋": "2000个",
                        "水泥": "100吨",
                        "防水布": "1000平方米",
                        "钢板": "50块"
                    }
                },
                {
                    name: "广西交通建设集团应急抢险队",
                    address: "柳州市城中区文昌路18号",
                    distance: "约15km",
                    personnel: "25人",
                    specialization: "道路抢险、桥梁维修、边坡治理",
                    leader: "李队长",
                    contact: "181xxxx4002",
                    equipment: {
                        "挖掘机": "3台",
                        "装载机": "2台",
                        "推土机": "2台",
                        "排水抢险车": "1台",
                        "运输车": "5辆",
                        "吊车": "2台",
                        "应急照明车": "1台",
                        "其它机械": "压路机2台"
                    },
                    supplies: {
                        "砂石料": "800吨",
                        "编织袋": "3000个",
                        "水泥": "150吨",
                        "防水布": "800平方米",
                        "钢板": "80块"
                    }
                }
            ];

            // 添加救援队伍信息（详细格式）
            nearbyRescueTeamsData.forEach(team => {
                const teamDiv = document.createElement('div');
                teamDiv.className = 'resource-item';
                teamDiv.style.marginBottom = '15px';
                teamDiv.style.padding = '10px';
                teamDiv.style.backgroundColor = 'rgba(40, 44, 52, 0.6)';
                teamDiv.style.borderRadius = '5px';

                // 队伍基本信息
                const teamHeader = document.createElement('div');
                teamHeader.innerHTML = `
                    <div style="color: #3498db; font-size: 20px; font-weight: bold; margin-bottom: 8px;">${team.name}</div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; color: #ecf0f1; font-size: 18px;">
                        <div><strong>地址:</strong> ${team.address}</div>
                        <div><strong>距离:</strong> ${team.distance}</div>
                        <div><strong>人数:</strong> ${team.personnel}</div>
                        <div><strong>专业方向:</strong> ${team.specialization}</div>
                        <div><strong>负责人:</strong> ${team.leader}</div>
                        <div><strong>联系方式:</strong> ${team.contact}</div>
                    </div>
                `;
                teamDiv.appendChild(teamHeader);

                // 装备配置标题
                const equipmentTitle = document.createElement('div');
                equipmentTitle.textContent = '装备配置';
                equipmentTitle.style.marginTop = '15px';
                equipmentTitle.style.marginBottom = '8px';
                equipmentTitle.style.fontWeight = 'bold';
                equipmentTitle.style.color = '#f8f9fa';
                equipmentTitle.style.borderBottom = '1px solid #495057';
                equipmentTitle.style.paddingBottom = '5px';
                teamDiv.appendChild(equipmentTitle);

                // 装备配置网格
                const equipmentGrid = document.createElement('div');
                equipmentGrid.style.display = 'grid';
                equipmentGrid.style.gridTemplateColumns = 'repeat(4, 1fr)';
                equipmentGrid.style.gap = '8px';
                equipmentGrid.style.marginBottom = '15px';
                equipmentGrid.style.fontSize = '18px';

                Object.entries(team.equipment).forEach(([key, value]) => {
                    const equipmentItem = document.createElement('div');
                    equipmentItem.style.color = '#e6e6e6';
                    equipmentItem.innerHTML = `<strong style="color: #3498db;">${key}:</strong> ${value}`;
                    equipmentGrid.appendChild(equipmentItem);
                });
                teamDiv.appendChild(equipmentGrid);

                // 物资储备标题
                const suppliesTitle = document.createElement('div');
                suppliesTitle.textContent = '物资储备';
                suppliesTitle.style.marginTop = '10px';
                suppliesTitle.style.marginBottom = '8px';
                suppliesTitle.style.fontWeight = 'bold';
                suppliesTitle.style.color = '#f8f9fa';
                suppliesTitle.style.borderBottom = '1px solid #495057';
                suppliesTitle.style.paddingBottom = '5px';
                teamDiv.appendChild(suppliesTitle);

                // 物资储备网格
                const suppliesGrid = document.createElement('div');
                suppliesGrid.style.display = 'grid';
                suppliesGrid.style.gridTemplateColumns = 'repeat(3, 1fr)';
                suppliesGrid.style.gap = '8px';
                suppliesGrid.style.fontSize = '18px';

                Object.entries(team.supplies).forEach(([key, value]) => {
                    const supplyItem = document.createElement('div');
                    supplyItem.style.color = '#e6e6e6';
                    supplyItem.innerHTML = `<strong style="color: #3498db;">${key}:</strong> ${value}`;
                    suppliesGrid.appendChild(supplyItem);
                });
                teamDiv.appendChild(suppliesGrid);

                rescueDivRisk.appendChild(teamDiv);
            });
            
            bottomInfoContainer.appendChild(rescueDivRisk);
            contentWrapper.appendChild(bottomInfoContainer);
            modalBody.appendChild(contentWrapper);

        } else if (markerId.startsWith('supply')) {
            const supplyPointData = { id: "supply001", name: "兴宁区应急物资储备中心", location: "昆仑大道辅路101号", area: "占地500平方米, 库容2000立方米", manager: "赵管理者 - 13501350135", updatedTime: "2024-07-25 09:00", items: [ { category: "防护用品", name: "N95口罩", model: "标准型", quantity: "20000个", unit: "个", status: "良好", notes: "有效期至2026年" }, { category: "防护用品", name: "防护服", model: "XL", quantity: "500套", unit: "套", status: "良好", notes: "-" }, { category: "救援工具", name: "液压剪扩器", model: "型号A", quantity: "5台", unit: "台", status: "良好", notes: "定期保养" }, { category: "救援工具", name: "应急照明灯组", model: "型号B", quantity: "20套", unit: "套", status: "待检修", notes: "3套电池老化" }, { category: "生活保障", name: "瓶装饮用水", model: "500ml", quantity: "100箱", unit: "箱", status: "良好", notes: "每箱24瓶" } ] };
            modalTitle.textContent = `应急物资储备点: ${supplyPointData.name}`;
            const supplyContentWrapper = document.createElement('div');
            supplyContentWrapper.className = 'modal-supply-details';
            const subTitle = document.createElement('h4');
            subTitle.className = 'supply-subtitle';
            subTitle.textContent = supplyPointData.name;
            supplyContentWrapper.appendChild(subTitle);
            const topGrid = document.createElement('div');
            topGrid.className = 'supply-info-grid';
            topGrid.appendChild(createGenericInfoItem("具体位置", supplyPointData.location, 'grid-item'));
            topGrid.appendChild(createGenericInfoItem("管辖/面积", supplyPointData.area, 'grid-item'));
            topGrid.appendChild(createGenericInfoItem("负责人", supplyPointData.manager, 'grid-item'));
            topGrid.appendChild(createGenericInfoItem("信息更新时间", supplyPointData.updatedTime, 'grid-item'));
            supplyContentWrapper.appendChild(topGrid);
            const itemListTitle = document.createElement('h5');
            itemListTitle.className = 'supply-item-list-title';
            itemListTitle.textContent = "物资列表";
            supplyContentWrapper.appendChild(itemListTitle);
            const tableContainer = document.createElement('div');
            tableContainer.className = 'supply-table-container';
            const table = document.createElement('table');
            table.className = 'supply-table';
            const thead = table.createTHead();
            const headerRow = thead.insertRow();
            const headers = ["类别", "名称", "型号", "数量", "单位", "状态", "备注"];
            headers.forEach(headerText => { const th = document.createElement('th'); th.textContent = headerText; headerRow.appendChild(th); });
            const tbody = table.createTBody();
            supplyPointData.items.forEach(item => { const row = tbody.insertRow(); row.insertCell().textContent = item.category; row.insertCell().textContent = item.name; row.insertCell().textContent = item.model; row.insertCell().textContent = item.quantity; row.insertCell().textContent = item.unit; const statusCell = row.insertCell(); statusCell.textContent = item.status; if (item.status === "待检修") statusCell.classList.add('status-needs-attention'); if (item.status === "良好") statusCell.classList.add('status-good'); row.insertCell().textContent = item.notes; });
            tableContainer.appendChild(table);
            supplyContentWrapper.appendChild(tableContainer);
            modalBody.appendChild(supplyContentWrapper);

        } else if (markerId.startsWith('rescue')) {
            const rescueTeamData = { id: "rescue001", teamName: "高速应急救援一队", status: "待命", personnelCount: 30, contactPerson: "赵队长 (13600000001)", mainEquipment: "清障车2辆, 吊车1辆, 破拆工具组", specialization: "道路清障、交通事故处理" };
            modalTitle.textContent = `应急救援力量: ${rescueTeamData.teamName}`;
            const rescueContentWrapper = document.createElement('div');
            rescueContentWrapper.className = 'modal-rescue-details';
            const teamNameBlock = document.createElement('div');
            teamNameBlock.className = 'rescue-team-name-block info-item';
            const teamNameValue = document.createElement('span');
            teamNameValue.className = 'info-value team-name-prominent';
            teamNameValue.textContent = rescueTeamData.teamName;
            teamNameBlock.appendChild(teamNameValue);
            rescueContentWrapper.appendChild(teamNameBlock);
            const detailsContainer = document.createElement('div');
            detailsContainer.className = 'rescue-details-container';
            detailsContainer.appendChild(createGenericInfoItem("状态", rescueTeamData.status, 'rescue-detail-item'));
            detailsContainer.appendChild(createGenericInfoItem("人数", `${rescueTeamData.personnelCount}人`, 'rescue-detail-item'));
            detailsContainer.appendChild(createGenericInfoItem("联系人", rescueTeamData.contactPerson, 'rescue-detail-item'));
            detailsContainer.appendChild(createGenericInfoItem("主要装备", rescueTeamData.mainEquipment, 'rescue-detail-item equipment-detail'));
            detailsContainer.appendChild(createGenericInfoItem("专业方向", rescueTeamData.specialization, 'rescue-detail-item specialization-detail'));
            rescueContentWrapper.appendChild(detailsContainer);
            modalBody.appendChild(rescueContentWrapper);

        } else if (markerId.startsWith('supplies')) {
            const supplyPointData = { id: "supplies001", name: "兴宁区应急物资储备中心", location: "昆仑大道辅路101号", area: "占地500平方米, 库容2000立方米", manager: "赵管理者 - 13501350135", updatedTime: "2024-07-25 09:00", items: [ { category: "防护用品", name: "N95口罩", model: "标准型", quantity: "20000个", unit: "个", status: "良好", notes: "有效期至2026年" }, { category: "防护用品", name: "防护服", model: "XL", quantity: "500套", unit: "套", status: "良好", notes: "-" }, { category: "救援工具", name: "液压剪扩器", model: "型号A", quantity: "5台", unit: "台", status: "良好", notes: "定期保养" }, { category: "救援工具", name: "应急照明灯组", model: "型号B", quantity: "20套", unit: "套", status: "待检修", notes: "3套电池老化" }, { category: "生活保障", name: "瓶装饮用水", model: "500ml", quantity: "100箱", unit: "箱", status: "良好", notes: "每箱24瓶" } ] };
            modalTitle.textContent = `应急物资储备点: ${supplyPointData.name}`;

            // 设置模态框大小
            modal.style.cssText = `
                display: block !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                z-index: 10000 !important;
                overflow: auto !important;
            `;
            
            const modalContentElement = modal.querySelector('.modal-content');
            if (modalContentElement) {
                modalContentElement.style.cssText = `
                    max-width: 800px !important;
                    width: 90% !important;
                    max-height: 80vh !important;
                    height: auto !important;
                    overflow-y: auto !important;
                    margin: 50px auto !important;
                `;
            }
            const supplyContentWrapper = document.createElement('div');
            supplyContentWrapper.className = 'modal-supply-details';
            const subTitle = document.createElement('h4');
            subTitle.className = 'supply-subtitle';
            subTitle.textContent = supplyPointData.name;
            supplyContentWrapper.appendChild(subTitle);
            const topGrid = document.createElement('div');
            topGrid.className = 'supply-info-grid';
            topGrid.appendChild(createGenericInfoItem("具体位置", supplyPointData.location, 'grid-item'));
            topGrid.appendChild(createGenericInfoItem("管辖/面积", supplyPointData.area, 'grid-item'));
            topGrid.appendChild(createGenericInfoItem("负责人", supplyPointData.manager, 'grid-item'));
            topGrid.appendChild(createGenericInfoItem("信息更新时间", supplyPointData.updatedTime, 'grid-item'));
            supplyContentWrapper.appendChild(topGrid);
            const itemListTitle = document.createElement('h5');
            itemListTitle.className = 'supply-item-list-title';
            itemListTitle.textContent = "物资列表";
            supplyContentWrapper.appendChild(itemListTitle);
            const tableContainer = document.createElement('div');
            tableContainer.className = 'supply-table-container';
            const table = document.createElement('table');
            table.className = 'supply-table';
            const thead = table.createTHead();
            const headerRow = thead.insertRow();
            const headers = ["类别", "名称", "型号", "数量", "单位", "状态", "备注"];
            headers.forEach(headerText => { const th = document.createElement('th'); th.textContent = headerText; headerRow.appendChild(th); });
            const tbody = table.createTBody();
            supplyPointData.items.forEach(item => { const row = tbody.insertRow(); row.insertCell().textContent = item.category; row.insertCell().textContent = item.name; row.insertCell().textContent = item.model; row.insertCell().textContent = item.quantity; row.insertCell().textContent = item.unit; const statusCell = row.insertCell(); statusCell.textContent = item.status; if (item.status === "待检修") statusCell.classList.add('status-needs-attention'); if (item.status === "良好") statusCell.classList.add('status-good'); row.insertCell().textContent = item.notes; });
            tableContainer.appendChild(table);
            supplyContentWrapper.appendChild(tableContainer);
            modalBody.appendChild(supplyContentWrapper);

        } else if (markerId.startsWith('medical')) {
            // 医疗点模拟数据
            const medicalData = {
                id: "medical001",
                name: "南宁市第一人民医院",
                type: "hospital",
                level: "三级甲等",
                status: "available",
                bedCount: "1200张",
                emergencyCapacity: "50人/小时",
                contact: "张院长",
                phone: "0771-2635268",
                address: "南宁市青秀区七星路89号",
                resources: [
                    { type: "急诊床位", count: "50张", status: "可用30张", notes: "24小时急诊服务" },
                    { type: "ICU病床", count: "30张", status: "可用20张", notes: "配备呼吸机" },
                    { type: "手术室", count: "15间", status: "可用10间", notes: "含2间急诊手术室" },
                    { type: "医护人员", count: "500人", status: "在岗450人", notes: "含急诊医生50人" },
                    { type: "血液库存", count: "充足", status: "可用", notes: "各型血液均有储备" }
                ],
                specialties: [
                    { name: "创伤救治", level: "省级重点", description: "具备大规模创伤救治能力，可同时处理30例重症创伤" },
                    { name: "烧伤救治", level: "市级重点", description: "具备中等规模烧伤救治能力，设有20张烧伤专用病床" },
                    { name: "中毒救治", level: "市级重点", description: "具备各类中毒救治能力，配备解毒药物库" }
                ]
            };

            // 设置模态框标题
            modalTitle.textContent = `医疗点详情: ${medicalData.name}`;

            // 设置模态框样式（参考救援力量模态框）
            modal.style.cssText = `
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                z-index: 10000 !important;
                align-items: center !important;
                justify-content: center !important;
                overflow: auto !important;
            `;
            
            const modalContentElement = modal.querySelector('.modal-content');
            if (modalContentElement) {
                modalContentElement.style.cssText = `
                    max-width: 800px !important;
                    width: 90% !important;
                    height: auto !important;
                    max-height: 85vh !important;
                    overflow: visible !important;
                    background-color: #2c3e50 !important;
                    color: #ecf0f1 !important;
                    margin: auto !important;
                    border-radius: 8px !important;
                    display: flex !important;
                    flex-direction: column !important;
                `;
            }

            // 设置header样式
            const modalHeader = modal.querySelector('.modal-header');
            if (modalHeader) {
                modalHeader.style.cssText = `
                    background-color: #34495e !important;
                    color: #ecf0f1 !important;
                    border-bottom: 1px solid #4a5f7a !important;
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                    padding: 15px 20px !important;
                    flex-shrink: 0 !important;
                `;
            }

            // 设置标题样式
            modalTitle.style.cssText = `
                margin: 0 !important;
                font-size: 24px !important;
                color: #ecf0f1 !important;
            `;

            // 设置关闭按钮样式
            const closeButton = modal.querySelector('.close-button');
            if (closeButton) {
                closeButton.style.cssText = `
                    color: #ecf0f1 !important;
                    font-size: 24px !important;
                    cursor: pointer !important;
                `;
            }

            // 设置body样式
            modalBody.style.cssText = `
                padding: 20px !important;
                background-color: #2c3e50 !important;
                overflow-y: auto !important;
                flex: 1 1 auto !important;
            `;

            // 创建医疗点详情内容
            const medicalContentWrapper = document.createElement('div');
            medicalContentWrapper.className = 'medical-info';

            // 基本信息板块
            const basicInfoPanel = document.createElement('div');
            basicInfoPanel.style.cssText = `
                background-color: rgba(40, 44, 52, 0.6);
                border-radius: 8px;
                padding: 20px;
            `;

            const basicInfoTitle = document.createElement('div');
            basicInfoTitle.className = 'resource-subsection-title';
            basicInfoTitle.style.cssText = `
                background-color: #3a4a5d;
                color: #fff;
                padding: 8px 15px;
                margin: -20px -20px 15px -20px;
                border-radius: 8px 8px 0 0;
                font-size: 20px;
                font-weight: bold;
            `;
            basicInfoTitle.textContent = '基本信息';
            basicInfoPanel.appendChild(basicInfoTitle);

            // 创建基本信息网格
            const basicInfoGrid = document.createElement('div');
            basicInfoGrid.style.cssText = `
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                font-size: 18px;
            `;

            // 添加基本信息项
            const basicInfoItems = [
                { label: '医疗点编号', value: medicalData.id },
                { label: '医疗点类型', value: medicalData.type === 'hospital' ? '定点医院' : '医疗点' },
                { label: '医疗等级', value: medicalData.level },
                { label: '当前状态', value: medicalData.status === 'available' ? '可用' : '停用' },
                { label: '床位数量', value: medicalData.bedCount },
                { label: '急诊容量', value: medicalData.emergencyCapacity },
                { label: '联系人', value: medicalData.contact },
                { label: '联系电话', value: medicalData.phone }
            ];

            basicInfoItems.forEach(item => {
                const infoItem = document.createElement('div');
                infoItem.className = 'info-item';
                infoItem.innerHTML = `
                    <div class="info-label" style="color: #95a5a6; margin-bottom: 5px;">${item.label}：</div>
                    <div class="info-value" style="color: #ecf0f1;">${item.value}</div>
                `;
                basicInfoGrid.appendChild(infoItem);
            });

            // 地址单独占一行
            const addressItem = document.createElement('div');
            addressItem.className = 'info-item';
            addressItem.style.gridColumn = '1 / -1';
            addressItem.innerHTML = `
                <div class="info-label" style="color: #95a5a6; margin-bottom: 5px;">医疗点地址：</div>
                <div class="info-value" style="color: #ecf0f1;">${medicalData.address}</div>
            `;
            basicInfoGrid.appendChild(addressItem);

            basicInfoPanel.appendChild(basicInfoGrid);
            medicalContentWrapper.appendChild(basicInfoPanel);





            modalBody.appendChild(medicalContentWrapper);

        } else if (markerId.startsWith('vehicle')) {
            // 救援车辆模拟数据
            const vehicleData = {
                id: "vehicle001",
                plateNumber: "桂A12345",
                type: "engineering",
                status: "available",
                unit: "南宁市公路应急抢险中心",
                capacity: "15吨",
                driver: "李师傅",
                phone: "13800138001",
                location: "南宁市西乡塘区友爱北路123号应急车库",
                equipment: [
                    { name: "液压破拆工具", count: "1套", status: "良好", usage: "破拆障碍物" },
                    { name: "发电机", count: "1台", status: "良好", usage: "现场供电" },
                    { name: "照明设备", count: "4套", status: "良好", usage: "夜间照明" },
                    { name: "抽水泵", count: "2台", status: "良好", usage: "排水抽水" },
                    { name: "救生绳", count: "200米", status: "良好", usage: "救援固定" }
                ],
                dispatchRecords: [
                    { time: "2024-05-15", eventType: "道路塌陷", location: "南宁市青秀区民族大道", task: "道路抢修、交通疏导", status: "已完成" },
                    { time: "2024-05-10", eventType: "山体滑坡", location: "南宁市邕宁区蒲庙镇", task: "道路清障、人员救援", status: "已完成" },
                    { time: "2024-05-01", eventType: "桥梁受损", location: "南宁市武鸣区双桥镇", task: "桥梁加固、交通管制", status: "已完成" }
                ]
            };

            // 根据车辆类型设置类型名称
            let vehicleTypeName = "救援车辆";
            if (vehicleData.type === 'ambulance') vehicleTypeName = "救护车";
            else if (vehicleData.type === 'engineering') vehicleTypeName = "工程抢险车";
            else if (vehicleData.type === 'transport') vehicleTypeName = "物资运输车";
            else if (vehicleData.type === 'drone') vehicleTypeName = "无人机";
            else if (vehicleData.type === 'command') vehicleTypeName = "应急指挥车";
            else if (vehicleData.type === 'fire') vehicleTypeName = "消防车";

            // 根据车辆状态设置状态文本
            let statusText = "未知";
            if (vehicleData.status === 'available') statusText = "可调度";
            else if (vehicleData.status === 'dispatched') statusText = "已出动";
            else if (vehicleData.status === 'maintenance') statusText = "检修中";

            // 设置模态框标题
            modalTitle.textContent = `救援车辆详情: ${vehicleTypeName} (${vehicleData.plateNumber})`;

            // 设置模态框样式（参考救援力量模态框）
            modal.style.cssText = `
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background-color: rgba(0, 0, 0, 0.5) !important;
                z-index: 10000 !important;
                align-items: center !important;
                justify-content: center !important;
                overflow: auto !important;
            `;
            
            const modalContentElement = modal.querySelector('.modal-content');
            if (modalContentElement) {
                modalContentElement.style.cssText = `
                    max-width: 800px !important;
                    width: 90% !important;
                    height: auto !important;
                    max-height: 85vh !important;
                    overflow: visible !important;
                    background-color: #2c3e50 !important;
                    color: #ecf0f1 !important;
                    margin: auto !important;
                    border-radius: 8px !important;
                    display: flex !important;
                    flex-direction: column !important;
                `;
            }

            // 设置header样式
            const modalHeader = modal.querySelector('.modal-header');
            if (modalHeader) {
                modalHeader.style.cssText = `
                    background-color: #34495e !important;
                    color: #ecf0f1 !important;
                    border-bottom: 1px solid #4a5f7a !important;
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: center !important;
                    padding: 15px 20px !important;
                    flex-shrink: 0 !important;
                `;
            }

            // 设置标题样式
            modalTitle.style.cssText = `
                margin: 0 !important;
                font-size: 24px !important;
                color: #ecf0f1 !important;
            `;

            // 设置关闭按钮样式
            const closeButton = modal.querySelector('.close-button');
            if (closeButton) {
                closeButton.style.cssText = `
                    color: #ecf0f1 !important;
                    font-size: 24px !important;
                    cursor: pointer !important;
                `;
            }

            // 设置body样式
            modalBody.style.cssText = `
                padding: 20px !important;
                background-color: #2c3e50 !important;
                overflow-y: auto !important;
                flex: 1 1 auto !important;
            `;

            // 创建救援车辆详情内容
            const vehicleContentWrapper = document.createElement('div');
            vehicleContentWrapper.className = 'vehicle-info';

            // 基本信息板块
            const basicInfoPanel = document.createElement('div');
            basicInfoPanel.style.cssText = `
                background-color: rgba(40, 44, 52, 0.6);
                border-radius: 8px;
                padding: 20px;
            `;

            const basicInfoTitle = document.createElement('div');
            basicInfoTitle.className = 'resource-subsection-title';
            basicInfoTitle.style.cssText = `
                background-color: #3a4a5d;
                color: #fff;
                padding: 8px 15px;
                margin: -20px -20px 15px -20px;
                border-radius: 8px 8px 0 0;
                font-size: 20px;
                font-weight: bold;
            `;
            basicInfoTitle.textContent = '基本信息';
            basicInfoPanel.appendChild(basicInfoTitle);

            // 创建基本信息网格
            const basicInfoGrid = document.createElement('div');
            basicInfoGrid.style.cssText = `
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                font-size: 18px;
            `;

            // 添加基本信息项
            const basicInfoItems = [
                { label: '车辆编号', value: vehicleData.id },
                { label: '车牌号', value: vehicleData.plateNumber },
                { label: '车辆类型', value: vehicleTypeName },
                { label: '当前状态', value: statusText },
                { label: '所属单位', value: vehicleData.unit },
                { label: '承载能力', value: vehicleData.capacity },
                { label: '驾驶员', value: vehicleData.driver },
                { label: '联系电话', value: vehicleData.phone }
            ];

            basicInfoItems.forEach(item => {
                const infoItem = document.createElement('div');
                infoItem.className = 'info-item';
                infoItem.innerHTML = `
                    <div class="info-label" style="color: #95a5a6; margin-bottom: 5px;">${item.label}：</div>
                    <div class="info-value" style="color: #ecf0f1;">${item.value}</div>
                `;
                basicInfoGrid.appendChild(infoItem);
            });

            // 停放位置单独占一行
            const locationItem = document.createElement('div');
            locationItem.className = 'info-item';
            locationItem.style.gridColumn = '1 / -1';
            locationItem.innerHTML = `
                <div class="info-label" style="color: #95a5a6; margin-bottom: 5px;">停放位置：</div>
                <div class="info-value" style="color: #ecf0f1;">${vehicleData.location}</div>
            `;
            basicInfoGrid.appendChild(locationItem);

            basicInfoPanel.appendChild(basicInfoGrid);
            vehicleContentWrapper.appendChild(basicInfoPanel);





            modalBody.appendChild(vehicleContentWrapper);

        } else if (markerId.startsWith('warning')) {
            // 气象预警模拟数据
            const warningData = {
                id: "warning001",
                name: "暴雨预警",
                location: "桂林市、柳州市",
                details: "预计未来72小时内上述地区将持续出现强降雨，累计降雨量可达150-200毫米，局部地区可能超过250毫米。",
                level: "红色",
                time: "2023-04-18 10:03",
                source: "广西气象局",
                duration: "72小时",
                impactAreas: [
                    { name: "桂林市", roads: ["G72泉南高速", "G65", "S305"] },
                    { name: "柳州市", roads: ["G72泉南高速", "S209"] }
                ],
                precautions: [
                    "加强公路巡查，重点关注山区路段、隧道出入口、高边坡等易受灾害影响的部位",
                    "做好排水设施疏通工作，确保排水畅通，防止雨水渗入山体",
                    "准备防汛物资，包括沙袋、抽水泵、警示标志等",
                    "对已发现存在安全隐患的路段加强监测，必要时实施交通管制或临时封闭",
                    "提前部署应急救援力量，做好应对突发事件的准备"
                ]
            };

            modalTitle.textContent = `气象预警: ${warningData.name}`;

            const warningContentWrapper = document.createElement('div');
            warningContentWrapper.className = 'modal-warning-details';

            // 基本信息部分 - 顶部横向板块
            const basicInfoSection = document.createElement('div');
            basicInfoSection.className = 'warning-section basic-info-panel';

            // 预警标题和等级
            const warningHeader = document.createElement('div');
            warningHeader.className = 'warning-header';

            // 左侧：预警名称和等级
            const warningHeaderLeft = document.createElement('div');
            warningHeaderLeft.className = 'warning-header-left';

            const warningNameValue = document.createElement('h4');
            warningNameValue.className = 'warning-name';
            warningNameValue.textContent = warningData.name;
            warningNameValue.style.fontSize = '22px'; // 增大预警名称字体

            const warningLevel = document.createElement('span');
            warningLevel.className = `warning-level ${warningData.level.toLowerCase()}`;
            warningLevel.textContent = warningData.level;
            warningLevel.style.fontSize = '16px'; // 增大预警等级字体

            warningHeaderLeft.appendChild(warningNameValue);
            warningHeaderLeft.appendChild(warningLevel);

            // 右侧：通知按钮
            const warningHeaderRight = document.createElement('div');
            warningHeaderRight.className = 'warning-header-right';

            const notifyButton = document.createElement('button');
            notifyButton.className = 'notify-button';
            notifyButton.innerHTML = '<i class="fas fa-bell"></i> 通知相关单位';
            notifyButton.style.fontSize = '16px'; // 增大按钮字体
            notifyButton.onclick = function() {
                openNotifyModal(warningData);
            };

            warningHeaderRight.appendChild(notifyButton);

            // 将左右两侧添加到头部
            warningHeader.appendChild(warningHeaderLeft);
            warningHeader.appendChild(warningHeaderRight);

            basicInfoSection.appendChild(warningHeader);

            // 创建基本信息表格布局
            const basicInfoTable = document.createElement('div');
            basicInfoTable.className = 'info-table';

            // 创建基本信息行
            const row1 = document.createElement('div');
            row1.className = 'info-row';
            row1.style.fontSize = '18px'; // 增大字体
            row1.appendChild(createInfoField("发布时间", warningData.time));
            row1.appendChild(createInfoField("预警来源", warningData.source));
            row1.appendChild(createInfoField("预警时长", warningData.duration));

            const row2 = document.createElement('div');
            row2.className = 'info-row';
            row2.style.fontSize = '18px'; // 增大字体
            row2.appendChild(createInfoField("预警区域", warningData.location, true));

            const row3 = document.createElement('div');
            row3.className = 'info-row';
            row3.style.fontSize = '18px'; // 增大字体
            row3.appendChild(createInfoField("预警详情", warningData.details, true));

            basicInfoTable.appendChild(row1);
            basicInfoTable.appendChild(row2);
            basicInfoTable.appendChild(row3);
            basicInfoSection.appendChild(basicInfoTable);
            warningContentWrapper.appendChild(basicInfoSection);

            // 创建预警通知跟踪部分
            const trackingSection = document.createElement('div');
            trackingSection.className = 'warning-section tracking-section';

            const trackingTitle = document.createElement('h5');
            trackingTitle.textContent = '预警通知跟踪';
            trackingTitle.style.fontSize = '20px'; // 增大标题字体
            trackingSection.appendChild(trackingTitle);

            // 创建通知跟踪表格
            const trackingTable = document.createElement('table');
            trackingTable.className = 'tracking-table';
            trackingTable.style.fontSize = '16px'; // 增大表格字体

            // 创建表头
            const tableHeader = document.createElement('thead');
            const headerRow = document.createElement('tr');

            const headers = ['单位名称', '联系人', '联系电话', '通知状态', '确认时间'];
            headers.forEach(headerText => {
                const th = document.createElement('th');
                th.textContent = headerText;
                th.style.fontSize = '16px'; // 增大表头字体
                headerRow.appendChild(th);
            });

            tableHeader.appendChild(headerRow);
            trackingTable.appendChild(tableHeader);

            // 创建表格内容
            const tableBody = document.createElement('tbody');

            // 模拟数据 - 未确认单位
            const unconfirmedUnits = [
                { name: '柳州市交通运输局', contact: '王局长', phone: '0772-12345678', status: '未确认', time: '-' },
                { name: '泉南高速公路运营公司', contact: '张经理', phone: '0773-87654321', status: '未确认', time: '-' }
            ];

            // 模拟数据 - 已确认单位
            const confirmedUnits = [
                { name: '广西交通运输厅', contact: '赵厅长', phone: '0771-11112222', status: '已确认', time: '2023-04-18 10:15' },
                { name: '桂林市交通运输局', contact: '刘局长', phone: '0773-12345678', status: '已确认', time: '2023-04-18 10:20' },
                { name: '广西高速公路发展中心', contact: '陈主任', phone: '0771-99998888', status: '已确认', time: '2023-04-18 10:25' },
                { name: '广西公路发展中心', contact: '李主任', phone: '0771-87654321', status: '已确认', time: '2023-04-18 10:30' }
            ];

            // 先添加未确认单位（突出显示）
            unconfirmedUnits.forEach(unit => {
                const row = document.createElement('tr');
                row.className = 'unconfirmed-row';

                Object.values(unit).forEach(value => {
                    const td = document.createElement('td');
                    td.textContent = value;
                    td.style.fontSize = '15px'; // 增大表格数据字体
                    if (value === '未确认') {
                        td.style.color = '#dc3545';
                        td.style.fontWeight = 'bold';
                    }
                    row.appendChild(td);
                });

                tableBody.appendChild(row);
            });

            // 再添加已确认单位
            confirmedUnits.forEach(unit => {
                const row = document.createElement('tr');

                Object.values(unit).forEach(value => {
                    const td = document.createElement('td');
                    td.textContent = value;
                    td.style.fontSize = '15px'; // 增大表格数据字体
                    if (value === '已确认') {
                        td.style.color = '#28a745';
                    }
                    row.appendChild(td);
                });

                tableBody.appendChild(row);
            });

            trackingTable.appendChild(tableBody);
            trackingSection.appendChild(trackingTable);

            // 添加催办按钮
            const urgeButtonContainer = document.createElement('div');
            urgeButtonContainer.className = 'urge-button-container';
            urgeButtonContainer.style.textAlign = 'right';
            urgeButtonContainer.style.marginTop = '10px';

            const urgeButton = document.createElement('button');
            urgeButton.className = 'urge-button';
            urgeButton.innerHTML = '<i class="fas fa-bell"></i> 催办未确认单位';
            urgeButton.style.fontSize = '16px'; // 增大按钮字体
            urgeButton.onclick = function() {
                alert('已向未确认单位发送催办通知');
            };

            urgeButtonContainer.appendChild(urgeButton);
            trackingSection.appendChild(urgeButtonContainer);

            warningContentWrapper.appendChild(trackingSection);

            // 创建下方两列布局容器
            const columnsContainer = document.createElement('div');
            columnsContainer.className = 'warning-columns-grid';

            // 左列：影响路段
            const leftColumn = document.createElement('div');
            leftColumn.className = 'warning-column left-column';

            // 影响路段部分
            const impactSection = document.createElement('div');
            impactSection.className = 'warning-section';

            const impactTitle = document.createElement('h5');
            impactTitle.textContent = '可能影响路段';
            impactTitle.style.fontSize = '18px'; // 增大标题字体
            impactSection.appendChild(impactTitle);

            const impactList = document.createElement('ul');
            impactList.className = 'impact-list';
            impactList.style.fontSize = '16px'; // 增大列表字体

            warningData.impactAreas.forEach(area => {
                const areaItem = document.createElement('li');
                areaItem.innerHTML = `<strong>${area.name}:</strong> ${area.roads.join(', ')}`;
                impactList.appendChild(areaItem);
            });

            impactSection.appendChild(impactList);
            leftColumn.appendChild(impactSection);

            // 将左列添加到列容器
            columnsContainer.appendChild(leftColumn);

            // 右列：防范措施
            const rightColumn = document.createElement('div');
            rightColumn.className = 'warning-column right-column';

            // 防范措施部分
            const precautionsSection = document.createElement('div');
            precautionsSection.className = 'warning-section';

            const precautionsTitle = document.createElement('h5');
            precautionsTitle.textContent = '防范措施建议';
            precautionsTitle.style.fontSize = '18px'; // 增大标题字体
            precautionsSection.appendChild(precautionsTitle);

            const precautionsList = document.createElement('ol');
            precautionsList.className = 'precautions-list';
            precautionsList.style.fontSize = '16px'; // 增大列表字体

            warningData.precautions.forEach(precaution => {
                const precautionItem = document.createElement('li');
                precautionItem.textContent = precaution;
                precautionsList.appendChild(precautionItem);
            });

            precautionsSection.appendChild(precautionsList);
            rightColumn.appendChild(precautionsSection);

            // 将右列添加到列容器
            columnsContainer.appendChild(rightColumn);

            // 将列容器添加到主容器
            warningContentWrapper.appendChild(columnsContainer);

            modalBody.appendChild(warningContentWrapper);



        } else if (markerId.startsWith('event')) {
            // 应急事件模拟数据
            const eventData = {
                id: "event001",
                title: "泉南高速吴家屯隧道山体塌方事故",
                basicInfo: {
                    type: "山体塌方 + 交通事故",
                    status: "处置中",
                    reportedTime: "2023-04-20 10:03",
                    location: "桂林至柳州段 吴家屯隧道出口（柳州端）",
                    impact: "泥石堆积堵塞高速主线双向车道，部分施工车辆被掩埋，通行完全中断，存在次生事故风险。涉及1辆槽罐车（装载33t粗苯）、1辆4.5t厢式货车、1辆载客30人客车、1辆小轿车，造成10人受伤被困车内，槽罐车阀门受损、粗苯泄漏。",
                    cause: "连续强降雨导致山体失稳，排水不畅诱发滑坡。"
                },
                emergencyPlan: {
                    planName: "广西壮族自治区公路交通突发事件应急预案",
                    applicableScope: "适用于公路交通突发事件引发的应急处置",
                    recommendedLevel: "Ⅱ级响应",
                    responseSteps: [
                        "1. 领导小组办公室安排相关人员 24 小时值班值守，及时跟踪监测、研判信息，并及时向领导小组报告信息。",
                        "2. 组织相关单位进行会商，研究解决应急处置的重大事项，视情派出现场工作组或专家组给予指导；",
                        "3. 组织做好应急通信保障工作，确保领导小组与现场指挥部的信息畅通，确保信息上报与接收无误；",
                        "4. 组织做好抢通保通以及有关抢险作业工作，协调、调度应急救援队伍、物资、装备等开展应急处置；",
                        "5. 低温雨雪冰冻灾害发生后，协调组织力量及时清除路面结冰积雪；",
                        "6. 加强灾区公路桥梁、隧道、高边坡等重点部位和薄弱环节的风险监测和隐患排查，防范次生、衍生灾害发生；",
                        "7. 领导小组办公室应与自治区人民政府相关部门、事发地人民政府及交通运输主管部门保持联系，准确掌握应急处置工作动态，及时报告上级应急指挥机构；",
                        "8. 当超出厅本级处置能力时，报请上级应急指挥机构请求支援。"
                    ]
                },
                commandGroup: {
                    title: "泉南高速吴家屯隧道山体塌方事故应急指挥部",
                    structure: {
                        "自治区应急指挥机构": {
                            "领导小组": {
                                members: [
                                    { role: "组长", name: "自治区交通运输厅主要领导", contact: "181xxxx1001" },
                                    { role: "副组长", name: "建设管理处负责人", contact: "181xxxx1002" },
                                    { role: "成员", name: "厅机关相关处室负责人1", contact: "181xxxx1003" },
                                    { role: "成员", name: "厅机关相关处室负责人2", contact: "181xxxx1004" },
                                    { role: "成员", name: "厅直属相关单位负责人", contact: "181xxxx1005" }
                                ]
                            },
                            "领导小组办公室": {
                                members: [
                                    { role: "主任", name: "厅安全总监", contact: "181xxxx1101" },
                                    { role: "副主任", name: "建设管理处负责人", contact: "181xxxx1102" },
                                    { role: "副主任", name: "安全监督处负责人", contact: "181xxxx1103" },
                                    { role: "成员", name: "办公室成员1", contact: "181xxxx1104" },
                                    { role: "成员", name: "办公室成员2", contact: "181xxxx1105" },
                                    { role: "成员", name: "办公室成员3", contact: "181xxxx1106" }
                                ]
                            },
                            "应急工作组": {
                                "综合协调组": {
                                    members: [
                                        { role: "组长", name: "厅办公室负责人", contact: "181xxxx1201" },
                                        { role: "副组长", name: "建设管理处相关负责人", contact: "181xxxx1202" },
                                        { role: "成员", name: "办公室相关人员", contact: "181xxxx1203" },
                                        { role: "成员", name: "建设管理处相关人员", contact: "181xxxx1204" },
                                        { role: "成员", name: "自治区公路发展中心相关人员", contact: "181xxxx1205" }
                                    ]
                                },
                                "应急指挥组": {
                                    members: [
                                        { role: "组长", name: "建设管理处负责人", contact: "181xxxx1301" },
                                        { role: "副组长", name: "自治区公路发展中心负责人", contact: "181xxxx1302" },
                                        { role: "副组长", name: "自治区交通运输综合行政执法局负责人", contact: "181xxxx1303" },
                                        { role: "成员", name: "厅建设管理处人员", contact: "181xxxx1304" },
                                        { role: "成员", name: "自治区公路发展中心人员", contact: "181xxxx1305" },
                                        { role: "成员", name: "自治区交通运输综合行政执法局人员", contact: "181xxxx1306" }
                                    ]
                                },
                                "运输保障组": {
                                    members: [
                                        { role: "组长", name: "综合运输管理处负责人", contact: "181xxxx1401" },
                                        { role: "副组长", name: "自治区道路运输发展中心负责人", contact: "181xxxx1402" },
                                        { role: "成员", name: "厅综合运输管理处人员", contact: "181xxxx1403" },
                                        { role: "成员", name: "自治区道路运输发展中心人员", contact: "181xxxx1404" }
                                    ]
                                },
                                "新闻宣传组": {
                                    members: [
                                        { role: "组长", name: "机关党委专职副书记", contact: "181xxxx1501" },
                                        { role: "副组长", name: "办公室负责人", contact: "181xxxx1502" },
                                        { role: "成员", name: "办公室相关人员", contact: "181xxxx1503" },
                                        { role: "成员", name: "机关党委相关人员", contact: "181xxxx1504" },
                                        { role: "成员", name: "政策法规处相关人员", contact: "181xxxx1505" }
                                    ]
                                },
                                "通信保障组": {
                                    members: [
                                        { role: "组长", name: "科教处负责人", contact: "181xxxx1601" },
                                        { role: "副组长", name: "自治区公路发展中心分管领导", contact: "181xxxx1602" },
                                        { role: "成员", name: "自治区公路发展中心相关人员", contact: "181xxxx1603" },
                                        { role: "成员", name: "科教处相关人员", contact: "181xxxx1604" }
                                    ]
                                },
                                "后勤保障组": {
                                    members: [
                                        { role: "组长", name: "机关服务中心负责人", contact: "181xxxx1701" },
                                        { role: "副组长", name: "财务处负责人", contact: "181xxxx1702" },
                                        { role: "成员", name: "财务处相关人员", contact: "181xxxx1703" },
                                        { role: "成员", name: "交通公安处相关人员", contact: "181xxxx1704" },
                                        { role: "成员", name: "机关服务中心相关人员", contact: "181xxxx1705" }
                                    ]
                                }
                            },
                            "现场工作组": {
                                members: [
                                    { role: "成员", name: "相关处室人员", contact: "181xxxx1801" },
                                    { role: "成员", name: "专业技术人员", contact: "181xxxx1802" },
                                    { role: "成员", name: "专家1", contact: "181xxxx1803" },
                                    { role: "成员", name: "专家2", contact: "181xxxx1804" }
                                ]
                            },
                            "专家组": {
                                members: [
                                    { role: "成员", name: "道路灾害防治专家", contact: "181xxxx1901" }
                                ]
                            }
                        },
                        "市、县级应急指挥机构": {
                            "市级应急指挥机构": {
                                members: [
                                    { role: "组长", name: "市交通运输局局长", contact: "182xxxx1001" },
                                    { role: "副组长", name: "市交通运输局副局长", contact: "182xxxx1002" },
                                    { role: "成员", name: "市公路中心主任", contact: "182xxxx1003" }
                                ]
                            },
                            "县级应急指挥机构": {
                                members: [
                                    { role: "组长", name: "县交通运输局局长", contact: "183xxxx1001" },
                                    { role: "副组长", name: "县交通运输局副局长", contact: "183xxxx1002" },
                                    { role: "成员", name: "县公路中心主任", contact: "183xxxx1003" }
                                ]
                            }
                        }
                    }
                },
                responseTeams: [
                    { name: "桂林市应急救援支队", contact: "救援队长 - 181xxxx2001", equipment: "破拆工具、生命探测仪", specialization: "人员搜救、隧道事故处置" },
                    { name: "柳州市消防救援支队", contact: "消防队长 - 181xxxx2002", equipment: "消防车、泡沫灭火设备", specialization: "危险品处置、火灾扑救" },
                    { name: "广西高速公路养护中心", contact: "养护中心主任 - 181xxxx2003", equipment: "大型清障车、挖掘机", specialization: "道路清障、交通恢复" }
                ],
                experts: [
                    { name: "道路灾害防治专家 - 张教授", expertise: "山体滑坡防治、隧道安全", contact: "181xxxx1901" },
                    { name: "危险化学品处置专家 - 李教授", expertise: "化学品泄漏处置、环境保护", contact: "181xxxx1902" },
                    { name: "应急管理专家 - 王教授", expertise: "应急指挥、预案评估", contact: "181xxxx1903" }
                ],
                nearbySupplies: [
                    { name: "桂林市应急物资储备库", location: "约15km", contact: "储备库主任 - 181xxxx3001", itemsPreview: "警示桩100个、反光锥200个、应急照明设备30套" },
                    { name: "柳州市交通应急物资点", location: "约20km", contact: "物资点负责人 - 181xxxx3002", itemsPreview: "吸油毡200张、围油栏100米、临时路障50个" },
                    { name: "泉南高速施工单位物资点", location: "约5km", contact: "施工单位负责人 - 181xxxx3003", itemsPreview: "挖掘机2台、推土机1台、装载机3台" }
                ]
            };

            modalTitle.textContent = `应急事件详情: ${eventData.title}`;

            // 创建模态框内容
            const eventContentWrapper = document.createElement('div');
            eventContentWrapper.className = 'event-modal-content';

            // 1. 基本信息部分 (顶部全宽)
            const basicInfoSection = document.createElement('div');
            basicInfoSection.className = 'event-section event-top-section';

            const basicInfoTitle = document.createElement('h5');
            basicInfoTitle.textContent = '应急事件基本信息';
            basicInfoSection.appendChild(basicInfoTitle);

            const basicInfoGrid = document.createElement('div');
            basicInfoGrid.className = 'event-basic-info-grid';

            const bi = eventData.basicInfo;
            basicInfoGrid.appendChild(createGenericInfoItem("事件类型", bi.type));
            basicInfoGrid.appendChild(createGenericInfoItem("事件状态", bi.status));
            basicInfoGrid.appendChild(createGenericInfoItem("事发时间", bi.reportedTime));
            basicInfoGrid.appendChild(createGenericInfoItem("事发地点", bi.location));

            basicInfoSection.appendChild(basicInfoGrid);

            const impactInfo = document.createElement('div');
            impactInfo.className = 'info-item';
            impactInfo.style.marginTop = '10px';

            const impactLabel = document.createElement('div');
            impactLabel.className = 'info-label';
            impactLabel.textContent = '事件影响:';
            impactInfo.appendChild(impactLabel);

            const impactValue = document.createElement('div');
            impactValue.className = 'info-value';
            impactValue.textContent = bi.impact;
            impactInfo.appendChild(impactValue);

            basicInfoSection.appendChild(impactInfo);

            const causeInfo = document.createElement('div');
            causeInfo.className = 'info-item';
            causeInfo.style.marginTop = '5px';

            const causeLabel = document.createElement('div');
            causeLabel.className = 'info-label';
            causeLabel.textContent = '初步原因:';
            causeInfo.appendChild(causeLabel);

            const causeValue = document.createElement('div');
            causeValue.className = 'info-value';
            causeValue.textContent = bi.cause;
            causeInfo.appendChild(causeValue);

            basicInfoSection.appendChild(causeInfo);

            eventContentWrapper.appendChild(basicInfoSection);

            // 创建两列布局容器
            const columnsContainer = document.createElement('div');
            columnsContainer.className = 'event-columns-grid';

            // 2. 左列：应急预案信息
            const leftColumn = document.createElement('div');
            leftColumn.className = 'event-column left-column';

            const planSection = document.createElement('div');
            planSection.className = 'event-section plan-section';

            const planTitleContainer = document.createElement('div');
            planTitleContainer.className = 'plan-title-container';

            const planTitle = document.createElement('h5');
            planTitle.textContent = '推荐应急预案';
            planTitleContainer.appendChild(planTitle);

            const viewPlanButton = document.createElement('button');
            viewPlanButton.className = 'view-plan-button';
            viewPlanButton.textContent = '查看预案详情';
            viewPlanButton.onclick = function() {
                window.location.href = 'plan_detail.html';
            };
            planTitleContainer.appendChild(viewPlanButton);

            planSection.appendChild(planTitleContainer);

            const planGrid = document.createElement('div');
            planGrid.className = 'plan-details-grid';

            // 创建预案信息区域
            const planInfoContainer = document.createElement('div');
            planInfoContainer.className = 'plan-info-container';
            planInfoContainer.style.marginBottom = '15px';
            planInfoContainer.style.display = 'flex';
            planInfoContainer.style.flexDirection = 'column';
            planInfoContainer.style.gap = '8px';

            const ep = eventData.emergencyPlan;

            // 预案名称
            const planNameItem = createGenericInfoItem("预案名称", ep.planName);
            planNameItem.style.marginBottom = '5px';
            planInfoContainer.appendChild(planNameItem);

            // 适用范围
            const scopeItem = createGenericInfoItem("适用范围", ep.applicableScope);
            planInfoContainer.appendChild(scopeItem);

            planGrid.appendChild(planInfoContainer);

            // 建议等级行
            const levelRow = document.createElement('div');
            levelRow.className = 'level-row';

            // 建议等级
            const levelItem = document.createElement('div');
            levelItem.className = 'level-item';

            const levelLabel = document.createElement('div');
            levelLabel.className = 'info-label';
            levelLabel.textContent = '建议等级:';
            levelItem.appendChild(levelLabel);

            const levelValue = document.createElement('div');
            levelValue.className = 'info-value';
            levelValue.textContent = ep.recommendedLevel;
            levelValue.style.color = '#ff8080';
            levelValue.style.fontWeight = 'bold';
            levelItem.appendChild(levelValue);

            levelRow.appendChild(levelItem);

            // 响应条件参考
            const conditionItem = document.createElement('div');
            conditionItem.className = 'condition-item';

            const conditionLabel = document.createElement('div');
            conditionLabel.className = 'info-label';
            conditionLabel.textContent = '响应条件参考:';
            conditionItem.appendChild(conditionLabel);

            const conditionValue = document.createElement('div');
            conditionValue.className = 'info-value condition-reference';
            conditionValue.innerHTML = `
                <div class="condition-text">
                    <ul>
                        <li>IV级 (一般): 一般事故，造成3人以下轻伤或仅有财产损失</li>
                        <li>III级 (较大): 较大事故，造成3人以下死亡或10人以下重伤，或造成重要交通干线中断2小时以上</li>
                        <li>II级 (重大): 重大事故，造成3-10人死亡或10-30人重伤，或造成区域性交通瘫痪</li>
                        <li>I级 (特别重大): 特别重大事故，造成10人以上死亡或30人以上重伤，或造成大范围、长时间交通瘫痪</li>
                    </ul>
                </div>
            `;
            conditionItem.appendChild(conditionValue);

            levelRow.appendChild(conditionItem);

            planGrid.appendChild(levelRow);

            // 事件等级选择
            const levelSelectContainer = document.createElement('div');
            levelSelectContainer.className = 'level-select-container';

            const levelSelectLabel = document.createElement('div');
            levelSelectLabel.className = 'level-select-label';
            levelSelectLabel.textContent = '调整事件等级:';
            levelSelectContainer.appendChild(levelSelectLabel);

            const levelSelect = document.createElement('select');
            levelSelect.className = 'level-select';
            levelSelect.id = 'event-level-select';

            const options = [
                { value: 'I', text: 'I级响应（特别重大）' },
                { value: 'II', text: 'II级响应（重大）', selected: true },
                { value: 'III', text: 'III级响应（较大）' },
                { value: 'IV', text: 'IV级响应（一般）' }
            ];

            options.forEach(option => {
                const optionElement = document.createElement('option');
                optionElement.value = option.value;
                optionElement.textContent = option.text;
                if (option.selected) {
                    optionElement.selected = true;
                }
                levelSelect.appendChild(optionElement);
            });

            levelSelectContainer.appendChild(levelSelect);

            // 添加确认按钮
            const confirmButton = document.createElement('button');
            confirmButton.className = 'level-confirm-button';
            confirmButton.textContent = '确认';
            confirmButton.onclick = function() {
                const selectedLevel = levelSelect.value;
                levelValue.textContent = selectedLevel + '级响应';
                // 这里可以添加更多逻辑，例如更新事件数据等
            };
            levelSelectContainer.appendChild(confirmButton);

            planGrid.appendChild(levelSelectContainer);

            planSection.appendChild(planGrid);

            // 响应步骤
            const stepsTitle = document.createElement('h6');
            stepsTitle.textContent = '应急处置';
            stepsTitle.style.marginTop = '15px';
            planSection.appendChild(stepsTitle);

            const stepsList = document.createElement('ol');
            stepsList.className = 'response-steps-list';

            ep.responseSteps.forEach(step => {
                const stepItem = document.createElement('li');
                stepItem.textContent = step.replace(/^\d+\.\s*/, ''); // 移除前面的数字
                stepsList.appendChild(stepItem);
            });

            planSection.appendChild(stepsList);

            // 添加指挥部信息
            const commandTitle = document.createElement('h6');
            commandTitle.textContent = eventData.commandGroup.title;
            commandTitle.style.marginTop = '15px';
            planSection.appendChild(commandTitle);

            // 添加CSS样式
            const commandTreeStyle = document.createElement('style');
            commandTreeStyle.textContent = `
                .command-tree {
                    font-size: 14px;
                    margin-top: 10px;
                }
                .command-tree ul {
                    list-style-type: none;
                    padding-left: 20px;
                }
                .command-tree > ul {
                    padding-left: 0;
                }
                .command-tree li {
                    position: relative;
                    padding: 3px 0;
                }
                .command-tree li::before {
                    content: "";
                    position: absolute;
                    top: 12px;
                    left: -15px;
                    width: 10px;
                    height: 1px;
                    background-color: #4a89dc;
                }
                .command-tree li::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -15px;
                    width: 1px;
                    background-color: #4a89dc;
                }
                .command-tree li:last-child::after {
                    height: 12px;
                }
                .command-tree > ul > li::before,
                .command-tree > ul > li::after {
                    display: none;
                }
                .command-tree .tree-node {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                }
                .command-tree .tree-node-content {
                    margin-left: 5px;
                    flex: 1;
                }
                .command-tree .tree-node-title {
                    font-weight: bold;
                    color: #4a89dc;
                }

                .command-tree .tree-node-members {
                    margin-top: 5px;
                    padding: 5px;
                    background-color: rgba(74, 137, 220, 0.1);
                    border-radius: 4px;
                }
                .command-tree .member-item {
                    margin-bottom: 3px;
                    display: flex;
                }
                .command-tree .member-role {
                    font-weight: bold;
                    min-width: 60px;
                }
                .command-tree .toggle-icon {
                    width: 16px;
                    height: 16px;
                    text-align: center;
                    line-height: 14px;
                    border-radius: 50%;
                    background-color: #4a89dc;
                    color: white;
                    font-size: 12px;
                    cursor: pointer;
                    user-select: none;
                }
                .command-tree .level-1 > .tree-node > .tree-node-content > .tree-node-title {
                    color: #4a89dc;
                    font-size: 15px;
                }
                .command-tree .level-2 > .tree-node > .tree-node-content > .tree-node-title {
                    color: #5cb85c;
                }
                .command-tree .level-3 > .tree-node > .tree-node-content > .tree-node-title {
                    color: #f0ad4e;
                }
                .command-tree .level-4 > .tree-node > .tree-node-content > .tree-node-title {
                    color: #d9534f;
                }
            `;
            document.head.appendChild(commandTreeStyle);

            // 创建树状结构容器
            const commandTree = document.createElement('div');
            commandTree.className = 'command-tree';

            // 递归创建树状结构
            function createTreeNode(data, level = 1) {
                const ul = document.createElement('ul');

                Object.entries(data).forEach(([key, value]) => {
                    const li = document.createElement('li');
                    li.className = `level-${level}`;

                    const treeNode = document.createElement('div');
                    treeNode.className = 'tree-node';

                    // 如果有子节点或成员，添加展开/折叠图标
                    if ((value.members && value.members.length > 0) ||
                        (typeof value === 'object' && !Array.isArray(value) && !value.members)) {
                        const toggleIcon = document.createElement('div');
                        toggleIcon.className = 'toggle-icon';
                        toggleIcon.textContent = '-';
                        toggleIcon.onclick = function(e) {
                            e.stopPropagation();
                            const childrenContainer = li.querySelector(':scope > ul');
                            if (childrenContainer) {
                                const isVisible = childrenContainer.style.display !== 'none';
                                childrenContainer.style.display = isVisible ? 'none' : 'block';
                                toggleIcon.textContent = isVisible ? '+' : '-';
                            }
                        };
                        treeNode.appendChild(toggleIcon);
                    }

                    const nodeContent = document.createElement('div');
                    nodeContent.className = 'tree-node-content';

                    const nodeTitle = document.createElement('div');
                    nodeTitle.className = 'tree-node-title';
                    nodeTitle.textContent = key;
                    nodeContent.appendChild(nodeTitle);

                    // 移除描述部分

                    treeNode.appendChild(nodeContent);
                    li.appendChild(treeNode);

                    // 如果有成员，添加成员列表
                    if (value.members && value.members.length > 0) {
                        const membersContainer = document.createElement('div');
                        membersContainer.className = 'tree-node-members';

                        value.members.forEach(member => {
                            const memberItem = document.createElement('div');
                            memberItem.className = 'member-item';

                            const memberRole = document.createElement('div');
                            memberRole.className = 'member-role';
                            memberRole.textContent = member.role + ':';

                            const memberInfo = document.createElement('div');
                            memberInfo.className = 'member-info';
                            memberInfo.textContent = `${member.name} (${member.contact})`;

                            memberItem.appendChild(memberRole);
                            memberItem.appendChild(memberInfo);
                            membersContainer.appendChild(memberItem);
                        });

                        nodeContent.appendChild(membersContainer);
                    }

                    // 递归处理子节点
                    if (typeof value === 'object' && !Array.isArray(value) && !value.members) {
                        // 只过滤掉members属性
                        const childData = {};
                        Object.entries(value).forEach(([childKey, childValue]) => {
                            if (childKey !== 'members') {
                                childData[childKey] = childValue;
                            }
                        });

                        if (Object.keys(childData).length > 0) {
                            const childrenContainer = createTreeNode(childData, level + 1);
                            li.appendChild(childrenContainer);
                        }
                    }

                    ul.appendChild(li);
                });

                return ul;
            }

            // 创建树状结构
            const treeRoot = createTreeNode(eventData.commandGroup.structure);
            commandTree.appendChild(treeRoot);

            planSection.appendChild(commandTree);

            leftColumn.appendChild(planSection);
            columnsContainer.appendChild(leftColumn);

            // 3. 右列：关联应急资源与专家
            const rightColumn = document.createElement('div');
            rightColumn.className = 'event-column right-column';

            const resourcesSection = document.createElement('div');
            resourcesSection.className = 'event-section resources-section';

            const resourcesTitle = document.createElement('h5');
            resourcesTitle.textContent = '附近应急资源与相应专家';
            resourcesSection.appendChild(resourcesTitle);

            // 创建三个子版块

            // 1. 邻近应急物资
            const suppliesTitle = document.createElement('div');
            suppliesTitle.className = 'resource-subsection-title';
            suppliesTitle.textContent = '邻近应急物资';
            suppliesTitle.style.backgroundColor = '#3a4a5d';
            suppliesTitle.style.color = '#fff';
            suppliesTitle.style.padding = '5px 10px';
            suppliesTitle.style.marginTop = '15px';
            suppliesTitle.style.marginBottom = '10px';
            resourcesSection.appendChild(suppliesTitle);

            // 添加应急物资信息
            eventData.nearbySupplies.forEach(supply => {
                const supplyDiv = document.createElement('div');
                supplyDiv.className = 'resource-item';
                supplyDiv.style.marginBottom = '10px';
                supplyDiv.innerHTML = `
                    <strong>${supply.name}</strong> (${supply.location})<br>
                    联系方式: ${supply.contact}<br>
                    主要物资: ${supply.itemsPreview}
                `;
                resourcesSection.appendChild(supplyDiv);
            });

            // 2. 邻近救援力量
            const rescueTitle = document.createElement('div');
            rescueTitle.className = 'resource-subsection-title';
            rescueTitle.textContent = '邻近救援力量';
            rescueTitle.style.backgroundColor = '#3a4a5d';
            rescueTitle.style.color = '#fff';
            rescueTitle.style.padding = '5px 10px';
            rescueTitle.style.marginTop = '15px';
            rescueTitle.style.marginBottom = '10px';
            resourcesSection.appendChild(rescueTitle);

            // 添加救援力量信息
            eventData.responseTeams.forEach(team => {
                const teamDiv = document.createElement('div');
                teamDiv.className = 'resource-item';
                teamDiv.style.marginBottom = '10px';
                teamDiv.innerHTML = `
                    <strong>${team.name}</strong> ${team.name.includes('已出动') ? '' : '(已出动至现场)'}<br>
                    联系方式: ${team.contact}<br>
                    主要装备: ${team.equipment}
                `;
                resourcesSection.appendChild(teamDiv);
            });

            // 3. 相关领域专家
            const expertsTitle = document.createElement('div');
            expertsTitle.className = 'resource-subsection-title';
            expertsTitle.textContent = '相关领域专家';
            expertsTitle.style.backgroundColor = '#3a4a5d';
            expertsTitle.style.color = '#fff';
            expertsTitle.style.padding = '5px 10px';
            expertsTitle.style.marginTop = '15px';
            expertsTitle.style.marginBottom = '10px';
            resourcesSection.appendChild(expertsTitle);

            // 添加专家信息
            const expertsInfo = document.createElement('div');
            expertsInfo.className = 'experts-info';

            eventData.experts.forEach(expert => {
                const expertDiv = document.createElement('div');
                expertDiv.className = 'expert-item';
                expertDiv.style.marginBottom = '10px';
                expertDiv.innerHTML = `<strong>${expert.name}</strong><br>专业领域: ${expert.expertise}<br>联系电话: ${expert.contact}`;
                expertsInfo.appendChild(expertDiv);
            });

            resourcesSection.appendChild(expertsInfo);

            rightColumn.appendChild(resourcesSection);
            columnsContainer.appendChild(rightColumn);

            eventContentWrapper.appendChild(columnsContainer);

            modalBody.appendChild(eventContentWrapper);

        } else if (markerId.startsWith('traffic')) {
            // 打开拥堵路段模态框
            const trafficJamModal = document.getElementById('traffic-jam-modal');
            if (trafficJamModal) {
                trafficJamModal.style.display = 'flex';
            }
            return; // 直接返回，不使用通用模态框

        } else if (markerId.startsWith('congestion')) {
            // 使用专用的拥堵路段模态框
            const trafficJamModal = document.getElementById('traffic-jam-modal');
            if (trafficJamModal) {
                trafficJamModal.style.display = 'flex';
            }
            return; // 直接返回，不使用通用模态框

        } else if (markerId.startsWith('project')) {
            // --- 驻地信息模拟数据 ---
            const siteData = {
                siteName: "上峒路No JL1总监办",
                siteType: "总监办驻地",
                coordinates: "无",
                projectName: "桂林龙胜（湘桂界）至峒中公路（上思至峒中段）",
                projectType: "高速公路",
                constructionUnit: "广西新发展交通集团有限公司",
                contractor: "广西路桥工程集团有限公司",
                siteAddress: "广西壮族自治区防城港市上思县思阳镇5#主路",
                adminRegion: "广西壮族自治区防城港市上思县思阳镇",
                sitePeopleCount: "13",
                siteRiskLevel: "低风险（I级）",
                houseType: "板房",
                whistleblower: "",
                constructionUnitLeader: "",
                contractorLeader: "",
                siteLeader: "",
                countyContact: "",
                cityContact: "",
                provinceContact: ""
            };

            // 原有项目数据（用于下方关联信息）
            const projectData = {
                id: "project001",
                projectName: siteData.projectName, // 复用驻地所属项目名
                relatedRisks: [ { id: "risk001", name: "泉南高速吴家屯隧道（柳州端）洞口上方山体塌方隐患" } ],
                nearbySupplies: [
                    { id: "supply001", name: "桂林市应急物资储备库" },
                    { id: "supply002", name: "柳州市交通应急物资点" },
                    { id: "supply003", name: "泉南高速施工单位物资点" }
                ],
                nearbyRescueTeams: [
                    { id: "rescue001", name: "桂林市应急救援支队" },
                    { id: "rescue002", name: "柳州市消防救援支队" },
                    { id: "rescue003", name: "广西高速公路养护中心" }
                ]
            };

            modalTitle.textContent = `驻地详情: ${siteData.siteName}`;

            const projectContentWrapper = document.createElement('div');
            projectContentWrapper.className = 'modal-project-details';

            // 1. 驻地基本信息面板
            const basicInfoPanel = document.createElement('div');
            basicInfoPanel.className = 'project-panel project-basic-info-panel';
            const basicInfoTitle = document.createElement('h5');
            basicInfoTitle.textContent = "驻地基本信息";
            basicInfoPanel.appendChild(basicInfoTitle);
            const basicInfoGrid = document.createElement('div');
            basicInfoGrid.className = 'project-basic-info-grid';
            // 按顺序添加字段
            basicInfoGrid.appendChild(createGenericInfoItem("驻地名称", siteData.siteName));
            basicInfoGrid.appendChild(createGenericInfoItem("驻地类型", siteData.siteType));
            basicInfoGrid.appendChild(createGenericInfoItem("坐标点位", siteData.coordinates));
            basicInfoGrid.appendChild(createGenericInfoItem("所属项目名称", siteData.projectName));
            basicInfoGrid.appendChild(createGenericInfoItem("项目类型", siteData.projectType));
            basicInfoGrid.appendChild(createGenericInfoItem("建设单位", siteData.constructionUnit));
            basicInfoGrid.appendChild(createGenericInfoItem("施工单位", siteData.contractor));
            basicInfoGrid.appendChild(createGenericInfoItem("驻地地址", siteData.siteAddress));
            basicInfoGrid.appendChild(createGenericInfoItem("行政区域", siteData.adminRegion));
            basicInfoGrid.appendChild(createGenericInfoItem("驻地人数", siteData.sitePeopleCount));
            basicInfoGrid.appendChild(createGenericInfoItem("驻地风险等级", siteData.siteRiskLevel));
            basicInfoGrid.appendChild(createGenericInfoItem("房建类型", siteData.houseType));
            basicInfoGrid.appendChild(createGenericInfoItem("吹哨人/联系电话", siteData.whistleblower));
            basicInfoGrid.appendChild(createGenericInfoItem("建设单位包保责任人/联系电话", siteData.constructionUnitLeader));
            basicInfoGrid.appendChild(createGenericInfoItem("施工单位包保责任人/联系电话", siteData.contractorLeader));
            basicInfoGrid.appendChild(createGenericInfoItem("驻地现场包保责任人/联系电话", siteData.siteLeader));
            basicInfoGrid.appendChild(createGenericInfoItem("县级包保联系人/联系电话", siteData.countyContact));
            basicInfoGrid.appendChild(createGenericInfoItem("市级包保联系人/联系电话", siteData.cityContact));
            basicInfoGrid.appendChild(createGenericInfoItem("省级包保联系人/联系电话", siteData.provinceContact));
            basicInfoPanel.appendChild(basicInfoGrid);
            projectContentWrapper.appendChild(basicInfoPanel);

            // 2. 关联信息面板（保留原有逻辑）
            const linkedItemsPanel = document.createElement('div');
            linkedItemsPanel.className = 'project-linked-items-panel';
            function createLinkedItemEntry(title, items, type) {
                const sectionDiv = document.createElement('div');
                sectionDiv.className = `linked-section linked-${type}-section`;
                const sectionTitle = document.createElement('h5');
                sectionTitle.textContent = title;
                sectionDiv.appendChild(sectionTitle);
                if (items && items.length > 0) {
                    items.forEach(item => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'linked-item-entry';
                        const itemName = document.createElement('span');
                        itemName.className = 'linked-item-name';
                        itemName.textContent = item.name;
                        itemDiv.appendChild(itemName);
                        const viewButton = document.createElement('button');
                        viewButton.className = 'view-details-button';
                        viewButton.textContent = "查看详情";
                        viewButton.onclick = () => openModal(item.id, { type: 'project', id: projectData.id, name: projectData.projectName });
                        itemDiv.appendChild(viewButton);
                        sectionDiv.appendChild(itemDiv);
                    });
                } else {
                    const noDataItem = document.createElement('p');
                    noDataItem.textContent = "无相关信息";
                    noDataItem.className = 'no-linked-data';
                    sectionDiv.appendChild(noDataItem);
                }
                return sectionDiv;
            }
            linkedItemsPanel.appendChild(createLinkedItemEntry("相关风险隐患", projectData.relatedRisks, "risks"));
            const supportWrapper = document.createElement('div');
            supportWrapper.className = 'linked-support-wrapper';
            supportWrapper.appendChild(createLinkedItemEntry("附近应急物资", projectData.nearbySupplies, "supplies"));
            supportWrapper.appendChild(createLinkedItemEntry("附近救援力量", projectData.nearbyRescueTeams, "rescue"));
            linkedItemsPanel.appendChild(supportWrapper);
            projectContentWrapper.appendChild(linkedItemsPanel);

            modalBody.appendChild(projectContentWrapper);

        } else {
            modalTitle.textContent = "未知点位";
            modalBody.textContent = "无法加载该点位的详细信息。";
        }
        modal.style.display = 'flex';
    }

    if(closeModalButton) {
        closeModalButton.addEventListener('click', () => {
            modal.style.display = 'none';
        });
    }

    window.addEventListener('click', (event) => {
        if (modal && event.target === modal) {
            modal.style.display = 'none';
        } else if (notifyModal && event.target === notifyModal) {
            notifyModal.style.display = 'none';
        }
        // 移除对trafficJamModal的引用，因为它不存在
    });

    // 通知模态框关闭按钮
    if(notifyCloseButton) {
        notifyCloseButton.addEventListener('click', () => {
            notifyModal.style.display = 'none';
        });
    }

    // 取消通知按钮
    if(cancelNotifyButton) {
        cancelNotifyButton.addEventListener('click', () => {
            notifyModal.style.display = 'none';
        });
    }

    // 全选按钮
    if(selectAllUnitsButton) {
        selectAllUnitsButton.addEventListener('click', () => {
            const checkboxes = notifyUnitsList.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        });
    }

    // 全不选按钮
    if(deselectAllUnitsButton) {
        deselectAllUnitsButton.addEventListener('click', () => {
            const checkboxes = notifyUnitsList.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        });
    }

    // 发送通知按钮
    if(sendNotifyButton) {
        sendNotifyButton.addEventListener('click', () => {
            const selectedUnits = [];
            const checkboxes = notifyUnitsList.querySelectorAll('input[type="checkbox"]:checked');

            checkboxes.forEach(checkbox => {
                selectedUnits.push(checkbox.value);
            });

            const message = notifyMessage.value.trim();

            if (selectedUnits.length === 0) {
                alert('请至少选择一个通知单位');
                return;
            }

            if (message === '') {
                alert('请输入通知内容');
                return;
            }

            // 这里可以添加实际的通知发送逻辑
            alert(`已成功向${selectedUnits.length}个单位发送通知`);
            notifyModal.style.display = 'none';
        });
    }

    // 打开通知模态框的函数
    function openNotifyModal(warningData) {
        // 设置预警信息
        notifyWarningName.textContent = warningData.name;
        notifyWarningLevel.textContent = warningData.level;
        notifyWarningLevel.className = `notify-warning-level ${warningData.level.toLowerCase()}`;

        // 生成单位列表
        notifyUnitsList.innerHTML = '';

        // 模拟数据：根据预警区域生成相关单位
        const units = [
            { id: 'unit1', name: '广西交通运输厅' },
            { id: 'unit2', name: '柳州市交通运输局' },
            { id: 'unit3', name: '桂林市交通运输局' },
            { id: 'unit4', name: '广西公路发展中心' },
            { id: 'unit5', name: '广西高速公路发展中心' },
            { id: 'unit6', name: '柳州市公路管理局' },
            { id: 'unit7', name: '桂林市公路管理局' },
            { id: 'unit8', name: '泉南高速公路运营公司' },
            { id: 'unit9', name: '广西交通投资集团' },
            { id: 'unit10', name: '广西壮族自治区气象局' }
        ];

        // 根据预警区域筛选相关单位（实际应用中可以根据业务逻辑调整）
        const relatedUnits = units.filter(unit => {
            // 如果预警区域包含单位所在地，则认为是相关单位
            return warningData.location.includes(unit.name.substring(0, 2));
        });

        // 如果没有匹配到相关单位，则显示所有单位
        const unitsToShow = relatedUnits.length > 0 ? relatedUnits : units;

        // 生成单位列表
        unitsToShow.forEach(unit => {
            const unitItem = document.createElement('div');
            unitItem.className = 'notify-unit-item';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `notify-unit-${unit.id}`;
            checkbox.value = unit.name;

            const label = document.createElement('label');
            label.htmlFor = `notify-unit-${unit.id}`;
            label.textContent = unit.name;

            unitItem.appendChild(checkbox);
            unitItem.appendChild(label);
            notifyUnitsList.appendChild(unitItem);
        });

        // 设置默认通知内容
        notifyMessage.value = `关于${warningData.name}的紧急通知：\n\n${warningData.details}\n\n请相关单位特别关注G72泉南高速桂林至柳州段，尤其是吴家屯隧道出口（柳州端）等山区路段，加强巡查和监测，做好防范工作，确保交通安全。`;

        // 显示模态框
        notifyModal.style.display = 'flex';
    }

    // --- 地图标注点点击事件 (Modal Opening) ---
    // 注意：这个事件绑定已被上面的事件委托替代，这里保留是为了兼容性
    mapMarkers.forEach(marker => {
        marker.addEventListener('click', (event) => {
            // 阻止事件冒泡，避免与事件委托冲突
            event.stopPropagation();
            
            const markerId = marker.getAttribute('data-id');
            const markerType = marker.getAttribute('data-type');

            console.log('旧版事件绑定被触发:', markerId, markerType);

            // 如果是拥堵路段标点，使用专用模态框
            if (markerType === 'congestion') {
                const trafficJamModal = document.getElementById('traffic-jam-modal');
                if (trafficJamModal) {
                    // 强制设置模态框样式
                    trafficJamModal.style.cssText = `
                        display: flex !important;
                        position: fixed !important;
                        z-index: 10000 !important;
                        left: 0 !important;
                        top: 0 !important;
                        width: 100% !important;
                        height: 100% !important;
                        overflow: auto !important;
                        background-color: rgba(0, 0, 0, 0.5) !important;
                        align-items: center !important;
                        justify-content: center !important;
                    `;
                    console.log('通过旧版事件绑定打开拥堵路段模态框，强制设置样式');
                    console.log('模态框元素:', trafficJamModal);
                    console.log('模态框当前样式:', trafficJamModal.style.cssText);
                    console.log('模态框计算样式:', window.getComputedStyle(trafficJamModal).display);
                    
                    // 检查模态框内容
                    const modalContent = trafficJamModal.querySelector('.modal-content');
                    if (modalContent) {
                        console.log('模态框内容元素:', modalContent);
                        console.log('模态框内容样式:', window.getComputedStyle(modalContent).display);
                        // 强制设置模态框内容样式
                        modalContent.style.cssText = `
                            max-width: 1200px !important;
                            max-height: 90vh !important;
                            overflow-y: auto !important;
                            background-color: #2c3e50 !important;
                            color: #ecf0f1 !important;
                            margin: auto !important;
                            border-radius: 8px !important;
                            display: block !important;
                            position: relative !important;
                        `;
                        console.log('已强制设置模态框内容样式');
                    
                    // 额外的调试信息
                    console.log('模态框位置信息:', trafficJamModal.getBoundingClientRect());
                    console.log('模态框是否可见:', trafficJamModal.offsetWidth > 0 && trafficJamModal.offsetHeight > 0);
                    console.log('模态框父元素:', trafficJamModal.parentElement);
                    console.log('页面body:', document.body);
                    
                    // 检查模态框是否在正确的父元素下
                    if (trafficJamModal.parentElement !== document.body) {
                        console.log('模态框不在body下，正在移动到body');
                        document.body.appendChild(trafficJamModal);
                        console.log('已将模态框移动到body下');
                    }
                    
                    // 尝试滚动到模态框位置
                    trafficJamModal.scrollIntoView();
                    

                    } else {
                        console.error('找不到模态框内容元素 .modal-content');
                    }
                    
                    // 填充模态框数据
                    const congestionLevel = marker.getAttribute('data-congestion-level');
                    let jamData = {
                        sectionCode: 'G72-K1500+200',
                        stakeRange: 'K1500+200 - K1501+500',
                        level: '严重拥堵',
                        levelColor: '#e74c3c',
                        startTime: '2024-05-26 14:30',
                        duration: '已持续2小时30分钟',
                        trafficFlow: '0辆/小时（交通中断）',
                        avgSpeed: '0km/h（交通中断）'
                    };

                    // 根据拥堵等级调整数据
                    if (congestionLevel === 'moderate') {
                        jamData = {
                            sectionCode: 'G72-K1485+500',
                            stakeRange: 'K1485+500 - K1486+200',
                            level: '中度拥堵',
                            levelColor: '#f39c12',
                            startTime: '2024-05-26 16:15',
                            duration: '已持续45分钟',
                            trafficFlow: '850辆/小时',
                            avgSpeed: '25km/h'
                        };
                    } else if (congestionLevel === 'light') {
                        jamData = {
                            sectionCode: 'G72-K1520+300',
                            stakeRange: 'K1520+300 - K1520+800',
                            level: '轻度拥堵',
                            levelColor: '#f1c40f',
                            startTime: '2024-05-26 17:00',
                            duration: '已持续20分钟',
                            trafficFlow: '1200辆/小时',
                            avgSpeed: '45km/h'
                        };
                    } else if (congestionLevel === 'severe-soon') {
                        jamData = {
                            sectionCode: 'G72-K1495+100',
                            stakeRange: 'K1495+100 - K1495+600',
                            level: '即将严重拥堵',
                            levelColor: '#e67e22',
                            startTime: '2024-05-26 17:30',
                            duration: '预警发布15分钟',
                            trafficFlow: '1800辆/小时',
                            avgSpeed: '35km/h'
                        };
                    }

                    // 更新模态框内容
                    const titleElement = document.getElementById('traffic-jam-title');
                    if (titleElement) {
                        titleElement.textContent = '拥堵路段详情: G72泉南高速吴家屯隧道路段';
                    }
                    
                    const sectionCodeElement = document.getElementById('jam-section-code');
                    if (sectionCodeElement) {
                        sectionCodeElement.textContent = jamData.sectionCode;
                    }
                    
                    const stakeRangeElement = document.getElementById('jam-stake-range');
                    if (stakeRangeElement) {
                        stakeRangeElement.textContent = jamData.stakeRange;
                    }
                    
                    const levelElement = document.getElementById('jam-level');
                    if (levelElement) {
                        levelElement.textContent = jamData.level;
                        levelElement.style.backgroundColor = jamData.levelColor;
                        levelElement.style.color = 'white';
                        levelElement.style.padding = '3px 10px';
                        levelElement.style.borderRadius = '4px';
                        levelElement.style.fontSize = '14px';
                    }
                    
                    const startTimeElement = document.getElementById('jam-start-time');
                    if (startTimeElement) {
                        startTimeElement.textContent = jamData.startTime;
                    }
                    
                    const durationElement = document.getElementById('jam-duration');
                    if (durationElement) {
                        durationElement.textContent = jamData.duration;
                    }
                    
                    const trafficFlowElement = document.getElementById('jam-traffic-flow');
                    if (trafficFlowElement) {
                        trafficFlowElement.textContent = jamData.trafficFlow;
                    }
                    
                    const avgSpeedElement = document.getElementById('jam-avg-speed');
                    if (avgSpeedElement) {
                        avgSpeedElement.textContent = jamData.avgSpeed;
                    }
                }
                return;
            }

            // 检查当前活动的标签页，如果是应急一张图且是应急事件标点，使用专用模态框
            const monitoringWarningTab = document.querySelector('.tab-button[data-tab="monitoring-warning"]');
            const isMonitoringWarningActive = monitoringWarningTab && monitoringWarningTab.classList.contains('active');
            
            if (isMonitoringWarningActive && markerType === 'event') {
                // 应急事件标点 - 使用完整的应急事件模态框
                const emergencyEventModal = document.getElementById('emergency-event-modal');
                if (emergencyEventModal) {
                    // 设置模态框样式
                    emergencyEventModal.style.cssText = `
                        display: flex !important;
                        position: fixed !important;
                        z-index: 10000 !important;
                        left: 0 !important;
                        top: 0 !important;
                        width: 100% !important;
                        height: 100% !important;
                        overflow: auto !important;
                        background-color: rgba(0, 0, 0, 0.5) !important;
                        align-items: center !important;
                        justify-content: center !important;
                    `;
                    
                    console.log('旧版事件绑定：打开完整的应急事件专用模态框');
                    
                    // 确保其他模态框不会显示
                    const modal = document.getElementById('details-modal');
                    if (modal) modal.style.display = 'none';
                }
                return;
            }
            
            // 其他标点使用通用详情模态框
            openModal(markerId);
        });
    });

    // 初始化防汛防台页面中的地图标记点点击事件
    const floodMapMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker');
    floodMapMarkers.forEach(marker => {
        marker.addEventListener('click', () => {
            const markerId = marker.getAttribute('data-id');
            const markerType = marker.getAttribute('data-type');

            // 如果是拥堵路段标点，不做处理（因为没有拥堵路段模态框）
            if (markerType === 'traffic-jam') {
                console.log('拥堵路段标点被点击，但没有对应的模态框');
                return;
            }

            // 所有标点都使用通用详情模态框
            openModal(markerId);
        });
    });

    // --- 风险详情列表中的 "查看详情" 按钮点击事件 ---
    // detailButtonsInTable.forEach(button => {
    //     button.addEventListener('click', () => {
    //         const markerId = button.getAttribute('data-id');
    //         openModal(markerId);
    //     });
    // });

    // --- Left Sidebar Filter Logic ---
    // 风险一张图的全选/全不选
    const resourceTypeSelectAll = document.getElementById('res-type-all');
    const resourceTypeCheckboxes = document.querySelectorAll('#risk-map-content .resource-type-selector input[name="resource-type"]:not(#res-type-all)');

    // 监测预警的全选/全不选
    const monitorSelectAll = document.getElementById('monitor-all');
    const monitorCheckboxes = document.querySelectorAll('#monitoring-warning-content .resource-type-selector input[type="checkbox"]:not(#monitor-all)');

    // 防汛防台的全选/全不选
    const floodSelectAll = document.getElementById('flood-all');
    const floodCheckboxes = document.querySelectorAll('#flood-typhoon-prevention-content .resource-type-selector input[type="checkbox"]:not(#flood-all)');

    // 获取树形结构的展开/折叠按钮
    const treeTogglers = document.querySelectorAll('.collapsible-tree .tree-toggler');

        // Function to update visibility of map markers based on resource type selection
        function updateMapMarkersVisibility() {
            console.log('updateMapMarkersVisibility called');

            // 获取风险一张图标签页中的所有标记点
            const riskMapMarkers = document.querySelectorAll('#risk-map-content .map-marker');

            if (!riskMapMarkers || riskMapMarkers.length === 0) {
                console.error('No map markers found in risk map tab!');
                return;
            }

            // 获取选中的资源类型
            const activeTypes = new Set();

            if (resourceTypeSelectAll && resourceTypeSelectAll.checked) {
                // 如果"全部"被选中，添加所有资源类型
                resourceTypeCheckboxes.forEach(cb => activeTypes.add(cb.value));
                // 显示所有标记点
                riskMapMarkers.forEach(marker => {
                    marker.style.display = '';
                });
                return;
            } else {
                // 否则，只添加被选中的资源类型
                resourceTypeCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        activeTypes.add(checkbox.value);
                    }
                });
            }

            console.log('Active types:', Array.from(activeTypes));

            // 更新标记点的可见性
            riskMapMarkers.forEach(marker => {
                const markerType = marker.getAttribute('data-type');
                const shouldBeVisible = activeTypes.has(markerType);

                console.log(`Marker type: ${markerType}, Should be visible: ${shouldBeVisible}`);

                marker.style.display = shouldBeVisible ? '' : 'none';
            });
        }

    // 风险一张图 - 全选/全不选
    if (resourceTypeSelectAll) {
        resourceTypeSelectAll.addEventListener('change', (event) => {
            resourceTypeCheckboxes.forEach(checkbox => {
                checkbox.checked = event.target.checked;
            });
            updateMapMarkersVisibility(); // Update map markers
            // console.log('All resource types toggled:', event.target.checked);
        });
    }

    // 监测预警 - 全选/全不选
    if (monitorSelectAll) {
        monitorSelectAll.addEventListener('change', (event) => {
            monitorCheckboxes.forEach(checkbox => {
                checkbox.checked = event.target.checked;
            });
            // 更新地图标记点可见性
            const monitorMapMarkers = document.querySelectorAll('#monitoring-warning-content .map-marker');
            if (event.target.checked) {
                // 如果全选，显示所有标记点
                monitorMapMarkers.forEach(marker => {
                    marker.style.display = '';
                });
            } else {
                // 如果全不选，隐藏所有标记点
                monitorMapMarkers.forEach(marker => {
                    marker.style.display = 'none';
                });
            }
        });
    }

    // 应急一张图 - 全选复选框
    const monitorAllTypesCheckbox = document.getElementById('monitor-all-types');
    if (monitorAllTypesCheckbox) {
        monitorAllTypesCheckbox.addEventListener('change', (event) => {
            // 获取所有标点
            const allMarkers = document.querySelectorAll('#monitoring-warning-content .map-marker');

            if (event.target.checked) {
                // 如果选中全选，显示所有标点
                allMarkers.forEach(marker => {
                    marker.style.display = '';
                    marker.style.visibility = 'visible';
                    marker.style.opacity = '1';
                    console.log('应急一张图标点显示(全选):', marker.getAttribute('data-id'));
                });

                // 同时，取消所有标签卡的选中状态
                const resourceTabButtons = document.querySelectorAll('.resource-type-tabs .resource-tab-button');
                resourceTabButtons.forEach(btn => {
                    btn.classList.remove('active');
                });

                // 默认选中第一个标签卡（应急事件）
                const firstTabButton = document.querySelector('.resource-type-tabs .resource-tab-button');
                if (firstTabButton) {
                    firstTabButton.classList.add('active');
                }

                // 显示第一个标签卡对应的筛选内容
                const firstTabContent = document.getElementById('emergency-events-content');
                if (firstTabContent) {
                    const contentElements = document.querySelectorAll('.resource-filter-content');
                    contentElements.forEach(content => {
                        content.classList.remove('active');
                    });
                    firstTabContent.classList.add('active');
                }
            } else {
                // 如果取消全选，根据当前选中的标签卡显示对应类型的标点
                const activeTabButton = document.querySelector('.resource-type-tabs .resource-tab-button.active');
                if (activeTabButton) {
                    const markerType = activeTabButton.getAttribute('data-marker-type');
                    allMarkers.forEach(marker => {
                        const type = marker.getAttribute('data-type');
                        if (type === markerType) {
                            marker.style.display = '';
                        } else {
                            marker.style.display = 'none';
                        }
                    });
                } else {
                    // 如果没有选中的标签卡，默认显示应急事件标点
                    allMarkers.forEach(marker => {
                        const type = marker.getAttribute('data-type');
                        if (type === 'event') {
                            marker.style.display = '';
                        } else {
                            marker.style.display = 'none';
                        }
                    });
                }
            }
        });
    }

    // 防汛防台 - 全选/全不选
    if (floodSelectAll) {
        floodSelectAll.addEventListener('change', (event) => {
            floodCheckboxes.forEach(checkbox => {
                checkbox.checked = event.target.checked;
            });
            // 更新地图标记点可见性
            const floodMapMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker');
            if (event.target.checked) {
                // 如果全选，显示所有标记点
                floodMapMarkers.forEach(marker => {
                    marker.style.display = '';
                    marker.style.visibility = 'visible';
                    marker.style.opacity = '1';
                    console.log('防汛防台标点显示(全选):', marker.getAttribute('data-id'));
                });
            } else {
                // 如果全不选，隐藏所有标记点
                floodMapMarkers.forEach(marker => {
                    marker.style.display = 'none';
                });
            }
        });
    }

    // 监测预警 - 单个复选框变化
    monitorCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            if (monitorSelectAll) {
                if (!checkbox.checked) {
                    monitorSelectAll.checked = false;
                } else {
                    let allChecked = true;
                    monitorCheckboxes.forEach(cb => {
                        if (!cb.checked) {
                            allChecked = false;
                        }
                    });
                    monitorSelectAll.checked = allChecked;
                }
            }

            // 更新对应类型的地图标记点可见性
            const markerType = checkbox.id.replace('monitor-', '');
            console.log('Marker type:', markerType);
            console.log('Checkbox ID:', checkbox.id);
            console.log('Checkbox checked:', checkbox.checked);

            // 使用data-type属性来匹配标记点
            const markers = document.querySelectorAll(`#monitoring-warning-content .map-marker[data-type="${markerType}"]`);
            console.log('Found markers:', markers.length);

            markers.forEach(marker => {
                marker.style.display = checkbox.checked ? '' : 'none';
                console.log('Setting marker display to:', checkbox.checked ? 'visible' : 'none');
            });
        });
    });

    // 防汛防台 - 单个复选框变化
    floodCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            if (floodSelectAll) {
                if (!checkbox.checked) {
                    floodSelectAll.checked = false;
                } else {
                    let allChecked = true;
                    floodCheckboxes.forEach(cb => {
                        if (!cb.checked) {
                            allChecked = false;
                        }
                    });
                    floodSelectAll.checked = allChecked;
                }
            }

            // 更新对应类型的地图标记点可见性
            const markerType = checkbox.id.replace('flood-', '');

            // 使用data-type属性来匹配标记点
            const markers = document.querySelectorAll(`#flood-typhoon-prevention-content .map-marker[data-type="${markerType}"]`);

            markers.forEach(marker => {
                marker.style.display = checkbox.checked ? '' : 'none';
            });
        });
    });

    // 防汛防台 - 预警等级筛选
    const warningLevelSelect = document.getElementById('warning-level-select');
    if (warningLevelSelect) {
        warningLevelSelect.addEventListener('change', () => {
            const selectedLevel = warningLevelSelect.value;
            const warningMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker[data-type="warning"]');

            warningMarkers.forEach(marker => {
                const markerLevel = marker.getAttribute('data-warning-level');
                if (selectedLevel === 'all' || selectedLevel === markerLevel) {
                    marker.style.display = '';
                } else {
                    marker.style.display = 'none';
                }
            });
        });
    }

    // 防汛防台 - 灾害类型筛选
    const disasterTypeSelect = document.getElementById('disaster-type-select');
    if (disasterTypeSelect) {
        disasterTypeSelect.addEventListener('change', () => {
            const selectedType = disasterTypeSelect.value;
            const warningMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker[data-type="warning"]');

            warningMarkers.forEach(marker => {
                const markerType = marker.getAttribute('data-warning-type');
                if (selectedType === 'all' || selectedType === markerType) {
                    marker.style.display = '';
                } else {
                    marker.style.display = 'none';
                }
            });
        });
    }

    // 防汛防台 - 发布时间筛选
    const publishDateInput = document.getElementById('publish-date');
    if (publishDateInput) {
        publishDateInput.addEventListener('change', () => {
            const selectedDate = publishDateInput.value;
            if (selectedDate) {
                // 这里可以添加日期筛选逻辑
                console.log('选择的日期:', selectedDate);
                // 实际应用中，需要根据标记点的日期属性进行筛选
            } else {
                // 如果没有选择日期，显示所有标记点
                const warningMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker[data-type="warning"]');
                warningMarkers.forEach(marker => {
                    marker.style.display = '';
                });
            }
        });
    }

    // 防汛防台 - 关键字搜索
    const keywordSearchInput = document.getElementById('keyword-search');
    const searchBtn = document.getElementById('search-btn');
    if (keywordSearchInput && searchBtn) {
        searchBtn.addEventListener('click', () => {
            const keyword = keywordSearchInput.value.trim().toLowerCase();
            if (keyword) {
                // 这里可以添加关键字搜索逻辑
                console.log('搜索关键字:', keyword);
                // 实际应用中，需要根据标记点的相关属性进行搜索
            } else {
                // 如果没有输入关键字，显示所有标记点
                const warningMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker[data-type="warning"]');
                warningMarkers.forEach(marker => {
                    marker.style.display = '';
                });
            }
        });

        // 回车键触发搜索
        keywordSearchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                searchBtn.click();
            }
        });
    }

    // 风险一张图 - 单个复选框变化
    resourceTypeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            if (resourceTypeSelectAll) { // Logic to sync "Select All" with individual checkboxes
                if (!checkbox.checked) {
                    resourceTypeSelectAll.checked = false;
                } else {
                    let allChecked = true;
                    resourceTypeCheckboxes.forEach(cb => {
                        if (!cb.checked) {
                            allChecked = false;
                        }
                    });
                    resourceTypeSelectAll.checked = allChecked;
                }
            }
            updateMapMarkersVisibility(); // Update map markers
            // console.log('Resource type changed:', checkbox.value, checkbox.checked);
        });
    });

    // 直接添加按单位/按路段划分标签卡切换功能
    document.addEventListener('click', function(event) {
        // 检查点击的是否是按单位/按路段划分标签卡按钮
        if (event.target.classList.contains('filter-tab-button')) {
            const button = event.target;

            // 移除所有按钮的active类
            const parentContainer = button.closest('.filter-tabs');
            if (parentContainer) {
                const siblingButtons = parentContainer.querySelectorAll('.filter-tab-button');
                siblingButtons.forEach(btn => btn.classList.remove('active'));
            }

            // 添加当前按钮的active类
            button.classList.add('active');

            // 获取目标内容ID
            const tabType = button.getAttribute('data-tab');
            const targetTabContentId = `filter-by-${tabType}-content`;

            // 获取相关的内容元素
            const parentSection = button.closest('.resource-condition-filter');
            if (parentSection) {
                const contentElements = parentSection.querySelectorAll('.filter-tab-content');

                contentElements.forEach(content => {
                    if (content.id === targetTabContentId) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            }
        }
    });

    // Collapsible Tree Toggler Logic
    treeTogglers.forEach(toggler => {
        toggler.addEventListener('click', () => {
            const parentLi = toggler.parentElement;
            const subList = parentLi.querySelector('ul');
            if (subList) {
                parentLi.classList.toggle('open');
                toggler.textContent = parentLi.classList.contains('open') ? '−' : '+';
            }
        });
        const parentLi = toggler.parentElement;
        if (parentLi.classList.contains('open')) {
            toggler.textContent = '−';
        }
    });

    // --- Collapsible Tree Checkbox Interaction Logic ---
    function getDescendantCheckboxes(parentLi) {
        const subUl = parentLi.querySelector('ul');
        if (subUl) {
            return subUl.querySelectorAll('input[type="checkbox"]');
        }
        return [];
    }

    function getParentCheckbox(currentLi) {
        const parentUl = currentLi.parentElement;
        if (parentUl && (parentUl.classList.contains('collapsible-tree') || (parentUl.tagName === 'UL' && parentUl.parentElement && parentUl.parentElement.tagName === 'LI'))) {
            const grandParentLi = parentUl.parentElement;
            if (grandParentLi && grandParentLi.tagName === 'LI') {
                const checkbox = Array.from(grandParentLi.childNodes).find(node => node.tagName === 'INPUT' && node.type === 'checkbox');
                return checkbox || null;
            }
        }
        return null;
    }

    function updateChildrenState(parentCheckbox) {
        const parentLi = parentCheckbox.closest('li');
        const children = getDescendantCheckboxes(parentLi);
        children.forEach(child => {
            if (child.checked !== parentCheckbox.checked) {
                child.checked = parentCheckbox.checked;
            }
        });
    }

    function updateParentState(currentCheckbox) {
        const currentLi = currentCheckbox.closest('li');
        const parentCheckbox = getParentCheckbox(currentLi);

        if (parentCheckbox) {
            const parentLi = parentCheckbox.closest('li');
            const directSubUl = parentLi.querySelector('ul');
            let allDirectChildrenChecked = true;
            if (directSubUl) {
                const directChildrenLi = Array.from(directSubUl.children).filter(node => node.tagName === 'LI');
                if (directChildrenLi.length > 0) {
                    for (const li of directChildrenLi) {
                        const cb = li.querySelector('input[type=\"checkbox\"]');
                        if (cb && !cb.checked) {
                            allDirectChildrenChecked = false;
                            break;
                        }
                    }
                } else {
                    allDirectChildrenChecked = parentCheckbox.checked;
                }
            } else {
                return;
            }

            if (parentCheckbox.checked !== allDirectChildrenChecked) {
                parentCheckbox.checked = allDirectChildrenChecked;
                updateParentState(parentCheckbox);
            }
        }
    }

    const treeCheckboxes = document.querySelectorAll('.collapsible-tree input[type="checkbox"]');
    treeCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', (event) => {
            const currentCheckbox = event.target;
            updateChildrenState(currentCheckbox);
            updateParentState(currentCheckbox);
            // console.log('Tree checkbox changed:', currentCheckbox.id, currentCheckbox.checked);
        });
    });

    // Initialize map marker visibility on page load based on initial checkbox states
    updateMapMarkersVisibility();

    // 初始化应急一张图标点显示状态
    if (monitorAllTypesCheckbox) {
        // 强制设置全选复选框为选中状态
        monitorAllTypesCheckbox.checked = true;
        console.log('应急一张图全选复选框初始化为选中状态');

        // 显示所有标点
        const allMarkers = document.querySelectorAll('#monitoring-warning-content .map-marker');
        allMarkers.forEach(marker => {
            marker.style.display = '';
            marker.style.visibility = 'visible';
            marker.style.opacity = '1';
            console.log('应急一张图标点初始化显示:', marker.getAttribute('data-id'));
        });

        // 确保应急事件标签卡处于激活状态
        const eventTabButton = document.querySelector('#monitoring-warning-content .resource-tab-button[data-tab="emergency-events"]');
        if (eventTabButton) {
            eventTabButton.classList.add('active');
        }

        // 显示应急事件内容
        const eventContent = document.getElementById('emergency-events-content');
        if (eventContent) {
            eventContent.classList.add('active');
        }
    }

    // 初始化防汛防台标点显示状态
    if (floodSelectAll) {
        // 强制设置全选复选框为选中状态
        floodSelectAll.checked = true;
        console.log('防汛防台全选复选框初始化为选中状态');

        // 显示所有标点
        const floodMarkers = document.querySelectorAll('#flood-typhoon-prevention-content .map-marker');
        floodMarkers.forEach(marker => {
            marker.style.display = '';
            marker.style.visibility = 'visible';
            marker.style.opacity = '1';
            console.log('防汛防台标点初始化显示:', marker.getAttribute('data-id'));
        });
    }

    // 初始化风险一张图标点显示状态
    const riskAllCheckbox = document.getElementById('res-type-all');
    if (riskAllCheckbox) {
        // 强制设置全选复选框为选中状态
        riskAllCheckbox.checked = true;
        console.log('风险一张图全选复选框初始化为选中状态');

        // 显示所有风险隐患点
        const riskMarkers = document.querySelectorAll('.risk-marker');
        riskMarkers.forEach(marker => {
            marker.style.display = 'block';
            marker.style.visibility = 'visible';
            marker.style.opacity = '1';
            console.log('风险一张图标点初始化显示:', marker.getAttribute('data-id'));
        });
    }

    // Initialize the first filter tab as active (if not already done by HTML)
    // const defaultFilterTab = document.querySelector('.filter-tab-button.active');
    // if (defaultFilterTab) defaultFilterTab.click(); // This might be redundant

    // 详情列表标签卡切换函数
    window.switchDetailsTab = function(button, tabType) {
        // 移除所有按钮的active类
        const parentContainer = button.closest('.details-tabs');
        if (parentContainer) {
            const siblingButtons = parentContainer.querySelectorAll('.details-tab-button');
            siblingButtons.forEach(btn => btn.classList.remove('active'));
        }

        // 添加当前按钮的active类
        button.classList.add('active');

        // 获取目标内容ID
        const targetTabContentId = tabType;

        // 获取相关的内容元素
        const parentSection = button.closest('.risk-details-list');
        if (parentSection) {
            const contentElements = parentSection.querySelectorAll('.details-tab-content');

            contentElements.forEach(content => {
                if (content.id === targetTabContentId) {
                    content.classList.add('active');
                } else {
                    content.classList.remove('active');
                }
            });
        }
    }
});