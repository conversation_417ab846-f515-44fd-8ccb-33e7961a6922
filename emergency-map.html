<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>应急一张图 - 广西交通运输应急管理系统</title>

  <!-- 引入公共样式 -->
  <link rel="stylesheet" href="css/emergency-common.css">
  <link rel="stylesheet" href="new_style.css">

  <!-- 引入字体图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

  <!-- 引入导航栏组件 -->
  <script src="js/navigation-component.js"></script>

  <!-- 自定义样式 -->
  <link rel="stylesheet" href="emergency-map.css">
  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

  <script>
    const API_BASE_URL = 'http://*************:8380';
  </script>
</head>
<body>
<div class="container">
  <!-- 顶部导航栏容器 -->
  <div id="navigation-container"></div>

  <!-- 主要内容区域 -->
  <main class="main-content">
    <div class="tab-content-container" style="width: 100%;">
      <!-- 应急一张图内容 -->
      <div id="emergency-map-content" class="tab-content" style="display: flex;width: 100%;">
        <aside class="left-sidebar">
          <div class="resource-filter-container">
            <!-- 1. 资源类型选择器 -->
            <div class="resource-type-selector">
              <h4>资源类型</h4>
              <div class="resource-type-item select-all-res-types">
                <input type="checkbox" id="res-type-all" name="resource-type-all" onclick="toggleAllMarkers(this)">
                <label for="res-type-all"><strong>全选/全不选</strong></label>
              </div>

              <div class="resource-type-tabs" style="position: relative;">
                <div style="display: flex; justify-content: space-between; width: 100%;">
                  <button style="padding: 5px" class="resource-tab-button" data-type="events">应急事件</button>
                  <button style="padding: 5px" class="resource-tab-button" data-type="supplies">应急物资</button>
                  <button style="padding: 5px" class="resource-tab-button" data-type="teams">救援队伍</button>
                  <button style="padding: 5px" class="resource-tab-button" data-type="others">其他</button>
                </div>
              </div>
            </div>

            <!-- 2. 各类型资源筛选内容 -->
            <div class="resource-content-container">
              <!-- 应急事件筛选内容 -->
              <div id="emergency-events-content" class="resource-tab-content active">
                <div class="filter-section">
                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="event-level-select">事件等级：</label>
                      <select id="event-level-select" class="filter-select">
                        <option value="all">所有等级</option>
                        <option value="1">I级(特别重大)</option>
                        <option value="2">II级(重大)</option>
                        <option value="3">III级(较大)</option>
                        <option value="4">IV级(一般)</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="event-type-select">事件类型：</label>
                      <select id="event-type-select" class="filter-select">
                        <option value="all">所有类型</option>
                        <option value="1">道路交通事故</option>
                        <option value="2">水路交通事故</option>
                        <option value="3">铁路交通事故</option>
                        <option value="4">航空交通事故</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="event-status-select">处置状态：</label>
                      <select id="event-status-select" class="filter-select">
                        <option value="all">所有状态</option>
                        <option value="1">待确认</option>
                        <option value="2">已确认</option>
                        <option value="3">已完成</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 应急物资筛选内容 -->
              <div id="emergency-supplies-content" class="resource-tab-content">
                <div class="filter-section">
                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="supply-type-select">物资类型：</label>
                      <select id="supply-type-select" class="filter-select">
                        <option value="all">所有类型</option>
                        <option value="rescue-equipment">救援装备</option>
                        <option value="medical-supplies">医疗用品</option>
                        <option value="living-supplies">生活物资</option>
                        <option value="communication-equipment">通信设备</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="supply-status-select">储备状态：</label>
                      <select id="supply-status-select" class="filter-select">
                        <option value="all">所有状态</option>
                        <option value="sufficient">充足</option>
                        <option value="normal">一般</option>
                        <option value="insufficient">不足</option>
                        <option value="shortage">紧缺</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="supply-level-select">物资等级：</label>
                      <select id="supply-level-select" class="filter-select">
                        <option value="all">所有等级</option>
                        <option value="level-1">一级储备</option>
                        <option value="level-2">二级储备</option>
                        <option value="level-3">三级储备</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 救援队伍筛选内容 -->
              <div id="emergency-teams-content" class="resource-tab-content">
                <div class="filter-section">
                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="team-type-select">队伍类型：</label>
                      <select id="team-type-select" class="filter-select">
                        <option value="all">所有类型</option>
                        <option value="professional-rescue">专业救援队</option>
                        <option value="fire-rescue">消防救援队</option>
                        <option value="traffic-rescue">交通救援队</option>
                        <option value="medical-rescue">医疗救援队</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="team-status-select">队伍状态：</label>
                      <select id="team-status-select" class="filter-select">
                        <option value="all">所有状态</option>
                        <option value="standby">待命</option>
                        <option value="dispatched">出动中</option>
                        <option value="on-mission">执行任务</option>
                        <option value="resting">休整中</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="team-level-select">队伍等级：</label>
                      <select id="team-level-select" class="filter-select">
                        <option value="all">所有等级</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="county">县级</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 其他资源筛选内容 -->
              <div id="emergency-others-content" class="resource-tab-content">
                <div class="filter-section">
                  <div class="filter-row">
                    <div class="filter-item other-type-list-container">
                      <label>资源类型：</label>
                      <div class="other-type-list" style="width: calc(100% - 20px);">
                        <div class="other-type-item">
                          <input type="checkbox" id="other-type-rescue-vehicle" name="other-type"
                                 class="filter-checkbox"
                                 checked>
                          <label for="other-type-rescue-vehicle">救援车辆</label>
                        </div>
                        <div class="other-type-item">
                          <input type="checkbox" id="other-type-medical-point" name="other-type" class="filter-checkbox"
                                 checked>
                          <label for="other-type-medical-point">医疗点</label>
                        </div>
                        <div class="other-type-item">
                          <input type="checkbox" id="other-type-fire-point" name="other-type" class="filter-checkbox"
                                 checked>
                          <label for="other-type-fire-point">消防点</label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="vehicle-type-select">车辆类型：</label>
                      <select id="vehicle-type-select" class="filter-select">
                        <option value="all">所有车辆</option>
                        <option value="ambulance">救护车</option>
                        <option value="fire-truck">消防车</option>
                        <option value="wrecker">清障车</option>
                        <option value="engineering">工程车</option>
                      </select>
                    </div>
                  </div>

                  <div class="filter-row">
                    <div class="filter-item">
                      <label for="facility-status-select">设施状态：</label>
                      <select id="facility-status-select" class="filter-select">
                        <option value="all">所有状态</option>
                        <option value="available">可用</option>
                        <option value="busy">使用中</option>
                        <option value="maintenance">维护中</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 3. 资源条件筛选 -->
            <div class="resource-condition-filter">
              <div class="filter-tabs">
                <button class="filter-tab-button active" data-tab="unit" onclick="switchFilterTab(this, 'unit')">
                  按单位划分
                </button>
                <button class="filter-tab-button" data-tab="road" onclick="switchFilterTab(this, 'road')">按路段划分
                </button>
              </div>

              <!-- 3.1 按单位划分内容 -->
              <div id="filter-by-unit-content" class="filter-tab-content active">
                <ul class="collapsible-tree">
                  <li>
                    <span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt" value="gxtyt"><label
                    for="unit-gxtyt">广西交通运输厅</label>
                    <ul>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-nn" value="gxtyt-nn"><label
                        for="unit-gxtyt-nn">南宁市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lz" value="gxtyt-lz"><label
                        for="unit-gxtyt-lz">柳州市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gl" value="gxtyt-gl"><label
                        for="unit-gxtyt-gl">桂林市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-wz" value="gxtyt-wz"><label
                        for="unit-gxtyt-wz">梧州市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bh" value="gxtyt-bh"><label
                        for="unit-gxtyt-bh">北海市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-fcg"
                                                                    value="gxtyt-fcg"><label for="unit-gxtyt-fcg">防城港市交通运输局</label>
                      </li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-qz" value="gxtyt-qz"><label
                        for="unit-gxtyt-qz">钦州市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gg" value="gxtyt-gg"><label
                        for="unit-gxtyt-gg">贵港市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-yl" value="gxtyt-yl"><label
                        for="unit-gxtyt-yl">玉林市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bs" value="gxtyt-bs"><label
                        for="unit-gxtyt-bs">百色市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hz" value="gxtyt-hz"><label
                        for="unit-gxtyt-hz">贺州市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hc" value="gxtyt-hc"><label
                        for="unit-gxtyt-hc">河池市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lb" value="gxtyt-lb"><label
                        for="unit-gxtyt-lb">来宾市交通运输局</label></li>
                      <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-cz" value="gxtyt-cz"><label
                        for="unit-gxtyt-cz">崇左市交通运输局</label></li>
                    </ul>
                  </li>
                  <li>
                    <input type="checkbox" id="unit-qy" value="qy"><label for="unit-qy">企业</label>
                  </li>
                </ul>
              </div>

              <!-- 3.2 按路段划分内容 -->
              <div id="filter-by-road-content" class="filter-tab-content">
                <ul class="collapsible-tree">
                  <li>
                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl" value="gl"><label
                    for="road-gl">公路</label>
                    <ul>
                      <li>
                        <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gs" value="gl-gs"><label
                        for="road-gl-gs">高速公路</label>
                        <ul>
                          <li><input type="checkbox" id="road-gl-gs-g72" value="gl-gs-g72"><label for="road-gl-gs-g72">G72</label>
                          </li>
                          <li><input type="checkbox" id="road-gl-gs-g80" value="gl-gs-g80"><label for="road-gl-gs-g80">G80</label>
                          </li>
                        </ul>
                      </li>
                      <li>
                        <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gsgd"
                                                                  value="gl-gsgd"><label
                        for="road-gl-gsgd">国省干道</label>
                        <ul>
                          <li><input type="checkbox" id="road-gl-gsgd-s201" value="gl-gsgd-s201"><label
                            for="road-gl-gsgd-s201">S201</label></li>
                        </ul>
                      </li>
                    </ul>
                  </li>
                  <li>
                    <span class="tree-toggler">+</span><input type="checkbox" id="road-sl" value="sl"><label
                    for="road-sl">水路</label>
                    <ul>
                      <li><input type="checkbox" id="road-sl-xn" value="sl-xn"><label for="road-sl-xn">西江航道</label>
                      </li>
                    </ul>
                  </li>
                </ul>
              </div>
            </div>

            <!-- 4. 告警信息列表 -->
            <div class="alert-list-container">
              <h4>告警信息</h4>
              <div class="alert-tabs" style="display: flex; margin-bottom: 15px; border-bottom: 1px solid #ddd;">
                <button class="alert-tab-button active" data-tab="emergency-events"
                        style="flex: 1; padding: 10px 15px; border: none; background: #f8f9fa; color: #333; cursor: pointer; border-bottom: 2px solid #ff6b35; font-weight: bold;">
                  应急事件
                </button>
                <button class="alert-tab-button" data-tab="verification-overdue"
                        style="flex: 1; padding: 10px 15px; border: none; background: #f8f9fa; color: #666; cursor: pointer; border-bottom: 2px solid transparent;">
                  校验超时
                </button>
              </div>

              <div id="alert-table" class="alert-tab-content active"
                   style="  height: 400px; overflow-y: auto;">
                <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                  <!-- 告警项将在这里动态添加 -->
                </ul>
              </div>
            </div>
          </div>
        </aside>

        <section class="map-display-area" style="width: calc( 100% - 630px);">
          <!-- 地图图片 -->
          <div id="map-container" style="width: 100%; height: calc(100% + 40px);"></div>

          <!-- 图例 - 应急资源主题 -->
          <div class="map-legend"
               style="position: absolute !important; right: 15px !important; bottom: 15px !important; left: auto !important; top: auto !important; background: rgba(255, 255, 255, 0.98) !important; padding: 10px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.15); max-width: 160px; z-index: 1000; font-size: 10px; border: 1px solid rgba(0,0,0,0.1);">
            <div class="legend-section" style="margin-bottom: 8px;">
              <div class="legend-title"
                   style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #ff6b35; padding-bottom: 2px;">
                应急事件等级
              </div>

              <div class="legend-items" style="gap: 3px">
                <div class="legend-item" style="display: flex; align-items: center; ">
                  <img src="image/emergency-map/events_1.png" height="24" width="20" alt=""/>
                  <div class="legend-text" style="font-size: 10px; color: #333;">I级(特别重大)</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; ">
                  <img src="image/emergency-map/events_2.png" height="24" width="20" alt=""/>
                  <div class="legend-text" style="font-size: 10px; color: #333;">II级(重大)</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; ">
                  <img src="image/emergency-map/events_3.png" height="24" width="20" alt=""/>
                  <div class="legend-text" style="font-size: 10px; color: #333;">III级(较大)</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; ">
                  <img src="image/emergency-map/events_4.png" height="24" width="20" alt=""/>
                  <div class="legend-text" style="font-size: 10px; color: #333;">IV级(一般)</div>
                </div>
              </div>
            </div>

            <div class="legend-section" style="margin-bottom: 8px;">
              <div class="legend-title"
                   style="font-size: 11px; font-weight: bold; color: #333; margin-bottom: 5px; border-bottom: 1px solid #007bff; padding-bottom: 2px;">
                应急资源类型
              </div>
              <div class="legend-items">
                <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                  <img src="image/emergency-map/supplies.png" height="24" width="20"/>
                  <div class="legend-text" style="font-size: 10px; color: #333;">应急物资</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                  <img src="image/emergency-map/teams.png" height="24" width="20"/>

                  <div class="legend-text" style="font-size: 10px; color: #333;">救援队伍</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                  <img src="image/emergency-map/others_rescueVehicle.png" height="24" width="20"/>

                  <div class="legend-text" style="font-size: 10px; color: #333;">救援车辆</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                  <img src="image/emergency-map/others_medicalPoint.png" height="24" width="20"/>

                  <div class="legend-text" style="font-size: 10px; color: #333;">医疗点</div>
                </div>
                <div class="legend-item" style="display: flex; align-items: center; margin-bottom: 3px;">
                  <img src="image/emergency-map/others_firePoint.png" height="24" width="20"/>

                  <div class="legend-text" style="font-size: 10px; color: #333;">消防点</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <aside class="right-sidebar">
          <div class="statistics-panel">
            <h3>统计分析</h3>
            <div class="stat-grid">
              <div class="stat-item">
                <div class="stat-label">应急事件数量</div>
                <div class="stat-value" id="event-count">28</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">救援队伍数量</div>
                <div class="stat-value" id="team-count">45</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">物资储备点</div>
                <div class="stat-value" id="supply-count">32</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">校验超时</div>
                <div class="stat-value risk-high-count" id="overdue-count">15</div>
              </div>
            </div>
          </div>
          <div class="risk-details-list">
            <h3>详情列表</h3>

            <!-- 详情列表标签卡 -->
            <div class="details-tabs" style="margin-bottom: 0">
              <button style="padding: 5px" class="details-tab-button active" data-tab="events">应急事件</button>
              <button style="padding: 5px" class="details-tab-button" data-tab="teams">救援队伍</button>
              <button style="padding: 5px" class="details-tab-button" data-tab="supplies">物资储备</button>
              <button style="padding: 5px" class="details-tab-button" data-tab="experts">专家库</button>
            </div>

            <div id="details-table" class="details-tab-content" style="display: block;">
              <table class="details-table">
                <thead>
                </thead>
                <tbody>
                </tbody>
              </table>
            </div>

          </div>
        </aside>
      </div>
    </div>
  </main>
</div>

<!-- 应急事件详情模态框 -->
<div id="emergency-event-modal" class="modal" style="display: none; z-index: 9999 !important;">
  <div class="modal-content" style="max-width: 1200px; width: 95%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">应急事件详情</h3>
      <span class="close" onclick="closeEmergencyEventModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>

    <!-- Tab Navigation -->
    <div class="modal-tabs"
         style="background: #2c3e50; padding: 0 20px; display: flex; border-bottom: 1px solid #34495e;">
      <button class="emergency-event-modal-tab-btn active" data-tab="event-info">事件信息</button>
      <button class="emergency-event-modal-tab-btn" data-tab="emergency-plan">应急预案</button>
      <button class="emergency-event-modal-tab-btn" data-tab="organization">应急机构</button>
      <button class="emergency-event-modal-tab-btn" data-tab="experts">推荐专家</button>
      <button class="emergency-event-modal-tab-btn" data-tab="supplies">应急物资</button>
      <button class="emergency-event-modal-tab-btn" data-tab="rescue-teams">救援队伍</button>
      <button class="emergency-event-modal-tab-btn" data-tab="medical">医疗单位</button>
      <button class="emergency-event-modal-tab-btn" data-tab="fire">消防单位</button>
      <button class="emergency-event-modal-tab-btn" data-tab="monitoring">监控视频</button>
    </div>

    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">
      <!-- 1. 事件信息板块 -->
      <div id="event-info" class="emergency-event-modal-tab-content active">
        <div class="event-info-panel"
             style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px; position: relative;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="color: #3498db; margin: 0; font-size: 18px;">事件信息</h4>

            <div>
              <button class="export-btn" onclick="exportEventInfo('1')"
                      style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                <i class="fas fa-download"></i> 应急辅助决策导出
              </button>
              <button class="export-btn" onclick="exportEventInfo('2')"
                      style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                <i class="fas fa-download"></i> 应急组织机构导出
              </button>
            </div>
          </div>

          <!-- 基本信息 -->
          <div class="basic-info-section" style="margin-bottom: 20px;">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">基本信息</h5>
            <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <!-- 这里的内容将由JavaScript动态填充 -->
            </div>
          </div>

          <!-- 事件预案启动与等级判别说明 -->
          <div class="plan-activation-section" style="margin-bottom: 20px;">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">事件预案启动与等级判别说明</h5>
            <div class="plan-description" style="color: #ecf0f1; font-size: 18px; line-height: 1.8;"></div>
          </div>

          <!-- 辅助决策 -->
          <div class="decision-support-section" style="margin-bottom: 20px;">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">辅助决策</h5>
            <div class="decision-display"
                 style="color: #ecf0f1; font-size: 18px; line-height: 1.8; margin-bottom: 10px;"></div>
            <div class="decision-edit-section" style="display: none;">
        <textarea id="decision-edit-text"
                  style="width: 100%; height: 100px; background: #2c3e50; color: #ecf0f1; border: 1px solid #95a5a6; border-radius: 4px; padding: 10px; font-size: 18px; resize: vertical;"></textarea>
              <div style="margin-top: 10px;">
                <button onclick="confirmDecisionEdit()"
                        style="background: #27ae60; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 10px;">
                  确认
                </button>
                <button onclick="cancelDecisionEdit()"
                        style="background: #95a5a6; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">
                  取消
                </button>
              </div>
            </div>
            <button onclick="editDecision()"
                    style="background: #3498db; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">
              <i class="fas fa-edit"></i> 编辑决策
            </button>
          </div>

          <!-- 项目运营企业 -->
          <div class="enterprise-section">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 14px;">项目运营企业</h5>
            <div class="enterprise-list" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div class="enterprise-item"
                   style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                <div class="enterprise-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px;">
                  广西交通投资集团有限公司
                </div>
                <div class="enterprise-contact">
                  <div style="color: #95a5a6; font-size: 16px;">负责人：<span
                    style="color: #ecf0f1; font-size: 16px;">张三</span></div>
                  <div style="color: #95a5a6; font-size: 16px;">联系方式：<span style="color: #ecf0f1; font-size: 16px;">0771-5607119</span>
                  </div>
                </div>
              </div>
              <div class="enterprise-item"
                   style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
                <div class="enterprise-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px;">
                  广西高速公路管理有限公司柳州分公司
                </div>
                <div class="enterprise-contact">
                  <div style="color: #95a5a6; font-size: 16px;">负责人：<span
                    style="color: #ecf0f1; font-size: 16px;">李四</span></div>
                  <div style="color: #95a5a6; font-size: 16px;">联系方式：<span style="color: #ecf0f1; font-size: 16px;">0772-3825119</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 2. 推荐应急预案 -->
      <div id="emergency-plan" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="emergency-plan-panel"
             style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">推荐应急预案</h4>
          <div class="plan-info"
               style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
            <div class="plan-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 16px;">
              广西壮族自治区公路交通突发事件应急预案
            </div>
            <div class="plan-scope" style="color: #95a5a6; font-size: 12px; margin-bottom: 15px;">适用范围：<span
              style="color: #ecf0f1;">自治区范围内Ⅱ级及以上公路交通突发事件</span></div>
            <div class="plan-actions">
              <button onclick="viewPlanDetails('d34e93a027fd44d9bca6be7f2fcf8e28')"
                      style="background: #27ae60; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px; font-size: 12px;">
                <i class="fas fa-eye"></i> 查看预案详情
              </button>
              <button onclick="viewOtherPlans()"
                      style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                <i class="fas fa-list"></i> 查看其他预案
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 3. 推荐应急组织机构 -->
      <div id="organization" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="organization-panel"
             style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">推荐应急组织机构</h4>

          <div id="recommend-organization-structure" style="display: flex; flex-direction: column; gap: 20px;">
            <!-- 这里的内容将由JavaScript动态生成 -->
          </div>
        </div>
      </div>

      <!-- 4. 推荐专家 -->
      <div id="experts" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="experts-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="color: #3498db; margin: 0; font-size: 18px;">推荐专家</h4>
            <button class="circle-btn" onclick="openCircleModal('experts')">
              <i class="fas fa-map-marked-alt"></i>应急救援圈
            </button>
          </div>
          <div class="experts-list"
               style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px;">
            <div class="expert-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
              <div class="expert-name" style="color: #3498db; font-weight: bold; margin-bottom: 5px;">李建华 -
                隧道工程专家
              </div>
              <div class="expert-title" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">
                广西大学土木建筑工程学院教授
              </div>
              <div class="expert-distance" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">
                距离事故点：约180公里
              </div>
              <div class="expert-contact" style="color: #ecf0f1; font-size: 12px;">联系电话：13807711001</div>
            </div>
            <div class="expert-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
              <div class="expert-name" style="color: #3498db; font-weight: bold; margin-bottom: 5px;">张明 -
                危化品处置专家
              </div>
              <div class="expert-title" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">
                广西安全生产科学研究院高级工程师
              </div>
              <div class="expert-distance" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">
                距离事故点：约175公里
              </div>
              <div class="expert-contact" style="color: #ecf0f1; font-size: 12px;">联系电话：13907712002</div>
            </div>
            <div class="expert-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6;">
              <div class="expert-name" style="color: #3498db; font-weight: bold; margin-bottom: 5px;">王强 -
                应急救援专家
              </div>
              <div class="expert-title" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">
                柳州市应急管理局副局长
              </div>
              <div class="expert-distance" style="color: #95a5a6; font-size: 12px; margin-bottom: 5px;">
                距离事故点：约15公里
              </div>
              <div class="expert-contact" style="color: #ecf0f1; font-size: 12px;">联系电话：13977213003</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 5. 附近应急物资 -->
      <div id="supplies" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="supplies-panel"
             style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="color: #3498db; margin: 0; font-size: 18px;">附近应急物资</h4>
            <button class="circle-btn" onclick="openCircleModal('supplies')">
              <i class="fas fa-map-marked-alt"></i>应急救援圈
            </button>
          </div>

          <!-- 20km范围内的物资 -->
          <div class="rescue-section" style="margin-bottom: 20px;">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 16px;">20km范围内</h5>
            <div class="supplies-list" id="supplies-20km">
              <!-- 这里的内容将由JavaScript动态填充 -->
            </div>
          </div>

          <!-- 40km范围内的物资 -->
          <div class="rescue-section">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 16px;">40km范围内</h5>
            <div class="supplies-list" id="supplies-40km">
              <!-- 这里的内容将由JavaScript动态填充 -->
            </div>
          </div>
        </div>
      </div>
      <!-- 6. 附近救援队伍 -->
      <!-- 在已有的模态框结构中，找到id="rescue-teams"的div -->
      <div id="rescue-teams" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="rescue-teams-panel"
             style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="color: #3498db; margin: 0; font-size: 18px;">附近救援队伍</h4>
            <button class="circle-btn" onclick="openCircleModal('teams')">
              <i class="fas fa-map-marked-alt"></i>应急救援圈
            </button>
          </div>

          <!-- 20km范围内的队伍 -->
          <div class="rescue-section" style="margin-bottom: 20px;">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 16px;">20km范围内</h5>
            <div class="rescue-teams-list" id="teams-20km">
              <!-- 这里的内容将由JavaScript动态填充 -->
            </div>
          </div>

          <!-- 40km范围内的队伍 -->
          <div class="rescue-section">
            <h5 style="color: #95a5a6; margin-bottom: 10px; font-size: 16px;">40km范围内</h5>
            <div class="rescue-teams-list" id="teams-40km">
              <!-- 这里的内容将由JavaScript动态填充 -->
            </div>
          </div>
        </div>
      </div>
      <!-- 8. 医疗单位 -->
      <div id="medical" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="medical-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="color: #3498db; margin: 0; font-size: 20px;">医疗单位</h4>
            <button class="circle-btn" onclick="openCircleModal('medical')">
              <i class="fas fa-map-marked-alt"></i>应急救援圈
            </button>
          </div>
          <div class="medical-list">
            <div class="medical-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="medical-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">
                柳州市人民医院
              </div>
              <div class="medical-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                <div style="color: #95a5a6; font-size: 14px;">地点：<span
                  style="color: #ecf0f1;">柳州市城中区文昌路8号</span></div>
                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">18km</span></div>
                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">张院长</span></div>
                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-2662222</span>
                </div>
              </div>
            </div>

            <div class="medical-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="medical-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">
                柳江区人民医院
              </div>
              <div class="medical-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                <div style="color: #95a5a6; font-size: 14px;">地点：<span
                  style="color: #ecf0f1;">柳江区拉堡镇建设中路</span></div>
                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">8km</span></div>
                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">李院长</span></div>
                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-7212120</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 9. 消防单位 -->
      <div id="fire" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="fire-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h4 style="color: #3498db; margin: 0; font-size: 20px;">消防单位</h4>
            <button class="circle-btn" onclick="openCircleModal('fire')">
              <i class="fas fa-map-marked-alt"></i>应急救援圈
            </button>
          </div>
          <div class="fire-list">
            <div class="fire-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="fire-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">
                柳州市消防救援支队
              </div>
              <div class="fire-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                <div style="color: #95a5a6; font-size: 14px;">地点：<span
                  style="color: #ecf0f1;">柳州市城中区中山中路</span></div>
                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">12km</span></div>
                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">张支队长</span></div>
                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-119</span>
                </div>
              </div>
            </div>

            <div class="fire-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="fire-name" style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">
                柳江区消防救援大队
              </div>
              <div class="fire-info" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                <div style="color: #95a5a6; font-size: 14px;">地点：<span
                  style="color: #ecf0f1;">柳江区拉堡镇柳堡路</span>
                </div>
                <div style="color: #95a5a6; font-size: 14px;">距离：<span style="color: #ecf0f1;">6km</span></div>
                <div style="color: #95a5a6; font-size: 14px;">负责人：<span style="color: #ecf0f1;">王大队长</span></div>
                <div style="color: #95a5a6; font-size: 14px;">联系方式：<span style="color: #ecf0f1;">0772-7212119</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 10. 事故附近监控视频 -->
      <div id="monitoring" class="emergency-event-modal-tab-content" style="display: none;">
        <div class="monitoring-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
          <h4 style="color: #3498db; margin-bottom: 15px; font-size: 20px;">事故附近监控视频</h4>
          <div class="monitoring-list">
            <div class="monitoring-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="monitoring-name"
                   style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">吴家屯隧道出口监控
              </div>
              <div class="monitoring-info"
                   style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                <div style="color: #95a5a6; font-size: 14px;">路段编号：<span style="color: #ecf0f1;">G72</span></div>
                <div style="color: #95a5a6; font-size: 14px;">起止桩号：<span style="color: #ecf0f1;">K1450+800</span>
                </div>
                <div>
                  <button onclick="viewMonitoring('camera1')"
                          style="background: #27ae60; color: white; border: none; padding: 10px 18px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    查看监控
                  </button>
                </div>
              </div>
            </div>

            <div class="monitoring-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="monitoring-name"
                   style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">事故路段监控
              </div>
              <div class="monitoring-info"
                   style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                <div style="color: #95a5a6; font-size: 14px;">路段编号：<span style="color: #ecf0f1;">G72</span></div>
                <div style="color: #95a5a6; font-size: 14px;">起止桩号：<span style="color: #ecf0f1;">K1451+000</span>
                </div>
                <div>
                  <button onclick="viewMonitoring('camera2')"
                          style="background: #27ae60; color: white; border: none; padding: 10px 18px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    查看监控
                  </button>
                </div>
              </div>
            </div>

            <div class="monitoring-item"
                 style="background: #2c3e50; padding: 15px; border-radius: 6px; border: 1px solid #95a5a6; margin-bottom: 15px;">
              <div class="monitoring-name"
                   style="color: #3498db; font-weight: bold; margin-bottom: 8px; font-size: 18px;">隧道入口监控
              </div>
              <div class="monitoring-info"
                   style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; align-items: center;">
                <div style="color: #95a5a6; font-size: 14px;">路段编号：<span style="color: #ecf0f1;">G72</span></div>
                <div style="color: #95a5a6; font-size: 14px;">起止桩号：<span style="color: #ecf0f1;">K1449+500</span>
                </div>
                <div>
                  <button onclick="viewMonitoring('camera3')"
                          style="background: #27ae60; color: white; border: none; padding: 10px 18px; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    查看监控
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 应急物资详情模态框 -->
<div id="emergency-supply-modal" class="modal" style="display: none; z-index: 9999 !important;">
  <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">应急物资详情</h3>
      <span class="close" onclick="closeSupplyModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

      <!-- 基本信息 -->
      <div class="supply-info-panel"
           style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <div style="margin-bottom: 15px;">
          <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
        </div>

        <div class="info-grid"
             style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="supply-name">柳州市应急物资储备中心</span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">位置：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="supply-location">柳州市城中区文昌路168号</span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="supply-manager">张主任</span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="supply-contact">0772-3825001</span>
          </div>
          <div class="info-item" style="grid-column: span 2;">
            <strong style="color: #3498db; font-size: 18px;">最新更新时间：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="supply-updateTime">2023年4月20日 09:30</span>
          </div>
        </div>
      </div>

      <!-- 物资列表 -->
      <div class="materials-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">物资列表</h4>
        <table style="width: 100%; border-collapse: collapse; background: #34495e;">
          <thead>
          <tr>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              物资名称
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              型号
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              数量
            </th>
          </tr>
          </thead>
          <tbody id="supply-items">

          </tbody>
        </table>
      </div>

      <!-- 装备列表 -->
      <div class="equipment-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">装备列表</h4>
        <table style="width: 100%; border-collapse: collapse; background: #34495e;">
          <thead>
          <tr>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              装备名称
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              型号
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              数量
            </th>
          </tr>
          </thead>
          <tbody id="supply-equipment">

          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- 救援队伍详情模态框 -->
<div id="rescue-team-modal" class="modal" style="display: none; z-index: 9999 !important;">
  <div class="modal-content" style="max-width: 1000px; width: 95%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">救援队伍详情</h3>
      <span class="close" onclick="closeTeamModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

      <!-- 基本信息 -->
      <div class="team-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <div style="margin-bottom: 15px;">
          <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
        </div>

        <div class="info-grid"
             style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; margin-bottom: 20px;">
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-name'></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">位置：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-location'></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">专业方向：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-type'></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-manager'></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-contact'></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">队伍人数：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-members'></span>
          </div>
          <div class="info-item" style="grid-column: span 2;">
            <strong style="color: #3498db; font-size: 18px;">最新更新时间：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id='team-updateTime'></span>
          </div>
        </div>
      </div>

      <!-- 物资列表 -->
      <div class="team-materials-panel"
           style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">物资列表</h4>
        <table style="width: 100%; border-collapse: collapse; background: #34495e;">
          <thead>
          <tr>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              物资名称
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              型号
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              数量
            </th>
          </tr>
          </thead>
          <tbody id="team-equipment1">

          </tbody>
        </table>
      </div>

      <!-- 装备列表 -->
      <div class="team-equipment-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">装备列表</h4>
        <table style="width: 100%; border-collapse: collapse; background: #34495e;">
          <thead>
          <tr>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              装备名称
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              型号
            </th>
            <th style="color: #3498db; padding: 12px; border: 1px solid #95a5a6; font-size: 16px; text-align: center;">
              数量
            </th>
          </tr>
          </thead>
          <tbody id="team-equipment2">

          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- 消防点详情模态框 -->
<div id="fire-station-modal" class="modal" style="display: none; z-index: 9999 !important;">
  <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">消防点详情</h3>
      <span class="close" onclick="closeFireStationModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

      <!-- 基本信息 -->
      <div class="fire-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
        <div style="margin-bottom: 15px;">
          <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
        </div>

        <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="fire-name"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">地点：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="fire-location"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="fire-manager"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="fire-contact"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 医疗点详情模态框 -->
<div id="medical-station-modal" class="modal" style="display: none; z-index: 9999 !important;">
  <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">医疗点详情</h3>
      <span class="close" onclick="closeMedicalStationModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

      <!-- 基本信息 -->
      <div class="medical-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
        <div style="margin-bottom: 15px;">
          <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
        </div>

        <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="medical-name"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">地点：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="medical-location"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="medical-manager"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="medical-contact"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 医疗点详情模态框 -->
<div id="rescue-vehicle-modal" class="modal" style="display: none; z-index: 9999 !important;">
  <div class="modal-content" style="max-width: 600px; width: 90%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">救援车辆详情</h3>
      <span class="close" onclick="closeRescueVehicleModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

      <!-- 基本信息 -->
      <div class="medical-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px;">
        <div style="margin-bottom: 15px;">
          <h4 style="color: #3498db; margin: 0; font-size: 22px;">基本信息</h4>
        </div>

        <div class="info-grid" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">名称：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="vehicle-name"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">地点：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="vehicle-location"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">负责人：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="vehicle-manager"></span>
          </div>
          <div class="info-item">
            <strong style="color: #3498db; font-size: 18px;">联系方式：</strong>
            <span style="color: #ecf0f1; font-size: 16px;" id="vehicle-contact"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 应急救援圈模态框 -->
<div id="emergency-circle-modal" class="modal" style="display: none; z-index: 10000 !important;">
  <div class="modal-content" style="max-width: 1600px; width: 98%; max-height: 95vh; overflow: hidden;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 id="circle-modal-title" style="margin: 0; font-size: 20px;">应急救援圈</h3>
      <span class="close" onclick="closeCircleModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body"
         style="padding: 20px; background: #2c3e50; color: #ecf0f1; display: flex; gap: 20px; height: calc(95vh - 80px); overflow: hidden;">

      <!-- 左侧地图容器 -->
      <div class="circle-map-section" style="flex: 2; display: flex; flex-direction: column;">
        <div id="circle-map-container" class="circle-map-container"
             style="position: relative; width: 100%; height: 600px; background: #34495e; border-radius: 8px; overflow: hidden; border: 2px solid #95a5a6;">
          <!-- 高德地图容器 -->
          <div id="circle-amap" style="width: 100%; height: calc(100% + 20px);"></div>

          <!-- 地图放大说明 -->
          <div
            style="position: absolute; top: 10px; left: 10px; background: rgba(44, 62, 80, 0.9); color: #ecf0f1; padding: 8px 12px; border-radius: 4px; font-size: 16px; z-index: 1001;">
            以应急事件为中心的局部区域
          </div>
        </div>

        <!-- 统计信息和图例 -->
        <div class="circle-info-panel"
             style="background: #34495e; padding: 15px; border-radius: 8px; margin-top: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div class="circle-statistics">
              <h5 style="color: #3498db; margin: 0 0 10px 0; font-size: 20px;">资源统计</h5>
              <div style="display: flex; gap: 30px;">
                <div style="color: #ecf0f1; font-size: 18px;">
                  <span style="color: #3498db;">20km范围内：</span>
                  <span id="count-20km">0</span> 个
                </div>
                <div style="color: #ecf0f1; font-size: 18px;">
                  <span style="color: #27ae60;">40km范围内：</span>
                  <span id="count-40km">0</span> 个
                </div>
                <div style="color: #ecf0f1; font-size: 18px;">
                  <span style="color: #95a5a6;">总计：</span>
                  <span id="count-total">0</span> 个
                </div>
              </div>
            </div>
            <div class="circle-legend">
              <h5 style="color: #3498db; margin: 0 0 10px 0; font-size: 20px;">图例</h5>
              <div style="display: flex; flex-direction: column; gap: 5px;">
                <div style="display: flex; align-items: center; gap: 8px; color: #ecf0f1; font-size: 16px;">

                  <div style="width: 12px; height: 12px; background: #e74c3c; border-radius: 50%;"></div>
                  <span>应急事件中心</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; color: #ecf0f1; font-size: 16px;">
                  <div style="width: 20px; height: 2px; border-top: 2px dashed #3498db;"></div>
                  <span>20km范围</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; color: #ecf0f1; font-size: 16px;">
                  <div style="width: 20px; height: 2px; border-top: 2px dashed #27ae60;"></div>
                  <span>40km范围</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧资源列表面板 -->
      <div class="circle-resource-panel"
           style="flex: 1; background: #34495e; border-radius: 8px; padding: 20px; overflow-y: auto; border: 2px solid #95a5a6;">
        <!-- 搜索栏 -->
        <div class="resource-search" style="margin-bottom: 20px;">
          <h4 id="resource-panel-title" style="color: #3498db; margin: 0 0 15px 0; font-size: 22px;">资源列表</h4>
          <div style="position: relative;">
            <input type="text" id="resource-search-input" placeholder="搜索资源..."
                   style="width: 100%; padding: 12px 15px;     box-sizing: border-box;border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
          </div>
        </div>

        <!-- 资源列表容器 -->
        <div id="resource-list-container" style="display: flex; flex-direction: column; gap: 12px;">
          <!-- 资源项目将通过JavaScript动态生成 -->
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 预案详情模态框 -->
<div id="plan-details-modal" class="modal" style="display: none; z-index: 11000 !important;">
  <div class="modal-content" style="max-width: 1200px; width: 90%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">预案详情</h3>
      <span class="close" onclick="closePlanDetailsModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">
      <div class="plan-details-content">
        <div class="plan-sub-tabs">
          <button class="plan-sub-tab-btn active" data-plan-tab="basic-info"
                  style="color: #3498db; border-bottom: 2px solid #3498db;">基本信息 & 总则
          </button>
          <button class="plan-sub-tab-btn" data-plan-tab="planOrganization"
                  style="color: #95a5a6; border-bottom: 2px solid transparent;">组织体系
          </button>
          <button class="plan-sub-tab-btn" data-plan-tab="prevention"
                  style="color: #95a5a6; border-bottom: 2px solid transparent;">预防与预警
          </button>
          <button class="plan-sub-tab-btn" data-plan-tab="response"
                  style="color: #95a5a6; border-bottom: 2px solid transparent;">应急响应
          </button>
          <button class="plan-sub-tab-btn" data-plan-tab="post-processing"
                  style="color: #95a5a6; border-bottom: 2px solid transparent;">后期处置
          </button>
          <button class="plan-sub-tab-btn" data-plan-tab="emergency-support"
                  style="color: #95a5a6; border-bottom: 2px solid transparent;">应急保障
          </button>
          <button class="plan-sub-tab-btn" data-plan-tab="plan-management"
                  style="color: #95a5a6; border-bottom: 2px solid transparent;">预案管理
          </button>
        </div>
        <div class=" plan-sub-tab-content" id="basic-info" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">基本信息 & 总则</h3>

          <!-- 基本信息 -->
          <div style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">基本信息</h4>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
              <div>
                <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">预案名称</p>
                <p style="color: #ecf0f1; font-size: 16px; margin: 0;" id="plan-name"></p>
              </div>
              <div>
                <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">预案类型</p>
                <p style="color: #ecf0f1; font-size: 16px; margin: 0;" id="plan-type"></p>
              </div>
              <div>
                <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">编制单位</p>
                <p style="color: #ecf0f1; font-size: 16px; margin: 0;" id="plan-dept"></p>
              </div>
              <div>
                <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">编制时间</p>
                <p style="color: #ecf0f1; font-size: 16px; margin: 0;" id="plan-lastCheckTime"></p>
              </div>
              <div style="grid-column: span 2;">
                <p style="color: #95a5a6; margin-bottom: 5px; font-size: 14px;">适用范围</p>
                <p style="color: #ecf0f1; font-size: 16px; margin: 0;" id="plan-scope">
                  </p>
              </div>
            </div>
          </div>

          <!-- 编制目的 -->
          <div style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">编制目的</h4>
            <p style="color: #ecf0f1; font-size: 16px; line-height: 1.6; margin: 0;" id="plan-purpose">
            </p>
          </div>

          <!-- 事件分级 -->
          <div style="background: #34495e; padding: 20px; border-radius: 8px;">
            <h4 style="color: #3498db; margin-bottom: 15px; font-size: 18px;">事件分级与响应条件</h4>
            <div style="display: flex; flex-direction: column; gap: 15px;">
              <div style="border: 2px solid #e74c3c; border-radius: 8px; padding: 15px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                  <span
                    style="background: #e74c3c; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅰ</span>
                  <span style="color: #e74c3c; font-weight: bold; font-size: 16px;">特别重大</span>
                </div>
                <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;" id="plan-basic-level-i-condition">
                </p>
              </div>
              <div style="border: 2px solid #f39c12; border-radius: 8px; padding: 15px;">
                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                  <span
                    style="background: #f39c12; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px;">Ⅱ</span>
                  <span style="color: #f39c12; font-weight: bold; font-size: 16px;">重大</span>
                </div>
                <p style="color: #ecf0f1; font-size: 14px; margin: 0; line-height: 1.5;" id="plan-basic-level-ii-condition">
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="plan-sub-tab-content" id="planOrganization" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">组织体系</h3>
          <p style="color: #95a5a6; margin-bottom: 20px; font-size: 16px;">
            自治区应急组织体系由自治区级、市级和县级三级组成。以下为自治区级应急指挥机构详情：</p>
          <div id="plan-organization-structure" style="display: flex; flex-direction: column; gap: 20px;">
            <!-- 这里的内容将由JavaScript动态生成 -->
          </div>
        </div>
   <div class="plan-sub-tab-content" id="prevention" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">预防与预警</h3>
          <div style="display: flex; flex-direction: column; gap: 20px;">
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预防措施</p>
              <div id="preventive-measures"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预警原则</p>
              <div id="warning-principle"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预警信息收集</p>
              <div id="warning-info-collect"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预警分级</p>
              <div id="warning-level"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预警发布</p>
              <div id="warning-publish"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
          </div>
        </div>
        <div class="plan-sub-tab-content" id="response" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">应急响应</h3>
          <div style="display: flex; flex-direction: column; gap: 20px;">
            <!-- 一级响应 -->
            <div style="border: 2px solid #e74c3c; border-radius: 8px; overflow: hidden;">
              <div style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-bottom: 1px solid #e74c3c;">
                <h4 style="color: #e74c3c; margin: 0; font-size: 18px; display: flex; align-items: center;">
                  <span
                    style="background: #e74c3c; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px; font-size: 14px;">I</span>
                  特别重大响应
                </h4>
              </div>
              <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                  <p
                    style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold; display: flex; align-items: center;">
                    <i class="fas fa-bullseye" style="margin-right: 8px;"></i>响应启动条件
                  </p>
                  <div
                    style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                    <p id="emergence-level-i-condition" class="event-level-condition"></p>
                  </div>
                </div>
                <div>
                  <p
                    style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold; display: flex; align-items: center;">
                    <i class="fas fa-tasks" style="margin-right: 8px;"></i>应急处置流程
                  </p>
                  <div id="emergence-level-i-process-flow"
                    style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                  </div>
                </div>
              </div>
            </div>

            <!-- 二级响应 -->
            <div style="border: 2px solid #f39c12; border-radius: 8px; overflow: hidden;">
              <div style="background: rgba(243, 156, 18, 0.1); padding: 15px; border-bottom: 1px solid #f39c12;">
                <h4 style="color: #f39c12; margin: 0; font-size: 18px; display: flex; align-items: center;">
                  <span
                    style="background: #f39c12; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; margin-right: 10px; font-size: 14px;">II</span>
                  重大响应
                </h4>
              </div>
              <div style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                  <p
                    style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold; display: flex; align-items: center;">
                    <i class="fas fa-bullseye" style="margin-right: 8px;"></i>响应启动条件
                  </p>
                  <div
                    style="background: rgba(243, 156, 18, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                    <p id="emergence-level-ii-condition" class="event-level-condition"></p>
                  </div>
                </div>
                <div>
                  <p
                    style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold; display: flex; align-items: center;">
                    <i class="fas fa-tasks" style="margin-right: 8px;"></i>应急处置流程
                  </p>
                  <div id="emergence-level-ii-process-flow"
                       style="background: rgba(231, 76, 60, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px;">
                  </div>
                </div>
              </div>
            </div>

            <!-- 信息报送和新闻发布 -->
            <div style="display: flex; flex-direction: column; gap: 15px;">
              <div style="border-left: 4px solid #3498db; padding-left: 15px;">
                <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">信息报送</p>
                <div id="info-report"
                     style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
                </div>
              </div>
              <div style="border-left: 4px solid #3498db; padding-left: 15px;">
                <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">新闻发布</p>
                <div id="news-release"
                     style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
                </div>
              </div>
              <div style="border-left: 4px solid #3498db; padding-left: 15px;">
                <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">响应调整与终止</p>
                <div id="response-adjust"
                     style="background: rgba(52, 152, 219, 0.1); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="plan-sub-tab-content" id="post-processing" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">后期处置</h3>
          <div style="display: flex; flex-direction: column; gap: 20px;">
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">善后处置</p>
              <div id="aftermath-disposal"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">总结评估</p>
              <div id="summary-evaluation"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
          </div>
        </div>
        <div class="plan-sub-tab-content" id="emergency-support" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">应急保障</h3>
          <div style="display: flex; flex-direction: column; gap: 20px;">
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">物资保障</p>
              <div id="material-support"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">通信保障</p>
              <div id="communication-support"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">交通保障</p>
              <div id="traffic-support"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">医疗保障</p>
              <div id="traffic-healthGuarantee"
                   style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">经费保障</p>
              <div id="funding-support"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
          </div>
        </div>
        <div class="plan-sub-tab-content" id="plan-management" style="display: none;">
          <h3 style="color: #3498db; margin-bottom: 20px; font-size: 20px;">预案管理</h3>
          <div style="display: flex; flex-direction: column; gap: 20px;">
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预案修订</p>
              <div id="publicity-revisionContent"
                   style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">宣传培训</p>
              <div id="publicity-training"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">预案演练</p>
              <div id="plan-drill"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
            <div>
              <p style="color: #95a5a6; margin-bottom: 8px; font-size: 14px; font-weight: bold;">实施时间</p>
              <div id="implement-time"
                style="background: rgba(52, 73, 94, 0.3); padding: 15px; border-radius: 6px; color: #ecf0f1; font-size: 14px; line-height: 1.6;">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 其他预案模态框 -->
<div id="other-plans-modal" class="modal" style="display: none; z-index: 10000 !important;">
  <div class="modal-content" style="max-width: 1200px; width: 90%; max-height: 90vh; overflow-y: auto;">
    <div class="modal-header"
         style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
      <h3 style="margin: 0; font-size: 20px;">其他预案</h3>
      <span class="close" onclick="closeOtherPlansModal()"
            style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
    </div>
    <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">
      <div class="other-plans-content">
        <h4 style="color: #3498db; margin-bottom: 15px; font-size: 22px;">其他预案列表</h4>
        <div class="plans-search-bar" style="margin-bottom: 20px; display: flex; gap: 10px;">
          <input type="text" id="plans-search" placeholder="搜索预案名称或编号..."
                 style="flex: 1; padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
          <button onclick="searchPlans()"
                  style="background: #3498db; color: white; padding: 12px 15px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer;">
            <i class="fas fa-search"></i> 搜索
          </button>
        </div>
        <div class="plans-filter" style="margin-bottom: 20px; display: flex; gap: 10px;">
          <select id="plans-category"
                  style="flex: 1; padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
            <option value="">所有类别</option>
            <option value="1">道路交通事故</option>
            <option value="2">水路交通事故</option>
            <option value="3">铁路交通事故</option>
            <option value="4">航空交通事故</option>
          </select>
          <select id="plans-level"
                  style="flex: 1; padding: 12px 15px; border: 2px solid #95a5a6; border-radius: 6px; background: #2c3e50; color: #ecf0f1; font-size: 16px;">
            <option value="">所有等级</option>
            <option value="1">Ⅰ级(特别重大)</option>
            <option value="2">Ⅱ级(重大)</option>
            <option value="3">Ⅲ级(较大)</option>
            <option value="4">Ⅳ级(一般)</option>
          </select>
        </div>
        <div class="plans-list" id="plans-list-container" style="max-height: 60vh; overflow-y: auto;">
          <!-- 预案列表将通过JavaScript动态生成 -->
          <div class="no-plans" style="text-align: center; padding: 40px 0; color: #95a5a6; display: none;">
            <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
            <p>暂无预案数据</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  document.querySelectorAll('.emergency-event-modal-tab-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      const tabId = btn.getAttribute('data-tab');
      document.querySelectorAll('.emergency-event-modal-tab-btn').forEach(b => b.classList.remove('active'));
      btn.classList.add('active');
      document.querySelectorAll('.emergency-event-modal-tab-content').forEach(content => {
        content.style.display = 'none';
      });
      document.getElementById(tabId).style.display = 'block';
    });
  });
</script>

<!-- 引入公共JavaScript -->
<script src="js/emergency-common.js"></script>

<!-- 应急一张图专用JavaScript -->
<script src="emergency-map.js"></script>
<script src="emergency-map-modal.js"></script>

</body>
</html>

