<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面开发中 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    
    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
    
    <style>
        .placeholder-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 70vh;
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            margin: 20px;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .placeholder-icon {
            font-size: 120px;
            margin-bottom: 30px;
            opacity: 0.8;
            animation: pulse 2s infinite;
        }
        
        .placeholder-title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .placeholder-subtitle {
            font-size: 24px;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .placeholder-description {
            font-size: 18px;
            line-height: 1.8;
            max-width: 600px;
            margin-bottom: 40px;
            opacity: 0.8;
        }
        
        .back-button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 2px solid rgba(255,255,255,0.3);
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }
        
        .back-button:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .feature-list {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
            max-width: 500px;
        }
        
        .feature-list h4 {
            color: #fff;
            margin-bottom: 15px;
            font-size: 20px;
        }
        
        .feature-list ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            font-size: 16px;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li i {
            margin-right: 10px;
            color: #ffd700;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="placeholder-container">
                <div class="placeholder-icon">
                    <i class="fas fa-tools"></i>
                </div>
                
                <h1 class="placeholder-title">页面开发中</h1>
                <h2 class="placeholder-subtitle" id="page-title">功能模块</h2>
                
                <p class="placeholder-description">
                    该功能模块正在紧张开发中，敬请期待！我们正在为您打造更加完善和强大的应急管理功能。
                </p>
                
                <div class="feature-list">
                    <h4><i class="fas fa-star"></i> 即将上线的功能</h4>
                    <ul id="feature-list">
                        <li><i class="fas fa-check"></i> 实时数据监控</li>
                        <li><i class="fas fa-check"></i> 智能预警系统</li>
                        <li><i class="fas fa-check"></i> 可视化数据展示</li>
                        <li><i class="fas fa-check"></i> 多维度分析报告</li>
                        <li><i class="fas fa-check"></i> 移动端适配</li>
                    </ul>
                </div>
                
                <a href="risk-map.html" class="back-button">
                    <i class="fas fa-arrow-left"></i>
                    返回风险一张图
                </a>
            </div>
        </main>
    </div>

    <script>
        // 根据URL参数或页面ID设置标题和功能列表
        function setPageContent() {
            const urlParams = new URLSearchParams(window.location.search);
            const pageTitle = urlParams.get('title') || '功能模块';
            const pageId = urlParams.get('id') || 'unknown';
            
            document.getElementById('page-title').textContent = pageTitle;
            document.title = `${pageTitle} - 广西交通运输应急管理系统`;
            
            // 根据不同页面设置不同的功能列表
            const featureMap = {
                'command-dispatch': [
                    '应急指挥中心',
                    '实时调度系统',
                    '资源统一调配',
                    '多部门协调',
                    '决策支持系统'
                ],
                'monitoring-warning': [
                    '实时监测网络',
                    '智能预警算法',
                    '多级预警机制',
                    '预警信息发布',
                    '监测数据分析'
                ],
                'duty-watch': [
                    '24小时值班制度',
                    '值班人员管理',
                    '交接班记录',
                    '应急响应流程',
                    '值班报告系统'
                ],
                'analysis-judgment': [
                    '态势分析引擎',
                    '风险评估模型',
                    '趋势预测算法',
                    '决策建议生成',
                    '分析报告输出'
                ],
                'situation-plotting': [
                    '实时态势标绘',
                    '地理信息展示',
                    '动态图层管理',
                    '标绘工具集',
                    '态势共享机制'
                ],
                'system-management': [
                    '用户权限管理',
                    '系统配置设置',
                    '数据备份恢复',
                    '日志审计功能',
                    '系统监控运维'
                ]
            };
            
            const features = featureMap[pageId] || [
                '实时数据监控',
                '智能预警系统', 
                '可视化数据展示',
                '多维度分析报告',
                '移动端适配'
            ];
            
            const featureList = document.getElementById('feature-list');
            featureList.innerHTML = features.map(feature => 
                `<li><i class="fas fa-check"></i> ${feature}</li>`
            ).join('');
        }
        
        // 初始化页面内容
        setPageContent();
        
        // 初始化导航栏（使用默认页面ID，因为这是占位页面）
        NavigationComponent.init('risk-map');
    </script>
</body>
</html>
