<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车辆与调度点管理 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
         body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
            margin: 0;
        }
        .sidebar-menu-item { /* Keep for reference */
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #4b5563;
            transition: all 0.2s;
            text-decoration: none;
        }
        .sidebar-menu-item:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }
         /* Active state handled by JS */

        /* 自定义 Tree Select 样式 */
        .el-tree-select {
            width: 100% !important;
        }
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
         /* Removed old layout styles */
         /* Tab 样式 (类似 plan_list.html) */
         .tab-btn {
             padding: 0.5rem 1rem;
             margin-right: 0.5rem;
             border-radius: 0.375rem 0.375rem 0 0; /* Top corners rounded */
             cursor: pointer;
             transition: background-color 0.2s, color 0.2s, border-color 0.2s;
             border-bottom: 2px solid transparent;
             display: inline-flex; /* Use flex for icon alignment */
             align-items: center;
         }
         .tab-btn.active {
             color: #2563EB; /* text-blue-600 */
             border-bottom-color: #2563EB; /* border-blue-600 */
             font-weight: 600; /* Apply bold directly for active */
         }
         .tab-content {
             display: none;
         }
         .tab-content.active {
             display: block;
         }
         /* Ensure Element Plus Dialog is on top */
         .el-overlay {
             z-index: 2000 !important;
         }
         .el-dialog {
            z-index: 2001 !important;
        }
    </style>
</head>
<body class="bg-gray-100"> <!-- Removed sidebar-expanded -->

    <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <!-- Removed pt-16 and main-content class, added id -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100 min-h-screen">
        <div class="py-6">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                        <h2 class="text-2xl font-bold text-gray-800">车辆与调度点管理</h2>
                        <p class="text-gray-600 mt-1">管理用于应急保障的车辆和调度点信息</p>
                    </div>
                    <!-- 新增按钮移到 Tab 内 -->
                </div>

                <!-- Tab 导航 -->
                <div class="mb-6 border-b border-gray-200">
                    <nav class="-mb-px flex space-x-4" aria-label="Tabs">
                        <button class="tab-btn font-medium text-gray-600 hover:text-gray-800 active" data-tab="vehicle-content">
                            <i class="fas fa-truck mr-2"></i> 车辆管理
                        </button>
                        <button class="tab-btn font-medium text-gray-600 hover:text-gray-800" data-tab="tow-point-content">
                            <i class="fas fa-map-marker-alt mr-2"></i> 调度点管理
                    </button>
                    </nav>
            </div>
            
                <!-- Tab 内容区域 -->
                <div id="tab-content-container">
                    <!-- 车辆管理内容 -->
                    <div id="vehicle-content" class="tab-content active">
                        <!-- 车辆搜索栏 -->
            <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                    <div>
                        <label for="org_unit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                        <div id="app">
                            <el-tree-select
                                            v-model="selectedUnit"
                                :data="unitOptions"
                                            :multiple="false"
                                            :check-strictly="true"
                                placeholder="请选择单位"
                                class="block w-full"
                                            clearable
                                @change="handleChange"
                            />
                        </div>
                    </div>
                    <div>
                        <label for="vehicle_type" class="block text-sm font-medium text-gray-700 mb-1">车辆类型</label>
                        <select id="vehicle_type" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">全部</option>
                            <option value="1">救援车</option>
                            <option value="2">消防车</option>
                                        <option value="3">救护车</option>
                                        <option value="4">工程车</option>
                                        <option value="5">指挥车</option>
                        </select>
                    </div>
                    <div>
                                    <label for="vehicle_filter_road_id" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                    <input type="text" id="vehicle_filter_road_id" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                    </div>
                    <div>
                        <label for="vehicle_status" class="block text-sm font-medium text-gray-700 mb-1">车辆状态</label>
                        <select id="vehicle_status" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">全部</option>
                                        <option value="1">正常</option>
                                        <option value="0">停用</option>
                        </select>
                    </div>
                                <div>
                                    <button type="button" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        <i class="fas fa-search mr-1"></i> 搜索
                    </button>
                                </div>
                </div>
            </div>
            
                        <!-- 新增车辆按钮 & 车辆列表 -->
                        <div class="flex justify-end mb-4">
                            <button id="btnAddVehicle" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center">
                                <i class="fas fa-plus mr-2"></i> 新增车辆
                            </button>
                        </div>
            <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车牌号码</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">车辆类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                                        <!-- 表格行示例 -->
                                        <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂A88888</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">救护车</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">南宁市应急管理局</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">停用</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">张医生</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13812345678</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="南宁市青秀区模拟地址1">南宁市青秀区模拟地址1</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G7211</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K10+000</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="发动机故障">发动机故障</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900 btn-edit-vehicle" data-id="1" title="编辑"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-900 btn-delete-vehicle" data-id="1" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                        <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂K12345</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">消防车</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">玉林市消防救援支队</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">李队长</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13987654321</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="玉林市玉州区模拟地址2">玉林市玉州区模拟地址2</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G75</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K2000+500</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="设备齐全">设备齐全</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900 btn-edit-vehicle" data-id="2" title="编辑"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-900 btn-delete-vehicle" data-id="2" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                        <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂B99999</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">工程车</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">自治区公路发展中心</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">停用</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">王工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13711112222</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="柳州市柳北区模拟地址3">柳州市柳北区模拟地址3</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G80</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K500+100</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="正在G72路段作业">正在G72路段作业</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900 btn-edit-vehicle" data-id="3" title="编辑"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-900 btn-delete-vehicle" data-id="3" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                        <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">桂H54321</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">指挥车</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">钦州市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">停用</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">刘科长</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13633334444</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="钦州市钦南区模拟地址4">钦州市钦南区模拟地址4</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">S40</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K20+000</td>
                                            <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="已达到报废年限">已达到报废年限</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900 btn-edit-vehicle" data-id="4" title="编辑"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-900 btn-delete-vehicle" data-id="4" title="删除"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                                        <!-- 更多行... -->
                        </tbody>
                    </table>
                </div>
                            <!-- 车辆分页 -->
                            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">32</span> 条记录
                        </div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                    <span class="sr-only">上一页</span>
                                        <i class="fas fa-chevron-left h-5 w-5"></i>
                                </a>
                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                    1
                                </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        2
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                                        3
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 hidden md:inline-flex relative items-center px-4 py-2 border text-sm font-medium">
                                        4
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                    <span class="sr-only">下一页</span>
                                        <i class="fas fa-chevron-right h-5 w-5"></i>
                                </a>
                            </nav>
                        </div>
                    </div>
                </div>

                    <!-- 调度点管理内容 -->
                    <div id="tow-point-content" class="tab-content">
                        <!-- 新增：调度点筛选栏 (恢复所属单位) -->
                        <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                                <div>
                                    <label for="org_unit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                    <div id="tow-point-filter-org-app">
                                    <el-tree-select
                                        v-model="selectedUnit"
                                        :data="unitOptions"
                                            :multiple="false"
                                            :check-strictly="true"
                                            placeholder="请选择单位"
                                            style="width: 100%;"
                                            clearable
                                    />
                                </div>
                            </div>
                                <div>
                                    <label for="tow_point_filter_name" class="block text-sm font-medium text-gray-700 mb-1">名称</label>
                                    <input type="text" id="tow_point_filter_name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入调度点名称">
                                </div>
                                <div>
                                    <label for="tow_point_filter_road_id" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                    <input type="text" id="tow_point_filter_road_id" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                                </div>
                                <div class="flex space-x-2 justify-end">
                                    <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                        <i class="fas fa-undo mr-1"></i> 重置
                                    </button>
                                    <button class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                        <i class="fas fa-search mr-1"></i> 查询
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 新增调度点按钮 & 调度点列表 -->
                        <div class="flex justify-end mb-4">
                            <button id="btnAddTowPoint" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center">
                                <i class="fas fa-plus mr-2"></i> 新增调度点
                            </button>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <!-- 调度点示例数据 -->
                                        <tr>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">G72高速公路拖车点A</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">自治区高速公路发展中心</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G72</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K1500+000</td>
                                           <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="G72高速K1500服务区">G72高速K1500服务区</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">张工</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13500135000</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                               <button class="text-blue-600 hover:text-blue-900 btn-edit-tow-point" data-id="tp1" title="编辑"><i class="fas fa-edit"></i></button>
                                               <button class="text-red-600 hover:text-red-900 btn-delete-tow-point" data-id="tp1" title="删除"><i class="fas fa-trash"></i></button>
                                           </td>
                                        </tr>
                                        <tr>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">G80高速公路拖车点B</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">自治区公路发展中心</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G80</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K200+000</td>
                                           <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs" title="G80高速K200收费站旁">G80高速K200收费站旁</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">钱工</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">13600136000</td>
                                           <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                               <button class="text-blue-600 hover:text-blue-900 btn-edit-tow-point" data-id="tp2" title="编辑"><i class="fas fa-edit"></i></button>
                                               <button class="text-red-600 hover:text-red-900 btn-delete-tow-point" data-id="tp2" title="删除"><i class="fas fa-trash"></i></button>
                                           </td>
                                        </tr>
                                        <!-- 更多调度点 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 调度点分页 -->
                            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                               <div class="text-sm text-gray-600">
                                   显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                            </div>
                               <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                   <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                       <span class="sr-only">上一页</span>
                                       <i class="fas fa-chevron-left h-5 w-5"></i>
                                   </a>
                                   <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                       1
                                   </a>
                                   <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                       <span class="sr-only">下一页</span>
                                       <i class="fas fa-chevron-right h-5 w-5"></i>
                                   </a>
                               </nav>
                            </div>
                    </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

     <!-- 添加/编辑车辆 Modal -->
    <div id="vehicleModalApp">
        <el-dialog
            v-model="dialogVisible"
            :title="isEditMode ? '编辑车辆信息' : '新增车辆'"
            width="60%"
            @closed="resetForm"
            :close-on-click-modal="false"
        >
            <el-form :model="vehicleForm" ref="vehicleFormRef" label-width="100px" label-position="right">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="车牌号码" required prop="plateNumber">
                            <el-input v-model="vehicleForm.plateNumber" placeholder="例如: 桂A88888"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="车辆类型" required prop="type">
                            <el-select v-model="vehicleForm.type" placeholder="请选择车辆类型">
                                <el-option label="救援车" value="1"></el-option>
                                <el-option label="消防车" value="2"></el-option>
                                <el-option label="救护车" value="3"></el-option>
                                <el-option label="工程车" value="4"></el-option>
                                <el-option label="指挥车" value="5"></el-option>
                                <el-option label="其他" value="99"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                         <el-form-item label="所属单位" required prop="orgUnit">
                             <el-tree-select
                                v-model="vehicleForm.orgUnit"
                                :data="unitOptions"
                                :multiple="false"
                                :check-strictly="true"
                                placeholder="请选择所属单位"
                             />
                        </el-form-item>
                    </el-col>
                     <el-col :span="12">
                         <el-form-item label="车辆状态" required prop="status">
                            <el-select v-model="vehicleForm.status" placeholder="请选择车辆状态">
                                <el-option label="正常" value="1"></el-option>
                                <el-option label="停用" value="0"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                 </el-row>
                 <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="负责人" required prop="manager">
                            <el-input v-model="vehicleForm.manager" placeholder="请输入负责人姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="联系电话" required prop="phone">
                            <el-input v-model="vehicleForm.phone" placeholder="请输入负责人联系电话"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                 <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="地址" prop="address">
                            <el-input v-model="vehicleForm.address" placeholder="请输入车辆停放或所在地址"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="路段编号" prop="roadId">
                            <el-input v-model="vehicleForm.roadId" placeholder="例如: G72"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="起始桩号" prop="startStake">
                    <el-input v-model="vehicleForm.startStake" placeholder="例如: K15+200"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remarks">
                     <el-input type="textarea" :rows="3" v-model="vehicleForm.remarks" placeholder="请输入备注信息"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                </span>
            </template>
        </el-dialog>
        </div>

    <!-- 新增: 添加/编辑调度点 Modal -->
    <div id="towPointModalApp">
        <el-dialog
             v-model="dialogVisible"
             :title="isEditMode ? '编辑调度点' : '新增调度点'"
             width="50%"
             @closed="resetForm"
             :close-on-click-modal="false"
          >
             <el-form :model="towPointForm" ref="towPointFormRef" label-width="100px">
                 <el-form-item label="调度点名称" required prop="name">
                     <el-input v-model="towPointForm.name" placeholder="请输入调度点名称"></el-input>
                 </el-form-item>
                  <el-form-item label="所属单位" required prop="orgUnit">
                     <el-tree-select
                         v-model="towPointForm.orgUnit"
                         :data="unitOptions"
                         :multiple="false"
                         :check-strictly="true"
                         placeholder="请选择所属单位"
                         style="width: 100%;"
                     />
                 </el-form-item>
                  <el-form-item label="路段编号" prop="roadId">
                     <el-input v-model="towPointForm.roadId" placeholder="例如: G72"></el-input>
                 </el-form-item>
                  <el-form-item label="起始桩号" prop="startMarker">
                     <el-input v-model="towPointForm.startMarker" placeholder="例如: K1500+000"></el-input>
                 </el-form-item>
                 <el-form-item label="地址" prop="address">
                     <el-input v-model="towPointForm.address" placeholder="请输入调度点地址"></el-input>
                 </el-form-item>
                 <el-form-item label="负责人" prop="manager">
                     <el-input v-model="towPointForm.manager" placeholder="请输入负责人姓名"></el-input>
                 </el-form-item>
                 <el-form-item label="联系方式" prop="phone">
                     <el-input v-model="towPointForm.phone" placeholder="请输入负责人联系方式"></el-input>
                 </el-form-item>
                 <el-form-item label="备注" prop="remarks">
                      <el-input type="textarea" :rows="3" v-model="towPointForm.remarks" placeholder="请输入备注信息"></el-input>
                 </el-form-item>
             </el-form>
             <template #footer>
                 <span class="dialog-footer">
                     <el-button @click="dialogVisible = false">取消</el-button>
                     <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                 </span>
             </template>
         </el-dialog>
    </div>

    <!-- 引入 Vue 3 和 Element Plus -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script>
        // Define unit options globally for reuse
        const standardUnitOptions = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [ // Corrected label
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
            // Add other top-level orgs if needed
        ];

        // Vue App for Org Structure Filter (Vehicle Tab)
        const OrgFilterApp = {
            data() {
                return {
                    selectedUnit: null, // Changed from selectedUnits
                    unitOptions: standardUnitOptions // Use standard data
                }
            },
            methods: {
                handleChange(value) {
                    console.log('Selected Unit for Vehicle Filter:', value);
                    // Trigger filter logic here
                }
            }
        };
        const filterVm = Vue.createApp(OrgFilterApp);
        filterVm.use(ElementPlus);
        filterVm.mount('#app'); // Mount to original #app for vehicle filter

        // Vue App for Vehicle Modal
         const VehicleModalApp = {
             data() {
                 return {
                     dialogVisible: false,
                     isEditMode: false,
                     vehicleForm: {
                         id: null,
                         plateNumber: '',
                         type: '',
                         orgUnit: null, // Changed from array/string to null
                         status: '',
                         manager: '',
                         phone: '',
                         roadId: '',    // Added roadId
                         startStake: '', // Added startStake
                         address: '',    // Re-added address
                         remarks: ''
                     },
                     unitOptions: standardUnitOptions // Use standard data
                 };
             },
             methods: {
                 openModal(isEdit = false, vehicleData = null) {
                     this.isEditMode = isEdit;
                     if (isEdit && vehicleData) {
                         // Populate form with existing data
                         this.vehicleForm = { ...vehicleData };
                     } else {
                         this.resetForm(); // Ensure form is clean for adding
                     }
                     this.dialogVisible = true;
                 },
                 resetForm() {
                     // Reset form fields
                     this.vehicleForm = {
                         id: null, plateNumber: '', type: '', orgUnit: null, status: '',
                         manager: '', phone: '', address: '', roadId: '', startStake: '', remarks: ''
                     };
                     if (this.$refs.vehicleFormRef) {
                         this.$refs.vehicleFormRef.resetFields(); // Also reset validation
                     }
                 },
                 submitForm() {
                     this.$refs.vehicleFormRef.validate((valid) => {
                         if (valid) {
                             console.log('Form data:', this.vehicleForm);
                             // TODO: Implement actual form submission (add/update)
                             alert('提交成功! (模拟)');
                             this.dialogVisible = false;
                             // Optionally refresh the table list here
                         } else {
                             console.log('Form validation failed!');
                             return false;
                         }
                     });
                 }
             }
         };
        const modalVm = Vue.createApp(VehicleModalApp);
        modalVm.use(ElementPlus);
        const mountedModal = modalVm.mount('#vehicleModalApp');

        // Vue App for Tow Point Modal
         const TowPointModalApp = {
             data() {
                 return {
                     dialogVisible: false,
                     isEditMode: false,
                     towPointForm: {
                         id: null,
                         name: '',
                         orgUnit: null, // Changed to null
                         roadId: '',    // Added roadId
                         startMarker: '',
                         manager: '',
                         phone: '',
                         address: '',    // Added address
                         remarks: ''
                     },
                      unitOptions: standardUnitOptions // Use standard data
                 };
             },
             methods: {
                 openModal(isEdit = false, towPointData = null) {
                     this.isEditMode = isEdit;
                     if (isEdit && towPointData) {
                         this.towPointForm = { ...towPointData };
                     } else {
                         this.resetForm();
                     }
                     this.dialogVisible = true;
                 },
                 resetForm() {
                     this.towPointForm = {
                         id: null, name: '', orgUnit: null, roadId: '', startMarker: '',
                         manager: '', phone: '', address: '', remarks: '' // Added roadId
                     };
                     if (this.$refs.towPointFormRef) {
                         this.$refs.towPointFormRef.resetFields();
                     }
                 },
                 submitForm() {
                     this.$refs.towPointFormRef.validate((valid) => {
                         if (valid) {
                             console.log('Tow Point Form data:', this.towPointForm);
                             alert('调度点提交成功! (模拟)');
                             this.dialogVisible = false;
                             // Optionally refresh the tow point table list here
                         } else {
                             console.log('Tow Point Form validation failed!');
                             return false;
                         }
                     });
                 }
             }
         };
         const towPointModalVm = Vue.createApp(TowPointModalApp);
         towPointModalVm.use(ElementPlus);
         const mountedTowPointModal = towPointModalVm.mount('#towPointModalApp');

        // Vue App for Tow Point Filter Org Unit
         const TowPointFilterOrgApp = {
            data() {
                return {
                     selectedUnit: null, // Changed from selectedUnits/selectedTowPointUnits
                     unitOptions: standardUnitOptions // Use standard data
                 }
             }
             // Removed methods as they weren't defined/needed previously for filter
         };
         const towPointFilterVm = Vue.createApp(TowPointFilterOrgApp);
         towPointFilterVm.use(ElementPlus);
         towPointFilterVm.mount('#tow-point-filter-org-app'); // Mount to tow point filter div

        // Tab Switching Logic
        const tabButtons = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.dataset.tab;

                // Update button active states
                tabButtons.forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.tab === targetTab);
                });

                // Update content active states
                tabContents.forEach(content => {
                    content.classList.toggle('active', content.id === targetTab);
                });
            });
        });

        // Event Listeners for Modals and Actions
        document.addEventListener('click', function(event) {
            // --- Vehicle Actions ---
            const addVehicleButton = event.target.closest('#btnAddVehicle');
            const editVehicleButton = event.target.closest('.btn-edit-vehicle');
            const viewVehicleButton = event.target.closest('.btn-view-vehicle');
            const deleteVehicleButton = event.target.closest('.btn-delete-vehicle');

            if (addVehicleButton) {
                mountedModal.openModal(false); // Open vehicle modal for adding
            }
            else if (editVehicleButton) {
                const vehicleId = editVehicleButton.dataset.id;
                console.log("Edit vehicle ID:", vehicleId);
                // TODO: Fetch actual vehicle data by ID
                const mockVehicleData = { 
                    id: vehicleId, 
                    plateNumber: '桂A' + vehicleId + '1234', 
                    type: '3', // Example: Ambulance
                    orgUnit: '1.2.2', // Example: Nanning
                    status: '1', // Example: Available (Normal)
                    manager: '模拟负责人 ' + vehicleId, 
                    phone: '1300000000' + vehicleId,
                    address: '模拟地址 ' + vehicleId + ' 号', // Re-added address
                    roadId: 'G' + (70 + parseInt(vehicleId)), // Mock roadId
                    startStake: 'K' + (10 * parseInt(vehicleId)) + '+000', // Mock startStake
                    remarks: '这是模拟编辑数据'
                };
                mountedModal.openModal(true, mockVehicleData); // Open vehicle modal for editing
            }
            else if (viewVehicleButton) {
                const vehicleId = viewVehicleButton.dataset.id;
                console.log("View vehicle ID:", vehicleId);
                alert('查看车辆 ID: ' + vehicleId + ' (功能待实现)');
            }
             else if (deleteVehicleButton) {
                const vehicleId = deleteVehicleButton.dataset.id;
                if (confirm(`确定要删除车辆 ID ${vehicleId} 吗？`)) {
                    console.log("Delete vehicle ID:", vehicleId);
                    alert('删除车辆 ID: ' + vehicleId + ' (功能待实现)');
                    // TODO: Implement actual deletion logic
                }
            }

            // --- Tow Point Actions ---
            const addTowPointButton = event.target.closest('#btnAddTowPoint');
            const editTowPointButton = event.target.closest('.btn-edit-tow-point');
            const deleteTowPointButton = event.target.closest('.btn-delete-tow-point');

            if (addTowPointButton) {
                mountedTowPointModal.openModal(false); // Open tow point modal for adding
            }
            else if (editTowPointButton) {
                const towPointId = editTowPointButton.dataset.id;
                console.log("Edit tow point ID:", towPointId);
                 // TODO: Fetch actual tow point data by ID
                const mockTowPointData = {
                    id: towPointId,
                    name: '模拟调度点 ' + towPointId,
                    orgUnit: '1.1.1', // Example: Highway Center
                    roadId: 'G' + (towPointId.startsWith('tp') ? towPointId.substring(2) : towPointId), // Mock Road ID
                    startMarker: 'K100+000',
                    manager: '模拟调度员 ' + towPointId,
                    phone: '1310000000' + (towPointId.length > 1 ? towPointId.substring(2) : towPointId), // Simple mock phone
                    address: '模拟调度点地址 ' + towPointId,
                    remarks: '这是模拟调度点编辑数据'
                };
                mountedTowPointModal.openModal(true, mockTowPointData); // Open tow point modal for editing
            }
             else if (deleteTowPointButton) {
                const towPointId = deleteTowPointButton.dataset.id;
                if (confirm(`确定要删除调度点 ID ${towPointId} 吗？`)) {
                    console.log("Delete tow point ID:", towPointId);
                    alert('删除调度点 ID: ' + towPointId + ' (功能待实现)');
                    // TODO: Implement actual deletion logic
                }
            }
        });

    </script>
     <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 