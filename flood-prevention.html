<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防汛防台 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    <link rel="stylesheet" href="alert-styles.css">
    <link rel="stylesheet" href="css/flood-prevention.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 防汛防台内容 -->
                <div id="flood-prevention-content" class="tab-content flood-prevention-container" style="display: flex;">
            <!-- 左侧资源目录 -->
            <aside class="left-sidebar">
                <div class="resource-filter">
                    <h3>资源目录</h3>
                    <div class="resource-filter-container">
                        <!-- 1. 资源类型选择器 -->
                        <div class="resource-type-selector">
                            <h4>资源类型</h4>
                            <!-- 筛选面板 -->
                            <div class="filter-panel">
                                <!-- 全选复选框 -->
                                <div class="filter-row">
                                    <div class="filter-item checkbox-item">
                                        <input type="checkbox" id="flood-all" name="resource-type" value="all" checked>
                                        <label for="flood-all">全选</label>
                                    </div>
                                </div>

                                <!-- 预警等级下拉框 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="warning-level-select">预警等级：</label>
                                        <select id="warning-level-select" class="filter-select">
                                            <option value="all">所有等级</option>
                                            <option value="red">红色</option>
                                            <option value="orange">橙色</option>
                                            <option value="yellow">黄色</option>
                                            <option value="blue">蓝色</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 灾害类型下拉框 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="disaster-type-select">灾害类型：</label>
                                        <select id="disaster-type-select" class="filter-select">
                                            <option value="all">所有类型</option>
                                            <option value="rainstorm">暴雨</option>
                                            <option value="flood">洪水</option>
                                            <option value="typhoon">台风</option>
                                            <option value="thunderstorm">雷暴大风</option>
                                            <option value="landslide">泥石流</option>
                                            <option value="mountain-flood">山洪</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 发布时间筛选 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="publish-date">发布时间：</label>
                                        <input type="date" id="publish-date" class="filter-date">
                                    </div>
                                </div>

                                <!-- 关键字搜索 -->
                                <div class="filter-row">
                                    <div class="filter-item">
                                        <label for="keyword-search">关键字搜索：</label>
                                        <div class="search-box">
                                            <input type="text" id="keyword-search" placeholder="输入关键字...">
                                            <button id="search-btn"><i class="fas fa-search"></i></button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 告警信息列表 -->
                <div class="alert-list-container">
                    <h4>告警信息 <i class="fas fa-bell" style="color: #dc3545; margin-left: 5px;"></i></h4>
                    <div class="alert-tabs">
                        <button class="alert-tab-button active" data-tab="weather-alerts" onclick="switchWeatherAlertTab(this, 'weather-alerts')">气象预警</button>
                    </div>

                    <!-- 气象预警内容 -->
                    <div id="alert-weather-alerts-content" class="alert-tab-content active">
                        <ul class="alert-list">
                            <li class="alert-item high">
                                <div class="alert-time">2024-05-20 07:30</div>
                                <div class="alert-content">
                                    <span class="alert-level">红色预警</span>
                                    <span class="alert-text">玉林市发布暴雨红色预警，预计未来3小时降雨量将超过100毫米</span>
                                </div>
                            </li>
                            <li class="alert-item medium">
                                <div class="alert-time">2024-05-19 18:45</div>
                                <div class="alert-content">
                                    <span class="alert-level">橙色预警</span>
                                    <span class="alert-text">柳州市发布洪水橙色预警，柳江水位持续上涨</span>
                                </div>
                            </li>
                            <li class="alert-item high">
                                <div class="alert-time">2024-05-19 16:15</div>
                                <div class="alert-content">
                                    <span class="alert-level">红色预警</span>
                                    <span class="alert-text">桂林市发布山洪红色预警，多地出现山体滑坡风险</span>
                                </div>
                            </li>
                            <li class="alert-item low">
                                <div class="alert-time">2024-05-18 21:20</div>
                                <div class="alert-content">
                                    <span class="alert-level">蓝色预警</span>
                                    <span class="alert-text">梧州市发布雷暴大风蓝色预警，局部地区可能出现8-10级大风</span>
                                </div>
                            </li>
                            <li class="alert-item medium">
                                <div class="alert-time">2024-05-18 14:30</div>
                                <div class="alert-content">
                                    <span class="alert-level">黄色预警</span>
                                    <span class="alert-text">北海市发布台风黄色预警，沿海地区将有7-9级大风</span>
                                </div>
                            </li>
                            <li class="alert-item high">
                                <div class="alert-time">2024-05-18 11:45</div>
                                <div class="alert-content">
                                    <span class="alert-level">红色预警</span>
                                    <span class="alert-text">百色市发布泥石流红色预警，山区道路存在严重安全隐患</span>
                                </div>
                            </li>
                            <li class="alert-item medium">
                                <div class="alert-time">2024-05-18 09:20</div>
                                <div class="alert-content">
                                    <span class="alert-level">橙色预警</span>
                                    <span class="alert-text">河池市发布山洪橙色预警，多条河流水位快速上涨</span>
                                </div>
                            </li>
                            <li class="alert-item low">
                                <div class="alert-time">2024-05-17 22:15</div>
                                <div class="alert-content">
                                    <span class="alert-level">蓝色预警</span>
                                    <span class="alert-text">贺州市发布暴雨蓝色预警，局部地区有中到大雨</span>
                                </div>
                            </li>
                            <li class="alert-item medium">
                                <div class="alert-time">2024-05-17 19:30</div>
                                <div class="alert-content">
                                    <span class="alert-level">黄色预警</span>
                                    <span class="alert-text">来宾市发布雷暴大风黄色预警，阵风可达9-11级</span>
                                </div>
                            </li>
                            <li class="alert-item high">
                                <div class="alert-time">2024-05-17 16:00</div>
                                <div class="alert-content">
                                    <span class="alert-level">红色预警</span>
                                    <span class="alert-text">崇左市发布洪水红色预警，左江流域水位超警戒线</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </aside>

            <!-- 地图显示区域 -->
            <section class="map-display-area">
                        <!-- 最新告警提示框 -->
                        <div class="latest-alert-container" id="flood-map-alert" style="position: absolute; top: 15px; left: 15px; z-index: 1000; width: 480px;">
                            <div class="latest-alert high" style="background: rgba(220, 53, 69, 0.95); color: white; padding: 12px 15px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); display: flex; align-items: center; gap: 10px; font-size: 13px;">
                                <div class="alert-icon" style="font-size: 16px; flex-shrink: 0;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content" style="flex: 1;">
                                    <div class="alert-message" style="line-height: 1.4;">
                                        <span class="alert-level" style="background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 3px; margin-right: 8px; font-size: 11px; font-weight: bold;">红色预警</span>
                                        <span class="alert-text" style="font-size: 13px;">玉林市发布暴雨红色预警，预计未来3小时降雨量将超过100毫米</span>
                                    </div>
                                </div>
                                <div class="alert-actions" style="display: flex; gap: 8px; flex-shrink: 0;">
                                    <button class="alert-more-btn" onclick="openWeatherAlertModal()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 5px 10px; border-radius: 4px; cursor: pointer; font-size: 11px; font-weight: bold;">
                                        查看详情
                                    </button>
                                    <button class="alert-close-btn" onclick="document.getElementById('flood-map-alert').style.display='none'" style="background: none; border: none; color: white; cursor: pointer; font-size: 14px; padding: 3px;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                <!-- 管理按钮容器 -->
                <div class="management-button-container">
                    <!-- 气象预警管理按钮 -->
                    <a href="weather_alerts.html" class="management-button">
                        <i class="fas fa-cloud-sun-rain"></i> 气象预警管理
                    </a>
                </div>

                <img src="lib/map_new.png" alt="广西地图" id="flood-map-image">

                <!-- 图例 -->
                <div class="map-legend">
                    <div class="legend-section">
                        <div class="legend-title">灾害类型</div>
                        <div class="legend-items">
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-cloud-showers-heavy" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">暴雨</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-water" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">洪水</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-wind" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">台风</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-bolt" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">雷暴大风</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-mountain" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">泥石流</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon">
                                    <i class="fas fa-stream" style="font-size: 8px;"></i>
                                </div>
                                <div class="legend-text">山洪</div>
                            </div>
                        </div>
                    </div>

                    <div class="legend-section">
                        <div class="legend-title">预警级别</div>
                        <div class="legend-items">
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(220, 53, 69, 0.8);"></div>
                                <div class="legend-text">红色预警</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(255, 153, 0, 0.8);"></div>
                                <div class="legend-text">橙色预警</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(255, 193, 7, 0.8);"></div>
                                <div class="legend-text">黄色预警</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-icon" style="background-color: rgba(13, 110, 253, 0.8);"></div>
                                <div class="legend-text">蓝色预警</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图上的标注点 - 暴雨预警标点 -->
                <div class="map-marker warning-marker" style="top: 25%; left: 20%; background-color: rgba(255, 193, 7, 0.8); cursor: pointer;" data-type="rainstorm" data-level="yellow" onclick="openWeatherAlertModal()">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 30%; left: 35%; background-color: rgba(13, 110, 253, 0.8); cursor: pointer;" data-type="rainstorm" data-level="blue" onclick="openWeatherAlertModal()">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 45%; left: 60%; background-color: rgba(220, 53, 69, 0.8); cursor: pointer;" data-type="rainstorm" data-level="red" onclick="openWeatherAlertModal()">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 55%; left: 75%; background-color: rgba(255, 153, 0, 0.8); cursor: pointer;" data-type="rainstorm" data-level="orange" onclick="openWeatherAlertModal()">
                    <i class="fas fa-cloud-showers-heavy"></i>
                </div>

                <!-- 洪水预警标点 -->
                <div class="map-marker warning-marker" style="top: 35%; left: 15%; background-color: rgba(255, 193, 7, 0.8); cursor: pointer;" data-type="flood" data-level="yellow" onclick="openWeatherAlertModal()">
                    <i class="fas fa-water"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 50%; left: 40%; background-color: rgba(220, 53, 69, 0.8); cursor: pointer;" data-type="flood" data-level="red" onclick="openWeatherAlertModal()">
                    <i class="fas fa-water"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 65%; left: 70%; background-color: rgba(13, 110, 253, 0.8); cursor: pointer;" data-type="flood" data-level="blue" onclick="openWeatherAlertModal()">
                    <i class="fas fa-water"></i>
                </div>

                <!-- 台风预警标点 -->
                <div class="map-marker warning-marker" style="top: 40%; left: 45%; background-color: rgba(220, 53, 69, 0.8); cursor: pointer;" data-type="typhoon" data-level="red" onclick="openWeatherAlertModal()">
                    <i class="fas fa-wind"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 60%; left: 80%; background-color: rgba(255, 153, 0, 0.8); cursor: pointer;" data-type="typhoon" data-level="orange" onclick="openWeatherAlertModal()">
                    <i class="fas fa-wind"></i>
                </div>

                <!-- 雷暴大风预警标点 -->
                <div class="map-marker warning-marker" style="top: 35%; left: 50%; background-color: rgba(13, 110, 253, 0.8); cursor: pointer;" data-type="thunderstorm" data-level="blue" onclick="openWeatherAlertModal()">
                    <i class="fas fa-bolt"></i>
                </div>
                <div class="map-marker warning-marker" style="top: 55%; left: 25%; background-color: rgba(255, 193, 7, 0.8); cursor: pointer;" data-type="thunderstorm" data-level="yellow" onclick="openWeatherAlertModal()">
                    <i class="fas fa-bolt"></i>
                </div>
            </section>

            <!-- 右侧信息面板 -->
            <aside class="right-sidebar">
                <div class="statistics-panel">
                    <h3>统计分析</h3>
                    <div class="stat-grid">
                        <div class="stat-item">
                            <div class="stat-label">红色预警</div>
                            <div class="stat-value" id="red-warning-count">5</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">橙色预警</div>
                            <div class="stat-value" id="orange-warning-count">5</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">黄色预警</div>
                            <div class="stat-value" id="yellow-warning-count">5</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">蓝色预警</div>
                            <div class="stat-value" id="blue-warning-count">5</div>
                        </div>
                    </div>
                </div>
                <div class="risk-details-list">
                    <h3>详情列表</h3>
                    <table class="details-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>地市</th>
                                <th>预警名称</th>
                                <th>预警级别</th>
                                <th>发布时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>南宁市</td>
                                <td>暴雨预警</td>
                                <td><span class="warning-level red">红色</span></td>
                                <td>2024-05-15</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>柳州市</td>
                                <td>台风预警</td>
                                <td><span class="warning-level orange">橙色</span></td>
                                <td>2024-05-15</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>桂林市</td>
                                <td>洪水预警</td>
                                <td><span class="warning-level red">红色</span></td>
                                <td>2024-05-16</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>玉林市</td>
                                <td>泥石流预警</td>
                                <td><span class="warning-level orange">橙色</span></td>
                                <td>2024-05-16</td>
                            </tr>
                            <tr>
                                <td>5</td>
                                <td>钦州市</td>
                                <td>雷暴大风预警</td>
                                <td><span class="warning-level yellow">黄色</span></td>
                                <td>2024-05-17</td>
                            </tr>
                            <tr>
                                <td>6</td>
                                <td>百色市</td>
                                <td>泥石流预警</td>
                                <td><span class="warning-level red">红色</span></td>
                                <td>2024-05-18</td>
                            </tr>
                            <tr>
                                <td>7</td>
                                <td>河池市</td>
                                <td>山洪预警</td>
                                <td><span class="warning-level orange">橙色</span></td>
                                <td>2024-05-18</td>
                            </tr>
                            <tr>
                                <td>8</td>
                                <td>贺州市</td>
                                <td>暴雨预警</td>
                                <td><span class="warning-level blue">蓝色</span></td>
                                <td>2024-05-17</td>
                            </tr>
                            <tr>
                                <td>9</td>
                                <td>来宾市</td>
                                <td>雷暴大风预警</td>
                                <td><span class="warning-level yellow">黄色</span></td>
                                <td>2024-05-17</td>
                            </tr>
                            <tr>
                                <td>10</td>
                                <td>崇左市</td>
                                <td>洪水预警</td>
                                <td><span class="warning-level red">红色</span></td>
                                <td>2024-05-17</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </aside>
                </div>
            </div>
        </main>
    </div>

    <!-- 气象预警详情模态框 -->
    <div id="weather-alert-modal" class="modal" style="display: none; z-index: 9999 !important;">
        <div class="modal-content" style="max-width: 1000px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">气象预警详情</h3>
                <span class="close" onclick="closeWeatherAlertModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 当前预警信息 -->
                <div class="alert-info-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 18px;">当前预警信息</h4>
                        <button onclick="openNotifyModal()" style="background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold;">
                            <i class="fas fa-bell" style="margin-right: 5px;"></i>通知
                        </button>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">发布地区：</strong><span>玉林市</span></div>
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">预警类型：</strong><span>暴雨预警</span></div>
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">预警等级：</strong><span style="color: #dc3545; font-weight: bold;">红色预警</span></div>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">发布时间：</strong><span>2024-05-20 07:30</span></div>
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">有效期至：</strong><span>2024-05-20 18:00</span></div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">预警内容：</strong></div>
                        <div style="margin-top: 8px; padding: 12px; background: #2c3e50; border-radius: 6px; line-height: 1.6; font-size: 15px;">
                            玉林市气象台2024年05月20日07时30分发布暴雨红色预警信号：预计未来3小时内，玉林市区及各县区将出现100毫米以上强降雨，局部地区可能超过150毫米，并伴有雷电、短时大风等强对流天气。请注意防范山洪、地质灾害、城市内涝等次生灾害。
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">防御指南：</strong></div>
                        <div style="margin-top: 8px; padding: 12px; background: #2c3e50; border-radius: 6px;">
                            <ul style="margin: 0; padding-left: 20px; line-height: 1.8; font-size: 15px;">
                                <li>政府及相关部门按照职责做好防暴雨应急工作</li>
                                <li>切断有危险的室外电源，暂停户外作业</li>
                                <li>处于危险地带的单位应当停课、停业，采取专门措施保护已到校学生、幼儿和其他上班人员的安全</li>
                                <li>做好城市、农田的排涝，注意防范可能引发的山洪、滑坡、泥石流等灾害</li>
                                <li>交通管理部门应当根据路况在强降雨路段采取交通管制措施，在积水路段实行交通引导</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-bottom: 15px;">
                        <div class="info-item" style="font-size: 16px;"><strong style="color: #95a5a6;">受影响道路：</strong></div>
                        <div style="margin-top: 8px; padding: 12px; background: #2c3e50; border-radius: 6px;">
                            <div style="margin-bottom: 8px; font-size: 15px;">• G80广昆高速玉林段</div>
                            <div style="margin-bottom: 8px; font-size: 15px;">• S21玉铁高速全线</div>
                            <div style="margin-bottom: 8px; font-size: 15px;">• G324国道玉林至北流段</div>
                            <div style="margin-bottom: 8px; font-size: 15px;">• S216省道容县至玉林段</div>
                            <div style="font-size: 15px;">• 市区主要道路及桥梁</div>
                        </div>
                    </div>
                </div>



                <!-- 通知确认进展 -->
                <div class="notification-progress-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h4 style="color: #3498db; margin: 0; font-size: 18px;">通知确认进展</h4>
                        <button onclick="urgentReminder()" style="background: #f39c12; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold;">
                            <i class="fas fa-exclamation-circle" style="margin-right: 5px;"></i>一键催办
                        </button>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; background: #2c3e50; border-radius: 6px; overflow: hidden;">
                            <thead>
                                <tr style="background: #34495e;">
                                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #3498db; font-size: 16px; color: #ecf0f1;">单位名称</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #3498db; font-size: 16px; color: #ecf0f1;">通知时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #3498db; font-size: 16px; color: #ecf0f1;">确认状态</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #3498db; font-size: 16px; color: #ecf0f1;">确认时间</th>
                                    <th style="padding: 12px; text-align: center; border-bottom: 2px solid #3498db; font-size: 16px; color: #ecf0f1;">操作</th>
                                </tr>
                            </thead>
                            <tbody id="notification-progress-tbody">
                                <tr style="border-bottom: 1px solid #4a5f7a;">
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">广西交通运输厅</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:35</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;"><span style="color: #28a745; font-weight: bold;">已确认</span></td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:38</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">-</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #4a5f7a;">
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">玉林市交通运输局</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:35</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;"><span style="color: #28a745; font-weight: bold;">已确认</span></td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:40</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">-</td>
                                </tr>
                                <tr style="border-bottom: 1px solid #4a5f7a;">
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">广西高速公路发展中心</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:35</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;"><span style="color: #dc3545; font-weight: bold;">未确认</span></td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">-</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">
                                        <button onclick="sendReminder('广西高速公路发展中心')" style="background: #f39c12; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">催办</button>
                                    </td>
                                </tr>
                                <tr style="border-bottom: 1px solid #4a5f7a;">
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">广西交投集团</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:35</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;"><span style="color: #dc3545; font-weight: bold;">未确认</span></td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">-</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">
                                        <button onclick="sendReminder('广西交投集团')" style="background: #f39c12; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">催办</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">玉林市应急管理局</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">2024-05-20 07:35</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;"><span style="color: #ffc107; font-weight: bold;">处理中</span></td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">-</td>
                                    <td style="padding: 10px; text-align: center; font-size: 15px;">
                                        <button onclick="sendReminder('玉林市应急管理局')" style="background: #f39c12; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">催办</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>



            </div>
        </div>
    </div>

    <!-- 通知单位模态框 -->
    <div id="notify-modal" class="modal" style="display: none; z-index: 10000 !important;">
        <div class="modal-content" style="max-width: 800px; width: 90%; max-height: 90vh; overflow-y: auto;">
            <div class="modal-header" style="background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0;">
                <h3 style="margin: 0; font-size: 20px;">通知单位</h3>
                <span class="close" onclick="closeNotifyModal()" style="position: absolute; right: 20px; top: 15px; font-size: 28px; cursor: pointer;">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px; background: #2c3e50; color: #ecf0f1;">

                <!-- 通知内容 -->
                <div class="notify-content-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="color: #3498db; margin: 0 0 15px 0; font-size: 18px;">通知内容</h4>
                    <textarea id="notify-content" style="width: 100%; height: 120px; background: #2c3e50; color: #ecf0f1; border: 1px solid #4a5f7a; border-radius: 6px; padding: 12px; font-size: 15px; resize: vertical;" placeholder="请输入通知内容...">【紧急通知】玉林市发布暴雨红色预警

各相关单位：

玉林市气象台于2024年05月20日07时30分发布暴雨红色预警信号，预计未来3小时内将出现100毫米以上强降雨，局部地区可能超过150毫米。

请各单位立即：
1. 启动相应级别应急响应
2. 加强值班值守，确保通讯畅通
3. 做好道路交通安全管控
4. 及时上报相关情况

请收到通知后立即确认。

广西交通运输应急管理系统
2024年05月20日</textarea>
                </div>

                <!-- 通知单位选择 -->
                <div class="notify-units-panel" style="background: #34495e; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <h4 style="color: #3498db; margin: 0 0 15px 0; font-size: 18px;">选择通知单位</h4>

                    <!-- 树形结构容器 -->
                    <div class="tree-container" style="background: #2c3e50; border-radius: 6px; padding: 15px; max-height: 400px; overflow-y: auto;">
                        <!-- 1、广西交通运输厅 -->
                        <div class="tree-node">
                            <div class="tree-item" style="display: flex; align-items: center; padding: 8px 0; cursor: pointer;" onclick="toggleTreeNode(this)">
                                <i class="fas fa-chevron-right tree-icon" style="margin-right: 8px; transition: transform 0.3s; color: #95a5a6;"></i>
                                <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                <span style="font-size: 16px; font-weight: bold; color: #3498db;">广西交通运输厅</span>
                            </div>
                            <div class="tree-children" style="margin-left: 20px; display: none;">
                                <!-- 1.1、市级交通运输局 -->
                                <div class="tree-node">
                                    <div class="tree-item" style="display: flex; align-items: center; padding: 6px 0; cursor: pointer;" onclick="toggleTreeNode(this)">
                                        <i class="fas fa-chevron-right tree-icon" style="margin-right: 8px; transition: transform 0.3s; color: #95a5a6;"></i>
                                        <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                        <span style="font-size: 15px; color: #e67e22;">市级交通运输局</span>
                                    </div>
                                    <div class="tree-children" style="margin-left: 20px; display: none;">
                                        <!-- 14个地市交通运输局 -->
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">南宁市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">柳州市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">桂林市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">梧州市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">北海市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">防城港市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">钦州市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">贵港市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">玉林市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">百色市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">贺州市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">河池市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">来宾市交通运输局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">崇左市交通运输局</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 1.2、直属单位 -->
                                <div class="tree-node">
                                    <div class="tree-item" style="display: flex; align-items: center; padding: 6px 0; cursor: pointer;" onclick="toggleTreeNode(this)">
                                        <i class="fas fa-chevron-right tree-icon" style="margin-right: 8px; transition: transform 0.3s; color: #95a5a6;"></i>
                                        <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                        <span style="font-size: 15px; color: #e67e22;">直属单位</span>
                                    </div>
                                    <div class="tree-children" style="margin-left: 20px; display: none;">
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">广西公路发展中心</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">广西高速公路发展中心</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">广西交通运输综合行政执法局</span>
                                        </div>
                                        <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                            <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                            <span style="font-size: 14px;">广西交通运输信息中心</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 2、企业 -->
                        <div class="tree-node">
                            <div class="tree-item" style="display: flex; align-items: center; padding: 8px 0; cursor: pointer;" onclick="toggleTreeNode(this)">
                                <i class="fas fa-chevron-right tree-icon" style="margin-right: 8px; transition: transform 0.3s; color: #95a5a6;"></i>
                                <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                <span style="font-size: 16px; font-weight: bold; color: #3498db;">企业</span>
                            </div>
                            <div class="tree-children" style="margin-left: 20px; display: none;">
                                <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                    <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                    <span style="font-size: 14px;">广西交投集团</span>
                                </div>
                                <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                    <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                    <span style="font-size: 14px;">广西北部湾投资集团</span>
                                </div>
                                <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                    <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                    <span style="font-size: 14px;">广西路桥工程集团</span>
                                </div>
                                <div class="tree-item" style="display: flex; align-items: center; padding: 4px 0;">
                                    <input type="checkbox" class="tree-checkbox" style="margin-right: 8px;" onchange="handleTreeCheckbox(this)">
                                    <span style="font-size: 14px;">广西交通设计集团</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div style="margin-top: 20px; text-align: right;">
                        <button onclick="selectAllUnits()" style="background: #27ae60; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; margin-right: 10px;">
                            全选
                        </button>
                        <button onclick="clearAllUnits()" style="background: #95a5a6; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; margin-right: 10px;">
                            清空
                        </button>
                        <button onclick="sendNotification()" style="background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: bold;">
                            <i class="fas fa-paper-plane" style="margin-right: 5px;"></i>发送通知
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- 引入公共JavaScript -->
    <script src="js/emergency-common.js"></script>

    <!-- 防汛防台专用JavaScript -->
    <script src="js/flood-prevention.js"></script>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('防汛防台页面已加载');

            // 初始化导航高亮
            if (typeof initializeNavigation === 'function') {
                initializeNavigation('flood-prevention');
            }
        });

        // 打开气象预警模态框
        function openWeatherAlertModal() {
            console.log('打开气象预警模态框');
            const modal = document.getElementById('weather-alert-modal');
            if (modal) {
                modal.style.display = 'block';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                modal.style.zIndex = '9999';
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        // 关闭气象预警模态框
        function closeWeatherAlertModal() {
            console.log('关闭气象预警模态框');
            const modal = document.getElementById('weather-alert-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const modal = document.getElementById('weather-alert-modal');
            if (event.target === modal) {
                closeWeatherAlertModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeWeatherAlertModal();
                closeNotifyModal();
            }
        });

        // 通知模态框相关函数
        function openNotifyModal() {
            console.log('打开通知模态框');
            const modal = document.getElementById('notify-modal');
            if (modal) {
                modal.style.display = 'block';
                modal.style.position = 'fixed';
                modal.style.top = '0';
                modal.style.left = '0';
                modal.style.width = '100%';
                modal.style.height = '100%';
                modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                modal.style.zIndex = '10000';
                document.body.style.overflow = 'hidden';
            }
        }

        function closeNotifyModal() {
            console.log('关闭通知模态框');
            const modal = document.getElementById('notify-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // 树形结构相关函数
        function toggleTreeNode(element) {
            const treeNode = element.closest('.tree-node');
            const children = treeNode.querySelector('.tree-children');
            const icon = element.querySelector('.tree-icon');

            if (children) {
                if (children.style.display === 'none') {
                    children.style.display = 'block';
                    icon.style.transform = 'rotate(90deg)';
                } else {
                    children.style.display = 'none';
                    icon.style.transform = 'rotate(0deg)';
                }
            }
        }

        function handleTreeCheckbox(checkbox) {
            const treeNode = checkbox.closest('.tree-node');
            const children = treeNode.querySelector('.tree-children');

            // 如果有子节点，同步选中状态
            if (children) {
                const childCheckboxes = children.querySelectorAll('.tree-checkbox');
                childCheckboxes.forEach(child => {
                    child.checked = checkbox.checked;
                });
            }

            // 向上更新父节点状态
            updateParentCheckbox(checkbox);
        }

        function updateParentCheckbox(checkbox) {
            const currentNode = checkbox.closest('.tree-node');
            const parentNode = currentNode.parentElement.closest('.tree-node');

            if (parentNode) {
                const parentCheckbox = parentNode.querySelector('.tree-checkbox');
                const siblingCheckboxes = currentNode.parentElement.querySelectorAll(':scope > .tree-node > .tree-item > .tree-checkbox');

                const checkedCount = Array.from(siblingCheckboxes).filter(cb => cb.checked).length;

                if (checkedCount === 0) {
                    parentCheckbox.checked = false;
                    parentCheckbox.indeterminate = false;
                } else if (checkedCount === siblingCheckboxes.length) {
                    parentCheckbox.checked = true;
                    parentCheckbox.indeterminate = false;
                } else {
                    parentCheckbox.checked = false;
                    parentCheckbox.indeterminate = true;
                }

                updateParentCheckbox(parentCheckbox);
            }
        }

        function selectAllUnits() {
            const checkboxes = document.querySelectorAll('#notify-modal .tree-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
                checkbox.indeterminate = false;
            });
        }

        function clearAllUnits() {
            const checkboxes = document.querySelectorAll('#notify-modal .tree-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
                checkbox.indeterminate = false;
            });
        }

        function sendNotification() {
            const content = document.getElementById('notify-content').value;
            const checkedUnits = [];

            // 获取选中的单位
            const checkboxes = document.querySelectorAll('#notify-modal .tree-checkbox:checked');
            checkboxes.forEach(checkbox => {
                const unitName = checkbox.nextElementSibling.textContent.trim();
                if (!unitName.includes('、')) { // 排除分类标题
                    checkedUnits.push(unitName);
                }
            });

            if (checkedUnits.length === 0) {
                alert('请选择要通知的单位！');
                return;
            }

            if (!content.trim()) {
                alert('请输入通知内容！');
                return;
            }

            // 模拟发送通知
            console.log('发送通知给：', checkedUnits);
            console.log('通知内容：', content);

            // 更新通知确认进展表格
            updateNotificationProgress(checkedUnits);

            alert(`通知已发送给 ${checkedUnits.length} 个单位！`);
            closeNotifyModal();
        }

        function updateNotificationProgress(units) {
            const tbody = document.getElementById('notification-progress-tbody');
            const currentTime = new Date().toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            }).replace(/\//g, '-');

            // 清空现有内容
            tbody.innerHTML = '';

            // 添加新的通知记录
            units.forEach(unit => {
                const row = document.createElement('tr');
                row.style.borderBottom = '1px solid #4a5f7a';
                row.innerHTML = `
                    <td style="padding: 10px; text-align: center; font-size: 15px;">${unit}</td>
                    <td style="padding: 10px; text-align: center; font-size: 15px;">${currentTime}</td>
                    <td style="padding: 10px; text-align: center; font-size: 15px;"><span style="color: #dc3545; font-weight: bold;">未确认</span></td>
                    <td style="padding: 10px; text-align: center; font-size: 15px;">-</td>
                    <td style="padding: 10px; text-align: center; font-size: 15px;">
                        <button onclick="sendReminder('${unit}')" style="background: #f39c12; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">催办</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function sendReminder(unitName) {
            console.log('发送催办通知给：', unitName);
            alert(`已向 ${unitName} 发送催办通知！`);
        }

        function urgentReminder() {
            const unconfirmedUnits = [];
            const rows = document.querySelectorAll('#notification-progress-tbody tr');

            rows.forEach(row => {
                const status = row.cells[2].textContent.trim();
                if (status === '未确认' || status === '处理中') {
                    const unitName = row.cells[0].textContent.trim();
                    unconfirmedUnits.push(unitName);
                }
            });

            if (unconfirmedUnits.length === 0) {
                alert('所有单位都已确认，无需催办！');
                return;
            }

            console.log('一键催办通知给：', unconfirmedUnits);
            alert(`已向 ${unconfirmedUnits.length} 个未确认单位发送催办通知！`);
        }

        // 点击模态框外部关闭
        window.addEventListener('click', function(event) {
            const weatherModal = document.getElementById('weather-alert-modal');
            const notifyModal = document.getElementById('notify-modal');

            if (event.target === weatherModal) {
                closeWeatherAlertModal();
            }
            if (event.target === notifyModal) {
                closeNotifyModal();
            }
        });

        // 初始化导航栏
        NavigationComponent.init('flood-prevention');
    </script>
</body>
</html>
