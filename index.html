<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>广西公路水路安全畅通与应急处置系统</title>
    <link rel="stylesheet" href="style.css">
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
</head>
<body>
    <header>
        <h1>广西公路水路安全畅通与应急处置系统</h1>
        <nav class="tab-navigation">
            <a href="index.html" class="tab-button active">总览</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>
    <main>
        <!-- New Top Controls Bar -->
        <div class="top-controls-bar">
            <div class="filter-group">
                <select id="unit-filter" name="unit-filter">
                    <option value="">按单位管理划分</option>
                    <!-- Add more <option> tags here via JS or hardcode if static -->
                    <option value="unit1">单位1</option>
                    <option value="unit2">单位2</option>
                </select>
                <select id="road-filter" name="road-filter">
                    <option value="">按路段划分</option>
                    <!-- Add more <option> tags here via JS or hardcode if static -->
                    <option value="road1">路段A</option>
                    <option value="road2">路段B</option>
                </select>
                <input type="text" id="name-search-input" placeholder="请输入名称搜索...">
                <button id="name-search-button">搜索</button>
            </div>

            <div class="layer-control-group">
                <!-- Layer controls will be moved here from the sidebar -->
                <label><input type="checkbox" id="select-all-layers" name="layer-select-all" checked> <strong>全选/全不选</strong></label>
                <hr class="layer-control-hr-top">
                <label><input type="checkbox" name="layer" value="highways" checked> 运营高速路</label>
                <label><input type="checkbox" name="layer" value="waterways" checked> 水路</label>
                <label><input type="checkbox" name="layer" value="roads" checked> 重要国省干道</label>
                <label><input type="checkbox" name="layer" value="railways" checked> 铁路运输网络</label>
                <label><input type="checkbox" name="layer" value="risks" checked> 风险隐患点</label>
                <label><input type="checkbox" name="layer" value="rescue" checked> 应急救援力量</label>
                <label><input type="checkbox" name="layer" value="supplies" checked> 应急物资</label>
                <label><input type="checkbox" name="layer" value="projects" checked> 在建项目</label>
                <label><input type="checkbox" name="layer" value="congestion" checked> 拥堵路段</label>
                <label><input type="checkbox" name="layer" value="events" checked> 应急事件</label>
                <label><input type="checkbox" name="layer" value="weatherAlerts" checked> 气象预警区</label>
            </div>
        </div>

        <section class="map-container" id="map-container">
            <img src="lib/map.png" alt="广西地图" id="map-image">
            <!-- Placeholder for map interactive elements if it were a real map -->
        </section>

        <div id="map-legend" class="map-legend">
            <!-- Legend content will be populated by JavaScript -->
        </div>

    </main>
    <footer>
        <p>&copy; 2025 广西壮族自治区交通运输厅</p>
    </footer>

    <!-- Modal for displaying details -->
    <div id="details-modal" class="modal">
        <div class="modal-content">
            <span class="close-button" onclick="closeModal()">&times;</span>
            <h3 id="modal-title">详细信息</h3>
            <div id="modal-body">
                <!-- Content will be injected here by JavaScript -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <!-- Vue and Element Plus JS (Order is important) -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
</body>
</html> 