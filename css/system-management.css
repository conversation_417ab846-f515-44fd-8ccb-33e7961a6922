/**
 * 系统管理页面专用样式
 * 参考 command-dispatch.css 的布局方式
 */

/* 强制覆盖外部样式限制 */
.container {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

body {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    overflow-x: hidden !important;
}

/* 覆盖可能的tab-content样式 */
.tab-content {
    padding: 0 !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

/* 主要内容区域重写 */
.main-content {
    flex-direction: row !important;
    padding: 0 !important;
    gap: 0 !important;
    height: calc(100vh - 52px) !important;
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    box-sizing: border-box !important;
}

/* 系统管理布局 */
.system-layout {
    display: flex;
    width: 100%;
    height: 100%;
    max-width: none !important;
}

/* 左侧菜单栏 */
.system-sidebar {
    width: 250px;
    background: linear-gradient(180deg, #0056b3 0%, #004494 100%);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

.sidebar-nav {
    flex: 1;
    padding: 25px 0;
    overflow-y: auto;
}

/* 加载菜单样式 */
.loading-menu {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.loading-menu i {
    font-size: 16px;
}

/* 菜单组样式 */
.menu-group {
    margin-bottom: 5px;
}

/* 菜单头部样式 */
.menu-header {
    width: 100%;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    text-align: left;
    border-left: 3px solid transparent;
}

.menu-header:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.menu-header.active {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border-left-color: white;
    font-weight: 600;
}

.menu-header .flex {
    display: flex;
    align-items: center;
    gap: 10px;
}

.menu-header i {
    font-size: 14px;
    width: 16px;
}

/* 菜单项样式 */
.menu-item {
    width: 100%;
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 10px 18px 10px 35px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 13px;
    text-align: left;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.08);
    color: rgba(255, 255, 255, 0.9);
}

.menu-item.active {
    background: rgba(255, 255, 255, 0.12);
    color: white;
    border-left-color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.menu-item i {
    font-size: 12px;
    width: 14px;
}

/* 菜单子项容器 */
.menu-children {
    background: rgba(0, 0, 0, 0.1);
    border-left: 2px solid rgba(255, 255, 255, 0.1);
    margin-left: 20px;
    animation: slideDown 0.2s ease-out;
}

/* 内容区域 */
.system-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #f8f9fa;
    width: 100% !important;
    max-width: none !important;
    min-width: 0;
}

/* 面包屑导航 */
.breadcrumb-nav {
    background: white;
    padding: 15px 25px;
    border-bottom: 1px solid #e9ecef;
    flex-shrink: 0;
}

.breadcrumb-path {
    color: #6c757d;
    font-size: 14px;
}

/* 内容区域 */
.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 25px;
    width: 100%;
    box-sizing: border-box;
}

.content-area::-webkit-scrollbar {
    width: 6px;
}

.content-area::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.content-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

/* 系统概览样式 */
.system-overview {
    width: 100%;
    height: 100%;
}

.overview-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    border: 1px solid #e9ecef;
    padding: 30px;
    height: 100%;
    overflow-y: auto;
    max-width: none;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
}

.overview-header {
    text-align: center;
    margin-bottom: 40px;
}

.overview-icon {
    font-size: 80px;
    color: #0056b3;
    margin-bottom: 20px;
}

.overview-header h1 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.overview-header p {
    font-size: 18px;
    color: #6c757d;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 25px;
    margin-bottom: 40px;
}

@media (max-width: 1200px) {
    .overview-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .overview-grid {
        grid-template-columns: 1fr;
    }
}

.overview-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
}

.overview-card:hover {
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
    transform: translateY(-3px);
}

.risk-card {
    border-color: #dc3545;
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.risk-card:hover {
    border-color: #c82333;
}

.emergency-card {
    border-color: #28a745;
    background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
}

.emergency-card:hover {
    border-color: #218838;
}

.system-card {
    border-color: #007bff;
    background: linear-gradient(135deg, #f0f8ff 0%, #cce7ff 100%);
}

.system-card:hover {
    border-color: #0056b3;
}

.card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.card-header i {
    font-size: 28px;
}

.risk-card .card-header i {
    color: #dc3545;
}

.emergency-card .card-header i {
    color: #28a745;
}

.system-card .card-header i {
    color: #007bff;
}

.card-header h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.risk-card .card-header h3 {
    color: #721c24;
}

.emergency-card .card-header h3 {
    color: #155724;
}

.system-card .card-header h3 {
    color: #004085;
}

.overview-card p {
    font-size: 14px;
    margin-bottom: 15px;
    line-height: 1.5;
}

.risk-card p {
    color: #721c24;
}

.emergency-card p {
    color: #155724;
}

.system-card p {
    color: #004085;
}

.overview-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.overview-card li {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 0;
    font-size: 14px;
}

.overview-card li i {
    font-size: 12px;
}

.risk-card li {
    color: #721c24;
}

.risk-card li i {
    color: #dc3545;
}

.emergency-card li {
    color: #155724;
}

.emergency-card li i {
    color: #28a745;
}

.system-card li {
    color: #004085;
}

.system-card li i {
    color: #007bff;
}

.overview-footer {
    text-align: center;
}

.overview-footer p {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 20px;
}

.tips {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.tip {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    font-size: 14px;
    color: #495057;
}

.tip i {
    font-size: 12px;
    color: #6c757d;
}

/* 加载状态样式 */
#loading-indicator {
    backdrop-filter: blur(2px);
    z-index: 9999;
}

#loading-indicator .bg-white {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
}

/* 错误模态框样式 */
#error-modal {
    backdrop-filter: blur(2px);
    z-index: 9999;
}

#error-modal .bg-white {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.3);
    border-radius: 12px;
}

/* 动画效果 */
@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 500px;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 内容加载动画 */
.content-loading {
    animation: fadeIn 0.3s ease-out;
}

.menu-item-entering {
    animation: slideInLeft 0.2s ease-out;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .system-sidebar {
        position: fixed;
        left: -250px;
        top: 52px;
        height: calc(100vh - 52px);
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .system-sidebar.mobile-open {
        left: 0;
    }

    .system-content {
        margin-left: 0;
    }

    .content-area {
        padding: 16px;
    }

    .breadcrumb-nav {
        padding: 10px 16px;
    }

    .overview-container {
        padding: 20px;
    }
}

@media (max-width: 640px) {
    .overview-header h1 {
        font-size: 28px;
    }

    .overview-icon {
        font-size: 60px;
    }

    .overview-container {
        padding: 15px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .menu-header,
    .menu-item {
        border: 1px solid #000;
    }

    .menu-item.active {
        background-color: #000 !important;
        color: #fff !important;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .menu-header,
    .menu-item,
    .menu-children,
    #system-sidebar {
        transition: none;
    }

    @keyframes slideDown,
    @keyframes fadeIn,
    @keyframes slideInLeft {
        from, to {
            opacity: 1;
            transform: none;
            max-height: none;
        }
    }
}

/* 打印样式 */
@media print {
    #system-sidebar {
        display: none;
    }

    #dynamic-content-container {
        margin: 0;
        box-shadow: none;
    }

    #breadcrumb-nav {
        border-bottom: 1px solid #000;
    }
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
    /* 深色模式样式将在后续版本中添加 */
}

/* 工具类 */
.menu-icon {
    width: 16px;
    text-align: center;
}

.menu-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.menu-badge {
    background-color: #ef4444;
    color: white;
    font-size: 0.625rem;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: auto;
}

/* 焦点样式 */
.menu-header:focus,
.menu-item:focus {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

/* 选择状态样式 */
.menu-header:active,
.menu-item:active {
    transform: scale(0.98);
}

/* 禁用状态样式 */
.menu-header.disabled,
.menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}
