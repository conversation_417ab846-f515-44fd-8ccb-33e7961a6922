/* 侧边栏样式 */
.sidebar {
  width: 250px;
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  z-index: 30;
}

.sidebar-header {
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaeaea;
}

.sidebar-logo {
  display: flex;
  align-items: center;
}

.sidebar-logo img {
  width: 32px;
  height: 32px;
}

.sidebar-logo span {
  margin-left: 10px;
  font-weight: 600;
  font-size: 16px;
  color: #333;
}

.sidebar-toggle {
  cursor: pointer;
  font-size: 20px;
  color: #777;
}

.sidebar-menu {
  padding: 15px 0;
}

.sidebar-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  padding: 0;
  margin: 0;
}

.sidebar-menu a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #555;
  text-decoration: none;
  transition: all 0.2s;
}

.sidebar-menu a:hover {
  background-color: #f5f5f5;
}

.sidebar-menu i {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.sidebar-menu span {
  margin-left: 10px;
}

.sidebar-menu .active {
  background-color: #f0f7ff;
  border-left: 3px solid #3b82f6;
}

.sidebar-menu .active i, 
.sidebar-menu .active span {
  color: #3b82f6;
}

/* 折叠状态 */
.sidebar.collapsed {
  width: 70px;
}

.sidebar.collapsed .sidebar-logo span,
.sidebar.collapsed .sidebar-menu span {
  display: none;
}

@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: -250px;
  }
  
  .sidebar.show {
    left: 0;
  }
} 