/* 表格样式 */
.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

table {
  width: 100%;
  border-collapse: collapse;
}

table th,
table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

table th {
  background-color: #f9fafb;
  font-weight: 600;
  color: #555;
  font-size: 14px;
}

table tbody tr:hover {
  background-color: #f5f7fa;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #166534;
}

.status-badge.leave {
  background-color: #fef3c7;
  color: #854d0e;
}

.status-badge.retired {
  background-color: #f3f4f6;
  color: #6b7280;
}

.btn-view,
.btn-edit,
.btn-delete {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  font-size: 14px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.btn-view {
  color: #3b82f6;
}

.btn-edit {
  color: #10b981;
}

.btn-delete {
  color: #ef4444;
}

.btn-view:hover,
.btn-edit:hover,
.btn-delete:hover {
  background-color: #f0f0f0;
} 