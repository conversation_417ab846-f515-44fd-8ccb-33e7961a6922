/* 统一的顶部导航栏样式 */
.top-nav {
    display: flex;
    justify-content: flex-start; /* 使系统名称和标签卡靠左对齐 */
    align-items: center; /* 垂直居中对齐 */
    padding: 10px 20px;
    background-color: #0056b3; /* 深蓝色背景 */
    color: white;
    flex-shrink: 0; /* 防止header被压缩 */
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    z-index: 100;
    position: relative;
}

.system-title {
    font-size: 1.5em; /* 稍大字体 */
    font-weight: bold;
    margin-right: 30px; /* 添加右侧间距，与标签卡分开 */
}

.tab-navigation {
    display: flex;
}

.tab-button {
    background-color: transparent;
    color: white;
    border: 1px solid white; /* 轻微边框 */
    padding: 8px 15px;
    margin-left: 10px;
    cursor: pointer;
    font-size: 1em;
    border-radius: 4px;
    transition: background-color 0.3s, color 0.3s;
    text-decoration: none;
}

.tab-button:hover {
    background-color: white;
    color: #0056b3;
    text-decoration: none;
}

.tab-button.active {
    background-color: #e9ecef; /* 激活标签的背景色 */
    color: #0056b3;
    border-color: #e9ecef;
    font-weight: bold;
}
