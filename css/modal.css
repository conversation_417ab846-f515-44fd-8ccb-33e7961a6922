/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  overflow: auto;
}

.modal-content {
  background-color: #fff;
  margin: 50px auto;
  width: 100%;
  max-width: 600px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  position: relative;
}

.modal-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  color: #333;
}

.close {
  font-size: 24px;
  font-weight: bold;
  color: #999;
  cursor: pointer;
}

.close:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
  max-height: calc(80vh - 120px); /* 限制最大高度，减去header和footer */
  overflow-y: auto; /* 内容超出时显示滚动条 */
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
  font-size: 16px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.btn-cancel,
.btn-confirm,
.btn-close,
.btn-delete-confirm {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.btn-cancel {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.btn-confirm {
  background-color: #3b82f6;
  color: white;
  border: none;
}

.btn-delete-confirm {
  background-color: #ef4444;
  color: white;
  border: none;
}

.btn-close {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-label {
  font-weight: 500;
  color: #555;
  font-size: 16px;
}

.detail-value {
  color: #333;
  font-size: 16px;
}