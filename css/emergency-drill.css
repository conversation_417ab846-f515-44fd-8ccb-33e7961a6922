/* 应急演练页面专用样式 */

/* 应急演练容器 */
.emergency-drill-container {
    padding: 20px;
    background-color: #f8f9fa;
    min-height: calc(100vh - 60px);
}

/* 演练数据分析部分 */
.drill-statistics-section {
    margin-bottom: 30px;
}

.drill-statistics-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.drill-statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.drill-stat-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.drill-stat-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.drill-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.drill-stat-item:nth-child(1) .drill-stat-icon {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.drill-stat-item:nth-child(2) .drill-stat-icon {
    background: linear-gradient(135deg, #3498db, #5dade2);
}

.drill-stat-item:nth-child(3) .drill-stat-icon {
    background: linear-gradient(135deg, #e74c3c, #ec7063);
}

.drill-stat-item:nth-child(4) .drill-stat-icon {
    background: linear-gradient(135deg, #f39c12, #f7dc6f);
}

.drill-stat-content {
    flex: 1;
}

.drill-stat-value {
    font-size: 32px;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.drill-stat-label {
    font-size: 14px;
    color: #7f8c8d;
    font-weight: 500;
}

/* 演练计划和桌面推演部分 */
.drill-plan-section,
.desktop-drill-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.section-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-box input {
    border: none;
    background: none;
    padding: 10px 15px;
    font-size: 14px;
    color: #495057;
    width: 250px;
    outline: none;
}

.search-box input::placeholder {
    color: #adb5bd;
}

.search-box button {
    background: #3498db;
    border: none;
    color: white;
    padding: 10px 15px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-box button:hover {
    background: #2980b9;
}

/* 操作按钮样式 */
.action-button {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.add-button {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.add-button:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-1px);
}

.start-button {
    background: linear-gradient(135deg, #3498db, #5dade2);
    color: white;
}

.start-button:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-1px);
}

.record-button {
    background: linear-gradient(135deg, #f39c12, #f7dc6f);
    color: white;
}

.record-button:hover {
    background: linear-gradient(135deg, #e67e22, #f39c12);
    transform: translateY(-1px);
}

/* 表格样式 */
.drill-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.drill-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
}

.drill-table th {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    border-bottom: 2px solid #2c3e50;
    white-space: nowrap;
}

.drill-table td {
    padding: 12px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    color: #495057;
    vertical-align: middle;
}

.drill-table tbody tr:hover {
    background-color: #f8f9fa;
}

.drill-table tbody tr:nth-child(even) {
    background-color: #fdfdfe;
}

.drill-table tbody tr:nth-child(even):hover {
    background-color: #f8f9fa;
}

/* 状态标签样式 */
.status-tag {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-tag.completed {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-tag.pending {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* 演练操作按钮样式 */
.drill-action-button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin: 2px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.submit-button {
    background: linear-gradient(135deg, #3498db, #5dade2);
    color: white;
}

.submit-button:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
}

.more-button {
    background: linear-gradient(135deg, #95a5a6, #bdc3c7);
    color: white;
}

.more-button:hover {
    background: linear-gradient(135deg, #7f8c8d, #95a5a6);
}

.view-button {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.view-button:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
}

.no-report {
    color: #adb5bd;
    font-style: italic;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 20px;
    padding: 20px 0;
}

.pagination-button {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    background: white;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-button:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.pagination-button.active {
    background: linear-gradient(135deg, #3498db, #5dade2);
    color: white;
    border-color: #3498db;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .drill-statistics-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .search-box input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .emergency-drill-container {
        padding: 15px;
    }

    .drill-statistics-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .drill-stat-item {
        padding: 20px;
    }

    .drill-stat-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .drill-stat-value {
        font-size: 24px;
    }

    .search-box input {
        width: 100%;
    }

    .drill-table {
        font-size: 12px;
    }

    .drill-table th,
    .drill-table td {
        padding: 8px 6px;
    }

    .drill-action-button {
        padding: 4px 8px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .drill-stat-item {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .pagination {
        flex-wrap: wrap;
        gap: 5px;
    }

    .pagination-button {
        padding: 6px 10px;
        font-size: 12px;
        min-width: 35px;
    }
}

/* 模态框样式 */
.drill-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(3px);
}

.drill-modal-content {
    background-color: #2c3e50;
    margin: 2% auto;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

.drill-modal-content.large-modal {
    max-width: 1200px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.drill-modal-header {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #34495e;
}

.drill-modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.drill-modal-close {
    color: #bdc3c7;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.drill-modal-close:hover {
    color: #ecf0f1;
}

.drill-modal-body {
    padding: 25px;
    color: #ecf0f1;
}

/* 表单样式 */
.drill-form {
    width: 100%;
}

/* 表单区域样式 */
.form-section {
    margin-bottom: 25px;
    padding: 20px;
    background-color: #34495e;
    border-radius: 8px;
    border: 1px solid #2c3e50;
    border-left: 4px solid #3498db;
}

.form-section-title {
    color: #3498db;
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #2c3e50;
}

.form-section-title i {
    font-size: 18px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    color: #ecf0f1;
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 14px;
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 15px;
    border: 1px solid #34495e;
    border-radius: 6px;
    background-color: #34495e;
    color: #ecf0f1;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    background-color: #2c3e50;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #95a5a6;
}

.form-group select option {
    background-color: #34495e;
    color: #ecf0f1;
}

/* 文件上传样式 */
.form-group input[type="file"] {
    padding: 8px 12px;
    background-color: #2c3e50;
    border: 2px dashed #34495e;
    border-radius: 6px;
    color: #ecf0f1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-group input[type="file"]:hover {
    border-color: #3498db;
    background-color: #34495e;
}

.form-group input[type="file"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.file-hint {
    color: #95a5a6;
    font-size: 12px;
    margin-top: 5px;
    display: block;
    font-style: italic;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    padding-top: 20px;
    border-top: 1px solid #34495e;
}

.btn-cancel,
.btn-submit {
    padding: 12px 25px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.btn-cancel {
    background: linear-gradient(135deg, #95a5a6, #bdc3c7);
    color: white;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #7f8c8d, #95a5a6);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(149, 165, 166, 0.3);
}

.btn-submit {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.btn-submit:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

/* 详情内容样式 */
.detail-content {
    width: 100%;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    color: #3498db;
    font-weight: 600;
    font-size: 14px;
}

.detail-item span {
    color: #ecf0f1;
    font-size: 14px;
    padding: 8px 0;
}

.detail-text {
    color: #ecf0f1;
    font-size: 14px;
    line-height: 1.6;
    padding: 12px 15px;
    background-color: #34495e;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

/* 附件区域样式 */
.attachment-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #34495e;
    border-radius: 8px;
    border: 1px solid #2c3e50;
}

.attachment-section h4 {
    color: #3498db;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.file-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 15px;
    background-color: #2c3e50;
    border-radius: 6px;
    border: 1px solid #34495e;
    transition: all 0.3s ease;
}

.file-item:hover {
    background-color: #34495e;
    border-color: #3498db;
}

.file-item i {
    font-size: 20px;
    color: #3498db;
}

.file-item span {
    flex: 1;
    color: #ecf0f1;
    font-size: 14px;
}

.btn-download {
    padding: 6px 12px;
    background: linear-gradient(135deg, #3498db, #5dade2);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-download:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
}

/* 照片画廊样式 */
.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.photo-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background-color: #2c3e50;
    border-radius: 6px;
    border: 1px solid #34495e;
    transition: all 0.3s ease;
}

.photo-item:hover {
    background-color: #34495e;
    border-color: #3498db;
}

.photo-item img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 4px;
    border: 2px solid #34495e;
}

.photo-item span {
    color: #ecf0f1;
    font-size: 12px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .drill-modal-content {
        width: 95%;
        margin: 1% auto;
    }

    .form-grid,
    .detail-grid {
        grid-template-columns: 1fr;
    }

    .form-section {
        padding: 15px;
    }

    .form-section-title {
        font-size: 15px;
    }

    .photo-gallery {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
}

@media (max-width: 768px) {
    .drill-modal-content {
        width: 98%;
        margin: 1% auto;
        max-height: 95vh;
    }

    .drill-modal-header {
        padding: 15px 20px;
    }

    .drill-modal-header h3 {
        font-size: 18px;
    }

    .drill-modal-body {
        padding: 15px;
    }

    .form-section {
        padding: 12px;
        margin-bottom: 20px;
    }

    .form-section-title {
        font-size: 14px;
        margin-bottom: 15px;
    }

    .form-section-title i {
        font-size: 16px;
    }

    .form-grid {
        gap: 15px;
    }

    .form-group label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 12px;
        font-size: 13px;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
    }

    .btn-cancel,
    .btn-submit {
        width: 100%;
        padding: 14px 20px;
    }

    .photo-gallery {
        grid-template-columns: repeat(2, 1fr);
    }

    .photo-item img {
        width: 80px;
        height: 80px;
    }
}

@media (max-width: 480px) {
    .drill-modal-content {
        width: 100%;
        margin: 0;
        border-radius: 0;
        max-height: 100vh;
    }

    .drill-modal-header {
        padding: 12px 15px;
        border-radius: 0;
    }

    .drill-modal-body {
        padding: 12px;
    }

    .form-section {
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 6px;
    }

    .form-section-title {
        font-size: 13px;
        margin-bottom: 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 8px 10px;
        font-size: 12px;
    }
}

/* 复选框组样式 */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    padding: 8px 0;
    transition: all 0.3s ease;
}

.checkbox-item:hover {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 8px 12px;
}

/* 隐藏原生复选框 */
.checkbox-item input[type="checkbox"] {
    display: none;
}

/* 自定义复选框样式 */
.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    background-color: white;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

/* 选中状态的复选框 */
.checkbox-item input[type="checkbox"]:checked + .checkmark {
    background-color: #3498db;
    border-color: #3498db;
}

/* 复选框对勾 */
.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* 复选框标签文字 */
.checkbox-item span:not(.checkmark) {
    color: #495057;
    font-size: 14px;
    font-weight: 500;
    user-select: none;
}

/* 表单显示值样式 */
.form-display-value {
    color: #495057;
    font-weight: 500;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    display: inline-block;
    min-width: 120px;
}