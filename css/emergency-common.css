/* 应急系统公共样式文件 */

/* 基础样式重置 */
body, html {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fa;
    color: #333;
}

/* 主容器布局 */
.container {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 使容器占满整个视口高度 */
}

/* 顶部导航栏样式 */
.top-nav {
    display: flex;
    justify-content: flex-start; /* 使系统名称和标签卡靠左对齐 */
    align-items: center; /* 垂直居中对齐 */
    padding: 6px 15px; /* 减小内边距 */
    background-color: #0056b3; /* 深蓝色背景 */
    color: white;
    flex-shrink: 0; /* 防止header被压缩 */
    min-height: 40px; /* 设置最小高度 */
}

.system-title {
    font-size: 1.2em; /* 减小字体 */
    font-weight: bold;
    margin-right: 20px; /* 减小右侧间距 */
    white-space: nowrap; /* 防止换行 */
}

.tab-navigation {
    display: flex;
    flex-wrap: wrap; /* 允许在必要时换行 */
    align-items: center;
}

.tab-button {
    background-color: transparent;
    color: white;
    border: 1px solid white; /* 轻微边框 */
    padding: 5px 8px; /* 减小内边距 */
    margin-left: 5px; /* 减小左侧间距 */
    margin-bottom: 5px; /* 添加底部间距，以防换行 */
    cursor: pointer;
    font-size: 0.8em; /* 减小字体 */
    border-radius: 3px; /* 减小圆角 */
    transition: background-color 0.3s, color 0.3s;
    white-space: nowrap; /* 防止按钮内文字换行 */
    text-decoration: none; /* 移除链接下划线 */
    display: inline-block;
}

.tab-button:hover {
    background-color: white;
    color: #0056b3;
    text-decoration: none;
}

.tab-button.active {
    background-color: #e9ecef; /* 激活标签的背景色 */
    color: #0056b3;
    border-color: #e9ecef;
}

/* 主要内容区域 */
.main-content {
    flex-grow: 1; /* 占据剩余空间 */
    display: flex; /* 用于控制内部tab-content的显示 */
    overflow-y: auto; /* 如果内容过多则显示滚动条 */
    height: calc(100vh - 52px); /* 减去顶部导航栏的高度，调整为新的高度 */
}

.tab-content {
    display: flex; /* 当前激活的标签页内容为flex布局 */
    flex-direction: row; /* 内部元素横向排列：左侧栏、地图、右侧栏 */
    gap: 15px; /* 各区域之间的间距 */
    height: 100%; /* 确保高度填满容器 */
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
}

/* 左侧资源目录筛选框 */
.left-sidebar {
    flex: 0 0 280px; /* Slightly wider sidebar */
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow-y: auto;
    font-size: 14px; /* 增大基础字体大小 */
}

/* 地图显示区域 */
.map-display-area {
    flex: 1; /* 占据剩余空间 */
    position: relative;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 地图图片样式 */
#map-image, #monitor-map-image, #flood-map-image, #road-network-map-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 5px;
}

/* 地图标点通用样式 */
.map-marker {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    z-index: 10;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.map-marker:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    z-index: 20;
}

/* 风险标点样式 */
.risk-marker {
    background-color: rgba(220, 53, 69, 0.9);
}

.risk-marker i {
    font-size: 10px;
}

.risk-high { background-color: rgba(220, 53, 69, 0.9); } /* 高风险：红色 */
.risk-medium { background-color: rgba(255, 153, 0, 0.9); } /* 中风险：橙色 */
.risk-low { background-color: rgba(40, 167, 69, 0.9); } /* 低风险：绿色 */

/* 项目标点样式 */
.project-marker {
    background-color: rgba(108, 117, 125, 0.9);
}

.project-marker i {
    font-size: 10px;
}

.project-risk { background-color: rgba(220, 53, 69, 0.9); } /* 存在风险：红色 */
.project-safe { background-color: rgba(108, 117, 125, 0.9); } /* 无风险：灰色 */

/* 应急事件标点样式 */
.event-marker {
    background-color: rgba(220, 53, 69, 0.9);
}

/* 救援资源标点样式 */
.rescue-marker, .supply-marker, .supplies-marker, .medical-marker, .vehicle-marker {
    border-radius: 4px;
}

.rescue-marker {
    background-color: rgba(40, 167, 69, 0.9);
}

.supply-marker, .supplies-marker {
    background-color: rgba(255, 193, 7, 0.9);
}

.medical-marker {
    background-color: rgba(220, 53, 69, 0.9);
}

.vehicle-marker {
    background-color: rgba(13, 110, 253, 0.9);
}

/* 摄像头标点样式 */
.camera-marker {
    background-color: rgba(108, 117, 125, 0.9);
    border-radius: 4px;
}

/* 气象预警标点样式 */
.weather-marker {
    background-color: rgba(255, 193, 7, 0.9);
    border-radius: 4px;
}

/* 拥堵标点样式 */
.congestion-marker {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    z-index: 10;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.congestion-marker:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    z-index: 20;
}

.congestion-marker.severe {
    background-color: rgba(220, 53, 69, 0.9); /* 严重拥堵：红色 */
}

.congestion-marker.moderate {
    background-color: rgba(255, 153, 0, 0.9); /* 中度拥堵：橙色 */
}

.congestion-marker.light {
    background-color: rgba(255, 193, 7, 0.9); /* 轻度拥堵：黄色 */
}

.congestion-marker.severe-soon {
    background-color: rgba(220, 53, 69, 0.7); /* 即将严重拥堵：半透明红色 */
}

.congestion-marker.moderate-soon {
    background-color: rgba(255, 153, 0, 0.7); /* 即将中度拥堵：半透明橙色 */
}

/* 地图图例样式 */
.map-legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    font-size: 14px;
    max-width: 250px;
    z-index: 100;
}

.legend-section {
    margin-bottom: 15px;
}

.legend-section:last-child {
    margin-bottom: 0;
}

.legend-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #0056b3;
    font-size: 15px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 4px;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.legend-icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.legend-color {
    width: 14px;
    height: 14px;
    border-radius: 2px;
    border: 1px solid #ccc;
}

.legend-line {
    width: 22px;
    height: 3px;
    border-radius: 1px;
}

.legend-text {
    font-size: 13px;
    color: #333;
    line-height: 1.2;
}

/* 模态框通用样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
}

/* 关闭按钮样式 */
.close-button {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    line-height: 1;
}

.close-button:hover {
    color: #333;
}

/* 按钮通用样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    border: none;
    text-decoration: none;
    transition: background-color 0.3s, color 0.3s;
}

.btn-primary {
    background-color: #0056b3;
    color: white;
}

.btn-primary:hover {
    background-color: #004494;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* 表单元素通用样式 */
input, select, textarea {
    font-family: inherit;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: #0056b3;
    box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        height: auto;
        min-height: 100vh;
    }

    .top-nav {
        flex-direction: column;
        padding: 10px;
        min-height: auto;
    }

    .system-title {
        margin-right: 0;
        margin-bottom: 10px;
        font-size: 1.1em;
    }

    .tab-navigation {
        justify-content: center;
    }

    .tab-button {
        margin: 2px;
        padding: 6px 10px;
        font-size: 0.75em;
    }

    .main-content {
        height: auto;
        min-height: calc(100vh - 100px);
        flex-direction: column;
    }

    .tab-content {
        flex-direction: column;
        padding: 10px;
    }

    .left-sidebar {
        flex: none;
        width: 100%;
        margin-bottom: 15px;
    }

    .map-display-area {
        height: 400px;
        min-height: 400px;
    }
}