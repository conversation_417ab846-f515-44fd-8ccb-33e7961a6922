/* 主内容区样式 */
.container {
  display: flex;
  height: 100vh;
  width: 100%;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f7f7f7;
  margin-left: 250px;
  transition: margin-left 0.3s;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.header-options {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-bar {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.search-bar input {
  padding: 8px 12px;
  border: none;
  outline: none;
  width: 200px;
}

.search-bar button {
  background-color: #f0f0f0;
  border: none;
  padding: 8px 12px;
  cursor: pointer;
}

.search-bar button:hover {
  background-color: #e0e0e0;
}

.btn-add {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-add:hover {
  background-color: #2563eb;
}

.content-body {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.filter-bar {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.filter-item label {
  font-size: 12px;
  color: #666;
}

.filter-item select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 150px;
}

#btnFilter, #btnReset {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 18px;
}

#btnFilter {
  background-color: #3b82f6;
  color: white;
  border: none;
}

#btnReset {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  margin-top: 20px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background-color: white;
  cursor: pointer;
  border-radius: 4px;
}

.page-btn.active {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.page-btn:disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
}

.page-info {
  margin-left: 15px;
  color: #666;
  font-size: 14px;
} 