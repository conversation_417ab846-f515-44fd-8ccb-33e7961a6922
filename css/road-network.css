/* 路网运行页面专用样式 */

/* 路网运行容器 */
.road-network-container {
    height: calc(100vh - 60px);
    overflow: hidden;
}

/* 拥堵标点样式 */
.congestion-marker {
    position: absolute;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 100;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.congestion-marker:hover {
    transform: scale(1.3);
    z-index: 101;
}

/* 严重拥堵标点 */
.congestion-marker.severe {
    background-color: rgba(220, 53, 69, 0.9);
    border-color: rgba(220, 53, 69, 1);
}

/* 中度拥堵标点 */
.congestion-marker.moderate {
    background-color: rgba(255, 153, 0, 0.9);
    border-color: rgba(255, 153, 0, 1);
}

/* 轻度拥堵标点 */
.congestion-marker.light {
    background-color: rgba(255, 193, 7, 0.9);
    border-color: rgba(255, 193, 7, 1);
}

/* 即将严重拥堵标点 */
.congestion-marker.severe-soon {
    background-color: rgba(220, 53, 69, 0.6);
    border: 2px dashed rgba(220, 53, 69, 0.9);
    animation: pulse-severe 2s infinite;
}

/* 即将中度拥堵标点 */
.congestion-marker.moderate-soon {
    background-color: rgba(255, 153, 0, 0.6);
    border: 2px dashed rgba(255, 153, 0, 0.9);
    animation: pulse-moderate 2s infinite;
}

/* 脉冲动画 */
@keyframes pulse-severe {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

@keyframes pulse-moderate {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 153, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 153, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 153, 0, 0);
    }
}

/* 图例颜色块样式 */
.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
}

/* 告警标签页样式 */
.alert-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.alert-tab-button {
    flex: 1;
    padding: 10px 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.alert-tab-button:hover {
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.1);
}

.alert-tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    font-weight: bold;
}

/* 告警内容样式 */
.alert-tab-content {
    display: none;
}

.alert-tab-content.active {
    display: block;
}

/* 筛选器样式增强 */
.filter-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
    color: #333;
}

.filter-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 树形结构样式增强 */
.collapsible-tree {
    list-style: none;
    padding: 0;
    margin: 0;
}

.collapsible-tree li {
    margin: 5px 0;
}

.collapsible-tree ul {
    list-style: none;
    padding-left: 20px;
    margin: 5px 0;
    display: none;
}

.collapsible-tree ul.expanded {
    display: block;
}

.tree-toggler {
    cursor: pointer;
    user-select: none;
    margin-right: 5px;
    font-weight: bold;
    color: #007bff;
    transition: transform 0.3s ease;
}

.tree-toggler.expanded {
    transform: rotate(90deg);
}

.tree-toggler:hover {
    color: #0056b3;
}

/* 统计面板样式增强 */
.stat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #495057;
}

/* 流量表格样式增强 */
.details-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 13px;
}

.details-table th {
    background-color: #f8f9fa;
    padding: 10px 8px;
    text-align: center;
    border: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.details-table td {
    padding: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
    color: #6c757d;
}

.details-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.details-table tbody tr:hover {
    background-color: #e9ecef;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stat-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .congestion-marker {
        width: 10px;
        height: 10px;
    }
}

@media (max-width: 768px) {
    .road-network-container {
        flex-direction: column;
        height: auto;
    }
    
    .left-sidebar,
    .right-sidebar {
        width: 100%;
        max-height: 300px;
        overflow-y: auto;
    }
    
    .map-display-area {
        flex: none;
        height: 400px;
    }
}

/* 拥堵详情模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(3px);
}

.modal-container {
    background-color: #2c3e50;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #3498db;
}

.modal-header h3 {
    color: #ecf0f1;
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.modal-close-btn {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close-btn:hover {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    transform: scale(1.1);
}

.modal-content {
    padding: 25px;
    color: #ecf0f1;
}

/* 信息板块样式 */
.info-section {
    margin-bottom: 30px;
    background: rgba(52, 73, 94, 0.3);
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #3498db;
}

.info-section:last-child {
    margin-bottom: 0;
}

.info-section h4 {
    color: #3498db;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-section h4 i {
    font-size: 18px;
}

/* 基本信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px 25px;
    line-height: 1.6;
    padding-left: 0;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    text-align: left;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item:hover {
    background: rgba(52, 73, 94, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    margin: -4px -8px;
}

.info-label {
    color: #bdc3c7;
    font-weight: 500;
    font-size: 14px;
}

.info-value {
    color: #ecf0f1;
    font-weight: 600;
    font-size: 14px;
}

/* 拥堵等级颜色 */
.info-value.congestion-level.severe {
    color: #e74c3c;
}

.info-value.congestion-level.moderate {
    color: #f39c12;
}

.info-value.congestion-level.light {
    color: #f1c40f;
}

.info-value.congestion-level.severe-soon {
    color: #e74c3c;
    animation: blink 2s infinite;
}

.info-value.congestion-level.moderate-soon {
    color: #f39c12;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.6; }
}

/* 绕行方案样式 */
.detour-routes {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detour-route {
    background: rgba(44, 62, 80, 0.5);
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #27ae60;
    transition: all 0.3s ease;
}

.detour-route:hover {
    background: rgba(52, 73, 94, 0.7);
    transform: translateX(5px);
}

.route-title {
    color: #27ae60;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 15px;
}

.route-description {
    color: #bdc3c7;
    line-height: 1.5;
    margin-bottom: 10px;
}

.route-info {
    display: flex;
    gap: 20px;
    font-size: 13px;
}

.route-distance {
    color: #3498db;
}

.route-time {
    color: #e67e22;
}

.route-status {
    color: #27ae60;
}

/* 监控视频网格 */
.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.video-item {
    background: rgba(44, 62, 80, 0.5);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid rgba(52, 152, 219, 0.2);
    transition: all 0.3s ease;
}

.video-item:hover {
    border-color: rgba(52, 152, 219, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.video-thumbnail {
    width: 100%;
    height: 120px;
    background: linear-gradient(45deg, #34495e, #2c3e50);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #7f8c8d;
    font-size: 24px;
    position: relative;
    cursor: pointer;
}

.video-thumbnail::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    border-left: 15px solid #3498db;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    opacity: 0.8;
}

.video-info {
    padding: 12px;
}

.video-title {
    color: #ecf0f1;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 5px;
}

.video-location {
    color: #bdc3c7;
    font-size: 12px;
    margin-bottom: 5px;
}

.video-status {
    font-size: 12px;
    padding: 3px 8px;
    border-radius: 12px;
    display: inline-block;
}

.video-status.online {
    background-color: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.video-status.offline {
    background-color: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-container {
        width: 95%;
        margin: 10px;
    }
    
    .modal-content {
        padding: 20px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .video-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .route-info {
        flex-direction: column;
        gap: 5px;
    }
} 