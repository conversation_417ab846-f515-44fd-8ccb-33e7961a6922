/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f5f5f5;
}

a {
  color: #3b82f6;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 用字体图标替代 Bootstrap Icons */
.bi {
  display: inline-block;
  width: 1em;
  height: 1em;
  vertical-align: -0.125em;
  font-style: normal;
}

.bi-list:before { content: "☰"; }
.bi-file-earmark-text:before { content: "📄"; }
.bi-diagram-3:before { content: "⋮"; }
.bi-people:before { content: "👥"; }
.bi-person-badge:before { content: "👤"; }
.bi-building:before { content: "🏢"; }
.bi-truck:before { content: "🚚"; }
.bi-house-door:before { content: "🏠"; }
.bi-eye:before { content: "👁️"; }
.bi-pencil:before { content: "✏️"; }
.bi-trash:before { content: "🗑️"; }
.bi-search:before { content: "🔍"; }
.bi-plus:before { content: "+"; }

/* 通用按钮样式 */
.btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  text-align: center;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border: none;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger:hover {
  background-color: #dc2626;
}

/* 通用表单样式 */
input, select, textarea {
  font-family: inherit;
  font-size: 14px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 12px;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

/* 辅助工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 2rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 2rem; }

.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 1rem; }
.ml-4 { margin-left: 1.5rem; }
.ml-5 { margin-left: 2rem; }

.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 1rem; }
.mr-4 { margin-right: 1.5rem; }
.mr-5 { margin-right: 2rem; }

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    transition: all 0.3s;
}

.main-content {
    margin-left: 250px;
    transition: all 0.3s;
}

.sidebar-menu-item {
    padding: 0.75rem 1.25rem;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
}

.sidebar-menu-item:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

.sidebar-menu-item.active {
    background-color: rgba(59, 130, 246, 0.2);
    border-left: 4px solid #2563eb;
}

.sidebar-menu-item.active i {
    color: #2563eb;
}

.sidebar-menu-item.active span {
    color: #2563eb;
    font-weight: 500;
}

/* 折叠侧边栏时的样式 */
.sidebar-collapsed .sidebar {
    width: 70px;
}

.sidebar-collapsed .main-content {
    margin-left: 70px;
}

.sidebar-collapsed .sidebar-menu-item span,
.sidebar-collapsed .sidebar .px-4 {
    display: none;
}

.sidebar-collapsed .sidebar-menu-item {
    justify-content: center;
    padding: 0.75rem 0;
}

.sidebar-collapsed .sidebar-menu-item i {
    margin: 0;
}

/* 标签页样式 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 通知徽章 */
.nav-link {
    position: relative;
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(25%, -25%);
}

/* 版本历史差异样式 */
.diff-added {
    background-color: #dcfce7;
}

.diff-removed {
    background-color: #fee2e2;
    text-decoration: line-through;
}

/* Added from style.css for consistent header */
header {
    background-color: #004080; /* Darker official blue */
    color: white;
    padding: 12px 20px; /* Slightly reduced padding */
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    z-index: 100; 
    position: relative; 
    /* width: 100%; Ensure it takes full width if not already */
}

header h1 {
    margin: 0;
    font-size: 1.6em; 
    font-weight: 500;
    margin-bottom: 10px; 
}

.tab-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    padding-bottom: 10px; 
}

.tab-button {
    background-color: #0056b3; 
    color: white;
    border: 1px solid #004080;
    padding: 8px 18px;
    margin: 0 5px;
    border-radius: 5px 5px 0 0; 
    cursor: pointer;
    font-size: 0.95em;
    text-decoration: none; 
    transition: background-color 0.3s ease, border-color 0.3s ease;
    outline: none;
}

.tab-button:hover {
    background-color: #0069d9;
    border-color: #0056b3;
}

.tab-button.active {
    background-color: #007bff; 
    border-bottom-color: #007bff; 
    color: #fff;
    font-weight: bold;
}
/* End of added header styles */ 