@font-face {
  font-family: "bootstrap-icons";
  src: url("./fonts/bootstrap-icons.woff2") format("woff2"),
       url("./fonts/bootstrap-icons.woff") format("woff");
}

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -0.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bi-list::before { content: "\f479"; }
.bi-file-earmark-text::before { content: "\f2e9"; }
.bi-diagram-3::before { content: "\f270"; }
.bi-people::before { content: "\f53c"; }
.bi-person-badge::before { content: "\f54a"; }
.bi-building::before { content: "\f1ad"; }
.bi-truck::before { content: "\f682"; }
.bi-house-door::before { content: "\f411"; }
.bi-eye::before { content: "\f2b1"; }
.bi-pencil::before { content: "\f528"; }
.bi-trash::before { content: "\f66b"; }
.bi-search::before { content: "\f52a"; }
.bi-plus::before { content: "\f4fe"; } 