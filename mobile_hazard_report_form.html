<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>风险隐患填报表单</title>
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f8f8;
            padding-bottom: 80px; /* Space for fixed bottom button */
        }
        .form-section {
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.08);
        }
        label.field-label {
            display: block;
            font-size: 15px;
            font-weight: 500;
            color: #333;
            margin-bottom: 8px;
        }
        label.field-label .required-star {
            color: #ef4444; /* red-500 */
            margin-left: 2px;
        }
        input[type="text"],
        input[type="date"],
        textarea,
        select {
            appearance: none;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px 12px;
            width: 100%;
            font-size: 16px;
            background-color: #fff;
            box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
            transition: border-color 0.2s ease;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3b82f6; /* blue-500 */
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }
        textarea {
            min-height: 100px;
        }
        .radio-group label {
            margin-right: 16px;
            display: inline-flex;
            align-items: center;
            font-size: 16px;
            color: #333;
        }
        .radio-group input[type="radio"] {
             margin-right: 6px;
             /* Consider using custom radio styles for better mobile appearance */
        }
        .file-input-area {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px 12px;
            background-color: #fff;
            display: flex;
            align-items: center;
        }
        .file-input-btn {
            background-color: #f3f4f6; /* gray-100 */
            border: 1px solid #d1d5db; /* gray-300 */
            padding: 4px 12px;
            border-radius: 6px;
            font-size: 14px;
            color: #374151; /* gray-700 */
            cursor: pointer;
            margin-right: 10px;
            white-space: nowrap;
        }
        .file-input-text {
            font-size: 14px;
            color: #6b7280; /* gray-500 */
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
         .submit-button {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #3b82f6; /* blue-600 */
            color: white;
            text-align: center;
            padding: 16px;
            font-size: 18px;
            font-weight: 600;
            border: none;
            cursor: pointer;
            box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
        }
        .submit-button:hover {
             background-color: #2563eb; /* blue-700 */
        }
        .upload-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 80px;
            height: 80px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            color: #aaa;
            cursor: pointer;
            background-color: #fff;
            transition: border-color 0.2s ease, color 0.2s ease;
            flex-shrink: 0; /* Prevent shrinking */
        }
        .upload-button:hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="bg-white shadow-sm sticky top-0 z-10 flex items-center justify-between px-4">
         <button onclick="history.back()" class="text-blue-600 p-2">
             <i class="fas fa-chevron-left"></i>
         </button>
        <h1 class="text-lg font-semibold text-center py-3 text-gray-800 flex-grow">风险隐患填报</h1>
         <div class="w-8"></div> <!-- Placeholder for balance -->
    </div>

    <!-- Form Content -->
    <div class="p-3 space-y-3">
        <!-- Section 1: Basic Info -->
        <div class="form-section">
            <div class="mb-4">
                <label for="check-category" class="field-label">检查类别 <span class="required-star">*</span></label>
                <select id="check-category" required>
                    <option value="">请选择</option>
                    <optgroup label="风险路段">
                        <option value="risk_section_flood">山洪淹没区风险路段</option>
                        <option value="risk_section_geology">地质灾害风险路段</option>
                    </optgroup>
                    <option value="management_mechanism">工作管理机制隐患</option>
                    <optgroup label="基础保障设施隐患">
                         <option value="basic_facilities_sign">防洪标识</option>
                         <option value="basic_facilities_trail">检查步道</option>
                         <option value="basic_facilities_hazard">涉灾隐患点</option>
                    </optgroup>
                     <optgroup label="涉灾隐患点">
                        <option value="hazard_points_slope">边坡</option>
                        <option value="hazard_points_drainage">防洪排水设施</option>
                        <option value="hazard_points_bridge">桥梁</option>
                        <option value="hazard_points_tunnel">隧道</option>
                    </optgroup>
                    <option value="other">其他</option>
                </select>
            </div>
            <div class="mb-4">
                <label for="city-county" class="field-label">市、区/县名称 <span class="required-star">*</span></label>
                <input type="text" id="city-county" placeholder="系统自动关联/手动选择" required>
            </div>
            <div>
                <label for="owning-unit" class="field-label">所属单位 <span class="required-star">*</span></label>
                <select id="owning-unit" required>
                    <option value="">请选择单位</option>
                    <option value="unit1">南宁市交通运输局</option>
                    <option value="unit2">钦州市交通运输局</option>
                    <option value="unit3">玉林市交通运输局</option>
                    <!-- Add more units as needed -->
                </select>
            </div>
        </div>

        <!-- Section 2: Location Details -->
        <div class="form-section">
            <div class="mb-4">
                <label for="road-number" class="field-label">公路编号 <span class="required-star">*</span></label>
                <select id="road-number" required>
                    <option value="">请先选择市/区县</option>
                    <option value="G324">G324</option>
                    <option value="S211">S211</option>
                    <option value="X456">X456</option>
                </select>
            </div>
             <div class="grid grid-cols-2 gap-4">
                 <div>
                    <label for="start-stake" class="field-label">起点桩号</label>
                    <input type="text" id="start-stake" placeholder="例如: K1500+200">
                 </div>
                 <div>
                     <label for="end-stake" class="field-label">止点桩号</label>
                    <input type="text" id="end-stake" placeholder="例如: K1500+500">
                 </div>
             </div>
        </div>
        
        <!-- Section 3: Hazard Details -->
        <div class="form-section">
             <div class="mb-4">
                 <label for="risk-description" class="field-label">风险点描述 <span class="required-star">*</span></label>
                <textarea id="risk-description" placeholder="请详细描述风险情况" required></textarea>
            </div>
             <div class="mb-4">
                 <label class="field-label">现场照片/附件</label>
                 <div class="flex space-x-3 overflow-x-auto py-2">
                     <!-- Placeholder for uploaded images (Example) -->
                     <div class="w-20 h-20 bg-gray-200 rounded flex items-center justify-center text-gray-400 text-xs italic flex-shrink-0">照片1</div>
                     <div class="w-20 h-20 bg-gray-200 rounded flex items-center justify-center text-gray-400 text-xs italic flex-shrink-0">照片2</div>
                     <!-- Upload Button -->
                     <label class="upload-button">
                         <i class="fas fa-plus fa-2x mb-1"></i> <!-- Changed from fa-camera to fa-plus -->
                         <span class="text-xs">添加照片</span>
                         <input type="file" id="site-photos" accept="image/*" multiple class="hidden">
                     </label>
                 </div>
             </div>
            <div>
                <label for="risk-level" class="field-label">风险等级 <span class="required-star">*</span></label>
                <select id="risk-level" required>
                     <option value="high">高</option>
                    <option value="medium">中</option>
                    <option value="low">低</option>
                    <option value="none">无风险</option>
                 </select>
            </div>
        </div>

        <!-- Section 4: Measures -->
        <div class="form-section">
            <div class="mb-4">
                 <label class="field-label">是否隐患点 <span class="required-star">*</span></label>
                 <div class="radio-group mt-2">
                     <label><input type="radio" name="is_hazard" value="yes"> 是</label>
                     <label><input type="radio" name="is_hazard" value="no" checked> 否</label>
                 </div>
            </div>
            <div class="mb-4">
                 <label class="field-label">是否已采取措施 <span class="required-star">*</span></label>
                 <div class="radio-group mt-2">
                     <label><input type="radio" name="measures_taken" value="yes"> 是</label>
                     <label><input type="radio" name="measures_taken" value="no" checked> 否</label>
                 </div>
            </div>
            <div class="mb-4">
                <label for="measures-detail" class="field-label">已（拟）采取的措施</label>
                <textarea id="measures-detail" placeholder="可多选预设措施，或手动填写"></textarea>
            </div>
             <div>
                 <label for="measures-files" class="field-label">措施附件</label>
                 <div class="file-input-area">
                     <label class="file-input-btn">
                         选择文件
                         <input type="file" id="measures-files" multiple class="hidden">
                     </label>
                     <span class="file-input-text">未选择任何文件</span>
                 </div>
             </div>
        </div>
        
        <!-- Section 5: Check Date -->
        <div class="form-section">
            <label for="check-date" class="field-label">检查日期 <span class="required-star">*</span></label>
            <input type="date" id="check-date" value="2025-05-06" required>
        </div>

    </div>

    <!-- Fixed Submit Button -->
    <button class="submit-button">
        <i class="fas fa-check mr-1"></i> 提交报告
    </button>

    <script>
        // Simple script to update file input text
        document.querySelectorAll('input[type="file"][id*="measures-files"]').forEach(input => {
            input.addEventListener('change', function() {
                const container = this.closest('.file-input-area');
                const textElement = container?.querySelector('.file-input-text');
                if (textElement) {
                    if (this.files && this.files.length > 0) {
                        textElement.textContent = this.files.length > 1 ? `${this.files.length} 个文件` : this.files[0].name;
                    } else {
                        textElement.textContent = '未选择任何文件';
                    }
                }
            });
        });
    </script>

</body>
</html> 