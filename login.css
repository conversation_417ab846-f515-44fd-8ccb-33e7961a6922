/* 登录页面样式 */
.login-button {
    background: linear-gradient(to right, #1e90ff, #4682b4);
    transition: all 0.3s;
}

.login-button:hover {
    background: linear-gradient(to right, #4682b4, #1e90ff);
    transform: translateY(-1px);
}

.login-button:active {
    transform: translateY(1px);
}

.login-input {
    transition: all 0.3s ease;
}

.login-input:focus {
    border-color: #1e90ff;
    box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.5);
}

/* 消息提示样式 */
.login-message {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 100;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.login-message.info {
    background-color: rgba(30, 144, 255, 0.9);
    color: white;
}

.login-message.error {
    background-color: rgba(225, 66, 66, 0.9);
    color: white;
}

.login-message.success {
    background-color: rgba(72, 187, 120, 0.9);
    color: white;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

.delay-100 {
    animation-delay: 0.1s;
}

.delay-200 {
    animation-delay: 0.2s;
}

.delay-300 {
    animation-delay: 0.3s;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .glass-card {
        margin: 0 1rem;
    }
}
