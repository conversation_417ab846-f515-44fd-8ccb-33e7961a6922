<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>监测预警 - 广西交通运输应急管理系统</title>

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">

    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>

    <!-- 自定义样式 -->
    <style>
        /* 监测预警页面特有样式 */

        /* 监测设施标点样式 */
        .facility-marker {
            background-color: rgba(40, 167, 69, 0.9);
            border-radius: 4px;
        }

        .facility-marker.bridge {
            background-color: rgba(40, 167, 69, 0.9);
        }

        .facility-marker.tunnel {
            background-color: rgba(23, 162, 184, 0.9);
        }

        .facility-marker.slope {
            background-color: rgba(253, 126, 20, 0.9);
        }

        .facility-marker.road {
            background-color: rgba(111, 66, 193, 0.9);
        }

        /* 安全监测标点样式 */
        .safety-marker {
            background-color: rgba(108, 117, 125, 0.9);
            border-radius: 4px;
        }

        .safety-marker.video {
            background-color: rgba(108, 117, 125, 0.9);
        }

        .safety-marker.overload {
            background-color: rgba(220, 53, 69, 0.9);
        }

        /* 两客一危车辆标点样式 */
        .vehicle-marker {
            border-radius: 50%;
        }

        .vehicle-marker.bus {
            background-color: rgba(255, 193, 7, 0.9);
        }

        .vehicle-marker.tour {
            background-color: rgba(13, 110, 253, 0.9);
        }

        .vehicle-marker.dangerous {
            background-color: rgba(220, 53, 69, 0.9);
        }

        /* 预警级别样式 */
        .warning-red {
            background-color: rgba(220, 53, 69, 0.9) !important;
            animation: pulse-warning 2s infinite;
        }
        .warning-orange {
            background-color: rgba(253, 126, 20, 0.9) !important;
            animation: pulse-warning 2s infinite;
        }
        .warning-yellow {
            background-color: rgba(255, 193, 7, 0.9) !important;
        }
        .warning-blue {
            background-color: rgba(13, 110, 253, 0.9) !important;
        }

        /* 预警脉冲动画 */
        @keyframes pulse-warning {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
        }

        /* 预警发布按钮 */
        .warning-publish-container {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 1000;
            display: flex;
            gap: 10px;
        }

        .warning-publish-btn, .warning-manage-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: background-color 0.3s ease;
        }

        .warning-manage-btn {
            background: #6c757d;
        }

        .warning-publish-btn:hover {
            background: #c82333;
        }

        .warning-manage-btn:hover {
            background: #5a6268;
        }

        /* 右侧边栏样式 */
        .right-sidebar {
            flex: 0 0 320px;
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow-y: auto;
            font-size: 14px;
        }

        /* 统计面板样式 */
        .statistics-panel {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .statistics-panel h3 {
            margin: 0 0 15px 0;
            color: #0056b3;
            font-size: 16px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }

        .stat-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
        }

        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #0056b3;
        }

        .stat-value.warning-count {
            color: #dc3545;
        }

        .stat-value.alert-count {
            color: #fd7e14;
        }

        /* 实时数据面板样式 */
        .realtime-data-panel {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        .realtime-data-panel h3 {
            margin: 0 0 15px 0;
            color: #0056b3;
            font-size: 16px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 8px;
        }

        .data-tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 2px solid #0056b3;
        }

        .data-tab-button {
            padding: 8px 12px;
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            border-bottom: none;
            cursor: pointer;
            font-size: 13px;
            color: #495057;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-right: 5px;
            transition: background-color 0.2s, color 0.2s;
            flex: 1;
            text-align: center;
        }

        .data-tab-button.active {
            background-color: #0056b3;
            color: white;
            border-color: #0056b3;
        }

        .data-tab-button:not(.active):hover {
            background-color: #d1d9e0;
        }

        .data-tab-content {
            display: none;
        }

        .data-tab-content.active {
            display: block;
        }

        .data-item {
            margin-bottom: 12px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
            border: 1px solid #e9ecef;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .data-item:hover {
            background-color: #f1f3f4;
        }

        .data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .data-name {
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .data-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }

        .data-status.normal {
            background-color: #d4edda;
            color: #155724;
        }

        .data-status.abnormal {
            background-color: #f8d7da;
            color: #721c24;
        }

        .data-status.warning {
            background-color: #fff3cd;
            color: #856404;
        }

        .data-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .data-field {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }

        .field-label {
            color: #6c757d;
        }

        .field-value {
            color: #333;
            font-weight: 500;
        }

        .field-value.warning {
            color: #dc3545;
            font-weight: bold;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 2000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #1a1a1a;
            margin: 2% auto;
            padding: 0;
            border-radius: 8px;
            width: 90%;
            max-width: 1200px;
            max-height: 90vh;
            overflow-y: auto;
            color: white;
        }

        .modal-header {
            background-color: #2c3e50;
            padding: 15px 20px;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 22px;
            font-weight: bold;
            color: white;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-close:hover {
            background-color: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .modal-body {
            padding: 20px;
        }

        /* 车辆详情模态框样式 */
        .vehicle-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-section {
            background-color: #2a2a2a;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #444;
        }

        .info-section h4 {
            margin: 0 0 15px 0;
            color: #4a90e2;
            font-size: 18px;
            border-bottom: 1px solid #444;
            padding-bottom: 8px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .info-item {
            display: flex;
            align-items: center;
            flex: 1;
            margin-right: 20px;
            flex-direction: row;
        }

        .info-item:last-child {
            margin-right: 0;
        }

        .info-label {
            color: #ccc;
            margin-right: 8px;
            flex-shrink: 0;
            white-space: nowrap;
        }

        .info-value {
            color: white;
            font-weight: 500;
            text-align: left;
            flex: 1;
        }

        /* 视频监控区域 */
        .video-monitoring {
            margin: 20px 0;
        }

        .video-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 15px;
            margin-top: 15px;
        }

        .video-container {
            background-color: #000;
            border-radius: 6px;
            overflow: hidden;
            border: 2px solid #444;
        }

        .video-header {
            background-color: #333;
            padding: 10px 12px;
            font-size: 14px;
            color: #ccc;
            text-align: center;
        }

        .video-content {
            width: 100%;
            height: 150px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            position: relative;
        }

        .video-placeholder {
            text-align: center;
        }

        .video-status {
            position: absolute;
            top: 5px;
            right: 5px;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
        }

        .video-status.online {
            background-color: #28a745;
            color: white;
        }

        .video-status.offline {
            background-color: #dc3545;
            color: white;
        }

        /* 地图区域 */
        .map-section {
            margin: 20px 0;
        }

        .map-container {
            width: 100%;
            height: 300px;
            background-color: #2a2a2a;
            border-radius: 6px;
            border: 1px solid #444;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            position: relative;
        }

        /* 操作按钮 */
        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #444;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 15px;
            transition: background-color 0.2s;
        }

        .modal-btn.primary {
            background-color: #4a90e2;
            color: white;
        }

        .modal-btn.primary:hover {
            background-color: #357abd;
        }

        .modal-btn.secondary {
            background-color: #6c757d;
            color: white;
        }

        .modal-btn.secondary:hover {
            background-color: #545b62;
        }

        /* 设施详情模态框样式 */
        .facility-info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-section {
            margin: 20px 0;
        }

        .chart-container {
            background-color: #2a2a2a;
            border-radius: 6px;
            border: 1px solid #444;
            padding: 15px;
            margin-bottom: 15px;
        }

        .chart-title {
            color: #4a90e2;
            font-size: 14px;
            margin-bottom: 10px;
            border-bottom: 1px solid #444;
            padding-bottom: 5px;
        }

        .chart-placeholder {
            width: 100%;
            height: 200px;
            background-color: #1a1a1a;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
            text-align: center;
        }

        /* 风险一张图样式的选项布局 - 整体卡片 */
        .facility-type-list-container,
        .vehicle-type-list-container {
            display: block !important;
            width: 100% !important;
        }

        .facility-type-list,
        .vehicle-type-list {
            display: block !important;
            width: 100% !important;
            padding: 12px !important;
            margin: 0 !important;
            background-color: #f8f9fa !important;
            border: 1px solid #e9ecef !important;
            border-radius: 4px !important;
            margin-top: 8px !important;
        }

        .facility-type-item,
        .vehicle-type-item {
            display: block !important;
            width: 100% !important;
            margin-bottom: 8px !important;
            padding: 0 !important;
            background: none !important;
            border: none !important;
            cursor: pointer !important;
        }

        .facility-type-item:last-child,
        .vehicle-type-item:last-child {
            margin-bottom: 0 !important;
        }

        .facility-type-item:hover,
        .vehicle-type-item:hover {
            background-color: rgba(0,0,0,0.05) !important;
            border-radius: 3px !important;
        }

        .facility-type-item input,
        .vehicle-type-item input {
            margin-right: 8px !important;
            vertical-align: middle !important;
        }

        .facility-type-item label,
        .vehicle-type-item label {
            display: inline !important;
            cursor: pointer !important;
            margin: 0 !important;
            font-size: 14px !important;
            color: #333 !important;
            vertical-align: middle !important;
            font-weight: normal !important;
            padding: 4px 0 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 监测预警内容 -->
                <div id="monitoring-warning-content" class="tab-content" style="display: flex;">
                    <aside class="left-sidebar">
                        <div class="resource-filter-container">
                            <!-- 1. 监测设施类型选择器 -->
                            <div class="resource-type-selector">
                                <h4>监测设施类型</h4>
                                <div class="resource-type-item select-all-res-types">
                                    <input type="checkbox" id="monitor-type-all" name="monitor-type-all" checked onclick="toggleAllMonitorMarkers()">
                                    <label for="monitor-type-all"><strong>全选/全不选</strong></label>
                                </div>

                                <div class="resource-type-tabs">
                                    <div style="display: flex; flex-wrap: wrap; gap: 5px;">
                                        <button class="resource-tab-button active" onclick="showFacilityMonitoring()">设施监测</button>
                                        <button class="resource-tab-button" onclick="showVehicleMonitoring()">两客一危</button>
                                    </div>
                                </div>
                            </div>

                            <!-- 2. 各类型监测设施筛选内容 -->
                            <div class="resource-content-container">
                                <!-- 设施监测筛选内容 -->
                                <div id="facility-monitoring-content" class="resource-tab-content active">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item facility-type-list-container">
                                                <label>监测类型：</label>
                                                <div class="facility-type-list">
                                                    <div class="facility-type-item">
                                                        <input type="checkbox" id="facility-bridge" name="facility-type" value="bridge" checked>
                                                        <label for="facility-bridge">桥梁监测</label>
                                                    </div>
                                                    <div class="facility-type-item">
                                                        <input type="checkbox" id="facility-tunnel" name="facility-type" value="tunnel" checked>
                                                        <label for="facility-tunnel">隧道监测</label>
                                                    </div>
                                                    <div class="facility-type-item">
                                                        <input type="checkbox" id="facility-slope" name="facility-type" value="slope" checked>
                                                        <label for="facility-slope">边坡监测</label>
                                                    </div>
                                                    <div class="facility-type-item">
                                                        <input type="checkbox" id="facility-road" name="facility-type" value="road" checked>
                                                        <label for="facility-road">路面监测</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="facility-status-select">设施状态：</label>
                                                <select id="facility-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="normal">正常</option>
                                                    <option value="abnormal">异常</option>
                                                    <option value="maintenance">维护中</option>
                                                    <option value="fault">故障</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="facility-level-select">监测等级：</label>
                                                <select id="facility-level-select" class="filter-select">
                                                    <option value="all">所有等级</option>
                                                    <option value="key">重点监测</option>
                                                    <option value="general">一般监测</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>



                                <!-- 两客一危监测筛选内容 -->
                                <div id="vehicle-monitoring-content" class="resource-tab-content">
                                    <div class="filter-section">
                                        <div class="filter-row">
                                            <div class="filter-item vehicle-type-list-container">
                                                <label>车辆类型：</label>
                                                <div class="vehicle-type-list">
                                                    <div class="vehicle-type-item">
                                                        <input type="checkbox" id="vehicle-bus" name="vehicle-type" value="bus" checked>
                                                        <label for="vehicle-bus">长途客车</label>
                                                    </div>
                                                    <div class="vehicle-type-item">
                                                        <input type="checkbox" id="vehicle-tour" name="vehicle-type" value="tour" checked>
                                                        <label for="vehicle-tour">旅游包车</label>
                                                    </div>
                                                    <div class="vehicle-type-item">
                                                        <input type="checkbox" id="vehicle-dangerous" name="vehicle-type" value="dangerous" checked>
                                                        <label for="vehicle-dangerous">危货车辆</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="vehicle-status-select">监测状态：</label>
                                                <select id="vehicle-status-select" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="normal">正常</option>
                                                    <option value="speeding">超速</option>
                                                    <option value="off-route">偏离路线</option>
                                                    <option value="illegal-parking">违规停车</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="vehicle-enterprise-select">企业类型：</label>
                                                <select id="vehicle-enterprise-select" class="filter-select">
                                                    <option value="all">所有企业</option>
                                                    <option value="passenger">客运企业</option>
                                                    <option value="dangerous">危货企业</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 3. 资源条件筛选 -->
                            <div class="resource-condition-filter">
                                <div class="filter-tabs">
                                    <button class="filter-tab-button active" data-tab="unit" onclick="switchFilterTab(this, 'unit')">按单位划分</button>
                                    <button class="filter-tab-button" data-tab="road" onclick="switchFilterTab(this, 'road')">按路段划分</button>
                                </div>

                                <!-- 3.1 按单位划分内容 -->
                                <div id="filter-by-unit-content" class="filter-tab-content active">
                                    <ul class="collapsible-tree">
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt" value="gxtyt"><label for="unit-gxtyt">广西交通运输厅</label>
                                            <ul>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-nn" value="gxtyt-nn"><label for="unit-gxtyt-nn">南宁市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lz" value="gxtyt-lz"><label for="unit-gxtyt-lz">柳州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gl" value="gxtyt-gl"><label for="unit-gxtyt-gl">桂林市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-wz" value="gxtyt-wz"><label for="unit-gxtyt-wz">梧州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bh" value="gxtyt-bh"><label for="unit-gxtyt-bh">北海市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-fcg" value="gxtyt-fcg"><label for="unit-gxtyt-fcg">防城港市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-qz" value="gxtyt-qz"><label for="unit-gxtyt-qz">钦州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-gg" value="gxtyt-gg"><label for="unit-gxtyt-gg">贵港市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-yl" value="gxtyt-yl"><label for="unit-gxtyt-yl">玉林市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-bs" value="gxtyt-bs"><label for="unit-gxtyt-bs">百色市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hz" value="gxtyt-hz"><label for="unit-gxtyt-hz">贺州市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-hc" value="gxtyt-hc"><label for="unit-gxtyt-hc">河池市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-lb" value="gxtyt-lb"><label for="unit-gxtyt-lb">来宾市交通运输局</label></li>
                                                <li><span class="tree-toggler">+</span><input type="checkbox" id="unit-gxtyt-cz" value="gxtyt-cz"><label for="unit-gxtyt-cz">崇左市交通运输局</label></li>
                                            </ul>
                                        </li>
                                        <li>
                                            <input type="checkbox" id="unit-qy" value="qy"><label for="unit-qy">企业</label>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 3.2 按路段划分内容 -->
                                <div id="filter-by-road-content" class="filter-tab-content">
                                    <ul class="collapsible-tree">
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="road-gl" value="gl"><label for="road-gl">公路</label>
                                            <ul>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gs" value="gl-gs"><label for="road-gl-gs">高速公路</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-gs-g72" value="gl-gs-g72"><label for="road-gl-gs-g72">G72</label></li>
                                                        <li><input type="checkbox" id="road-gl-gs-g80" value="gl-gs-g80"><label for="road-gl-gs-g80">G80</label></li>
                                                    </ul>
                                                </li>
                                                <li>
                                                    <span class="tree-toggler">+</span><input type="checkbox" id="road-gl-gsgd" value="gl-gsgd"><label for="road-gl-gsgd">国省干道</label>
                                                    <ul>
                                                        <li><input type="checkbox" id="road-gl-gsgd-s201" value="gl-gsgd-s201"><label for="road-gl-gsgd-s201">S201</label></li>
                                                    </ul>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <span class="tree-toggler">+</span><input type="checkbox" id="road-sl" value="sl"><label for="road-sl">水路</label>
                                            <ul>
                                                <li><input type="checkbox" id="road-sl-xn" value="sl-xn"><label for="road-sl-xn">西江航道</label></li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 4. 预警信息列表 -->
                            <div class="alert-list-container">
                                <h4>预警信息 <i class="fas fa-exclamation-triangle" style="color: #dc3545; margin-left: 5px;"></i></h4>

                                <ul class="alert-list" style="list-style: none; padding: 0; margin: 0;">
                                    <li class="alert-item high" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #dc3545; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-20 09:15</div>
                                        <div class="alert-content">
                                            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #dc3545; color: white; border-radius: 3px; margin-right: 8px;">红色预警</span>
                                            <span class="alert-text" style="font-size: 14px; color: #333;">G72高速K1499+500处桥梁监测异常</span>
                                        </div>
                                    </li>
                                    <li class="alert-item medium" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #fd7e14; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-20 08:30</div>
                                        <div class="alert-content">
                                            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #fd7e14; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">橙色预警</span>
                                            <span class="alert-text" style="font-size: 14px; color: #333;">S201省道K45+200处边坡位移超限</span>
                                        </div>
                                    </li>
                                    <li class="alert-item low" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #ffc107; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-20 07:45</div>
                                        <div class="alert-content">
                                            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #ffc107; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">黄色预警</span>
                                            <span class="alert-text" style="font-size: 14px; color: #333;">G80高速K890+100处视频监控离线</span>
                                        </div>
                                    </li>
                                    <li class="alert-item info" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #0d6efd; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">2024-05-20 06:20</div>
                                        <div class="alert-content">
                                            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #0d6efd; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">蓝色预警</span>
                                            <span class="alert-text" style="font-size: 14px; color: #333;">桂A12345客车超速行驶</span>
                                        </div>
                                    </li>
                                    <li class="alert-item resolved" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #6c757d; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-05-19 14:20 (已解除)</div>
                                        <div class="alert-content">
                                            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #6c757d; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">已解除</span>
                                            <span class="alert-text" style="font-size: 14px; color: #333;">柳州隧道渗水监测异常</span>
                                        </div>
                                    </li>
                                    <li class="alert-item resolved" style="margin-bottom: 12px; padding: 12px; background: #fff; border-left: 4px solid #6c757d; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <div class="alert-time" style="font-size: 12px; color: #333; margin-bottom: 5px; font-weight: bold;">2024-05-19 10:15 (已解除)</div>
                                        <div class="alert-content">
                                            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: #6c757d; color: white; border-radius: 3px; font-size: 12px; margin-right: 8px;">已解除</span>
                                            <span class="alert-text" style="font-size: 14px; color: #333;">桂B67890危货车辆偏离指定路线</span>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </aside>

                    <section class="map-display-area">
                        <!-- 预警提示框 (左上角) -->
                        <div class="latest-alert-container" id="monitoring-warning-alert" style="position: absolute; top: 15px; left: 15px; z-index: 1000; width: 480px;">
                            <div class="latest-alert high" style="background: rgba(220, 53, 69, 0.95); color: white; padding: 12px 15px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); display: flex; align-items: center; gap: 10px; font-size: 13px;">
                                <div class="alert-icon" style="font-size: 16px; flex-shrink: 0;">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content" style="flex: 1;">
                                    <div class="alert-message" style="line-height: 1.4;">
                                        <strong>最新预警：</strong>G72高速K1499+500处桥梁监测异常 (红色预警)
                                    </div>
                                    <div class="alert-time" style="font-size: 11px; opacity: 0.9; margin-top: 3px;">
                                        2024-05-20 09:15
                                    </div>
                                </div>
                                <div class="alert-actions" style="display: flex; gap: 8px;">
                                    <button class="alert-action-btn" onclick="showWarningDetails()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 11px;">查看详情</button>
                                    <button class="alert-close-btn" onclick="document.getElementById('monitoring-warning-alert').style.display='none'" style="background: none; border: none; color: white; cursor: pointer; font-size: 14px; padding: 3px;">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 预警发布按钮组 (右上角) -->
                        <div class="warning-publish-container">
                            <button class="warning-publish-btn" onclick="openWarningPublishModal()">
                                <i class="fas fa-broadcast-tower"></i> 发布预警
                            </button>
                            <button class="warning-manage-btn" onclick="openWarningManageModal()">
                                <i class="fas fa-cog"></i> 预警管理
                            </button>
                        </div>

                        <!-- 地图图片 -->
                        <img src="lib/map_new.png" alt="广西地图" id="monitor-map-image">

                        <!-- 地图标点 (动态生成) -->
                        <!-- 设施监测点 -->
                        <div class="map-marker facility-marker bridge" style="position: absolute; top: 35%; left: 45%;" onclick="showFacilityDetail('bridge-001')" title="南宁大桥监测点">
                            <i class="fas fa-bridge"></i>
                        </div>
                        <div class="map-marker facility-marker tunnel" style="position: absolute; top: 25%; left: 35%;" onclick="showFacilityDetail('tunnel-001')" title="柳州隧道监测点">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <div class="map-marker facility-marker slope warning-red" style="position: absolute; top: 30%; left: 40%;" onclick="showFacilityDetail('slope-001')" title="桂林边坡监测点 - 异常">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="map-marker facility-marker road" style="position: absolute; top: 40%; left: 50%;" onclick="showFacilityDetail('road-001')" title="梧州路面监测点">
                            <i class="fas fa-road"></i>
                        </div>



                        <!-- 两客一危车辆位置 -->
                        <div class="map-marker vehicle-marker bus" style="position: absolute; top: 36%; left: 46%;" onclick="showVehicleDetail('bus-001')" title="桂A12345 长途客车">
                            <i class="fas fa-bus"></i>
                        </div>
                        <div class="map-marker vehicle-marker tour" style="position: absolute; top: 28%; left: 38%;" onclick="showVehicleDetail('tour-001')" title="桂B23456 旅游包车">
                            <i class="fas fa-bus-alt"></i>
                        </div>
                        <div class="map-marker vehicle-marker dangerous warning-orange" style="position: absolute; top: 42%; left: 52%;" onclick="showVehicleDetail('dangerous-001')" title="桂C34567 危货车辆 - 超速">
                            <i class="fas fa-truck"></i>
                        </div>

                        <!-- 地图图例 (右下角) -->
                        <div class="map-legend">
                            <div class="legend-section">
                                <div class="legend-title">监测设施</div>
                                <div class="legend-items">
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(40, 167, 69, 0.9); border-radius: 4px;"><i class="fas fa-bridge"></i></div>
                                        <span class="legend-text">桥梁监测</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(23, 162, 184, 0.9); border-radius: 4px;"><i class="fas fa-mountain"></i></div>
                                        <span class="legend-text">隧道监测</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(253, 126, 20, 0.9); border-radius: 4px;"><i class="fas fa-chart-line"></i></div>
                                        <span class="legend-text">边坡监测</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(111, 66, 193, 0.9); border-radius: 4px;"><i class="fas fa-road"></i></div>
                                        <span class="legend-text">路面监测</span>
                                    </div>
                                </div>
                            </div>



                            <div class="legend-section">
                                <div class="legend-title">两客一危</div>
                                <div class="legend-items">
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(255, 193, 7, 0.9);"><i class="fas fa-bus"></i></div>
                                        <span class="legend-text">长途客车</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(13, 110, 253, 0.9);"><i class="fas fa-bus-alt"></i></div>
                                        <span class="legend-text">旅游包车</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-icon" style="background: rgba(220, 53, 69, 0.9);"><i class="fas fa-truck"></i></div>
                                        <span class="legend-text">危货车辆</span>
                                    </div>
                                </div>
                            </div>

                            <div class="legend-section">
                                <div class="legend-title">预警级别</div>
                                <div class="legend-items">
                                    <div class="legend-item">
                                        <div class="legend-color" style="background: #dc3545;"></div>
                                        <span class="legend-text">红色预警</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background: #fd7e14;"></div>
                                        <span class="legend-text">橙色预警</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background: #ffc107;"></div>
                                        <span class="legend-text">黄色预警</span>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background: #0d6efd;"></div>
                                        <span class="legend-text">蓝色预警</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <aside class="right-sidebar">
                        <!-- 1. 监测数据统计面板 -->
                        <div class="statistics-panel">
                            <h3>监测统计</h3>
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <div class="stat-label">监测设施总数</div>
                                    <div class="stat-value" id="facility-count">156</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">异常监测点</div>
                                    <div class="stat-value warning-count" id="abnormal-count">8</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">当前预警数</div>
                                    <div class="stat-value alert-count" id="warning-count">4</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">两客一危车辆</div>
                                    <div class="stat-value" id="vehicle-count">245</div>
                                </div>
                            </div>
                        </div>

                        <!-- 2. 实时监测数据面板 -->
                        <div class="realtime-data-panel">
                            <h3>实时监测数据</h3>
                            <div class="data-tabs">
                                <button class="data-tab-button active" onclick="switchDataTab(this, 'facility-data')">设施数据</button>
                                <button class="data-tab-button" onclick="switchDataTab(this, 'vehicle-data')">两客一危数据</button>
                            </div>

                            <!-- 设施监测数据 -->
                            <div id="facility-data-content" class="data-tab-content active">
                                <div class="data-item" onclick="showFacilityDetail('bridge-001')">
                                    <div class="data-header">
                                        <span class="data-name">南宁大桥监测点</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">位移:</span>
                                            <span class="field-value">2.3mm</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">应力:</span>
                                            <span class="field-value">15.6MPa</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:15:30</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">位置:</span>
                                            <span class="field-value">G72 K1499</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showFacilityDetail('slope-001')">
                                    <div class="data-header">
                                        <span class="data-name">桂林边坡监测点</span>
                                        <span class="data-status abnormal">异常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">位移:</span>
                                            <span class="field-value warning">8.5mm</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">倾斜:</span>
                                            <span class="field-value warning">2.1°</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:14:45</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">位置:</span>
                                            <span class="field-value">S201 K45</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showFacilityDetail('tunnel-001')">
                                    <div class="data-header">
                                        <span class="data-name">柳州隧道监测点</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">渗水量:</span>
                                            <span class="field-value">2.1L/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">温度:</span>
                                            <span class="field-value">18.2°C</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:13:20</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">位置:</span>
                                            <span class="field-value">G80 K890</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showFacilityDetail('road-002')">
                                    <div class="data-header">
                                        <span class="data-name">梧州路面监测点</span>
                                        <span class="data-status warning">维护中</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">沉降量:</span>
                                            <span class="field-value">1.2mm</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">裂缝宽度:</span>
                                            <span class="field-value">0.5mm</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">08:45:15</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">位置:</span>
                                            <span class="field-value">G80 K1200</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showFacilityDetail('bridge-002')">
                                    <div class="data-header">
                                        <span class="data-name">北海大桥监测点</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">位移:</span>
                                            <span class="field-value">1.8mm</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">应力:</span>
                                            <span class="field-value">12.3MPa</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:12:45</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">位置:</span>
                                            <span class="field-value">G75 K650</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showSafetyDetail('video-003')">
                                    <div class="data-header">
                                        <span class="data-name">钦州视频监控点</span>
                                        <span class="data-status abnormal">离线</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">设备状态:</span>
                                            <span class="field-value warning">离线</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">离线时长:</span>
                                            <span class="field-value warning">2小时</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">最后在线:</span>
                                            <span class="field-value">07:30:20</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">位置:</span>
                                            <span class="field-value">S312 K120</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 车辆监测数据 -->
                            <div id="vehicle-data-content" class="data-tab-content">
                                <div class="data-item" onclick="showVehicleDetail('bus-001')">
                                    <div class="data-header">
                                        <span class="data-name">桂A12345 (长途客车)</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">G72 K1520</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">行驶速度:</span>
                                            <span class="field-value">85km/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">载客人数:</span>
                                            <span class="field-value">42人</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:15:10</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showVehicleDetail('dangerous-001')">
                                    <div class="data-header">
                                        <span class="data-name">桂C34567 (危货车辆)</span>
                                        <span class="data-status warning">超速</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">S201 K45</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">行驶速度:</span>
                                            <span class="field-value warning">95km/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">货物类型:</span>
                                            <span class="field-value">易燃液体</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:14:30</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showVehicleDetail('tour-001')">
                                    <div class="data-header">
                                        <span class="data-name">桂B23456 (旅游包车)</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">G80 K1200</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">行驶速度:</span>
                                            <span class="field-value">78km/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">载客人数:</span>
                                            <span class="field-value">35人</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:13:45</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showVehicleDetail('bus-002')">
                                    <div class="data-header">
                                        <span class="data-name">桂D78901 (长途客车)</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">G75 K890</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">行驶速度:</span>
                                            <span class="field-value">92km/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">载客人数:</span>
                                            <span class="field-value">38人</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:14:20</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showVehicleDetail('dangerous-002')">
                                    <div class="data-header">
                                        <span class="data-name">桂E45678 (危货车辆)</span>
                                        <span class="data-status warning">偏离路线</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">S201 K78</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">行驶速度:</span>
                                            <span class="field-value">65km/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">货物类型:</span>
                                            <span class="field-value">腐蚀性物质</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:11:30</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showVehicleDetail('tour-002')">
                                    <div class="data-header">
                                        <span class="data-name">桂F56789 (旅游包车)</span>
                                        <span class="data-status abnormal">违规停车</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">G72 K1650</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">停车时长:</span>
                                            <span class="field-value warning">45分钟</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">载客人数:</span>
                                            <span class="field-value">28人</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:10:15</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="data-item" onclick="showVehicleDetail('dangerous-003')">
                                    <div class="data-header">
                                        <span class="data-name">桂G67890 (危货车辆)</span>
                                        <span class="data-status normal">正常</span>
                                    </div>
                                    <div class="data-details">
                                        <div class="data-field">
                                            <span class="field-label">当前位置:</span>
                                            <span class="field-value">G80 K1450</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">行驶速度:</span>
                                            <span class="field-value">75km/h</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">货物类型:</span>
                                            <span class="field-value">压缩气体</span>
                                        </div>
                                        <div class="data-field">
                                            <span class="field-label">更新时间:</span>
                                            <span class="field-value">09:12:50</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </aside>
                </div>
            </div>
        </main>
    </div>

    <!-- 车辆详情模态框 -->
    <div id="vehicleDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="vehicleModalTitle">车辆详情</h2>
                <button class="modal-close" onclick="closeVehicleModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 车辆信息网格 -->
                <div class="vehicle-info-grid">
                    <div class="info-section">
                        <h4>车辆信息</h4>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">车牌号码:</span>
                                <span class="info-value" id="vehiclePlate">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">车辆类型:</span>
                                <span class="info-value" id="vehicleType">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">所属公司:</span>
                                <span class="info-value" id="vehicleCompany">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">驾驶员:</span>
                                <span class="info-value" id="driverName">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">联系电话:</span>
                                <span class="info-value" id="driverPhone">-</span>
                            </div>
                            <div class="info-item">
                                <!-- 空占位 -->
                            </div>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>运输信息</h4>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">货物类型:</span>
                                <span class="info-value" id="cargoType">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">危险等级:</span>
                                <span class="info-value" id="dangerLevel">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">起始地点:</span>
                                <span class="info-value" id="startLocation">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">目的地点:</span>
                                <span class="info-value" id="endLocation">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">预计到达:</span>
                                <span class="info-value" id="estimatedArrival">-</span>
                            </div>
                            <div class="info-item">
                                <!-- 空占位 -->
                            </div>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>当前状态</h4>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">行驶速度:</span>
                                <span class="info-value" id="currentSpeed">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">当前位置:</span>
                                <span class="info-value" id="currentLocation">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">驾驶员状态:</span>
                                <span class="info-value" id="driverStatus">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">车辆状态:</span>
                                <span class="info-value" id="vehicleStatus">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">最后更新:</span>
                                <span class="info-value" id="lastUpdate">-</span>
                            </div>
                            <div class="info-item">
                                <!-- 空占位 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时视频监控 -->
                <div class="video-monitoring">
                    <h4 style="color: #4a90e2; margin-bottom: 15px;">实时视频监控</h4>
                    <div class="video-grid">
                        <div class="video-container">
                            <div class="video-header">车前摄像头</div>
                            <div class="video-content">
                                <div class="video-status online">在线</div>
                                <div class="video-placeholder">
                                    <i class="fas fa-video" style="font-size: 24px; margin-bottom: 5px;"></i><br>
                                    实时视频画面
                                </div>
                            </div>
                        </div>

                        <div class="video-container">
                            <div class="video-header">驾驶员监控</div>
                            <div class="video-content">
                                <div class="video-status online">在线</div>
                                <div class="video-placeholder">
                                    <i class="fas fa-user" style="font-size: 24px; margin-bottom: 5px;"></i><br>
                                    驾驶员监控
                                </div>
                            </div>
                        </div>

                        <div class="video-container">
                            <div class="video-header">车内监控</div>
                            <div class="video-content">
                                <div class="video-status online">在线</div>
                                <div class="video-placeholder">
                                    <i class="fas fa-eye" style="font-size: 24px; margin-bottom: 5px;"></i><br>
                                    车内监控
                                </div>
                            </div>
                        </div>

                        <div class="video-container">
                            <div class="video-header">车后监控</div>
                            <div class="video-content">
                                <div class="video-status offline">离线</div>
                                <div class="video-placeholder">
                                    <i class="fas fa-video-slash" style="font-size: 24px; margin-bottom: 5px;"></i><br>
                                    设备离线
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 行驶轨迹地图 -->
                <div class="map-section">
                    <h4 style="color: #4a90e2; margin-bottom: 15px;">行驶轨迹地图</h4>
                    <div class="map-container">
                        <div style="text-align: center;">
                            <i class="fas fa-map-marked-alt" style="font-size: 48px; margin-bottom: 10px;"></i><br>
                            行驶轨迹地图<br>
                            <small>显示车辆实时位置和历史轨迹</small>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="modal-actions">
                    <button class="modal-btn secondary" onclick="viewHistoryTrack()">查看历史轨迹</button>
                    <button class="modal-btn secondary" onclick="contactDriver()">联系驾驶员</button>
                    <button class="modal-btn primary" onclick="sendReminder()">发送提醒</button>
                    <button class="modal-btn secondary" onclick="closeVehicleModal()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 设施详情模态框 -->
    <div id="facilityDetailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="facilityModalTitle">设施详情</h2>
                <button class="modal-close" onclick="closeFacilityModal()">&times;</button>
            </div>
            <div class="modal-body">
                <!-- 设施信息网格 -->
                <div class="facility-info-grid">
                    <div class="info-section">
                        <h4>基本信息</h4>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">设施编号:</span>
                                <span class="info-value" id="facilityId">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">设施类型:</span>
                                <span class="info-value" id="facilityType">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">所属路段:</span>
                                <span class="info-value" id="facilityRoad">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">安装时间:</span>
                                <span class="info-value" id="installDate">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">维护单位:</span>
                                <span class="info-value" id="maintainUnit">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">联系方式:</span>
                                <span class="info-value" id="contactInfo">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">设备状态:</span>
                                <span class="info-value" id="facilityStatus">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">最后检测:</span>
                                <span class="info-value" id="lastCheck">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="info-section">
                        <h4>当前监测数据</h4>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">主要参数1:</span>
                                <span class="info-value" id="param1">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">主要参数2:</span>
                                <span class="info-value" id="param2">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">主要参数3:</span>
                                <span class="info-value" id="param3">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">预警阈值:</span>
                                <span class="info-value" id="warningThreshold">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">数据更新频率:</span>
                                <span class="info-value" id="updateFreq">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">数据质量:</span>
                                <span class="info-value" id="dataQuality">-</span>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-item">
                                <span class="info-label">异常次数:</span>
                                <span class="info-value" id="abnormalCount">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">最后更新:</span>
                                <span class="info-value" id="facilityLastUpdate">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 实时监测数据图表 -->
                <div class="chart-section">
                    <h4 style="color: #4a90e2; margin-bottom: 15px;">实时监测数据图表</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="chart-container">
                            <div class="chart-title">参数变化趋势图</div>
                            <div class="chart-placeholder">
                                <div>
                                    <i class="fas fa-chart-line" style="font-size: 32px; margin-bottom: 10px;"></i><br>
                                    实时数据趋势图<br>
                                    <small>显示最近24小时数据变化</small>
                                </div>
                            </div>
                        </div>

                        <div class="chart-container">
                            <div class="chart-title">历史数据对比图</div>
                            <div class="chart-placeholder">
                                <div>
                                    <i class="fas fa-chart-bar" style="font-size: 32px; margin-bottom: 10px;"></i><br>
                                    历史数据对比<br>
                                    <small>与历史同期数据对比分析</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="modal-actions">
                    <button class="modal-btn secondary" onclick="viewHistoryData()">查看历史数据</button>
                    <button class="modal-btn secondary" onclick="exportReport()">导出报告</button>
                    <button class="modal-btn primary" onclick="setWarningThreshold()">设置预警阈值</button>
                    <button class="modal-btn secondary" onclick="viewMaintenanceRecord()">维护记录</button>
                    <button class="modal-btn secondary" onclick="closeFacilityModal()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化导航栏
        NavigationComponent.init('monitoring-warning');

        // 监测设施类型切换功能
        function showFacilityMonitoring() {
            // 切换标签按钮状态
            document.querySelectorAll('.resource-tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 切换内容显示
            document.querySelectorAll('.resource-tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById('facility-monitoring-content').classList.add('active');

            // 更新地图标点显示
            updateMapMarkers();
        }



        function showVehicleMonitoring() {
            // 切换标签按钮状态
            document.querySelectorAll('.resource-tab-button').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 切换内容显示
            document.querySelectorAll('.resource-tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById('vehicle-monitoring-content').classList.add('active');

            // 更新地图标点显示
            updateMapMarkers();
        }

        // 全选/全不选功能
        function toggleAllMonitorMarkers() {
            const allCheckbox = document.getElementById('monitor-type-all');
            const isChecked = allCheckbox.checked;

            // 更新所有子复选框状态
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.id !== 'monitor-type-all') {
                    checkbox.checked = isChecked;
                }
            });

            // 更新地图标点显示
            updateMapMarkers();
        }

        // 筛选标签切换功能
        function switchFilterTab(button, tabType) {
            // 切换按钮状态
            document.querySelectorAll('.filter-tab-button').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // 切换内容显示
            document.querySelectorAll('.filter-tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(`filter-by-${tabType}-content`).classList.add('active');
        }

        // 数据标签切换功能
        function switchDataTab(button, tabType) {
            // 切换按钮状态
            document.querySelectorAll('.data-tab-button').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');

            // 切换内容显示
            document.querySelectorAll('.data-tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(`${tabType}-content`).classList.add('active');
        }

        // 更新地图标点显示
        function updateMapMarkers() {
            // 获取当前选中的监测类型
            const activeTab = document.querySelector('.resource-tab-button.active').textContent;

            // 隐藏所有标点
            document.querySelectorAll('.map-marker').forEach(marker => {
                marker.style.display = 'none';
            });

            // 根据选中的标签显示对应标点
            if (activeTab === '设施监测') {
                // 检查各个设施类型的复选框状态
                if (document.getElementById('facility-bridge').checked) {
                    document.querySelectorAll('.facility-marker.bridge').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
                if (document.getElementById('facility-tunnel').checked) {
                    document.querySelectorAll('.facility-marker.tunnel').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
                if (document.getElementById('facility-slope').checked) {
                    document.querySelectorAll('.facility-marker.slope').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
                if (document.getElementById('facility-road').checked) {
                    document.querySelectorAll('.facility-marker.road').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
            } else if (activeTab === '两客一危') {
                // 检查车辆类型的复选框状态
                if (document.getElementById('vehicle-bus').checked) {
                    document.querySelectorAll('.vehicle-marker.bus').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
                if (document.getElementById('vehicle-tour').checked) {
                    document.querySelectorAll('.vehicle-marker.tour').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
                if (document.getElementById('vehicle-dangerous').checked) {
                    document.querySelectorAll('.vehicle-marker.dangerous').forEach(marker => {
                        marker.style.display = 'block';
                    });
                }
            }
        }

        // 设施详情数据
        const facilityData = {
            'bridge-001': {
                id: 'BRG-001',
                type: '桥梁监测',
                name: 'G72高速大桥监测点',
                road: 'G72高速 K1499+500',
                installDate: '2020-03-15',
                maintainUnit: '广西高速公路养护中心',
                contactInfo: '0771-1234567',
                status: '异常',
                lastCheck: '2024-05-20 08:30:00',
                param1: '位移: 3.2mm (超限)',
                param2: '应力: 15.8MPa',
                param3: '温度: 28.5°C',
                warningThreshold: '位移>3.0mm',
                updateFreq: '每5分钟',
                dataQuality: '良好',
                abnormalCount: '3次/本月',
                lastUpdate: '09:15:30'
            },
            'tunnel-001': {
                id: 'TUN-001',
                type: '隧道监测',
                name: '柳州隧道监测点',
                road: 'G80高速 K890+100',
                installDate: '2019-08-20',
                maintainUnit: '柳州高速维护公司',
                contactInfo: '0772-2345678',
                status: '正常',
                lastCheck: '2024-05-20 07:45:00',
                param1: '渗水量: 2.1L/h',
                param2: '温度: 18.2°C',
                param3: '湿度: 65%',
                warningThreshold: '渗水量>5.0L/h',
                updateFreq: '每10分钟',
                dataQuality: '优秀',
                abnormalCount: '0次/本月',
                lastUpdate: '09:13:20'
            },
            'road-002': {
                id: 'RD-002',
                type: '路面监测',
                name: '梧州路面监测点',
                road: 'G80高速 K1200+300',
                installDate: '2021-06-10',
                maintainUnit: '梧州路桥养护队',
                contactInfo: '0774-3456789',
                status: '维护中',
                lastCheck: '2024-05-19 16:20:00',
                param1: '沉降量: 1.2mm',
                param2: '裂缝宽度: 0.5mm',
                param3: '平整度: 良好',
                warningThreshold: '沉降量>2.0mm',
                updateFreq: '每15分钟',
                dataQuality: '良好',
                abnormalCount: '1次/本月',
                lastUpdate: '08:45:15'
            },
            'bridge-002': {
                id: 'BRG-002',
                type: '桥梁监测',
                name: '北海大桥监测点',
                road: 'G75高速 K650+800',
                installDate: '2020-11-25',
                maintainUnit: '北海交通设施管理处',
                contactInfo: '0779-4567890',
                status: '正常',
                lastCheck: '2024-05-20 06:15:00',
                param1: '位移: 1.8mm',
                param2: '应力: 12.3MPa',
                param3: '振动频率: 2.1Hz',
                warningThreshold: '位移>3.0mm',
                updateFreq: '每5分钟',
                dataQuality: '优秀',
                abnormalCount: '0次/本月',
                lastUpdate: '09:12:45'
            },
            'video-003': {
                id: 'VID-003',
                type: '视频监控',
                name: '钦州视频监控点',
                road: 'S312省道 K120+200',
                installDate: '2022-01-08',
                maintainUnit: '钦州交通监控中心',
                contactInfo: '0777-5678901',
                status: '离线',
                lastCheck: '2024-05-19 18:30:00',
                param1: '设备状态: 离线',
                param2: '离线时长: 2小时',
                param3: '信号强度: 0%',
                warningThreshold: '离线>30分钟',
                updateFreq: '实时',
                dataQuality: '无数据',
                abnormalCount: '5次/本月',
                lastUpdate: '07:30:20'
            }
        };

        // 显示设施详情
        function showFacilityDetail(facilityId) {
            const facility = facilityData[facilityId];
            if (!facility) {
                alert('设施信息不存在');
                return;
            }

            // 更新模态框标题
            document.getElementById('facilityModalTitle').textContent = `设施详情 - ${facility.name}`;

            // 更新基本信息
            document.getElementById('facilityId').textContent = facility.id;
            document.getElementById('facilityType').textContent = facility.type;
            document.getElementById('facilityRoad').textContent = facility.road;
            document.getElementById('installDate').textContent = facility.installDate;
            document.getElementById('maintainUnit').textContent = facility.maintainUnit;
            document.getElementById('contactInfo').textContent = facility.contactInfo;
            document.getElementById('facilityStatus').textContent = facility.status;
            document.getElementById('lastCheck').textContent = facility.lastCheck;

            // 更新监测数据
            document.getElementById('param1').textContent = facility.param1;
            document.getElementById('param2').textContent = facility.param2;
            document.getElementById('param3').textContent = facility.param3;
            document.getElementById('warningThreshold').textContent = facility.warningThreshold;
            document.getElementById('updateFreq').textContent = facility.updateFreq;
            document.getElementById('dataQuality').textContent = facility.dataQuality;
            document.getElementById('abnormalCount').textContent = facility.abnormalCount;
            document.getElementById('facilityLastUpdate').textContent = facility.lastUpdate;

            // 显示模态框
            document.getElementById('facilityDetailModal').style.display = 'block';
        }

        // 关闭设施详情模态框
        function closeFacilityModal() {
            document.getElementById('facilityDetailModal').style.display = 'none';
        }

        // 查看历史数据
        function viewHistoryData() {
            alert('查看历史数据功能');
        }

        // 导出报告
        function exportReport() {
            alert('导出报告功能');
        }

        // 设置预警阈值
        function setWarningThreshold() {
            alert('设置预警阈值功能');
        }

        // 查看维护记录
        function viewMaintenanceRecord() {
            alert('查看维护记录功能');
        }

        // 更新模态框点击外部关闭功能
        window.onclick = function(event) {
            const vehicleModal = document.getElementById('vehicleDetailModal');
            const facilityModal = document.getElementById('facilityDetailModal');

            if (event.target === vehicleModal) {
                closeVehicleModal();
            }
            if (event.target === facilityModal) {
                closeFacilityModal();
            }
        }



        // 车辆详情数据
        const vehicleData = {
            'bus-001': {
                plate: '桂A12345',
                type: '危货车辆',
                company: '广西运输有限公司',
                driver: '张师傅',
                phone: '138****5678',
                cargoType: '易燃液体',
                dangerLevel: '3类危险品',
                startLocation: '南宁市',
                endLocation: '柳州市',
                estimatedArrival: '2024-05-20 14:30',
                currentSpeed: '75km/h',
                currentLocation: 'G72高速 K1350+200',
                driverStatus: '正常',
                vehicleStatus: '正常行驶',
                lastUpdate: '09:15:30'
            },
            'tour-001': {
                plate: '桂B23456',
                type: '旅游包车',
                company: '桂林旅游客运公司',
                driver: '李师傅',
                phone: '139****6789',
                cargoType: '乘客',
                dangerLevel: '无',
                startLocation: '桂林市',
                endLocation: '阳朔县',
                estimatedArrival: '2024-05-20 12:00',
                currentSpeed: '78km/h',
                currentLocation: 'G80高速 K1200+500',
                driverStatus: '正常',
                vehicleStatus: '正常行驶',
                lastUpdate: '09:13:45'
            },
            'bus-002': {
                plate: '桂D78901',
                type: '长途客车',
                company: '梧州客运总站',
                driver: '王师傅',
                phone: '137****7890',
                cargoType: '乘客',
                dangerLevel: '无',
                startLocation: '梧州市',
                endLocation: '南宁市',
                estimatedArrival: '2024-05-20 15:45',
                currentSpeed: '92km/h',
                currentLocation: 'G75高速 K890+100',
                driverStatus: '正常',
                vehicleStatus: '正常行驶',
                lastUpdate: '09:14:20'
            },
            'dangerous-002': {
                plate: '桂E45678',
                type: '危货车辆',
                company: '钦州危化品运输公司',
                driver: '陈师傅',
                phone: '135****8901',
                cargoType: '腐蚀性物质',
                dangerLevel: '8类危险品',
                startLocation: '钦州市',
                endLocation: '北海市',
                estimatedArrival: '2024-05-20 16:20',
                currentSpeed: '65km/h',
                currentLocation: 'S201省道 K78+300',
                driverStatus: '正常',
                vehicleStatus: '偏离路线',
                lastUpdate: '09:11:30'
            },
            'tour-002': {
                plate: '桂F56789',
                type: '旅游包车',
                company: '玉林旅游运输公司',
                driver: '刘师傅',
                phone: '136****9012',
                cargoType: '乘客',
                dangerLevel: '无',
                startLocation: '玉林市',
                endLocation: '北海市',
                estimatedArrival: '2024-05-20 13:30',
                currentSpeed: '0km/h',
                currentLocation: 'G72高速 K1650+800',
                driverStatus: '休息中',
                vehicleStatus: '违规停车',
                lastUpdate: '09:10:15'
            },
            'dangerous-003': {
                plate: '桂G67890',
                type: '危货车辆',
                company: '河池运输集团',
                driver: '赵师傅',
                phone: '134****0123',
                cargoType: '压缩气体',
                dangerLevel: '2类危险品',
                startLocation: '河池市',
                endLocation: '南宁市',
                estimatedArrival: '2024-05-20 17:00',
                currentSpeed: '75km/h',
                currentLocation: 'G80高速 K1450+600',
                driverStatus: '正常',
                vehicleStatus: '正常行驶',
                lastUpdate: '09:12:50'
            }
        };

        // 显示车辆详情
        function showVehicleDetail(vehicleId) {
            const vehicle = vehicleData[vehicleId];
            if (!vehicle) {
                alert('车辆信息不存在');
                return;
            }

            // 更新模态框标题
            document.getElementById('vehicleModalTitle').textContent = `车辆详情 - ${vehicle.plate} (${vehicle.type})`;

            // 更新车辆信息
            document.getElementById('vehiclePlate').textContent = vehicle.plate;
            document.getElementById('vehicleType').textContent = vehicle.type;
            document.getElementById('vehicleCompany').textContent = vehicle.company;
            document.getElementById('driverName').textContent = vehicle.driver;
            document.getElementById('driverPhone').textContent = vehicle.phone;

            // 更新运输信息
            document.getElementById('cargoType').textContent = vehicle.cargoType;
            document.getElementById('dangerLevel').textContent = vehicle.dangerLevel;
            document.getElementById('startLocation').textContent = vehicle.startLocation;
            document.getElementById('endLocation').textContent = vehicle.endLocation;
            document.getElementById('estimatedArrival').textContent = vehicle.estimatedArrival;

            // 更新当前状态
            document.getElementById('currentSpeed').textContent = vehicle.currentSpeed;
            document.getElementById('currentLocation').textContent = vehicle.currentLocation;
            document.getElementById('driverStatus').textContent = vehicle.driverStatus;
            document.getElementById('vehicleStatus').textContent = vehicle.vehicleStatus;
            document.getElementById('lastUpdate').textContent = vehicle.lastUpdate;

            // 显示模态框
            document.getElementById('vehicleDetailModal').style.display = 'block';
        }

        // 关闭车辆详情模态框
        function closeVehicleModal() {
            document.getElementById('vehicleDetailModal').style.display = 'none';
        }

        // 查看历史轨迹
        function viewHistoryTrack() {
            alert('查看历史轨迹功能');
        }

        // 联系驾驶员
        function contactDriver() {
            alert('联系驾驶员功能');
        }

        // 发送提醒
        function sendReminder() {
            alert('发送提醒功能');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('vehicleDetailModal');
            if (event.target === modal) {
                closeVehicleModal();
            }
        }

        // 显示预警详情
        function showWarningDetails() {
            // 这里可以打开模态框显示预警详细信息
            alert('显示预警详情');
        }

        // 打开预警发布模态框
        function openWarningPublishModal() {
            // 这里可以打开预警发布模态框
            alert('打开预警发布模态框');
        }

        // 打开预警管理模态框
        function openWarningManageModal() {
            // 这里可以打开预警管理模态框
            alert('打开预警管理模态框');
        }

        // 打开预警历史模态框
        function openWarningHistoryModal() {
            // 这里可以打开预警历史模态框
            alert('打开预警历史模态框');
        }

        // 树形结构展开/折叠功能
        document.addEventListener('DOMContentLoaded', function() {
            // 为树形结构添加点击事件
            document.querySelectorAll('.tree-toggler').forEach(toggler => {
                toggler.addEventListener('click', function() {
                    const parentLi = this.parentElement;
                    const childUl = parentLi.querySelector('ul');

                    if (childUl) {
                        if (childUl.style.display === 'none' || !childUl.style.display) {
                            childUl.style.display = 'block';
                            this.textContent = '-';
                        } else {
                            childUl.style.display = 'none';
                            this.textContent = '+';
                        }
                    }
                });
            });

            // 初始化时隐藏所有子级
            document.querySelectorAll('.collapsible-tree ul').forEach(ul => {
                ul.style.display = 'none';
            });

            // 为筛选条件添加变化监听
            document.querySelectorAll('input[type="checkbox"], select').forEach(element => {
                element.addEventListener('change', updateMapMarkers);
            });

            // 为选项卡片添加点击事件（点击卡片也能切换复选框状态）
            document.querySelectorAll('.facility-type-item, .vehicle-type-item').forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果点击的不是复选框本身，则切换复选框状态
                    if (e.target.type !== 'checkbox') {
                        const checkbox = this.querySelector('input[type="checkbox"]');
                        if (checkbox) {
                            checkbox.checked = !checkbox.checked;
                            updateMapMarkers();
                        }
                    }
                });
            });

            // 初始化地图标点显示
            updateMapMarkers();
        });
    </script>
</body>
</html>