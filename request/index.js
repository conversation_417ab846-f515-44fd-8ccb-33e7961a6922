// 创建 axios 实例
const createApiInstance = (baseURL, timeout = 10000) => {
  const instance = axios.create({
    baseURL,
    timeout,
    headers: {
      'Content-Type': 'application/json',
    }
  });

  // 请求拦截器（可统一添加 token）
  instance.interceptors.request.use(
    config => {
      const token = window.localStorage.getItem('token')
      if (token) config.headers.Authorization = `Bearer ${token}`;
      return config;
    },
    error => Promise.reject(error)
  );

  // 响应拦截器（统一处理错误）
  instance.interceptors.response.use(
    res => {
      if(res.data.code == 200) {
        return Promise.resolve(res.data)
      } else {
        return Promise.reject(res.data);
      }
    },
    error => {
      console.error('请求出错:', error);
      alert(`请求出错: ${ error.message }`);
      return Promise.reject(error.response?.data || error.message);
    }
  );

  return instance;
};

// 默认实例（根据你的后端地址修改）
// const API_BASE_URL = 'http://***********:10003/prod-api';
const API_BASE_URL = 'http://localhost:8380';
const api = createApiInstance(API_BASE_URL);

// 封装成全局对象，方便多个 HTML 使用
window.Http = {
  get: (url, params) => api.get(url, { params }),
  post: (url, data) => api.post(url, data),
  put: (url, data) => api.put(url, data),
  delete: (url) => api.delete(url),
  upload: (url, file) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.post(url, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
  }
};