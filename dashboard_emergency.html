<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统仪表盘 - 应急管理系统</title>
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
            margin: 0;
        }
        .sidebar-menu-item {
            padding: 0.75rem 1.25rem;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #4a5568;
        }
        .sidebar-menu-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
        .nav-link {
            position: relative;
        }
        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            transform: translate(25%, -25%);
        }
        /* 简单的模态框样式 */
        .modal {
            background-color: rgba(0, 0, 0, 0.5);
        }
        
        /* 自定义切换按钮样式 */
        .toggle-checkbox {
            right: 0;
            z-index: 5;
            opacity: 0;
            width: 38px;
            height: 22px;
        }
        .toggle-label {
            width: 38px;
            height: 22px;
            background: #ddd;
            border-radius: 100px;
            position: relative;
            transition: all 0.3s ease;
        }
        .toggle-label:after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 18px;
            height: 18px;
            background: #fff;
            border-radius: 100px;
            transition: all 0.3s ease;
        }
        .toggle-checkbox:checked + .toggle-label {
            background: #3b82f6;
        }
        .toggle-checkbox:checked + .toggle-label:after {
            left: calc(100% - 2px);
            transform: translateX(-100%);
        }
        
        /* 添加滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }
        
        /* 卡片悬停效果 */
        .card-hover {
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .card-hover:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>
            
    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container" style="display: flex;"> 
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder">
            <!-- Sidebar will be loaded here by JS -->
            </div>
            
        <!-- Main Content Area -->
        <!-- Replaced flex-grow with flex-1 for potentially better shrinking behavior -->
        <main id="main-content" class="flex-1 pb-8 px-6 bg-gray-100 min-h-screen overflow-hidden min-w-0">
        <div class="py-6">
            <!-- 页面标题 -->
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-800">系统仪表盘</h2>
                <p class="text-gray-600 mt-1">欢迎使用应急管理系统，请查看地图信息和待办事项</p>
            </div>
            
            <!-- 地图和筛选区域 -->
            <div id="map-container" class="relative w-full h-[600px] mb-8 rounded-lg shadow-md overflow-hidden">
                <!-- 通过JavaScript动态设置地图 -->
                <div id="map-loading" class="absolute inset-0 bg-gray-200 flex items-center justify-center">
                    <div class="text-center">
                        <svg class="animate-spin h-10 w-10 mx-auto text-blue-500 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="text-gray-600">地图加载中...</p>
                    </div>
                </div>
            
                <!-- 新增：图例放置在地图左下角 -->
                <div class="absolute bottom-16 left-4 bg-white bg-opacity-90 p-3 rounded-md shadow-lg z-10">
                    <h6 class="font-medium text-gray-700 mb-2 text-sm">图例</h6>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="flex items-center text-xs text-gray-600">
                            <span class="inline-block w-3 h-3 bg-red-500 rounded-full mr-1"></span>
                            <span>隐患点</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <span class="inline-block w-3 h-3 bg-blue-500 rounded-full mr-1"></span>
                            <span>仓库</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <span class="inline-block w-3 h-3 bg-green-500 rounded-full mr-1"></span>
                            <span>车辆</span>
                        </div>
                        <div class="flex items-center text-xs text-gray-600">
                            <span class="inline-block w-3 h-3 bg-yellow-500 rounded-full mr-1"></span>
                            <span>拖车点</span>
                        </div>
                    </div>
                </div>
            
                <!-- 地图控件占位 (原左下角信息) -->
                <div class="absolute bottom-4 left-4 bg-white bg-opacity-90 p-2 rounded-md shadow-sm z-10 text-xs text-gray-700">
                    <span>广西地区 | 模拟数据</span>
                </div>

                <!-- 筛选面板 -->
                <div id="map-filter-panel" class="absolute top-4 right-4 bg-white p-4 rounded-lg shadow-xl z-10 w-72 max-h-[calc(100%-2rem)] overflow-y-auto border border-gray-100">
                    <div class="flex justify-between items-center mb-3 border-b pb-2">
                        <h5 class="text-lg font-semibold text-gray-800">图层筛选</h5>
                        <button id="collapse-filter-btn" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                        </button>
                    </div>

                    <div id="filter-content">
                        <!-- 新增：城市筛选模块 -->
                        <div class="mb-4">
                            <h6 class="font-medium text-gray-700 mb-2 text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                                地市筛选
                            </h6>
                            <div class="bg-gray-50 p-3 rounded">
                                <label for="city-filter-select" class="text-sm text-gray-700 block mb-2 font-medium">选择城市:</label>
                                <select id="city-filter-select" class="block w-full text-sm border border-gray-300 rounded-md shadow-sm py-1.5 px-3 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <option value="" selected>全部城市</option>
                                    <option value="nanning">南宁市</option>
                                    <option value="liuzhou">柳州市</option>
                                    <option value="guilin">桂林市</option>
                                    <option value="wuzhou">梧州市</option>
                                    <option value="beihai">北海市</option>
                                    <option value="fangchenggang">防城港市</option>
                                    <option value="qinzhou">钦州市</option>
                                    <option value="guigang">贵港市</option>
                                    <option value="yulin">玉林市</option>
                                    <option value="baise">百色市</option>
                                    <option value="hezhou">贺州市</option>
                                    <option value="hechi">河池市</option>
                                    <option value="laibin">来宾市</option>
                                    <option value="chongzuo">崇左市</option>
                                </select>
                            </div>
                        </div>

                        <!-- 风险隐患 -->
                        <!-- <div class="mb-4">
                            <h6 class="font-medium text-gray-700 mb-2 text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                风险隐患
                            </h6>
                            <label class="flex items-center space-x-2 cursor-pointer bg-gray-50 p-2 rounded hover:bg-gray-100 transition-colors">
                                <div class="relative">
                                    <input type="checkbox" class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer transition-all duration-150 checked:bg-blue-500 checked:border-blue-500" data-layer="hazard_points">
                                    <div class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></div>
                                </div>
                                <span class="text-sm text-gray-700 font-medium">隐患点</span>
                                <span class="text-xs text-gray-500 ml-auto">24处</span>
                            </label>
                        </div> -->

                        <!-- 应急资源 -->
                        <div class="mb-4">
                            <h6 class="font-medium text-gray-700 mb-2 text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                </svg>
                                应急资源
                            </h6>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-2 cursor-pointer bg-gray-50 p-2 rounded hover:bg-gray-100 transition-colors">
                                    <div class="relative">
                                        <input type="checkbox" class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer transition-all duration-150 checked:bg-blue-500 checked:border-blue-500" data-layer="emergency_warehouses">
                                        <div class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></div>
                                    </div>
                                    <span class="text-sm text-gray-700 font-medium">应急仓库</span>
                                    <span class="text-xs text-gray-500 ml-auto">8处</span>
                                </label>
                                <label class="flex items-center space-x-2 cursor-pointer bg-gray-50 p-2 rounded hover:bg-gray-100 transition-colors">
                                    <div class="relative">
                                        <input type="checkbox" class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer transition-all duration-150 checked:bg-blue-500 checked:border-blue-500" data-layer="emergency_vehicles">
                                        <div class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></div>
                                    </div>
                                    <span class="text-sm text-gray-700 font-medium">应急车辆</span>
                                    <span class="text-xs text-gray-500 ml-auto">16辆</span>
                                </label>
                                <label class="flex items-center space-x-2 cursor-pointer bg-gray-50 p-2 rounded hover:bg-gray-100 transition-colors">
                                    <div class="relative">
                                        <input type="checkbox" class="toggle-checkbox absolute block w-5 h-5 rounded-full bg-white border-4 appearance-none cursor-pointer transition-all duration-150 checked:bg-blue-500 checked:border-blue-500" data-layer="towing_points">
                                        <div class="toggle-label block overflow-hidden h-5 rounded-full bg-gray-300 cursor-pointer"></div>
                                    </div>
                                    <span class="text-sm text-gray-700 font-medium">拖车点</span>
                                    <span class="text-xs text-gray-500 ml-auto">12处</span>
                                </label>
                            </div>
                            <div class="mt-3 bg-gray-50 p-3 rounded">
                                <label for="emergency-supplies-select" class="text-sm text-gray-700 block mb-2 font-medium">应急物资:</label>
                                <select id="emergency-supplies-select" class="block w-full text-sm border border-gray-300 rounded-md shadow-sm py-1.5 px-3 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <option value="" selected>全部物资类型</option>
                                    <!-- 模拟物资选项，带有计数 -->
                                    <option value="water_pump">水泵 (12)</option>
                                    <option value="generator">发电机 (8)</option>
                                    <option value="life_jacket">救生衣 (45)</option>
                                    <option value="sandbags">沙袋 (200)</option>
                                    <option value="first_aid_kit">急救箱 (18)</option>
                                </select>
                            </div>
                        </div>

                        <!-- 应急事件快速检索按钮 -->
                        <button id="quick-search-btn" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded transition duration-150 ease-in-out flex items-center justify-center shadow-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            预案快速检索
                        </button>
                                <!-- 新增：物资快速检索按钮 -->
                                <button id="quick-material-search-btn" class="mt-3 w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded transition duration-150 ease-in-out flex items-center justify-center shadow-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10l-8 4" />
                                    </svg>
                                    物资快速检索
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 待办事项和预警信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 待办事项 -->
                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4 flex justify-between items-center">
                        <span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>待办事项</span>
                        <a href="#" class="text-sm text-blue-600 hover:underline hover:text-blue-800 transition-colors">查看全部</a>
                    </h4>
                    <ul class="space-y-3">
                        <li class="flex items-center justify-between pb-2 border-b border-gray-200 hover:bg-gray-50 p-2 rounded transition-colors">
                            <div>
                                <p class="text-gray-800 font-medium">审批: 2024年度防汛应急预案</p>
                                <p class="text-xs text-gray-500">提交人: 王五 | 时间: 2024-07-20 09:30</p>
                    </div>
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded hover:bg-blue-50 transition-colors focus:outline-none">去处理</button>
                        </li>
                        <li class="flex items-center justify-between pb-2 border-b border-gray-200 hover:bg-gray-50 p-2 rounded transition-colors">
                            <div>
                                <p class="text-gray-800 font-medium">任务: 更新应急联系人信息</p>
                                <p class="text-xs text-gray-500">截止日期: 2024-07-25</p>
                                </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded hover:bg-blue-50 transition-colors focus:outline-none">去处理</button>
                        </li>
                        <li class="flex items-center justify-between hover:bg-gray-50 p-2 rounded transition-colors">
                            <div>
                                <p class="text-gray-800 font-medium">审批: 物资采购申请单 #20240719001</p>
                                <p class="text-xs text-gray-500">提交人: 李四 | 时间: 2024-07-19 15:00</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-medium px-3 py-1 rounded hover:bg-blue-50 transition-colors focus:outline-none">去处理</button>
                        </li>
                    </ul>
                                    </div>
                
                <!-- 预警信息 -->
                <div class="bg-white rounded-lg shadow-md p-6 card-hover">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4 flex justify-between items-center">
                        <span class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>预警信息</span>
                        <a href="#" class="text-sm text-blue-600 hover:underline hover:text-blue-800 transition-colors">查看全部</a>
                    </h4>
                    <ul class="space-y-3" id="alert-list">
                        <!-- 隐患整改到期预警 示例 -->
                        <li class="flex items-start pb-2 border-b border-gray-200 hover:bg-gray-50 p-2 rounded transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-yellow-500 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <p class="text-yellow-700 font-medium">隐患整改到期预警: 青山路立交桥 (HD-2024-023) 整改即将到期</p>
                                <p class="text-xs text-gray-500">到期日期: 2024-08-05 | 负责人: 李明</p>
                                <div class="mt-1 flex">
                                    <button class="text-xs text-blue-600 hover:text-blue-800 mr-3 transition-colors">查看详情</button>
                                    <button class="text-xs text-green-600 hover:text-green-800 transition-colors">标记已处理</button>
                                </div>
                            </div>
                        </li>
                         <!-- 更多到期预警会动态加载到这里 -->
                        <li class="flex items-start hover:bg-gray-50 p-2 rounded transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-500 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <div>
                                <p class="text-red-700 font-medium">隐患整改超期: 南宁收费站排水沟 (HD-2024-016) 整改已超期</p>
                                <p class="text-xs text-gray-500">到期日期: 2024-07-24 | 负责人: 张伟</p>
                                <div class="mt-1 flex">
                                    <button class="text-xs text-blue-600 hover:text-blue-800 mr-3 transition-colors">查看详情</button>
                                    <button class="text-xs text-green-600 hover:text-green-800 transition-colors">标记已处理</button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 应急事件快速检索模态框 (初始隐藏) -->
        <div id="search-modal" class="fixed inset-0 z-50 hidden items-center justify-center p-4 modal overflow-auto">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col transform transition-all duration-300 scale-95 opacity-0">
                <!-- Modal Header -->
                <div class="flex justify-between items-center p-4 border-b">
                    <h5 class="text-lg font-semibold text-gray-800">预案快速检索</h5>
                    <button id="close-search-modal-btn" class="text-gray-500 hover:text-gray-700 focus:outline-none transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <!-- Modal Body -->
                <div class="p-6 space-y-4 overflow-y-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="search-unit" class="block text-sm font-medium text-gray-700">所属单位</label>
                            <input type="text" id="search-unit" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                        <div>
                            <label for="search-road" class="block text-sm font-medium text-gray-700">路段编号</label>
                            <input type="text" id="search-road" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                        <div>
                            <label for="search-start-stake" class="block text-sm font-medium text-gray-700">起始桩号</label>
                            <input type="text" id="search-start-stake" placeholder="例如 K100+200" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                        <div>
                            <label for="search-event-type" class="block text-sm font-medium text-gray-700">事件类型</label>
                            <select id="search-event-type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                                <option value="">请选择</option>
                                <!-- 参考预案库类型 -->
                                <option value="traffic_accident">交通事故</option>
                                <option value="fire">火灾事故</option>
                                <option value="natural_disaster">自然灾害</option>
                                <option value="hazardous_material">危化品泄漏</option>
                                <option value="public_health">公共卫生</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                    <button id="perform-search-btn" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        检索
                    </button>

                    <!-- 检索结果区域 -->
                    <div id="search-results" class="mt-6 border-t pt-4 hidden">
                        <div class="flex justify-between items-center mb-3">
                            <h6 class="text-md font-semibold text-gray-800">检索结果</h6>
                            <span class="text-xs text-gray-500">找到 1 条结果</span>
                        </div>
                        <div class="space-y-3" id="search-results-list">
                            <!-- 模拟结果项 -->
                            <div class="border p-4 rounded-md bg-gray-50 hover:bg-white hover:shadow-md transition-all">
                                <h4 class="font-medium text-gray-900">广西南宁高速公路交通事故应急预案</h4>
                                <p class="text-sm text-gray-500 mt-1">适用范围: K100-K150 南宁城区段</p>
                                <div class="mt-3 flex items-start space-x-4">
                                    <div class="w-2/5">
                                        <label class="block text-sm font-medium text-gray-700 mb-1">选择事件分级:</label>
                                        <select class="event-level-select text-sm w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-plan-id="plan1">
                                            <option value="">请选择</option>
                                            <option value="level4">IV级 (一般)</option>
                                            <option value="level3">III级 (较大)</option>
                                            <option value="level2">II级 (重大)</option>
                                            <option value="level1">I级 (特别重大)</option>
                                        </select>
                                    </div>
                                    <div class="response-condition text-xs text-gray-600 mt-1 p-3 bg-blue-50 rounded border border-blue-200 w-3/5">
                                        <strong>响应条件:</strong><br>
                                        IV级 (一般): 一般事故，造成3人以下死亡<br>
                                        III级 (较大): 较大事故，造成3-10人死亡<br>
                                        II级 (重大): 重大事故，造成10-30人死亡<br>
                                        I级 (特别重大): 特别重大事故，造成30人以上死亡
                                    </div>
                                </div>
                                <div class="flex mt-3 space-x-2">
                                    <button class="view-plan-details-btn text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-3 py-1.5 rounded transition-colors" data-plan-id="plan1">
                                        查看预案详情 <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                        </svg>
                                    </button>
                                    <button class="show-plan-actions-btn text-sm text-green-600 hover:text-green-800 hover:bg-green-50 px-3 py-1.5 rounded transition-colors" data-plan-id="plan1">
                                        显示应急处置信息 <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                </div>
                                <!-- 应急处置信息区域 (初始隐藏) -->
                                <div class="plan-actions-details mt-4 border-t pt-3 hidden bg-white p-4 rounded">
                                     <h5 class="text-sm font-semibold mb-3 text-gray-700 border-b pb-2">应急指挥机构</h5>
                                     
                                     <div class="space-y-4 text-sm"> <!-- Increased base font size, adjusted spacing -->
                                         <!-- 领导小组 -->
                                         <div>
                                             <p class="font-semibold text-gray-800">领导小组</p>
                                             <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                 <p class="text-gray-700">广西交通运输厅</p>
                                                 <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                     <p class="text-gray-600">厅办公室 - 主任: 张三 (13800138000)</p>
                                                     <p class="text-gray-600">厅办公室 - 副主任: 李四 (13900139000)</p>
                                                 </div>
                                             </div>
                                         </div>

                                         <!-- 领导小组办公室 -->
                                         <div>
                                             <p class="font-semibold text-gray-800">领导小组办公室</p>
                                             <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                 <p class="text-gray-700">广西交通运输厅</p>
                                                 <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                     <p class="text-gray-600">安监处 - 处长: 王五 (13700137000)</p>
                                                     <p class="text-gray-600">安监处 - 科员: 赵六 (13600136000)</p>
                                                 </div>
                                             </div>
                                         </div>

                                         <!-- 应急工作组 - 抢险组 -->
                                         <div>
                                             <p class="font-semibold text-gray-800">应急工作组 - 抢险组</p>
                                             <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                 <p class="text-gray-700">自治区公路发展中心</p>
                                                 <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                     <p class="text-gray-600">技术科 - 科长: 孙七 (13500135000)</p>
                                                     <p class="text-gray-600">巡查科 - 成员: 周八 (13400134000)</p>
                                                 </div>
                                             </div>
                                         </div>
                                         
                                         <!-- 应急工作组 - 道路运输保障组 (Example, add if needed following the pattern) -->
                                         <!--
                                         <div>
                                             <p class="font-semibold text-gray-800">应急工作组 - 道路运输保障组</p>
                                             <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                 <p class="text-gray-700">自治区道路运输发展中心</p>
                                                 <div class="mt-1 ml-2 pl-3 border-l-2 border-gray-300 space-y-1">
                                                     <p class="text-gray-600">客运科 - 组长: 吴保障 (13400134000)</p>
                                                     <p class="text-gray-600">调度中心 - 成员: 郑调度 (13400134001)</p>
                                                 </div>
                                             </div>
                                         </div>
                                         -->
                                     </div>
                                     
                                     <!-- 原应急物资和处置流程保留或根据需要调整 -->
                                     <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-4 pt-3 border-t border-gray-200">
                                         <!-- <div class="mb-2">
                                             <strong class="text-xs block text-gray-500 uppercase tracking-wider">应急物资</strong>
                                             <div class="mt-1">
                                                 <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">救生衣 (50件)</span>
                                                 <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">发电机 (2台)</span>
                                                 <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">拖车 (1辆)</span>
                                             </div>
                                         </div> -->
                                         <div class="mb-2">
                                             <strong class="text-xs block text-gray-500 uppercase tracking-wider">处置流程</strong>
                                             <ol class="list-decimal list-inside text-sm text-gray-800 space-y-1 mt-1 pl-1">
                                                 <li class="py-1 hover:bg-gray-50 rounded pl-2">设置警戒区</li>
                                                 <li class="py-1 hover:bg-gray-50 rounded pl-2">疏散人员</li>
                                                 <li class="py-1 hover:bg-gray-50 rounded pl-2">扑灭火源 / 清理现场</li>
                                                 <li class="py-1 hover:bg-gray-50 rounded pl-2">恢复交通</li>
                                             </ol>
                                         </div>
                                     </div>
                                 </div>
                            </div>
                            <!-- 可以添加更多模拟结果 -->
                        </div>
                    </div>
                </div>
                <!-- Modal Footer -->
                <div class="flex justify-end items-center p-4 border-t bg-gray-50 rounded-b-lg">
                    <button id="cancel-search-btn" class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">取消</button>
                </div>
            </div>
        </div>

        <!-- 新增：物资快速检索模态框 (初始隐藏) -->
        <div id="material-search-modal" class="fixed inset-0 z-50 hidden items-center justify-center p-4 modal overflow-auto">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col transform transition-all duration-300 scale-95 opacity-0">
                <!-- Modal Header -->
                <div class="flex justify-between items-center p-4 border-b">
                    <h5 class="text-lg font-semibold text-gray-800">物资快速检索</h5>
                    <button id="close-material-search-modal-btn" class="text-gray-500 hover:text-gray-700 focus:outline-none transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <!-- Modal Body -->
                <div class="p-6 space-y-4 overflow-y-auto">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="search-material-name" class="block text-sm font-medium text-gray-700">物资名称</label>
                            <input type="text" id="search-material-name" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                        <div>
                            <label for="search-material-type" class="block text-sm font-medium text-gray-700">物资类型</label>
                            <select id="search-material-type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                                <option value="">请选择</option>
                                <option value="water_pump">水泵</option>
                                <option value="generator">发电机</option>
                                <option value="life_jacket">救生衣</option>
                                <option value="sandbags">沙袋</option>
                                <option value="first_aid_kit">急救箱</option>
                                <option value="tent">帐篷</option>
                                <option value="food_water">食品和水</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="search-material-city-modal" class="block text-sm font-medium text-gray-700">广西地市</label>
                            <select id="search-material-city-modal" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm py-2 px-3">
                                <option value="">请选择地市</option>
                                <option value="nanning">南宁市</option>
                                <option value="liuzhou">柳州市</option>
                                <option value="guilin">桂林市</option>
                                <option value="wuzhou">梧州市</option>
                                <option value="beihai">北海市</option>
                                <option value="fangchenggang">防城港市</option>
                                <option value="qinzhou">钦州市</option>
                                <option value="guigang">贵港市</option>
                                <option value="yulin">玉林市</option>
                                <option value="baise">百色市</option>
                                <option value="hezhou">贺州市</option>
                                <option value="hechi">河池市</option>
                                <option value="laibin">来宾市</option>
                                <option value="chongzuo">崇左市</option>
                            </select>
                        </div>
                        <div>
                            <label for="search-material-warehouse" class="block text-sm font-medium text-gray-700">所属仓库</label>
                            <input type="text" id="search-material-warehouse" placeholder="例如: 中心仓库A栋" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                        <div>
                            <label for="search-road-number-material" class="block text-sm font-medium text-gray-700">路段编号</label>
                            <input type="text" id="search-road-number-material" placeholder="例如: G7211" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                        <div>
                            <label for="search-start-stake-material" class="block text-sm font-medium text-gray-700">起始桩号</label>
                            <input type="text" id="search-start-stake-material" placeholder="例如: K10+200" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 text-sm">
                        </div>
                    </div>
                    <button id="perform-material-search-btn" class="w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        检索物资
                    </button>

                    <!-- 物资检索结果区域 -->
                    <div id="material-search-results" class="mt-6 border-t pt-4 hidden">
                        <div class="flex justify-between items-center mb-3">
                            <h6 class="text-md font-semibold text-gray-800">物资检索结果</h6>
                            <span class="text-xs text-gray-500">找到 2 个仓库的物资 (模拟)</span>
                        </div>
                        <div class="space-y-4" id="material-search-results-list">
                            <!-- 第一个仓库的模拟结果 -->
                            <div class="border p-4 rounded-md bg-gray-50">
                                <div class="mb-3 pb-2 border-b">
                                    <h4 class="text-lg font-semibold text-gray-800">中心仓库A栋</h4>
                                    <p class="text-xs text-gray-500 mt-1">
                                        <span class="mr-3"><i class="fas fa-map-marker-alt mr-1 text-gray-400"></i>位置: 南宁市XX路123号</span> 
                                        <span class="mr-3"><i class="fas fa-user mr-1 text-gray-400"></i>负责人: 张保管</span> 
                                        <span><i class="fas fa-phone mr-1 text-gray-400"></i>联系方式: 13812345678</span>
                                    </p>
                                </div>
                                <div class="space-y-2">
                                    <p class="text-sm font-medium text-gray-700 mb-1">库存物资:</p>
                                    <div class="text-xs pl-2 space-y-2">
                                        <div class="p-2 rounded bg-white shadow-sm">
                                            <div class="flex justify-between items-center">
                                                <p><strong class="font-medium">水泵 (型号 A)</strong> - 类型: 排水设备</p>
                                                <!-- <button class="text-xs text-green-600 hover:text-green-800">申请调用</button> -->
                                            </div>
                                            <p class="text-gray-600">数量: 15台 | 状态: <span class="text-green-600 font-semibold">可用</span></p>
                                        </div>
                                        <div class="p-2 rounded bg-white shadow-sm">
                                            <div class="flex justify-between items-center">
                                                <p><strong class="font-medium">沙袋</strong> - 类型: 防汛物资</p>
                                                <!-- <button class="text-xs text-gray-500 cursor-not-allowed">申请调用</button> -->
                                            </div>
                                            <p class="text-gray-600">数量: 2000条 | 状态: <span class="text-green-600 font-semibold">可用</span></p>
                                        </div>
                                        <div class="p-2 rounded bg-white shadow-sm">
                                            <div class="flex justify-between items-center">
                                                <p><strong class="font-medium">发电机 (型号 B)</strong> - 类型: 电力设备</p>
                                                <!-- <button class="text-xs text-gray-500 cursor-not-allowed">申请调用</button> -->
                                            </div>
                                            <p class="text-gray-600">数量: 3台 | 状态: <span class="text-yellow-600 font-semibold">维修中</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 第二个仓库的模拟结果 -->
                            <div class="border p-4 rounded-md bg-gray-50">
                                <div class="mb-3 pb-2 border-b">
                                    <h4 class="text-lg font-semibold text-gray-800">东区应急物资储备点</h4>
                                    <p class="text-xs text-gray-500 mt-1">
                                        <span class="mr-3"><i class="fas fa-map-marker-alt mr-1 text-gray-400"></i>位置: 桂林市YY大道456号</span> 
                                        <span class="mr-3"><i class="fas fa-user mr-1 text-gray-400"></i>负责人: 刘备料</span> 
                                        <span><i class="fas fa-phone mr-1 text-gray-400"></i>联系方式: 13798765432</span>
                                    </p>
                                </div>
                                <div class="space-y-2">
                                    <p class="text-sm font-medium text-gray-700 mb-1">库存物资:</p>
                                    <div class="text-xs pl-2 space-y-2">
                                        <div class="p-2 rounded bg-white shadow-sm">
                                            <div class="flex justify-between items-center">
                                                <p><strong class="font-medium">应急照明灯</strong> - 类型: 照明设备</p>
                                                <!-- <button class="text-xs text-green-600 hover:text-green-800">申请调用</button> -->
                                            </div>
                                            <p class="text-gray-600">数量: 52盏 | 状态: <span class="text-green-600 font-semibold">可用</span></p>
                                        </div>
                                        <div class="p-2 rounded bg-white shadow-sm">
                                            <div class="flex justify-between items-center">
                                                <p><strong class="font-medium">帐篷 (多人型)</strong> - 类型: 住宿设备</p>
                                                <!-- <button class="text-xs text-green-600 hover:text-green-800">申请调用</button> -->
                                            </div>
                                            <p class="text-gray-600">数量: 25顶 | 状态: <span class="text-green-600 font-semibold">可用</span></p>
                                        </div>
                                        <div class="p-2 rounded bg-white shadow-sm">
                                            <div class="flex justify-between items-center">
                                                <p><strong class="font-medium">通讯电台 (手持)</strong> - 类型: 通讯设备</p>
                                                <!-- <button class="text-xs text-gray-500 cursor-not-allowed">申请调用</button> -->
                                            </div>
                                            <p class="text-gray-600">数量: 8部 | 状态: <span class="text-blue-600 font-semibold">部分预订</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- 可以添加更多仓库的模拟结果 -->
                        </div>
                    </div>
                </div>
                <!-- Modal Footer -->
                <div class="flex justify-end items-center p-4 border-t bg-gray-50 rounded-b-lg">
                    <button id="cancel-material-search-btn" class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors">取消</button>
                </div>
            </div>
        </div>

    </main>
    </div>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- 新增: 控制模态框和筛选交互的脚本 (可以放在一个新文件如 js/dashboardInteractive.js 中) -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 地图筛选面板交互
            const collapseFilterBtn = document.getElementById('collapse-filter-btn');
            const filterContent = document.getElementById('filter-content');
            const mapFilterPanel = document.getElementById('map-filter-panel');
            let isFilterPanelCollapsed = false;

            // 折叠/展开筛选面板
            collapseFilterBtn.addEventListener('click', () => {
                isFilterPanelCollapsed = !isFilterPanelCollapsed;
                if (isFilterPanelCollapsed) {
                    filterContent.classList.add('hidden');
                    collapseFilterBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>`;
                    mapFilterPanel.classList.add('w-auto');
                    mapFilterPanel.classList.remove('w-72');
                } else {
                    filterContent.classList.remove('hidden');
                    collapseFilterBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                    </svg>`;
                    mapFilterPanel.classList.remove('w-auto');
                    mapFilterPanel.classList.add('w-72');
                }
            });

            // 图层切换交互
            const toggleCheckboxes = document.querySelectorAll('.toggle-checkbox');
            toggleCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', (e) => {
                    const layer = e.target.dataset.layer;
                    const isChecked = e.target.checked;
                    
                    // 模拟激活图层效果
                    const label = e.target.closest('label');
                    if (isChecked) {
                        label.classList.add('bg-blue-50', 'border', 'border-blue-200');
                        label.classList.remove('bg-gray-50');
                        
                        // 模拟在地图上显示图层
                        console.log(`显示图层: ${layer}`);
                        
                        // 如果有真实地图集成，这里会调用地图API
                        // 例如: map.showLayer(layer);
                    } else {
                        label.classList.remove('bg-blue-50', 'border', 'border-blue-200');
                        label.classList.add('bg-gray-50');
                        
                        // 模拟在地图上隐藏图层
                        console.log(`隐藏图层: ${layer}`);
                        
                        // 如果有真实地图集成，这里会调用地图API
                        // 例如: map.hideLayer(layer);
                    }
                });
            });

            // 物资筛选下拉框交互
            const suppliesSelect = document.getElementById('emergency-supplies-select');
            suppliesSelect.addEventListener('change', () => {
                const selectedSupply = suppliesSelect.value;
                // 重置其他选项的选中状态，以便与地图联动
                if (selectedSupply) {
                    console.log(`筛选物资类型: ${selectedSupply}`);
                    // 这里可以添加真实地图上的物资筛选逻辑
                } else {
                    console.log('显示所有物资类型');
                }
            });

            // 搜索模态框交互
            const quickSearchBtn = document.getElementById('quick-search-btn');
            const searchModal = document.getElementById('search-modal');
            const closeSearchModalBtn = document.getElementById('close-search-modal-btn');
            const cancelSearchBtn = document.getElementById('cancel-search-btn');
            const performSearchBtn = document.getElementById('perform-search-btn');
            const searchResultsDiv = document.getElementById('search-results');
            const searchResultsList = document.getElementById('search-results-list');

            // 打开搜索模态框
            quickSearchBtn.addEventListener('click', () => {
                searchModal.classList.remove('hidden');
                searchModal.classList.add('flex'); // 使用flex居中
                
                // 添加优雅的动画
                setTimeout(() => {
                    const modalContent = searchModal.querySelector('.bg-white');
                    modalContent.classList.add('transform', 'scale-100', 'opacity-100');
                    modalContent.classList.remove('scale-95', 'opacity-0');
                }, 10);
            });

            // 关闭搜索模态框
            const closeSearchModal = () => {
                // 添加关闭动画
                const modalContent = searchModal.querySelector('.bg-white');
                modalContent.classList.add('transform', 'scale-95', 'opacity-0');
                modalContent.classList.remove('scale-100', 'opacity-100');
                
                // 延迟隐藏以完成动画
                setTimeout(() => {
                    searchModal.classList.add('hidden');
                    searchModal.classList.remove('flex');
                    searchResultsDiv.classList.add('hidden'); // 关闭时也隐藏结果
                }, 200);
            };
            
            closeSearchModalBtn.addEventListener('click', closeSearchModal);
            cancelSearchBtn.addEventListener('click', closeSearchModal);
            
            // 点击模态框外部区域关闭
            searchModal.addEventListener('click', (event) => {
                if (event.target === searchModal) {
                    closeSearchModal();
                }
            });

            // 处理搜索按钮点击 (模拟)
            performSearchBtn.addEventListener('click', () => {
                // 显示加载状态
                performSearchBtn.disabled = true;
                performSearchBtn.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    搜索中...
                `;
                
                // 模拟API请求延迟
                setTimeout(() => {
                    // 恢复按钮状态
                    performSearchBtn.disabled = false;
                    performSearchBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>检索`;
                    
                    // 显示结果
                    searchResultsDiv.classList.remove('hidden');
                    // 添加结果交互
                    addResultInteraction();
                }, 1000);
            });

            // 为动态添加的结果或模拟结果添加交互
            function addResultInteraction() {
                // 事件委托: 监听结果列表上的点击事件
                searchResultsList.addEventListener('change', function(event) {
                    if (event.target.classList.contains('event-level-select')) {
                        const selectElement = event.target;
                        // The responseDiv is no longer the immediate next sibling due to the new <div> wrapper for the select.
                        // It's the second sibling of the select's parent div, or more robustly, find by class within the same result item.
                        const resultItemDiv = selectElement.closest('.border'); // Assuming each result item is wrapped in a div with class 'border'
                        const responseDiv = resultItemDiv.querySelector('.response-condition'); 
                        
                        if (responseDiv) { // Ensure responseDiv is found
                            if (selectElement.value) { // A specific level is selected
                                let levelName = '';
                                let conditionText = '';
                                // Find the selected option's text for levelName
                                for (let i = 0; i < selectElement.options.length; i++) {
                                    if (selectElement.options[i].value === selectElement.value) {
                                        levelName = selectElement.options[i].text;
                                        break;
                                    }
                                }

                                switch (selectElement.value) {
                                    case 'level1':
                                        conditionText = '特别重大事故，造成30人以上死亡';
                                        break;
                                    case 'level2':
                                        conditionText = '重大事故，造成10-30人死亡';
                                        break;
                                    case 'level3':
                                        conditionText = '较大事故，造成3-10人死亡';
                                        break;
                                    case 'level4':
                                        conditionText = '一般事故，造成3人以下死亡';
                                        break;
                                }
                                responseDiv.innerHTML = `<strong>响应条件:</strong><br>${levelName}: ${conditionText}`;
                            }
                            // If a level is selected or deselected, hide the emergency disposal info section for that plan
                            const detailsDiv = resultItemDiv.querySelector('.plan-actions-details');
                            if(detailsDiv) detailsDiv.classList.add('hidden');
                            const showBtn = resultItemDiv.querySelector('.show-plan-actions-btn');
                            if(showBtn) showBtn.innerHTML = '显示应急处置信息 <i class="fas fa-chevron-down text-xs ml-1"></i>'; // Reset button text
                        } // End if (responseDiv)
                    }
                });

                searchResultsList.addEventListener('click', function(event) {
                    // 处理 "查看预案详情" 按钮
                    if (event.target.closest('.view-plan-details-btn')) {
                        const planId = event.target.closest('.view-plan-details-btn').dataset.planId;
                        console.log(`跳转到预案详情页: ${planId}`);
                        // window.location.href = `/plan-details/${planId}`; // 实际跳转
                        // alert(`模拟跳转到预案 ${planId} 的详情页`);
                        window.location.href = `./plan_detail.html`
                    }

                    // 处理 "显示/隐藏应急处置信息" 按钮
                    if (event.target.closest('.show-plan-actions-btn')) {
                        const button = event.target.closest('.show-plan-actions-btn');
                        const detailsDiv = button.closest('.border').querySelector('.plan-actions-details');
                        if (detailsDiv) {
                            detailsDiv.classList.toggle('hidden');
                            // 更新按钮文本和图标
                            if (detailsDiv.classList.contains('hidden')) {
                                button.innerHTML = '显示应急处置信息 <i class="fas fa-chevron-down text-xs ml-1"></i>';
                            } else {
                                button.innerHTML = '隐藏应急处置信息 <i class="fas fa-chevron-up text-xs ml-1"></i>';
                            }
                        }
                    }
                });
            }

            // 新增: 物资搜索模态框交互
            const quickMaterialSearchBtn = document.getElementById('quick-material-search-btn');
            const materialSearchModal = document.getElementById('material-search-modal');
            const closeMaterialSearchModalBtn = document.getElementById('close-material-search-modal-btn');
            const cancelMaterialSearchBtn = document.getElementById('cancel-material-search-btn');
            const performMaterialSearchBtn = document.getElementById('perform-material-search-btn');
            const materialSearchResultsDiv = document.getElementById('material-search-results');
            const materialSearchResultsList = document.getElementById('material-search-results-list');

            // 打开物资搜索模态框
            if (quickMaterialSearchBtn) {
                quickMaterialSearchBtn.addEventListener('click', () => {
                    materialSearchModal.classList.remove('hidden');
                    materialSearchModal.classList.add('flex');
                    setTimeout(() => {
                        const modalContent = materialSearchModal.querySelector('.bg-white');
                        modalContent.classList.add('transform', 'scale-100', 'opacity-100');
                        modalContent.classList.remove('scale-95', 'opacity-0');
                    }, 10);
                });
            }

            // 关闭物资搜索模态框
            const closeMaterialSearchModal = () => {
                const modalContent = materialSearchModal.querySelector('.bg-white');
                modalContent.classList.add('transform', 'scale-95', 'opacity-0');
                modalContent.classList.remove('scale-100', 'opacity-100');
                setTimeout(() => {
                    materialSearchModal.classList.add('hidden');
                    materialSearchModal.classList.remove('flex');
                    materialSearchResultsDiv.classList.add('hidden');
                }, 200);
            };
            
            if (closeMaterialSearchModalBtn) closeMaterialSearchModalBtn.addEventListener('click', closeMaterialSearchModal);
            if (cancelMaterialSearchBtn) cancelMaterialSearchBtn.addEventListener('click', closeMaterialSearchModal);
            if (materialSearchModal) {
                materialSearchModal.addEventListener('click', (event) => {
                    if (event.target === materialSearchModal) {
                        closeMaterialSearchModal();
                    }
                });
            }

            // 处理物资搜索按钮点击 (模拟)
            if (performMaterialSearchBtn) {
                performMaterialSearchBtn.addEventListener('click', () => {
                    performMaterialSearchBtn.disabled = true;
                    performMaterialSearchBtn.innerHTML = `<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>搜索中...`;
                    
                    setTimeout(() => {
                        performMaterialSearchBtn.disabled = false;
                        performMaterialSearchBtn.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>检索物资`;
                        materialSearchResultsDiv.classList.remove('hidden');
                        // Placeholder for adding material result interactions if needed in the future
                    }, 1000);
                });
            }
        });
    </script>
    
    <!-- 添加: 确保地图加载的脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取地图容器
            const mapContainer = document.getElementById('map-container');
            const mapLoading = document.getElementById('map-loading');
            
            // 尝试加载地图（尝试多种路径）
            function loadMap() {
                console.log("尝试加载地图...");
                
                // 创建图片元素
                const mapImg = document.createElement('img');
                mapImg.className = 'w-full h-full object-cover';
                mapImg.alt = '广西地图';
                
                // 设置加载和错误处理
                mapImg.onload = function() {
                    console.log("地图加载成功!");
                    if (mapLoading && mapLoading.parentNode) {
                        mapLoading.remove();
                    }
                };
                
                // 错误处理和备用方案链
                mapImg.onerror = function() {
                    console.log("路径1失败，尝试路径2...");
                    mapImg.src = './lib/map.png'; // 尝试相对路径2
                    
                    mapImg.onerror = function() {
                        console.log("路径2失败，尝试路径3...");
                        mapImg.src = '/html/lib/map.png'; // 尝试绝对路径
                        
                        mapImg.onerror = function() {
                            console.log("路径3失败，尝试路径4...");
                            mapImg.src = '../lib/map.png'; // 尝试上级目录
                            
                            mapImg.onerror = function() {
                                console.log("路径4失败，尝试直接显示内容...");
                                // 如果所有路径都失败，显示错误信息
                                showMapError();
                            };
                        };
                    };
                };
                
                // 开始尝试第一个路径
                mapImg.src = 'lib/map.png';
                
                // 添加到容器
                mapContainer.appendChild(mapImg);
            }
            
            // 显示地图错误
            function showMapError() {
                if (mapLoading) {
                    mapLoading.innerHTML = `
                        <div class="text-center p-6">
                            <svg class="w-12 h-12 mx-auto text-gray-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                            </svg>
                            <h3 class="text-lg font-medium">无法加载地图</h3>
                            <p class="mt-2 text-sm">请确认 map.png 文件位于正确位置</p>
                            <button id="retry-map-load" class="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm">重试加载</button>
                        </div>
                    `;
                    // 添加重试按钮事件
                    document.getElementById('retry-map-load').addEventListener('click', function() {
                        mapLoading.innerHTML = `
                            <div class="text-center">
                                <svg class="animate-spin h-10 w-10 mx-auto text-blue-500 mb-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <p class="text-gray-600">地图加载中...</p>
                            </div>
                        `;
                        loadMap();
                    });
                }
            }
            
            // 开始加载地图
            loadMap();
        });
    </script>
</body>
</html> 