let fetchOption = {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
  }
};
// 应急事件模态框相关函数
// 事件信息板块相关函数
function openEmergencyEventModal(data) {
  console.log('打开应急事件模态框');
  const modal = document.getElementById('emergency-event-modal');
  if (modal) {

    // 填充事件信息
    populateEventInfo(data);
    // 填充应急机构 todo暂无planId
    // viewPlanDetails(data.emerPlanId)
    viewPlanDetails('d34e93a027fd44d9bca6be7f2fcf8e28', false)

    populateEmergencyResources(data.eventId);

    populateEmergencyTeams(data.eventId);

    currentEvent = data
    modal.style.display = 'block';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    modal.style.zIndex = '9999';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为emergency-event-modal的元素');
  }
}

function closeEmergencyEventModal() {
  console.log('关闭应急事件模态框');
  const modal = document.getElementById('emergency-event-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto'; // 恢复背景滚动
  }
}

// 导出事件信息
function exportEventInfo(type) {
  if (!currentEvent) {
    alert('当前没有可导出的事件信息');
    return;
  }

  if (type === '1') {
    // 应急辅助决策导出 - 使用fetch处理docx文件流
    const eventId = currentEvent.eventId;
    if (!eventId) {
      alert('事件ID缺失，无法导出');
      return;
    }

    // 配置POST请求
    fetch(`${API_BASE_URL}/emergency/event/export/decisionAdvice?eventId=${eventId}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
        'Content-Type': 'application/json'
      },
      body: {}    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`导出失败: ${response.status} ${response.statusText}`);
      }

      // 获取文件名（从Content-Disposition或使用默认名）
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = `应急决策建议_${currentEvent.eventName || '事件'}_${new Date().toISOString().slice(0,10)}.docx`;

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }

      return response.blob().then(blob => ({ blob, filename }));
    })
    .then(({ blob, filename }) => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();

      // 清理
      setTimeout(() => {
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }, 100);
    })
    .catch(error => {
      console.error('导出失败:', error);
      // alert(error.message);
    });

  }
  if (type === '2') {
      // 应急组织机构导出 - 从预案对象中提取完整组织结构
      try {
        // 假设currentPlan是当前预案对象
        const planData = currentPlan || {};
        const orgStructures = planData.emPrePlanDeptDTOList || [];

        if (orgStructures.length === 0) {
          alert('没有可导出的组织机构信息');
          return;
        }

        // 递归提取所有层级的组织机构数据
        function extractOrgData(orgs, level = 0) {
          let result = [];
          orgs.forEach(org => {
            // 当前组织信息
            const orgInfo = {
              level: level,
              name: org.deptName || '未命名部门',
              leader: org.leader || '',
              leaderAss: org.leaderAss || '',
              member: org.member || '',
              pro: org.pro || ''
            };
            result.push(orgInfo);

            // 递归处理子组织
            if (org.children && org.children.length > 0) {
              result = result.concat(extractOrgData(org.children, level + 1));
            }
          });
          return result;
        }

        // 提取所有组织机构数据
        const allOrgs = extractOrgData(orgStructures);

        // 生成CSV内容
        let csvContent = "层级,组织机构名称,负责人,副负责人,成员,专家\n";

        allOrgs.forEach(org => {
          // 根据层级添加缩进
          const indent = '  '.repeat(org.level);
          csvContent += `"${org.level}","${indent}${org.name}","${org.leader}","${org.leaderAss}","${org.member}","${org.pro}"\n`;
        });

        // 创建下载链接
        const blob = new Blob(["\uFEFF" + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `应急组织机构_${planData.planName || '预案'}_${new Date().toISOString().slice(0,10)}.csv`;
        document.body.appendChild(link);
        link.click();

        // 清理
        setTimeout(() => {
          document.body.removeChild(link);
          URL.revokeObjectURL(url);
        }, 100);

      } catch (error) {
        console.error('导出组织机构失败:', error);
        // alert('导出组织机构时出错: ' + error.message);
      }
    } else {
      // alert('未知的导出类型');
    }
}
// 编辑辅助决策
function editDecision() {
  console.log('编辑辅助决策');
  const displayDiv = document.querySelector('.decision-display');
  const editSection = document.querySelector('.decision-edit-section');
  const editText = document.getElementById('decision-edit-text');

  if (displayDiv && editSection && editText) {
    // 将当前显示的内容复制到编辑框
    editText.value = displayDiv.textContent.trim();

    // 隐藏显示区域，显示编辑区域
    displayDiv.style.display = 'none';
    editSection.style.display = 'block';
  }
}

// 确认决策编辑
function confirmDecisionEdit() {
  console.log('确认决策编辑');
  const displayDiv = document.querySelector('.decision-display');
  const editSection = document.querySelector('.decision-edit-section');
  const editText = document.getElementById('decision-edit-text');

  if (displayDiv && editSection && editText) {
    // 更新显示内容
    displayDiv.textContent = editText.value;

    // 显示显示区域，隐藏编辑区域
    displayDiv.style.display = 'block';
    editSection.style.display = 'none';
  }
}

// 取消决策编辑
function cancelDecisionEdit() {
  console.log('取消决策编辑');
  const displayDiv = document.querySelector('.decision-display');
  const editSection = document.querySelector('.decision-edit-section');

  if (displayDiv && editSection) {
    // 显示显示区域，隐藏编辑区域
    displayDiv.style.display = 'block';
    editSection.style.display = 'none';
  }
}

// 预案相关函数

function createMapMarker(item, container, color, size) {
  const marker = document.createElement('div');
  marker.className = 'circle-resource-marker';
  marker.setAttribute('data-resource-id', item.id);
  marker.style.cssText = `
                position: absolute;
                top: ${item.y}%;
                left: ${item.x}%;
                width: ${size}px;
                height: ${size}px;
                background: ${color};
                border: 2px solid #fff;
                border-radius: 50%;
                cursor: pointer;
                z-index: 1002;
                box-shadow: 0 2px 8px ${color}66;
                transition: all 0.3s ease;
            `;

  // 创建信息卡片
  const tooltip = document.createElement('div');
  tooltip.className = 'circle-marker-tooltip';
  tooltip.innerHTML = `
                <div class="tooltip-title">${item.name}</div>
                <div class="tooltip-info">
                    <div class="tooltip-item">
                        <span class="tooltip-label">距离:</span>
                        <span class="tooltip-value">${item.distance}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">地点:</span>
                        <span class="tooltip-value">${item.location}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">负责人:</span>
                        <span class="tooltip-value">${item.manager}</span>
                    </div>
                    <div class="tooltip-item">
                        <span class="tooltip-label">联系方式:</span>
                        <span class="tooltip-value">${item.contact}</span>
                    </div>
                </div>
            `;

  // 添加悬停效果和信息卡片显示
  marker.addEventListener('mouseenter', function (e) {
    this.style.transform = 'scale(1.5)';
    this.style.zIndex = '1003';

    // 显示信息卡片
    const rect = this.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    tooltip.style.left = (rect.left - containerRect.left + 20) + 'px';
    tooltip.style.top = (rect.top - containerRect.top - 10) + 'px';

    container.appendChild(tooltip);
    setTimeout(() => tooltip.classList.add('show'), 10);
  });

  marker.addEventListener('mouseleave', function () {
    this.style.transform = 'scale(1)';
    this.style.zIndex = '1002';

    // 隐藏信息卡片
    tooltip.classList.remove('show');
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 300);
  });

  container.appendChild(marker);
}

// 生成资源列表
// function generateResourceList(data, resourceType) {
//   const listContainer = document.getElementById('resource-list-container');
//   const panelTitle = document.getElementById('resource-panel-title');
//
//   if (!listContainer || !panelTitle) {
//     return;
//   }
//
//   // 设置面板标题
//   const typeNames = {
//     'supplies': '应急物资',
//     'teams': '救援队伍',
//     'vehicles': '救援车辆',
//     'medical': '医疗单位',
//     'fire': '消防单位',
//     'experts': '应急专家'
//   };
//   panelTitle.textContent = `${typeNames[resourceType] || '资源'}列表`;
//
//   // 清空列表
//   listContainer.innerHTML = '';
//
//   // 合并20km和40km的数据
//   const allResources = [...data['20km'], ...data['40km']];
//
//   // 按距离排序
//   allResources.sort((a, b) => {
//     const distanceA = parseInt(a.distance.replace('km', ''));
//     const distanceB = parseInt(b.distance.replace('km', ''));
//     return distanceA - distanceB;
//   });
//
//   // 生成列表项
//   allResources.forEach(item => {
//     const listItem = createResourceListItem(item, resourceType);
//     1
//     listContainer.appendChild(listItem);
//   });
// }

// 创建资源列表项
// function createResourceListItem(item, resourceType) {
//   const listItem = document.createElement('div');
//   listItem.className = 'resource-list-item';
//   listItem.setAttribute('data-resource-id', item.id);
//
//   // 根据距离确定颜色
//   const distance = parseInt(item.distance.replace('km', ''));
//   const rangeColor = distance <= 20 ? '#3498db' : '#27ae60';
//   const rangeText = distance <= 20 ? '20km内' : '40km内';
//
//   listItem.style.cssText = `
//                 background: #2c3e50;
//                 border: 1px solid #95a5a6;
//                 border-radius: 6px;
//                 padding: 15px;
//                 cursor: pointer;
//                 transition: all 0.3s ease;
//                 border-left: 4px solid ${rangeColor};
//             `;
//
//   listItem.innerHTML = `
//                 <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom:
// 8px;"> <h6 style="color: #3498db; margin: 0; font-size: 20px; font-weight: bold;">${item.name}</h6> <span
// style="background: ${rangeColor}; color: white; padding: 4px 10px; border-radius: 12px; font-size: 14px;
// font-weight: bold;">${rangeText}</span> </div> <div style="display: flex; flex-direction: column; gap: 6px;
// font-size: 16px;"> <div style="color: #95a5a6;"> <span style="color: #ecf0f1;">${item.location}</span> </div> <div
// style="color: #95a5a6;"> <span style="color: #ecf0f1;">距离 ${item.distance}</span> </div> <div style="color:
// #95a5a6;"> <span style="color: #ecf0f1;">${item.manager}</span> </div> <div style="color: #95a5a6;"> <span
// style="color: #ecf0f1;">${item.contact}</span> </div> </div> `;  // 添加悬停效果 listItem.addEventListener('mouseenter',
// function () { this.style.background = '#34495e'; this.style.borderColor = '#3498db'; this.style.transform =
// 'translateX(5px)';  // 高亮对应的地图标点 highlightMapMarker(item.id, true); });  listItem.addEventListener('mouseleave',
// function () { this.style.background = '#2c3e50'; this.style.borderColor = '#95a5a6'; this.style.transform = 'translateX(0)';  // 取消高亮地图标点 highlightMapMarker(item.id, false); });  // 添加点击事件 listItem.addEventListener('click', function () { // 聚焦到对应的地图标点 focusOnMapMarker(item.id); });  return listItem; }

// 高亮地图标点
// function highlightMapMarker(resourceId, highlight) {
//   const marker = document.querySelector(`[data-resource-id="${resourceId}"]`);
//   if (marker) {
//     if (highlight) {
//       marker.style.transform = 'scale(1.8)';
//       marker.style.zIndex = '1004';
//       marker.style.boxShadow = '0 0 20px #f39c12, 0 0 40px #f39c12';
//     } else {
//       marker.style.transform = 'scale(1)';
//       marker.style.zIndex = '1002';
//       marker.style.boxShadow = marker.style.background.includes('#3498db') ?
//                                '0 2px 8px #3498db66' : '0 2px 8px #27ae6066';
//     }
//   }
// }

// 聚焦到地图标点
// function focusOnMapMarker(resourceId) {
//   const marker = document.querySelector(`[data-resource-id="${resourceId}"]`);
//   if (marker) {
//     // 添加聚焦动画
//     marker.style.animation = 'pulse 1s ease-in-out 3';
//     marker.style.transform = 'scale(2)';
//     marker.style.zIndex = '1005';
//     marker.style.boxShadow = '0 0 30px #e74c3c, 0 0 60px #e74c3c';
//
//     // 3秒后恢复正常
//     setTimeout(() => {
//       marker.style.animation = '';
//       marker.style.transform = 'scale(1)';
//       marker.style.zIndex = '1002';
//       marker.style.boxShadow = marker.style.background.includes('#3498db') ?
//                                '0 2px 8px #3498db66' : '0 2px 8px #27ae6066';
//     }, 3000);
//   }
// }

// 初始化搜索功能
function initResourceSearch() {
  const searchInput = document.getElementById('resource-search-input');
  if (!searchInput) {
    return;
  }

  searchInput.addEventListener('input', function () {
    const searchTerm = this.value.toLowerCase().trim();
    filterResourceList(searchTerm);
  });
}

// 过滤资源列表
function filterResourceList(searchTerm) {
  const listItems = document.querySelectorAll('.resource-item');

  listItems.forEach(item => {
    const text = item.textContent.toLowerCase();
    if (searchTerm === '' || text.includes(searchTerm)) {
      item.style.display = 'block';
    } else {
      item.style.display = 'none';
    }
  });
}

// 点击模态框外部关闭模态框
window.onclick = function (event) {
  const emergencyModal = document.getElementById('emergency-event-modal');
  const supplyModal = document.getElementById('emergency-supply-modal');
  const teamModal = document.getElementById('rescue-team-modal');
  const fireModal = document.getElementById('fire-station-modal');
  const medicalModal = document.getElementById('medical-station-modal');
  const vehicleModal = document.getElementById('rescue-vehicle-modal');
  const circleModal = document.getElementById('emergency-circle-modal');
  const planDetailsModal = document.getElementById('plan-details-modal');
  const otherPlansModal = document.getElementById('other-plans-modal');

  if (event.target === emergencyModal) {
    closeEmergencyEventModal();
  } else if (event.target === supplyModal) {
    closeSupplyModal();
  } else if (event.target === teamModal) {
    closeTeamModal();
  } else if (event.target === fireModal) {
    closeFireStationModal();
  } else if (event.target === medicalModal) {
    closeMedicalStationModal();
  } else if (event.target === vehicleModal) {
    closeRescueVehicleModal();
  } else if (event.target === circleModal) {
    closeCircleModal();
  } else if (event.target === planDetailsModal) {
    closePlanDetailsModal();
  } else if (event.target === otherPlansModal) {
    closeOtherPlansModal();
  }
}

// 初始化导航栏
NavigationComponent.init('emergency-map');

// 填充事件信息
function populateEventInfo(data) {
  if (!data) {
    return;
  }

  // 格式化时间
  const occurTime = new Date(data.occurTime * 1000);
  const formattedTime = `${occurTime.getFullYear()}年${occurTime.getMonth()
                                                       + 1}月${occurTime.getDate()}日 ${occurTime.getHours()}:${occurTime.getMinutes()
  .toString().padStart(2, '0')}`;

  // 事件类型映射
  const eventTypeMap = {
    '1': '道路交通事故',
    '2': '水路交通事故'
  };

  // 事故类型映射
  const accidentTypeMap = {
    '1': '碰撞事故',
    '2': '翻车事故',
    '3': '火灾事故',
    '4': '危化品事故'
  };

  // 事件等级映射
  const eventLevelMap = {
    '1': 'Ⅰ级特别重大',
    '2': 'Ⅱ级重大',
    '3': 'Ⅲ级较大',
    '4': 'Ⅳ级一般'
  };

  // 方向映射
  const directionMap = {
    '1': '上行',
    '2': '下行',
    '3': '双向'
  };

  // 填充基本信息
  document.querySelector('.info-grid').innerHTML = `
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">发生时间：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${formattedTime}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">地点：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${data.administrativeArea
                                                                          || ''} ${data.detailedAddress || ''}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">道路编号：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${data.roadSectionCode || '未知'}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">起止桩号：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${data.startStakeNumber
                                                                          || '未知'} - ${data.endStakeNumber || '未知'}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">人员伤亡情况：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${data.roadCasualtySituation || '未知'}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">事故原因：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${data.eventCause || '未知'}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">事故类型：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${eventTypeMap[data.eventType]
                                                                          || '未知'} - ${accidentTypeMap[data.accidentType]
                                                                                         || '未知'}</span>
    </div>
    <div class="info-item">
      <span class="info-label" style="color: #95a5a6; font-size: 12px;">事件等级：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${eventLevelMap[data.eventLevel] || '未知'}</span>
    </div>
    <div class="info-item" style="margin-top: 15px;">
      <span class="info-label" style="color: #95a5a6; font-size: 12px; display: block; margin-bottom: 5px;">影响范围及事态发展趋势：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px;">${data.impactTrend || '暂无相关信息'}</span>
    </div>
    <div class="info-item" style="margin-top: 15px;">
      <span class="info-label" style="color: #95a5a6; font-size: 12px; display: block; margin-bottom: 5px;">事件描述：</span>
      <span class="info-value" style="color: #ecf0f1; font-size: 14px; line-height: 1.5;">${data.eventDescription
                                                                                            || '暂无事件描述'}</span>
    </div>
  `;

  // 填充预案启动说明
  const planDescription = document.querySelector('.plan-description');
  if (planDescription) {
    planDescription.textContent =
      `根据《广西壮族自治区公路交通突发事件应急预案》，该预案适用于自治区范围内发生的${eventLevelMap[data.eventLevel]
                                                                                    || 'Ⅱ级及以上'}公路交通突发事件。当国道、省道、高速公路发生交通中断，且抢修时间预计超过24小时时，应启动${eventLevelMap[data.eventLevel]
                                                                                                                                                                                        || 'Ⅱ级'}应急响应。本次事件涉及${data.roadSectionCode
                                                                                                                                                                                                                        || '高速公路'}${data.administrativeArea
                                                                                                                                                                                                                                        || '路段'}因${data.eventCause
                                                                                                                                                                                                                                                      || '事故'}造成交通中断${data.accidentType
                                                                                                                                                                                                                                                                              === '4'
                                                                                                                                                                                                                                                                              ? '，伴随危化品泄漏'
                                                                                                                                                                                                                                                                              : ''}，抢险难度大、处置时间长，符合${eventLevelMap[data.eventLevel]
                                                                                                                                                                                                                                                                                                                || 'Ⅱ级'}响应启动条件。`;
  }

  // 填充辅助决策
  const decisionDisplay = document.querySelector('.decision-display');
  if (decisionDisplay) {
    decisionDisplay.innerHTML = `
      该事故发生在由${data.administrativeArea || '相关单位'}负责的高速公路路段，已判定为${eventLevelMap[data.eventLevel]
                                                                                         || '重大'}公路交通突发事件。根据《广西壮族自治区公路交通突发事件应急预案》，符合${eventLevelMap[data.eventLevel]
                                                                                                                                                                       || 'Ⅱ级'}响应启动条件，建议启动${eventLevelMap[data.eventLevel]
                                                                                                                                                                                                       || 'Ⅱ级'}应急响应，由自治区交通运输厅统一指挥和调度。${data.accidentType
                                                                                                                                                                                                                                                            === '4'
                                                                                                                                                                                                                                                            ? '推荐派遣危化品处置专家、'
                                                                                                                                                                                                                                                            : ''}应急救援专家共同参与，确保高效处置。<br>
      根据事故现场${data.eventCause || '事故'}${data.accidentType === '4' ? '、危化品泄漏'
                                                                          : ''}等特点，需快速开展清障、救援${data.accidentType
                                                                                                           === '4'
                                                                                                           ? '和危化品转运处置'
                                                                                                           : ''}工作。建议调配：${data.emergencyForces
                                                                                                                                || '消防车、救护车等应急力量'}；具体配置可视现场情况动态调整。
    `;
  }
}

// 查看预案详情
let currentPlan = {};
function viewPlanDetails(planId1, show = true) {
  const planId = planId1; // 使用传入的planId或默认值
  console.log('查看预案详情:', planId);

  fetch(`${API_BASE_URL}/em/prePlan/${planId}`, fetchOption)
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      const planDetails = data.data;
      currentPlan = planDetails;
      if (show) {
        showPlanDetailsModal();
      }
      fillPlanDetailsModal(planDetails);
    } else {
      alert('获取预案详情失败: ' + data.msg);
    }
  })
  .catch(error => {
    console.error('Error:', error);
    alert('获取预案详情失败');
  });
}

// 填充预案详情模态框
function fillPlanDetailsModal(planDetails) {
  // 基本信息
  document.getElementById('plan-name').textContent = planDetails.planName || '暂无数据';
  document.getElementById('plan-type').textContent = planDetails.planType == 1 ? '公路交通类' : '未知';
  document.getElementById('plan-dept').textContent = planDetails.dept || '广西交通运输厅';
  document.getElementById('plan-lastCheckTime').textContent = planDetails.lastCheckTime || '暂无数据';
  document.getElementById('plan-scope').textContent = planDetails.scope || '暂无数据';
  // 编制目的
  document.getElementById('plan-purpose').textContent = planDetails.purpose || '暂无数据';
  // 事件分级与响应条件
  if (planDetails.levelDTOList && planDetails.levelDTOList.length > 0) {
    planDetails.levelDTOList.forEach(level => {
      if (level.eventLevel === 1) {
        document.getElementById('plan-basic-level-i-condition').textContent = level.conditions || '暂无数据';
      } else if (level.eventLevel === 2) {
        document.getElementById('plan-basic-level-ii-condition').textContent = level.conditions || '暂无数据';
      }
    });
  }
  // 组织机构
  fillOrganizationStructure(planDetails.emPrePlanDeptDTOList || []);

  // 预防与预警
  document.getElementById('preventive-measures').textContent = planDetails.preventiveMeasures || '暂无数据';
  document.getElementById('warning-principle').textContent = planDetails.warningPrinciple || '暂无数据';
  document.getElementById('warning-info-collect').textContent = planDetails.warningInfoCollect || '暂无数据';
  document.getElementById('warning-level').textContent = planDetails.warningLevel || '暂无数据';
  document.getElementById('warning-publish').textContent = planDetails.warningPublish || '暂无数据';

  // 应急响应
  if (planDetails.levelDTOList && planDetails.levelDTOList.length > 0) {
    planDetails.levelDTOList.forEach(level => {
      if (level.eventLevel === 1) {
        document.getElementById('emergence-level-i-condition').textContent = level.conditions || '暂无数据';
        document.getElementById('emergence-level-i-process-flow').textContent = level.processFlow || '暂无数据';
      } else if (level.eventLevel === 2) {
        document.getElementById('emergence-level-ii-condition').textContent = level.conditions || '暂无数据';
        document.getElementById('emergence-level-ii-process-flow').textContent = level.processFlow || '暂无数据';
      }
    });
  }
  document.getElementById('info-report').textContent = planDetails.infoReport || '暂无数据';
  document.getElementById('news-release').textContent = planDetails.newsRelease || '暂无数据';
  document.getElementById('response-adjust').textContent = planDetails.responseAdjust || '暂无数据';

  // 后期处置
  document.getElementById('aftermath-disposal').textContent = planDetails.aftermathDisposal || '暂无数据';
  document.getElementById('summary-evaluation').textContent = planDetails.summaryEvaluation || '暂无数据';

  // 应急保障
  document.getElementById('communication-support').textContent = planDetails.communicationSupport || '暂无数据';
  document.getElementById('traffic-support').textContent = planDetails.trafficSupport || '暂无数据';
  document.getElementById('traffic-healthGuarantee').textContent = planDetails.healthGuarantee || '暂无数据';
  document.getElementById('material-support').textContent = planDetails.materialSupport || '暂无数据';
  document.getElementById('funding-support').textContent = planDetails.fundingSupport || '暂无数据';

  // 预案管理
  document.getElementById('publicity-revisionContent').textContent = planDetails.revisionContent || '暂无数据';
  document.getElementById('publicity-training').textContent = planDetails.publicityTraining || '暂无数据';
  document.getElementById('plan-drill').textContent = planDetails.planDrill || '暂无数据';
  document.getElementById('implement-time').textContent = planDetails.implementTime || '暂无数据';
}

//  填充组织机构结构
function fillOrganizationStructure(deptDTOList) {
  const orgContainer1 = document.getElementById('plan-organization-structure');
  const orgContainer2 = document.getElementById('recommend-organization-structure');

  // 清空容器
  orgContainer1.innerHTML = '';
  orgContainer2.innerHTML = '';

  if (!deptDTOList || deptDTOList.length === 0) {
    orgContainer1.innerHTML = '<p style="color: #95a5a6;">暂无组织机构数据</p>';
    orgContainer2.innerHTML = '<p style="color: #95a5a6;">暂无组织机构数据</p>';
    return;
  }

  deptDTOList.forEach(dept => {

    // 创建部门元素的辅助函数
    function createDeptElement(dept) {
      const deptElement = document.createElement('div');
      deptElement.className = 'org-section';
      deptElement.style.border = '1px solid #3498db';
      deptElement.style.borderRadius = '8px';
      deptElement.style.padding = '15px';
      deptElement.style.marginBottom = '15px';
      deptElement.style.backgroundColor = 'rgba(52, 152, 219, 0.1)';

      let html = `<h4 style="color: #3498db; margin-top: 0;">${dept.deptName || '未命名部门'}</h4>`;

      if (dept.leader) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">负责人:</strong> <span style="color: #ecf0f1;">${dept.leader}</span></p>`;
      }

      if (dept.leaderAss) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">副负责人:</strong> <span style="color: #ecf0f1;">${dept.leaderAss}</span></p>`;
      }

      if (dept.member) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">成员:</strong> <span style="color: #ecf0f1;">${dept.member}</span></p>`;
      }

      if (dept.pro) {
        html +=
          `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">专家:</strong> <span style="color: #ecf0f1;">${dept.pro}</span></p>`;
      }

      if (dept.children && dept.children.length > 0) {
        html += '<div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #3498db;">';
        dept.children.forEach(child => {
          html += `<div style="margin-bottom: 10px;">
        <h5 style="color: #3498db; margin: 10px 0 5px 0;">${child.deptName || '未命名子部门'}</h5>
        ${child.leader
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">负责人:</strong> <span style="color: #ecf0f1;">${child.leader}</span></p>`
          : ''}
        ${child.leaderAss
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">副负责人:</strong> <span style="color: #ecf0f1;">${child.leaderAss}</span></p>`
          : ''}
        ${child.member
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">成员:</strong> <span style="color: #ecf0f1;">${child.member}</span></p>`
          : ''}
        ${child.pro
          ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">专家:</strong> <span style="color: #ecf0f1;">${child.pro}</span></p>`
          : ''}
      </div>`;

          if (child.children && child.children.length > 0) {
            html += '<div style="margin-top: 10px; padding-left: 15px; border-left: 2px solid #3498db;">';
            child.children.forEach(child1 => {
              html += `<div style="margin-bottom: 10px;">
            <h5 style="color: #3498db; margin: 10px 0 5px 0;">${child1.deptName || '未命名子部门'}</h5>
            ${child1.leader
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">负责人:</strong> <span style="color: #ecf0f1;">${child1.leader}</span></p>`
              : ''}
            ${child1.leaderAss
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">副负责人:</strong> <span style="color: #ecf0f1;">${child1.leaderAss}</span></p>`
              : ''}
            ${child1.member
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">成员:</strong> <span style="color: #ecf0f1;">${child1.member}</span></p>`
              : ''}
            ${child1.pro
              ? `<p style="margin: 5px 0;"><strong style="color: #95a5a6;">专家:</strong> <span style="color: #ecf0f1;">${child1.pro}</span></p>`
              : ''}
          </div>`;
            });
            html += '</div>';
          }
        });
        html += '</div>';
      }

      deptElement.innerHTML = html;
      return deptElement;
    }

    // 为每个容器创建独立的元素
    const deptElement1 = createDeptElement(dept);
    const deptElement2 = createDeptElement(dept);

    orgContainer1.appendChild(deptElement1);
    orgContainer2.appendChild(deptElement2);
  });
}

// 显示预案详情模态框
function showPlanDetailsModal() {
  const modal = document.getElementById('plan-details-modal');
  if (modal) {
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // 初始化子标签页
    initPlanSubTabs();
  }
}

// 初始化预案子标签页
function initPlanSubTabs() {
  // 为所有子标签按钮添加点击事件
  const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
  subTabBtns.forEach(btn => {
    btn.addEventListener('click', function () {
      const targetTab = this.getAttribute('data-plan-tab');
      switchPlanSubTab(targetTab);
    });
  });

  // 默认显示第一个标签页
  switchPlanSubTab('basic-info');
}

// 切换预案子标签页
function switchPlanSubTab(targetTab) {
  // 更新按钮状态
  const subTabBtns = document.querySelectorAll('.plan-sub-tab-btn');
  subTabBtns.forEach(btn => {
    if (btn.getAttribute('data-plan-tab') === targetTab) {
      btn.classList.add('active');
      btn.style.color = '#3498db';
      btn.style.borderBottomColor = '#3498db';
    } else {
      btn.classList.remove('active');
      btn.style.color = '#95a5a6';
      btn.style.borderBottomColor = 'transparent';
    }
  });

  // 更新内容显示
  const subTabContents = document.querySelectorAll('.plan-sub-tab-content');
  subTabContents.forEach(content => {
    if (content.id === targetTab) {
      content.style.display = 'block';
    } else {
      content.style.display = 'none';
    }
  });
}

// 填充应急物资
/**
 * 获取并填充应急物资数据
 * @param {string} eventId 事件ID
 */
function populateEmergencyResources(eventId) {
  // 显示加载状态
  const supplies20km = document.getElementById('supplies-20km');
  const supplies40km = document.getElementById('supplies-40km');
  supplies20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';
  supplies40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';

  // 调用API获取数据
  fetch(`${API_BASE_URL}/rescue/circle/warehouse/event/${eventId}`, fetchOption)
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      renderSuppliesData(data.data);
    } else {
      throw new Error(data.msg || '获取应急物资数据失败');
    }
  })
  .catch(error => {
    console.error('Error fetching emergency resources:', error);
    supplies20km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
    supplies40km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
  });
}

/**
 * 渲染应急物资数据
 * @param {Object} data API返回的数据
 */
function renderSuppliesData(data) {
  const supplies20km = document.getElementById('supplies-20km');
  const supplies40km = document.getElementById('supplies-40km');

  // 渲染20km范围内的物资
  if (data.warehouses20km && data.warehouses20km.length > 0) {
    supplies20km.innerHTML = '';
    data.warehouses20km.forEach(warehouse => {
      supplies20km.appendChild(createWarehouseItem(warehouse));
    });
  } else {
    supplies20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">20km范围内没有应急物资储备点</div>';
  }

  // 渲染40km范围内的物资
  if (data.warehouses40km && data.warehouses40km.length > 0) {
    supplies40km.innerHTML = '';
    data.warehouses40km.forEach(warehouse => {
      supplies40km.appendChild(createWarehouseItem(warehouse));
    });
  } else {
    supplies40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">40km范围内没有应急物资储备点</div>';
  }
}

/**
 * 创建单个仓库的DOM元素
 * @param {Object} warehouse 仓库数据
 * @returns {HTMLElement} 仓库DOM元素
 */
function createWarehouseItem(warehouse) {
  const item = document.createElement('div');
  item.className = 'rescue-team-item';
  item.style.background = '#2c3e50';
  item.style.padding = '15px';
  item.style.borderRadius = '6px';
  item.style.border = '1px solid #95a5a6';
  item.style.marginBottom = '15px';

  // 仓库名称
  const name = document.createElement('div');
  name.className = 'team-name';
  name.style.color = '#3498db';
  name.style.fontWeight = 'bold';
  name.style.marginBottom = '8px';
  name.style.fontSize = '18px';
  name.textContent = warehouse.warehouseName;
  item.appendChild(name);

  // 仓库基本信息
  const info = document.createElement('div');
  info.className = 'team-info';
  info.style.display = 'grid';
  info.style.gridTemplateColumns = 'repeat(3, 1fr)';
  info.style.gap = '10px';
  info.style.marginBottom = '15px';

  info.innerHTML = `
        <div><span style="color: #95a5a6; font-size: 14px;">地点：</span><span style="color: #ecf0f1;">${warehouse.address}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">距离：</span><span style="color: #ecf0f1;">${warehouse.distance
                                                                                                        ? warehouse.distance.toFixed(
    2) + 'km' : '未知'}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">负责人：</span><span style="color: #ecf0f1;">${warehouse.principal}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">联系方式：</span><span style="color: #ecf0f1;">${warehouse.contactPhone}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">所属单位：</span><span style="color: #ecf0f1;">${warehouse.belongOrgName}</span></div>
    `;
  item.appendChild(info);

  // 物资列表
  if (warehouse.materials && warehouse.materials.length > 0) {
    const materialsTitle = document.createElement('h6');
    materialsTitle.style.color = '#95a5a6';
    materialsTitle.style.marginTop = '5px';
    materialsTitle.style.marginBottom = '8px';
    materialsTitle.style.fontSize = '14px';
    materialsTitle.textContent = '物资列表：';
    item.appendChild(materialsTitle);

    const materialsTable = document.createElement('table');
    materialsTable.style.width = '100%';
    materialsTable.style.borderCollapse = 'collapse';
    materialsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">物资名称</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">型号</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">数量</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">状态</th>
            </tr>
        `;
    materialsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    warehouse.materials.forEach(material => {
      const row = document.createElement('tr');
      row.innerHTML = `
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.materialName}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.specModel
                                                                                                        || '-'}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.quantity} ${material.unit}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.statusName
                                                                                                        || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    materialsTable.appendChild(tbody);
    item.appendChild(materialsTable);
  }

  // 装备列表
  if (warehouse.equipments && warehouse.equipments.length > 0) {
    const equipmentsTitle = document.createElement('h6');
    equipmentsTitle.style.color = '#95a5a6';
    equipmentsTitle.style.marginTop = '5px';
    equipmentsTitle.style.marginBottom = '8px';
    equipmentsTitle.style.fontSize = '14px';
    equipmentsTitle.textContent = '装备列表：';
    item.appendChild(equipmentsTitle);

    const equipmentsTable = document.createElement('table');
    equipmentsTable.style.width = '100%';
    equipmentsTable.style.borderCollapse = 'collapse';
    equipmentsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">装备名称</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">型号</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">数量</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">状态</th>
            </tr>
        `;
    equipmentsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    warehouse.equipments.forEach(equipment => {
      const row = document.createElement('tr');
      row.innerHTML = `
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.materialName}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.specModel
                                                                                                        || '-'}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.quantity} ${equipment.unit}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.statusName
                                                                                                        || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    equipmentsTable.appendChild(tbody);
    item.appendChild(equipmentsTable);
  }

  return item;
}

// 填充 救援队伍
/**
 * 获取并填充救援队伍数据
 * @param {string} eventId 事件ID
 */
function populateEmergencyTeams(eventId) {
  // 显示加载状态
  const teams20km = document.getElementById('teams-20km');
  const teams40km = document.getElementById('teams-40km');
  teams20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';
  teams40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">加载中...</div>';

  // 调用API获取数据
  fetch(`${API_BASE_URL}/rescue/circle/team/event/${eventId}`, fetchOption)
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      renderTeamsData(data.data);
    } else {
      throw new Error(data.msg || '获取救援队伍数据失败');
    }
  })
  .catch(error => {
    console.error('Error fetching emergency teams:', error);
    teams20km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
    teams40km.innerHTML = `<div style="color: #e74c3c; padding: 15px;">加载失败: ${error.message}</div>`;
  });
}

/**
 * 渲染救援队伍数据
 * @param {Object} data API返回的数据
 */
function renderTeamsData(data) {
  const teams20km = document.getElementById('teams-20km');
  const teams40km = document.getElementById('teams-40km');

  // 渲染20km范围内的队伍
  if (data.rescueTeams20km && data.rescueTeams20km.length > 0) {
    teams20km.innerHTML = '';
    data.rescueTeams20km.forEach(team => {
      teams20km.appendChild(createTeamItem(team));
    });
  } else {
    teams20km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">20km范围内没有救援队伍</div>';
  }

  // 渲染40km范围内的队伍
  if (data.rescueTeams40km && data.rescueTeams40km.length > 0) {
    teams40km.innerHTML = '';
    data.rescueTeams40km.forEach(team => {
      teams40km.appendChild(createTeamItem(team));
    });
  } else {
    teams40km.innerHTML = '<div style="color: #95a5a6; padding: 15px;">40km范围内没有救援队伍</div>';
  }
}

/**
 * 创建单个救援队伍的DOM元素
 * @param {Object} team 队伍数据
 * @returns {HTMLElement} 队伍DOM元素
 */
function createTeamItem(team) {
  const item = document.createElement('div');
  item.className = 'rescue-team-item';
  item.style.background = '#2c3e50';
  item.style.padding = '15px';
  item.style.borderRadius = '6px';
  item.style.border = '1px solid #95a5a6';
  item.style.marginBottom = '15px';

  // 队伍名称
  const name = document.createElement('div');
  name.className = 'team-name';
  name.style.color = '#3498db';
  name.style.fontWeight = 'bold';
  name.style.marginBottom = '8px';
  name.style.fontSize = '18px';
  name.textContent = team.teamName;
  item.appendChild(name);

  // 队伍基本信息
  const info = document.createElement('div');
  info.className = 'team-info';
  info.style.display = 'grid';
  info.style.gridTemplateColumns = 'repeat(3, 1fr)';
  info.style.gap = '10px';
  info.style.marginBottom = '15px';

  info.innerHTML = `
        <div><span style="color: #95a5a6; font-size: 14px;">地点：</span><span style="color: #ecf0f1;">${team.address}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">距离：</span><span style="color: #ecf0f1;">${team.distance
                                                                                                        ? team.distance.toFixed(
    2) + 'km' : '未知'}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">负责人：</span><span style="color: #ecf0f1;">${team.leaderName}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">联系方式：</span><span style="color: #ecf0f1;">${team.leaderPhone}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">队伍人数：</span><span style="color: #ecf0f1;">${team.teamSize}人</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">专业方向：</span><span style="color: #ecf0f1;">${team.specialties}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">所属单位：</span><span style="color: #ecf0f1;">${team.jurisdictionUnit}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">单位负责人：</span><span style="color: #ecf0f1;">${team.jurisdictionLeader}</span></div>
        <div><span style="color: #95a5a6; font-size: 14px;">单位联系方式：</span><span style="color: #ecf0f1;">${team.jurisdictionPhone}</span></div>
    `;
  item.appendChild(info);

  // 物资列表
  if (team.materials && team.materials.length > 0) {
    const materialsTitle = document.createElement('h6');
    materialsTitle.style.color = '#95a5a6';
    materialsTitle.style.marginTop = '5px';
    materialsTitle.style.marginBottom = '8px';
    materialsTitle.style.fontSize = '14px';
    materialsTitle.textContent = '物资列表：';
    item.appendChild(materialsTitle);

    const materialsTable = document.createElement('table');
    materialsTable.style.width = '100%';
    materialsTable.style.borderCollapse = 'collapse';
    materialsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">物资名称</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">型号</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">数量</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">状态</th>
            </tr>
        `;
    materialsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    team.materials.forEach(material => {
      const row = document.createElement('tr');
      row.innerHTML = `
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.materialName}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.specModel
                                                                                                        || '-'}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.quantity} ${material.unit}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${material.statusName
                                                                                                        || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    materialsTable.appendChild(tbody);
    item.appendChild(materialsTable);
  }

  // 装备列表
  if (team.equipments && team.equipments.length > 0) {
    const equipmentsTitle = document.createElement('h6');
    equipmentsTitle.style.color = '#95a5a6';
    equipmentsTitle.style.marginTop = '5px';
    equipmentsTitle.style.marginBottom = '8px';
    equipmentsTitle.style.fontSize = '14px';
    equipmentsTitle.textContent = '装备列表：';
    item.appendChild(equipmentsTitle);

    const equipmentsTable = document.createElement('table');
    equipmentsTable.style.width = '100%';
    equipmentsTable.style.borderCollapse = 'collapse';
    equipmentsTable.style.background = '#34495e';

    const thead = document.createElement('thead');
    thead.innerHTML = `
            <tr>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">装备名称</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">型号</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">数量</th>
                <th style="color: #3498db; padding: 10px; border: 1px solid #95a5a6; font-size: 14px; text-align: center;">状态</th>
            </tr>
        `;
    equipmentsTable.appendChild(thead);

    const tbody = document.createElement('tbody');
    team.equipments.forEach(equipment => {
      const row = document.createElement('tr');
      row.innerHTML = `
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.materialName}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.specModel
                                                                                                        || '-'}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.quantity} ${equipment.unit}</td>
                <td style="color: #ecf0f1; padding: 8px; border: 1px solid #95a5a6; font-size: 13px;">${equipment.statusName
                                                                                                        || '正常'}</td>
            `;
      tbody.appendChild(row);
    });
    equipmentsTable.appendChild(tbody);
    item.appendChild(equipmentsTable);
  }

  return item;
}

// 关闭预案详情模态框
function closePlanDetailsModal() {
  console.log('关闭预案详情模态框');
  const modal = document.getElementById('plan-details-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 查看其他预案
function viewOtherPlans() {
  console.log('查看其他预案');
  // 先关闭预案详情模态框
  closePlanDetailsModal();

  const modal = document.getElementById('other-plans-modal');
  if (modal) {
    loadPlans(); // 加载预案列表

    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  }
}

// 加载预案列表
function loadPlans() {
  const searchValue = document.getElementById('plans-search').value;
  const category = document.getElementById('plans-category').value;
  const level = document.getElementById('plans-level').value;

  // 显示加载状态
  const container = document.getElementById('plans-list-container');
  container.innerHTML =
    '<div class="loading" style="text-align: center; padding: 40px 0;"><i class="fas fa-spinner fa-spin" style="font-size: 24px;"></i><p>加载中...</p></div>';

  // 构建查询参数
  const params = new URLSearchParams();
  if (searchValue) {
    params.append('planName', searchValue);
  }
  if (category) {
    params.append('planType', category);
  }
  if (level) {
    params.append('eventLevel', level);
  }

  // 调用API获取预案列表
  fetch(`${API_BASE_URL}/em/prePlan/list?${params.toString()}`, fetchOption)
  .then(response => response.json())
  .then(data => {
    if (data.code === 200 && data.rows && data.rows.length > 0) {
      renderPlansList(data.rows);
    } else {
      container.innerHTML =
        '<div class="no-plans" style="text-align: center; padding: 40px 0; color: #95a5a6;"><i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i><p>暂无预案数据</p></div>';
    }
  })
  .catch(error => {
    console.error('获取预案列表失败:', error);
    container.innerHTML =
      '<div class="error" style="text-align: center; padding: 40px 0; color: #e74c3c;"><i class="fas fa-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><p>加载失败，请稍后重试</p></div>';
  });
}

// 渲染预案列表
function renderPlansList(plans) {
  const container = document.getElementById('plans-list-container');
  container.innerHTML = '';

  plans.forEach(plan => {
    const planItem = document.createElement('div');
    planItem.className = 'plan-item';
    planItem.style = 'background: #34495e; padding: 15px; border-radius: 8px; margin-bottom: 15px;';

    // 预案基本信息
    const planInfo = document.createElement('div');
    planInfo.style = 'display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;';

    const planName = document.createElement('div');
    planName.style = 'font-size: 16px;';
    planName.innerHTML =
      `<span style="color: #3498db;">预案名称：</span><span style="font-weight: bold;">${plan.planName
                                                                                        || '未知预案'}</span>`;

    const planType = document.createElement('div');
    planType.style = 'font-size: 14px; color: #95a5a6;margin-left: 10px;';
    planType.innerHTML = `<span style="color: #3498db;">类型：</span>${getPlanTypeName(plan.planType)}`;

    planInfo.appendChild(planName);
    planInfo.appendChild(planType);

    // 预案详情
    const planDetails = document.createElement('div');
    planDetails.style = 'margin-bottom: 15px;';

    const planLevel = document.createElement('div');
    planLevel.style = 'font-size: 14px; margin-bottom: 5px;';
    planLevel.innerHTML = `<span style="color: #3498db;">事件等级：</span>${getEventLevelName(plan.eventLevel)}`;

    const planScope = document.createElement('div');
    planScope.style = 'font-size: 14px; margin-bottom: 5px;';
    planScope.innerHTML = `<span style="color: #3498db;">适用范围：</span>${plan.scope || '未指定'}`;

    const planTime = document.createElement('div');
    planTime.style = 'font-size: 14px;';
    planTime.innerHTML = `<span style="color: #3498db;">更新时间：</span>${plan.updateTime || '未知'}`;

    planDetails.appendChild(planLevel);
    planDetails.appendChild(planScope);
    planDetails.appendChild(planTime);

    // 操作按钮
    const planActions = document.createElement('div');
    planActions.style = 'display: flex; justify-content: flex-end; gap: 10px;';

    const viewBtn = document.createElement('button');
    viewBtn.style =
      'background: #3498db; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;';
    viewBtn.innerHTML = '<i class="fas fa-eye"></i> 查看详情';
    viewBtn.onclick = () => viewPlanDetails(plan.id);

    const downloadBtn = document.createElement('button');
    downloadBtn.style =
      'background: #27ae60; color: white; padding: 8px 15px; border: none; border-radius: 6px; font-size: 14px; cursor: pointer;';
    downloadBtn.innerHTML = '<i class="fas fa-download"></i> 下载';
    downloadBtn.onclick = () => downloadPlan(plan.id);

    planActions.appendChild(viewBtn);
    planActions.appendChild(downloadBtn);

    // 组合所有元素
    planItem.appendChild(planInfo);
    planItem.appendChild(planDetails);
    planItem.appendChild(planActions);

    container.appendChild(planItem);
  });
}

// 搜索预案
function searchPlans() {
  loadPlans();
}

// 下载预案
function downloadPlan(planId) {
  console.log('下载预案');
  alert('预案下载功能开发中...');
  // // 这里可以调用下载预案的API
  // window.open(`${API_BASE_URL}/em/prePlan/export/${planId}`, '_blank');
}

// 辅助函数：获取事件类型名称
function getPlanTypeName(type) {
  const types = {
    '1': '道路交通事故',
    '2': '水路交通事故',
    '3': '铁路交通事故',
    '4': '航空交通事故'
  };
  return types[type] || '未知类型';
}

// 辅助函数：获取事件等级名称
function getEventLevelName(level) {
  const levels = {
    '1': 'Ⅰ级(特别重大)',
    '2': 'Ⅱ级(重大)',
    '3': 'Ⅲ级(较大)',
    '4': 'Ⅳ级(一般)'
  };
  return levels[level] || '未知等级';
}

// 在文档加载完成后添加事件监听
document.addEventListener('DOMContentLoaded', function () {
  // 其他预案模态框的搜索输入框回车事件
  document.getElementById('plans-search').addEventListener('keypress', function (e) {
    if (e.key === 'Enter') {
      searchPlans();
    }
  });

  // 预案筛选下拉框变化事件
  document.getElementById('plans-category').addEventListener('change', searchPlans);
  document.getElementById('plans-level').addEventListener('change', searchPlans);
});

// 关闭其他预案模态框
function closeOtherPlansModal() {
  console.log('关闭其他预案模态框');
  const modal = document.getElementById('other-plans-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 全局变量
let circleMap = null; // 高德地图实例
let eventMarker = null; // 事件标记点
let circle20km = null; // 20km范围圆
let circle40km = null; // 40km范围圆
let resourceMarkers = []; // 资源标记点数组
let currentResources = []; // 当前显示的资源数据
let currentEvent = null; // 当前事件数据
let currentResourceType = ''; // 当前资源类型

// 打开应急救援圈模态框
function openCircleModal(resourceType) {
  currentResourceType = resourceType;

  // 获取当前事件数据（假设从全局变量或DOM中获取）
  if (!currentEvent || !currentEvent.longitude || !currentEvent.latitude) {
    alert('无法获取事件位置信息');
    return;
  }

  // 设置模态框标题
  const titleMap = {
    'teams': '救援队伍',
    'medical': '医疗单位',
    'fire': '消防单位',
    'supplies': '应急物资',
    'experts': '专家库'
  };
  document.getElementById('circle-modal-title').textContent = `应急救援圈 - ${titleMap[resourceType]}`;
  document.getElementById('resource-panel-title').textContent = `${titleMap[resourceType]}列表`;

  // 显示模态框
  document.getElementById('emergency-circle-modal').style.display = 'block';

  // 初始化地图
  initCircleMap();

  // 加载资源数据
  loadResources(resourceType);
}

// 关闭应急救援圈模态框
function closeCircleModal() {
  document.getElementById('emergency-circle-modal').style.display = 'none';

  // 清理地图资源
  if (circleMap) {
    circleMap.destroy();
    circleMap = null;
  }
  resourceMarkers = [];
  currentResources = [];
}

// 初始化高德地图
function initCircleMap() {
  if (!window.AMap) {
    const key = 'c149d16ec64fa406fbaafe432f12c7c9';

    // 动态加载高德地图JS API
    const script = document.createElement('script');
    script.src = 'https://webapi.amap.com/maps?v=2.0&key=' + key + '&plugin=AMap.Circle,AMap.Marker,AMap.InfoWindow';
    script.onload = () => {
      createCircleMap();
    };
    document.head.appendChild(script);
  } else {
    createCircleMap();
  }
}

// 创建高德地图实例
function createCircleMap() {
  // 创建地图实例
  circleMap = new AMap.Map('circle-amap', {
    zoom: 11, // 初始缩放级别
    center: [currentEvent.longitude, currentEvent.latitude], // 中心点坐标
    viewMode: '2D', // 2D地图
    mapStyle: 'amap://styles/blue' // 深色主题
  });

  // 添加事件标记点
  eventMarker = new AMap.Marker({
                                  position: [currentEvent.longitude, currentEvent.latitude],
                                  map: circleMap,
                                  icon: new AMap.Icon({
                                                        image: 'image/emergency-map/events_' + (currentEvent.level
                                                                                                || "4")
                                                               + '.png',
                                                        size: new AMap.Size(30, 36),
                                                        offset: new AMap.Pixel(-15, -36),
                                                      }),
                                });

  // 添加20km范围圆
  circle20km = new AMap.Circle({
                                 center: [currentEvent.longitude, currentEvent.latitude],
                                 radius: 20000, // 20km
                                 strokeColor: '#3498db',
                                 strokeOpacity: 0.8,
                                 strokeWeight: 2,
                                 strokeDasharray: [10, 5], // 虚线样式
                                 fillColor: 'rgba(52, 152, 219, 0.1)',
                                 fillOpacity: 0.3,
                                 zIndex: 50
                               });
  circle20km.setMap(circleMap);

  // 添加40km范围圆
  circle40km = new AMap.Circle({
                                 center: [currentEvent.longitude, currentEvent.latitude],
                                 radius: 40000, // 40km
                                 strokeColor: '#27ae60',
                                 strokeOpacity: 0.8,
                                 strokeWeight: 2,
                                 strokeDasharray: [10, 5], // 虚线样式
                                 fillColor: 'rgba(39, 174, 96, 0.1)',
                                 fillOpacity: 0.3,
                                 zIndex: 40
                               });
  circle40km.setMap(circleMap);

  // 调整视图以包含两个圆
  circleMap.setFitView([circle20km, circle40km]);
}

// 加载资源数据
function loadResources(resourceType) {
  // 根据资源类型调用不同的API
  let apiUrl = '';
  switch (resourceType) {
    case 'supplies':
      apiUrl = `${API_BASE_URL}/warehouse/list`;
      break;
    case 'teams':
      apiUrl = `${API_BASE_URL}/rescue/team/list?pageNum=1&pageSize=1000`;
      break;
    default:
      console.error('未知的资源类型:', resourceType);
      currentResources=[]

      // 生成资源列表
      generateResourceList();

      // 在地图上显示资源点
      showResourcesOnMap();

      // 更新统计信息
      updateStatistics();

      // 初始化搜索功能
      initResourceSearch();
      return;
  }

  // 发起请求获取资源数据
  fetch(apiUrl, fetchOption)
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      switch (resourceType) {
        case 'supplies':
          currentResources = data.data || data.rows || [];
          currentResources = currentResources.map(item => ({
            resourceType: "supplies",
            id: item.id,
            name: item.warehouseName || '未知仓库',
            type: item.warehouseType || "1", // 仓库类型
            location: item.address || '未知位置',
            position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [109.4, 24.3],
            manager: item.principal || '未知负责人',
            contact: item.contactPhone || '未知联系方式',
            roadCode: item.roadCode || '未知路段',
            stake: item.stake || '未知桩号',
            totalCount: item.totalMaterialCount || 0,
            status: 1, // 默认状态正常
            statusName: '正常',
            materials: [
              ...(item.materials?.map(material => ({
                id: material.id,
                name: material.materialName,
                type: "应急物资", // materialType="0"
                specModel: material.specModel,
                quantity: material.quantity,
                unit: material.unit,
                expiryDate: material.expiryDate,
                status: material.status,
                statusName: material.statusName
              })) || [])
            ],
            equipments: [
              ...(item.equipments?.map(equipment => ({
                id: equipment.id,
                name: equipment.materialName,
                type: "应急装备", // materialType="1"
                specModel: equipment.specModel,
                quantity: equipment.quantity,
                unit: equipment.unit,
                expiryDate: equipment.expiryDate,
                status: equipment.status,
                statusName: equipment.statusName
              })) || [])
            ],
            updateTime: item.updateTime || '未知更新时间'
          })) || []
          break;
        case 'teams':
          currentResources = data.data || data.rows || [];

          currentResources = currentResources.map((item, index) => ({
            id: item.id,
            location: item.address || '未知位置',
            name: item.teamName || '未知队伍',
            teamType: item.teamTypeName || 'professional-rescue',
            members: item.teamSize || 0
          })) || []

          break;
        default:
          console.error('未知的资源类型:', resourceType);
          return;
      }

      currentResources = data.data || data.rows || [];

      // 计算每个资源到事件的距离
      currentResources.forEach(resource => {
        resource.distance = calculateDistance(
          currentEvent.longitude,
          currentEvent.latitude,
          resource.longitude,
          resource.latitude
        );
      });

      // 生成资源列表
      generateResourceList();

      // 在地图上显示资源点
      showResourcesOnMap();

      // 更新统计信息
      updateStatistics();

      // 初始化搜索功能
      initResourceSearch();
    } else {
      console.error('获取资源数据失败:', data.message);
    }
  })
  .catch(error => {
    console.error('获取资源数据出错:', error);
  });
}

// 计算两点之间的距离（km）
function calculateDistance(lng1, lat1, lng2, lat2) {
  const R = 6371; // 地球半径，单位km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}
const warehouseTypeDict = {
  "1": "中心库",
  "2": "区域库",
  "3": "前置库",
}
// 生成资源列表
function generateResourceList() {
  const container = document.getElementById('resource-list-container');
  container.innerHTML = '';

  // 按距离排序
  currentResources.sort((a, b) => a.distance - b.distance);

  currentResources.forEach((resource, index) => {
    const item = document.createElement('div');
    item.className = 'resource-item';
    item.style.background = '#2c3e50';
    item.style.padding = '15px';
    item.style.borderRadius = '6px';
    item.style.border = '1px solid #95a5a6';
    item.style.cursor = 'pointer';
    item.dataset.index = index;

    // 鼠标悬停时高亮对应的地图标记
    item.addEventListener('mouseenter', () => {
      if (resourceMarkers[index]) {
        resourceMarkers[index].emit('mouseover');
        circleMap.setCenter([resource.longitude, resource.latitude]);
      }
    });

    item.addEventListener('mouseleave', () => {
      if (resourceMarkers[index]) {
        resourceMarkers[index].emit('mouseout');
      }
    });

    // 点击时居中显示该资源
    item.addEventListener('click', () => {
      if (resourceMarkers[index]) {
        circleMap.setCenter([resource.longitude, resource.latitude]);
        circleMap.setZoom(14);
      }
    });

    // 根据资源类型设置不同的图标和内容
    let iconSrc = '';
    let title = '';

    switch (currentResourceType) {
      case 'supplies':
        iconSrc = 'image/emergency-map/supplies.png';
        title = `${resource.warehouseName} - ${warehouseTypeDict[resource.warehouseType] || "中心库"}`;
        item.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <strong style="color: #3498db; font-size: 16px;">${title}</strong>
            </div>
            <div style="color: #95a5a6; font-size: 14px; margin-bottom: 5px;">
                距离: <span style="color: ${getDistanceColor(resource.distance)}">${resource.distance.toFixed(1)} km</span>
            </div>
            <div style="color: #95a5a6; font-size: 14px; margin-bottom: 5px;">
                地址: ${resource.address || '未知'}
            </div>
            <div style="color: #95a5a6; font-size: 14px;margin-bottom: 5px;">
                负责人: ${resource.principal || '未知'} ${resource.contactPhone ? `(${resource.contactPhone})` : ''}
            </div>
            <div style="color: #95a5a6; font-size: 14px;margin-bottom: 5px;">
                物资类型: ${resource.totalMaterialCount || '0'} 种 
            </div>
        `;
        break;
      case 'teams':
        iconSrc = 'image/emergency-map/teams.png';
        title = `${resource.teamName} - ${teamTypeDict[resource.teamType] || "专业救援队"}`;
        item.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <strong style="color: #3498db; font-size: 16px;">${title}</strong>
            </div>
            <div style="color: #95a5a6; font-size: 14px; margin-bottom: 5px;">
                距离: <span style="color: ${getDistanceColor(resource.distance)}">${resource.distance.toFixed(1)} km</span>
            </div>
            <div style="color: #95a5a6; font-size: 14px; margin-bottom: 5px;">
                地址: ${resource.address || '未知'}
            </div>
            <div style="color: #95a5a6; font-size: 14px;margin-bottom: 5px;">
                负责人: ${resource.leaderName || '未知'} ${resource.leaderPhone ? `(${resource.leaderPhone})` : ''}
            </div>
            <div style="color: #95a5a6; font-size: 14px;margin-bottom: 5px;">
                专业方向: ${resource.specialties || '未知'}  
            </div>
            <div style="color: #95a5a6; font-size: 14px;margin-bottom: 5px;">
                人数: ${resource.teamSize || '未知'}  
            </div>
        `;
        break;
    }



    container.appendChild(item);
  });
}

// 根据距离获取颜色
function getDistanceColor(distance) {
  if (distance <= 20) {
    return '#3498db';
  }
  if (distance <= 40) {
    return '#27ae60';
  }
  return '#e74c3c';
}

// 在地图上显示资源点
function showResourcesOnMap() {
  // 清除之前的标记
  resourceMarkers.forEach(marker => {
    marker.setMap(null);
  });
  resourceMarkers = [];
  // 创建信息窗口
  const infoWindow = new AMap.InfoWindow({
                                           offset: new AMap.Pixel(6, -30),
                                           closeWhenClickMap: true
                                         });

  currentResources.forEach((resource, index) => {
    // 根据资源类型设置不同的图标
    let iconSrc = '';
    let title = '';
    let content = '';

    switch (currentResourceType) {
      case 'supplies':
        iconSrc = 'image/emergency-map/supplies.png';
        title = resource.warehouseName;
        content = `
                    <div style="min-width: 200px; padding: 10px;">
                        <h4 style="margin: 0 0 5px 0; color: #3498db;">${resource.warehouseName}</h4>
                        <p style="margin: 0 0 5px 0; color: #333;">类型: ${warehouseTypeDict[resource.warehouseType] || "中心库"}</p>
                        <p style="margin: 0 0 5px 0; color: #333;">距离: ${resource.distance.toFixed(1)} km</p>
                        <p style="margin: 0 0 5px 0; color: #333;">物资类型: ${resource.totalMaterialCount || '0'} 种 </p>
                        <p style="margin: 0 0 5px 0; color: #333;">负责人: ${resource.principal || '未知'}</p>
                        <p style="margin: 0; color: #333;">联系方式: ${resource.contactPhone || '未知'}</p>
                    </div>
                `;
        break;
      case 'teams':
        iconSrc = 'image/emergency-map/teams.png';
        title = resource.teamName;
        content = `
                    <div style="min-width: 200px; padding: 10px;">
                        <h4 style="margin: 0 0 5px 0; color: #3498db;">${resource.teamName}</h4>
                        <p style="margin: 0 0 5px 0; color: #333;">类型: ${teamTypeDict[resource.teamType] || "专业救援队"}</p>
                        <p style="margin: 0 0 5px 0; color: #333;">距离: ${resource.distance.toFixed(1)} km</p>
                        <p style="margin: 0 0 5px 0; color: #333;">人数: ${resource.teamSize || '未知'}</p>
                        <p style="margin: 0 0 5px 0; color: #333;">专业方向: ${resource.specialties || '未知'}</p>
                        <p style="margin: 0 0 5px 0; color: #333;">负责人: ${resource.leaderName || '未知'}</p>
                        <p style="margin: 0; color: #333;">联系方式: ${resource.leaderPhone || '未知'}</p>
                    </div>
                `;
        break;
    }

    // 创建标记
    const marker = new AMap.Marker({
                                     position: [resource.longitude, resource.latitude],
                                     map: circleMap,
                                     icon: new AMap.Icon({
                                                           image: iconSrc,
                                                           size: new AMap.Size(30, 36),
                                                           offset: new AMap.Pixel(-15, -36),
                                                         }),
                                     title: title,
                                     extData: { // 存储额外数据
                                       index: index,
                                       content: content
                                     }
                                   });

    // 鼠标悬停时显示信息窗口
    marker.on('mouseover', () => {
      infoWindow.setContent(marker.getExtData().content);
      infoWindow.open(circleMap, marker.getPosition());

      // 高亮对应的列表项
      const items = document.querySelectorAll('.resource-item');
      if (items[marker.getExtData().index]) {
        items[marker.getExtData().index].style.border = '1px solid #3498db';
        items[marker.getExtData().index].style.boxShadow = '0 0 8px rgba(52, 152, 219, 0.5)';
      }
    });

    marker.on('mouseout', () => {
      infoWindow.close();

      // 取消高亮列表项
      const items = document.querySelectorAll('.resource-item');
      if (items[marker.getExtData().index]) {
        items[marker.getExtData().index].style.border = '1px solid #95a5a6';
        items[marker.getExtData().index].style.boxShadow = 'none';
      }
    });

    // 点击时居中显示
    marker.on('click', () => {
      circleMap.setCenter(marker.getPosition());
      circleMap.setZoom(14);
    });

    resourceMarkers.push(marker);
  });
}

// 更新统计信息
function updateStatistics() {
  let count20km = 0;
  let count40km = 0;

  currentResources.forEach(resource => {
    if (resource.distance <= 20) {
      count20km++;
    } else if (resource.distance <= 40) {
      count40km++;
    }
  });

  document.getElementById('count-20km').textContent = count20km;
  document.getElementById('count-40km').textContent = count40km;
  document.getElementById('count-total').textContent = currentResources.length;
}

// 初始化救援圈地图
function initializeCircleMap(resourceType) {
  console.log('初始化救援圈地图，资源类型:', resourceType);

  const mapContainer = document.querySelector('#emergency-circle-modal .circle-map-container');
  if (mapContainer) {
    // 清除之前的所有动态添加的元素
    const existingMarkers = mapContainer.querySelectorAll('.circle-resource-marker');
    existingMarkers.forEach(marker => marker.remove());

    const existingCircles = mapContainer.querySelectorAll('.rescue-circle, .range-circle-20km, .range-circle-40km');
    existingCircles.forEach(circle => circle.remove());

    const existingCenterPoints = mapContainer.querySelectorAll('.accident-center-point, .emergency-center-point');
    existingCenterPoints.forEach(point => point.remove());

    // 清除所有工具提示
    const existingTooltips = mapContainer.querySelectorAll('.circle-marker-tooltip');
    existingTooltips.forEach(tooltip => tooltip.remove());

    console.log('已清除之前的元素，开始添加新的救援圈');

    // 添加救援圈
    // addRescueCircles(mapContainer);

    // 根据资源类型添加相应的标点
    // addResourceMarkers(mapContainer, resourceType);

    // 生成资源列表
    // generateResourceList(getResourceData(resourceType), resourceType);

    // // 初始化搜索功能
    // initResourceSearch();

    console.log('救援圈地图初始化完成');
  } else {
    console.error('未找到地图容器');
  }
}

// 添加救援圈
// function addRescueCircles(container) {
//   console.log('开始添加救援圈');
//
//   // 确保容器中没有现有的救援圈
//   const existingCircles = container.querySelectorAll('.rescue-circle, .accident-center-point');
//   if (existingCircles.length > 0) {
//     console.log('发现现有圆圈，先清除:', existingCircles.length);
//     existingCircles.forEach(circle => circle.remove());
//   }
//
//   // 获取容器尺寸并计算中心点
//   const containerRect = container.getBoundingClientRect();
//   const centerX = containerRect.width / 2;
//   const centerY = containerRect.height / 2;
//
//   // 添加20km圆圈
//   const circle20 = document.createElement('div');
//   circle20.className = 'rescue-circle circle-20km';
//   const radius20 = 150; // 固定半径150px
//   circle20.style.cssText = `
//                 position: absolute;
//                 top: ${centerY - radius20}px;
//                 left: ${centerX - radius20}px;
//                 width: ${radius20 * 2}px;
//                 height: ${radius20 * 2}px;
//                 border: 4px dashed #ff6b35;
//                 border-radius: 50%;
//                 pointer-events: none;
//                 opacity: 0.9;
//                 box-shadow: 0 0 15px rgba(255, 107, 53, 0.4);
//                 z-index: 1001;
//             `;
//
//   // 添加20km标签
//   const label20 = document.createElement('div');
//   label20.style.cssText = `
//                 position: absolute;
//                 top: -30px;
//                 left: 50%;
//                 transform: translateX(-50%);
//                 background: #ff6b35;
//                 color: white;
//                 padding: 6px 15px;
//                 border-radius: 6px;
//                 font-size: 16px;
//                 font-weight: bold;
//                 box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
//             `;
//   label20.textContent = '20km';
//   circle20.appendChild(label20);
//   container.appendChild(circle20);
//   console.log('已添加20km圆圈');
//
//   // 添加40km圆圈
//   const circle40 = document.createElement('div');
//   circle40.className = 'rescue-circle circle-40km';
//   const radius40 = 250; // 固定半径250px
//   circle40.style.cssText = `
//                 position: absolute;
//                 top: ${centerY - radius40}px;
//                 left: ${centerX - radius40}px;
//                 width: ${radius40 * 2}px;
//                 height: ${radius40 * 2}px;
//                 border: 4px dashed #3498db;
//                 border-radius: 50%;
//                 pointer-events: none;
//                 opacity: 0.9;
//                 box-shadow: 0 0 15px rgba(52, 152, 219, 0.4);
//                 z-index: 1001;
//             `;
//
//   // 添加40km标签
//   const label40 = document.createElement('div');
//   label40.style.cssText = `
//                 position: absolute;
//                 top: -30px;
//                 left: 50%;
//                 transform: translateX(-50%);
//                 background: #3498db;
//                 color: white;
//                 padding: 6px 15px;
//                 border-radius: 6px;
//                 font-size: 16px;
//                 font-weight: bold;
//                 box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
//             `;
//   label40.textContent = '40km';
//   circle40.appendChild(label40);
//   container.appendChild(circle40);
//   console.log('已添加40km圆圈');
//
//   // 添加事故中心点
//   const centerPoint = document.createElement('div');
//   centerPoint.className = 'accident-center-point';
//   centerPoint.style.cssText = `
//                 position: absolute;
//                 top: ${centerY - 10}px;
//                 left: ${centerX - 10}px;
//                 width: 20px;
//                 height: 20px;
//                 background: #e74c3c;
//                 border: 3px solid #fff;
//                 border-radius: 50%;
//                 z-index: 1003;
//                 box-shadow: 0 0 20px rgba(231, 76, 60, 0.8);
//                 animation: pulse 2s infinite;
//             `;
//
//   // 添加脉冲动画环
//   const pulseRing = document.createElement('div');
//   pulseRing.style.cssText = `
//                 position: absolute;
//                 top: -5px;
//                 left: -5px;
//                 width: 30px;
//                 height: 30px;
//                 border: 2px solid #e74c3c;
//                 border-radius: 50%;
//                 animation: pulse-ring 2s infinite;
//             `;
//   centerPoint.appendChild(pulseRing);
//   container.appendChild(centerPoint);
//   console.log('已添加事故中心点');
//
//   console.log('救援圈添加完成，当前容器中的圆圈数量:',
//               container.querySelectorAll('.rescue-circle, .accident-center-point').length);
// }

// 根据资源类型添加标点
// function addResourceMarkers(container, resourceType) {
//   const resourceData = getResourceData(resourceType);
//
//   // 添加20km范围内的资源
//   resourceData['20km'].forEach(item => {
//     createMapMarker(item, container, '#3498db', 12);
//   });
//
//   // 添加40km范围内的资源
//   resourceData['40km'].forEach(item => {
//     createMapMarker(item, container, '#27ae60', 10);
//   });
// }

// 获取资源数据
// function getResourceData(resourceType) {
//   const resourceDataMap = {
//     'experts': {
//       '20km': [
//         {
//           id: 'expert1',
//           name: '张教授',
//           location: '柳州市城中区',
//           distance: '12km',
//           manager: '交通安全专家',
//           contact: '13800138001',
//           x: 45,
//           y: 40
//         },
//         {
//           id: 'expert2',
//           name: '李工程师',
//           location: '柳江区拉堡镇',
//           distance: '8km',
//           manager: '道路工程专家',
//           contact: '13800138002',
//           x: 55,
//           y: 35
//         }
//       ],
//       '40km': [
//         {
//           id: 'expert3',
//           name: '王博士',
//           location: '桂林市象山区',
//           distance: '35km',
//           manager: '应急管理专家',
//           contact: '13800138003',
//           x: 30,
//           y: 25
//         },
//         {
//           id: 'expert4',
//           name: '陈主任',
//           location: '南宁市青秀区',
//           distance: '38km',
//           manager: '危化品处置专家',
//           contact: '13800138004',
//           x: 70,
//           y: 60
//         }
//       ]
//     },
//     'teams': {
//       '20km': [
//         {
//           id: 'team1',
//           name: '柳州市消防救援支队',
//           location: '柳州市城中区',
//           distance: '12km',
//           manager: '张支队长',
//           contact: '0772-119',
//           x: 42,
//           y: 38
//         },
//         {
//           id: 'team2',
//           name: '柳江区应急救援队',
//           location: '柳江区拉堡镇',
//           distance: '8km',
//           manager: '陈队长',
//           contact: '0772-7212001',
//           x: 58,
//           y: 32
//         }
//       ],
//       '40km': [
//         {
//           id: 'team3',
//           name: '桂林市专业救援队',
//           location: '桂林市象山区',
//           distance: '35km',
//           manager: '李队长',
//           contact: '0773-2825001',
//           x: 28,
//           y: 22
//         },
//         {
//           id: 'team4',
//           name: '广西危化品处置队',
//           location: '南宁市青秀区',
//           distance: '38km',
//           manager: '王队长',
//           contact: '0771-5607001',
//           x: 72,
//           y: 65
//         }
//       ]
//     },
//     'medical': {
//       '20km': [
//         {
//           id: 'medical1',
//           name: '柳州市人民医院',
//           location: '柳州市城中区文昌路8号',
//           distance: '18km',
//           manager: '张院长',
//           contact: '0772-2662222',
//           x: 40,
//           y: 42
//         },
//         {
//           id: 'medical2',
//           name: '柳江区人民医院',
//           location: '柳江区拉堡镇建设中路',
//           distance: '8km',
//           manager: '李院长',
//           contact: '0772-7212120',
//           x: 60,
//           y: 30
//         }
//       ],
//       '40km': [
//         {
//           id: 'medical3',
//           name: '桂林市第二人民医院',
//           location: '桂林市叠彩区',
//           distance: '32km',
//           manager: '王院长',
//           contact: '0773-2800120',
//           x: 25,
//           y: 20
//         },
//         {
//           id: 'medical4',
//           name: '南宁市第一人民医院',
//           location: '南宁市兴宁区',
//           distance: '40km',
//           manager: '陈院长',
//           contact: '0771-2636120',
//           x: 75,
//           y: 68
//         }
//       ]
//     },
//     'fire': {
//       '20km': [
//         {
//           id: 'fire1',
//           name: '柳州市消防救援支队',
//           location: '柳州市城中区中山中路',
//           distance: '12km',
//           manager: '张支队长',
//           contact: '0772-119',
//           x: 43,
//           y: 39
//         },
//         {
//           id: 'fire2',
//           name: '柳江区消防救援大队',
//           location: '柳江区拉堡镇柳堡路',
//           distance: '6km',
//           manager: '王大队长',
//           contact: '0772-7212119',
//           x: 57,
//           y: 33
//         }
//       ],
//       '40km': [
//         {
//           id: 'fire3',
//           name: '桂林市消防救援支队',
//           location: '桂林市秀峰区',
//           distance: '30km',
//           manager: '李支队长',
//           contact: '0773-119',
//           x: 27,
//           y: 18
//         },
//         {
//           id: 'fire4',
//           name: '南宁市消防救援支队',
//           location: '南宁市西乡塘区',
//           distance: '42km',
//           manager: '陈支队长',
//           contact: '0771-119',
//           x: 73,
//           y: 70
//         }
//       ]
//     },
//     'supplies': {
//       '20km': [
//         {
//           id: 'supply1',
//           name: '柳州市应急物资储备中心',
//           location: '柳州市城中区文昌路168号',
//           distance: '15km',
//           manager: '张主任',
//           contact: '0772-3825001',
//           x: 41,
//           y: 41
//         },
//         {
//           id: 'supply2',
//           name: '柳江区物资储备库',
//           location: '柳江区拉堡镇工业园区',
//           distance: '10km',
//           manager: '李主任',
//           contact: '0772-7212002',
//           x: 59,
//           y: 31
//         }
//       ],
//       '40km': [
//         {
//           id: 'supply3',
//           name: '桂林市应急物资中心',
//           location: '桂林市临桂区',
//           distance: '36km',
//           manager: '王主任',
//           contact: '0773-2825002',
//           x: 26,
//           y: 24
//         },
//         {
//           id: 'supply4',
//           name: '南宁市物资储备中心',
//           location: '南宁市良庆区',
//           distance: '39km',
//           manager: '陈主任',
//           contact: '0771-5607002',
//           x: 74,
//           y: 66
//         }
//       ]
//     },
//     'vehicles': {
//       '20km': [
//         {
//           id: 'vehicle1',
//           name: '应急救援车队A',
//           location: '柳州市城中区',
//           distance: '14km',
//           manager: '张队长',
//           contact: '13800138011',
//           x: 44,
//           y: 37
//         },
//         {
//           id: 'vehicle2',
//           name: '应急救援车队B',
//           location: '柳江区拉堡镇',
//           distance: '9km',
//           manager: '李队长',
//           contact: '13800138012',
//           x: 56,
//           y: 34
//         }
//       ],
//       '40km': [
//         {
//           id: 'vehicle3',
//           name: '桂林应急车队',
//           location: '桂林市七星区',
//           distance: '33km',
//           manager: '王队长',
//           contact: '13800138013',
//           x: 29,
//           y: 21
//         },
//         {
//           id: 'vehicle4',
//           name: '南宁应急车队',
//           location: '南宁市江南区',
//           distance: '41km',
//           manager: '陈队长',
//           contact: '13800138014',
//           x: 71,
//           y: 67
//         }
//       ]
//     }
//   };
//
//   return resourceDataMap[resourceType] || {'20km': [], '40km': []};
// }

// 应急物资详情模态框
async function openSupplyModal(data) {
  console.log('打开应急物资模态框');
  const modal = document.getElementById('emergency-supply-modal');

  if (modal) {
    // 填充基本信息
    document.getElementById('supply-name').textContent = data.name || '未知物资点';
    document.getElementById('supply-location').textContent = data.location || '未知位置';
    document.getElementById('supply-manager').textContent = data.manager || '未知负责人';
    document.getElementById('supply-contact').textContent = data.contact || '未知联系方式';
    document.getElementById('supply-updateTime').textContent = data.updateTime || '未知更新时间';

    // 填充物资列表
    const itemsList = document.getElementById('supply-items');
    itemsList.innerHTML = ''; // 清空现有内容

    if (data.materials && data.materials.length > 0) {
      data.materials.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.name || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.specModel || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        itemsList.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无物资信息
          </td>
      `;
      itemsList.appendChild(row);
    }

    // 填充装备列表
    const equipmentList = document.getElementById('supply-equipment');
    equipmentList.innerHTML = ''; // 清空现有内容

    if (data.equipments && data.equipments.length > 0) {
      data.equipments.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.specModel || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        equipmentList.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无装备信息
          </td>
      `;
      equipmentList.appendChild(row);
    }

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为emergency-supply-modal的元素');
  }
}

function closeSupplyModal() {
  console.log('关闭应急物资模态框');
  const modal = document.getElementById('emergency-supply-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 救援队伍详情模态框
async function openTeamModal(data) {
  console.log('打开救援队伍模态框');
  const modal = document.getElementById('rescue-team-modal');

// 获取队伍类型名称
  function getTeamTypeName(type) {
    const typeMap = {
      'fire-rescue': '消防救援',
      'medical-rescue': '医疗救援',
      'road-rescue': '道路救援',
      'water-rescue': '水上救援',
      'chemical-rescue': '危化品救援',
      "professional-rescue": "专业救援",
      "traffic-rescue": "交通救援",
    };
    return typeMap[type] || type;
  }

  if (modal) {
    // 填充基本信息
    document.getElementById('team-name').textContent = data.name || '未知队伍';
    document.getElementById('team-location').textContent = data.location || '未知位置';
    document.getElementById('team-type').textContent = getTeamTypeName(data.type) || '未知专业方向';
    document.getElementById('team-manager').textContent = data.manager || '未知负责人';
    document.getElementById('team-contact').textContent = data.contact || '未知联系方式';
    document.getElementById('team-members').textContent = data.members || '0';
    document.getElementById('team-updateTime').textContent = data.updateTime || '未知更新时间';

    // 填充物资列表
    const equipmentList1 = document.getElementById('team-equipment1');
    equipmentList1.innerHTML = ''; // 清空现有内容

    if (data.materials && data.materials.length > 0) {
      data.materials.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialTypeName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        equipmentList1.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无物资信息
          </td>
      `;
      equipmentList1.appendChild(row);
    }

    // 填充装备列表
    const equipmentList2 = document.getElementById('team-equipment2');
    equipmentList2.innerHTML = ''; // 清空现有内容

    if (data.equipments && data.equipments.length > 0) {
      data.equipments.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.materialTypeName || '未知'}
            </td>
            <td style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
              ${item.quantity || '0'} ${item.unit || ''}
            </td>
        `;
        equipmentList2.appendChild(row);
      });
    } else {
      const row = document.createElement('tr');
      row.innerHTML = `
          <td colspan="3" style="color: #ecf0f1; padding: 10px; border: 1px solid #95a5a6; font-size: 15px; text-align: center;">
            暂无装备信息
          </td>
      `;
      equipmentList2.appendChild(row);
    }

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为rescue-team-modal的元素');
  }
}

function closeTeamModal() {
  console.log('关闭救援队伍模态框');
  const modal = document.getElementById('rescue-team-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 消防点详情模态框
async function openFireStationModal(data1) {
  console.log('打开消防点模态框');
  const modal = document.getElementById('fire-station-modal');
  // 这里使用您现有的假数据，实际应用中替换为真实API调用

  // todo 模拟API延迟
  // let data = await window.Http.post('/risk/type/add', data1);
  let data = {
    name: '柳州市城中区消防站',
    location: '柳州市城中区中山中路',
    manager: '李站长',
    contact: '0772-119',
    personnel: 25,
    vehicles: 4,
    coverage: '城中区主要区域',
    updateTime: '2023年4月10日 14:30'
  };

  if (modal) {
    // 填充模态框内容
    document.getElementById('fire-name').textContent = data.name || '未知消防点';
    document.getElementById('fire-location').textContent = data.location || '未知位置';
    document.getElementById('fire-manager').textContent = data.manager || '未知负责人';
    document.getElementById('fire-contact').textContent = data.contact || '未知联系方式';

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为fire-station-modal的元素');
  }
}

function closeFireStationModal() {
  console.log('关闭消防点模态框');
  const modal = document.getElementById('fire-station-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 医疗点详情模态框
async function openMedicalStationModal(data1) {
  console.log('打开医疗点模态框');
  const modal = document.getElementById('medical-station-modal');
  // 这里使用您现有的假数据，实际应用中替换为真实API调用

  // todo 模拟API延迟
  // let data = await window.Http.post('/risk/type/add', data1);
  let data = {
    name: '柳州市人民医院急诊中心',
    location: '柳州市城中区文昌路8号',
    manager: '王主任',
    contact: '0772-2662222',
    beds: 50,
    doctors: 15,
    nurses: 30,
    specialties: '创伤急救、心肺复苏、中毒救治',
    updateTime: '2023年4月5日 16:45'
  };

  if (modal) {
    // 填充模态框内容
    document.getElementById('medical-name').textContent = data.name || '未知医疗点';
    document.getElementById('medical-location').textContent = data.location || '未知位置';
    document.getElementById('medical-manager').textContent = data.manager || '未知负责人';
    document.getElementById('medical-contact').textContent = data.contact || '未知联系方式';

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为medical-station-modal的元素');
  }
}

function closeMedicalStationModal() {
  console.log('关闭医疗点模态框');
  const modal = document.getElementById('medical-station-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}

// 救援车辆详情模态框
async function openRescueVehicleModal(data1) {
  console.log('打开救援车辆模态框');
  const modal = document.getElementById('rescue-vehicle-modal');
  // 这里使用您现有的假数据，实际应用中替换为真实API调用

  // todo 模拟API延迟
  // let data = await window.Http.post('/risk/type/add', data1);
  let data = {
    name: '救援车辆023',
    location: '柳州市',
    manager: '张三',
    contact: '0772-2662222',
  };

  if (modal) {
    // 填充模态框内容
    document.getElementById('vehicle-name').textContent = data.name || '未知医疗点';
    document.getElementById('vehicle-location').textContent = data.location || '未知位置';
    document.getElementById('vehicle-manager').textContent = data.manager || '未知负责人';
    document.getElementById('vehicle-contact').textContent = data.contact || '未知联系方式';

    // 显示模态框
    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';
  } else {
    console.error('未找到ID为rescue-vehicle-modal的元素');
  }
}

function closeRescueVehicleModal() {
  console.log('关闭救援车辆模态框');
  const modal = document.getElementById('rescue-vehicle-modal');
  if (modal) {
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
  }
}
