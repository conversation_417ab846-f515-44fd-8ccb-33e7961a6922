<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐患与整改 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- 添加 Element Plus 样式 (假设两个子页面都需要) -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="css/common.css"> <!-- Added common CSS for header -->
    <link rel="stylesheet" href="css/unified_header.css">
    <style>
        html, body { height: 100%; margin: 0; }
        body { font-family: "Microsoft YaHei", "PingFang SC", sans-serif; }
        /* Tab styles (borrowed from previous examples) */
        .tab-btn.active {
             color: #2563eb; /* blue-600 */
             border-bottom: 2px solid #2563eb;
         }
         .tab-content:not(.active) {
            display: none;
         }
         /* Minor adjustments for Element Plus components if needed */
         .el-tree-select, .el-date-editor { width: 100% !important; }

        /* Added from hazard_check_list.html */
        .status-badge {
          padding: 0.25rem 0.5rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
          display: inline-block;
          text-align: center;
          min-width: 4rem; /* 保持一致宽度 */
        }
        .status-high { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 高风险 */
        .status-medium { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 中风险 */
        .status-low { background-color: #DBEAFE; color: #1E40AF; } /* 蓝色 - 低风险 */
        .status-none { background-color: #F3F4F6; color: #4B5563; } /* 灰色 - 无风险/已整改 */

        /* 自定义 Tree Select 样式 from hazard_check_list.html */
        /* .el-tree-select is already defined above, ensuring width: 100% */
        .el-select-dropdown__wrap {
            max-height: 400px;
        }
        .el-tree-node__content {
            height: 32px;
        }
        .el-tree-node__label {
            font-size: 14px;
        }
        .upload-list-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
        }

        /* Styles from rectification_task_list.html START */
        /* 任务状态样式 */
        /* .status-badge is already defined, we might need to ensure these specific colors don't conflict or merge them if they are meant to be global */
        .status-pending { background-color: #FEF3C7; color: #92400E; } /* 黄色 - 待处理 */
        .status-progress { background-color: #DBEAFE; color: #1E40AF; } /* 蓝色 - 整改中 */
        .status-completed { background-color: #DEF7EC; color: #03543E; } /* 绿色 - 已完成 */
        .status-overdue { background-color: #FEE2E2; color: #991B1B; } /* 红色 - 已逾期 */
        /* Styles from rectification_task_list.html END */

        /* 调整所有模态框的位置 */
        #hazardModal > div,
        #viewModal > div,
        #deleteConfirmModal > div,
        #rectificationTaskModal > div,
        #deleteRectificationConfirmModal > div {
            margin-top: 100px !important;
            transform: none !important;
        }

        /* 确保模态框容器从顶部开始 */
        #hazardModal,
        #viewModal,
        #deleteConfirmModal,
        #rectificationTaskModal,
        #deleteRectificationConfirmModal {
            align-items: flex-start !important;
            padding-top: 20px;
        }
    </style>
</head>

<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script src="./request/index.js"></script>
<body class="bg-gray-100 min-h-screen flex flex-col">
    <!-- 顶部导航栏容器 -->
    <div id="navigation-container"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" id="mainVue" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
            <div class="py-6">
                <!-- 页面标题 -->
                <div class="mb-6">
                    <h2 class="text-2xl font-semibold text-gray-800">隐患与整改</h2>
                    <p class="text-sm text-gray-500 mt-1">整合查看隐患检查记录与整改任务</p>
                </div>

                <!-- 选项卡导航 -->
                <div class="bg-white rounded-t-lg shadow-sm mb-0">
                    <nav class="flex space-x-4 p-4 sm:px-6 border-b border-gray-200">
                        <button 
                            class="tab-btn px-3 py-2 text-sm font-medium focus:outline-none"
                            :class="{'text-blue-600 border-b-2 border-blue-600': activeTab === 'hazard-list-content', 'text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300': activeTab !== 'hazard-list-content'}"
                            @click="switchTab('hazard-list-content')">
                            <i class="fas fa-shield-alt mr-1"></i>隐患列表
                        </button>
                        <button 
                            class="tab-btn px-3 py-2 text-sm font-medium focus:outline-none"
                            :class="{'text-blue-600 border-b-2 border-blue-600': activeTab === 'rectification-list-content', 'text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300': activeTab !== 'rectification-list-content'}"
                            @click="switchTab('rectification-list-content')">
                            <i class="fas fa-tasks mr-1"></i>整改任务列表
                        </button>
                    </nav>
                </div>

                <!-- 标签页内容区域 -->
                <div class="bg-white rounded-b-lg shadow-md overflow-hidden">
                    <!-- 隐患列表内容 -->
                    <section id="hazard-list-content" class="tab-content p-6" :class="{'active': activeTab === 'hazard-list-content'}" v-show="activeTab === 'hazard-list-content'">
                        <!-- Content from hazard_check_list.html START -->
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <!-- Tab specific title can be minor if needed, or rely on tab button -->
                                <h3 class="text-xl font-semibold text-gray-700">隐患检查记录</h3>
                            </div>
                            <button id="btnAddHazard" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-plus mr-2"></i> 添加隐患
                            </button>
                        </div>

                        <!-- 过滤栏 -->
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <label for="filterCity" class="block text-sm font-medium text-gray-700 mb-1">市</label>
                                    <input type="text" id="filterCity" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入市名称">
                                </div>
                                <div>
                                    <label for="filterCounty" class="block text-sm font-medium text-gray-700 mb-1">区/县</label>
                                    <input type="text" id="filterCounty" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入区/县名称">
                                </div>
                                <!-- <div>
                                    <label for="filterOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                    <div id="filterOrgApp">
                                        <el-tree-select
                                            v-model="selectedUnits"
                                            :data="unitOptions"
                                            multiple
                                            show-checkbox
                                            :props="{ value: 'id', label: 'name', children: 'children' }"
                                            placeholder="请选择单位"
                                            class="block w-full"
                                            @change="handleFilterOrgChange"
                                        />
                                    </div>
                                </div> -->
                                <div>
                                    <label for="filterOrgApp" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                                    <div id="filterOrgApp">
                                        <el-tree-select
                                            v-model="selectedOrgResp"
                                            :data="unitOptions"
                                            :props="{ value: 'id', label: 'name', children: 'children' }"
                                            placeholder="请选择单位"
                                            class="block w-full"
                                            clearable
                                            @change="handleFilterOrgChange"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label for="filterCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别</label>
                                    <div id="filterCategoryApp">
                                        <el-tree-select
                                            v-model="selectedCategories"
                                            :data="categoryOptions"
                                            multiple
                                            show-checkbox
                                            check-strictly="false"
                                            :props="{ value: 'value', label: 'label', children: 'children' }"
                                            placeholder="请选择检查类别"
                                            class="block w-full"
                                            @change="handleFilterCategoryChange"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label for="filterRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级</label>
                                    <select id="filterRiskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">全部</option>
                                        <option value="high">高</option>
                                        <option value="medium">中</option>
                                        <option value="low">低</option>
                                        <option value="none">无风险/已整改</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="filterIsHazard" class="block text-sm font-medium text-gray-700 mb-1">是否为隐患点</label>
                                    <select id="filterIsHazard" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">全部</option>
                                        <option value="yes">是</option>
                                        <option value="no">否</option>
                                    </select>
                                </div>
                            </div>
                             <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
                                 <div>
                                    <label for="filterRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">路段编号</label>
                                    <input type="text" id="filterRoadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入路段编号">
                                </div>
                                <div class="col-start-4 md:col-start-5 flex items-end space-x-2 justify-end">
                                    <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                      <i class="fas fa-search mr-1"></i> 查询
                                    </button>
                                    <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                      <i class="fas fa-undo mr-1"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">市</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区/县</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否隐患点</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险点描述</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="hazardListTableBody" class="bg-white divide-y divide-gray-200">
                                        <!-- 示例数据行 -->
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">青秀区</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">风险路段-地质灾害风险</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">G324</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-high">高</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">K1500+200处边坡有落石风险</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="1"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="1"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="1"><i class="fas fa-trash-alt"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="1" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">灵山县</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基础保障设施隐患-防洪标识</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">S211</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-low">低</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">否</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">桥梁限高标识不清</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="2"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="2"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="2"><i class="fas fa-trash-alt"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="2" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                            </td>
                                        </tr>
                                         <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">福绵区</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">涉灾隐患点-桥梁</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">X456</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-medium">中</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-semibold">是</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">XX桥梁伸缩缝堵塞</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="3"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="3"><i class="fas fa-edit"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="3"><i class="fas fa-trash-alt"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="3" title="生成整改任务"><i class="fas fa-clipboard-list"></i></button>
                                            </td>
                                        </tr>
                                        <!-- 更多数据行 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 分页 -->
                            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                                <div class="flex justify-between items-center">
                                    <div class="text-sm text-gray-700">
                                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                                    </div>
                                    <div>
                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">上一页</span>
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                                1
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">下一页</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Content from hazard_check_list.html END -->
                    </section>

                    <!-- 整改任务列表内容 -->
                    <section id="rectification-list-content" class="tab-content p-6" :class="{'active': activeTab === 'rectification-list-content'}" v-show="activeTab === 'rectification-list-content'">
                        <!-- Content from rectification_task_list.html START -->
                        <div class="flex justify-between items-center mb-6">
                            <div>
                                <h3 class="text-xl font-semibold text-gray-700">整改任务列表</h3>
                                <p class="text-sm text-gray-500 mt-1">跟踪和管理隐患整改任务的进度</p>
                            </div>
                            <button id="btnAddRectificationTask" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                <i class="fas fa-plus mr-2"></i> 添加任务
                            </button>
                        </div>

                        <!-- 过滤栏 -->
                        <div class="bg-white p-4 rounded-lg shadow-sm mb-6 border border-gray-200">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label for="filterTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态</label>
                                    <select id="filterTaskStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                        <option value="">全部</option>
                                        <option value="pending">待处理</option>
                                        <option value="progress">整改中</option>
                                        <option value="completed">已完成</option>
                                        <option value="overdue">已逾期</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="filterRectificationRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位</label>
                                    <div id="filterRectificationOrgApp">
                                         <el-tree-select
                                            v-model="selectedUnit"
                                            :data="unitOptions"
                                            :multiple="false"
                                            :check-strictly="true"
                                            :props="{ value: 'value', label: 'label', children: 'children' }"
                                            placeholder="请选择责任单位"
                                            class="block w-full"
                                            clearable
                                            @change="handleFilterOrgChange"
                                        />
                                    </div>
                                </div>
                                <div>
                                    <label for="filterRectificationDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限</label>
                                    <div id="filterRectificationDateApp">
                                        <el-date-picker
                                            v-model="deadlineRange"
                                            type="daterange"
                                            range-separator="至"
                                            start-placeholder="开始日期"
                                            end-placeholder="结束日期"
                                            class="block w-full"
                                            value-format="YYYY-MM-DD"
                                            @change="handleDateChange"
                                        />
                                    </div>
                                </div>
                                <div class="flex items-end space-x-2 justify-end">
                                    <button id="btnRectificationFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                      <i class="fas fa-search mr-1"></i> 查询
                                    </button>
                                    <button id="btnRectificationResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                      <i class="fas fa-undo mr-1"></i> 重置
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 数据表格 -->
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200">
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务ID</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联隐患</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任单位</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任人</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">整改期限</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="rectificationListTableBody" class="bg-white divide-y divide-gray-200">
                                        <!-- 示例数据行 -->
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK001</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="1" title="ID:1, K1500+200处边坡有落石风险">隐患ID:1</a></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">南宁市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-28</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-10</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-pending">待处理</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK001" title="查看"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK001" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK001" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK001" title="删除"><i class="fas fa-trash-alt"></i></button>
                                            </td>
                                        </tr>
                                         <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK002</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="2" title="ID:2, 桥梁限高标识不清">隐患ID:2</a></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">钦州市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-29</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-08-05</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-progress">整改中</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                 <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK002" title="查看"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK002" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK002" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK002" title="删除"><i class="fas fa-trash-alt"></i></button>
                                            </td>
                                        </tr>
                                          <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TASK003</td>
                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs"><a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" data-hazard-id="3" title="ID:3, XX桥梁伸缩缝堵塞">隐患ID:3</a></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">玉林市交通运输局</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张工</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-20</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-25</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm"><span class="status-badge status-overdue">已逾期</span></td>
                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                                <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" data-id="TASK003" title="查看"><i class="fas fa-eye"></i></button>
                                                <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" data-id="TASK003" title="编辑/更新状态"><i class="fas fa-edit"></i></button>
                                                <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" data-id="TASK003" title="标记完成"><i class="fas fa-check-circle"></i></button>
                                                <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" data-id="TASK003" title="删除"><i class="fas fa-trash-alt"></i></button>
                                            </td>
                                        </tr>
                                        <!-- 更多数据行 -->
                                    </tbody>
                                </table>
                            </div>
                            <!-- 分页 -->
                            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                                <div class="flex justify-between items-center">
                                    <div class="text-sm text-gray-700">
                                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                                    </div>
                                    <div>
                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">上一页</span>
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                                1
                                            </a>
                                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <span class="sr-only">下一页</span>
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Content from rectification_task_list.html END -->
                    </section>
                </div>
            </div>
        </main>
    </div>

    <!-- Modals related to Rectification Tasks START -->
    <!-- 查看/编辑任务模态框 (from rectification_task_list.html) -->
    <div id="rectificationTaskModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-start justify-center pt-[100px] hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="rectificationModalTitle">查看/编辑整改任务</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-rectification-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="rectificationTaskForm">
                    <input type="hidden" id="rectificationTaskId" name="rectificationTaskId">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 pb-4 border-b">
                         <div><strong class="text-gray-600 block mb-1">任务ID:</strong> <span id="view-rectificationTaskId"></span></div>
                         <div>
                            <label for="modalPitfallId" class="block text-sm font-medium text-gray-700 mb-1">关联隐患ID <span class="text-red-500">*</span></label>
                            <select id="modalPitfallId" name="pitfallsId" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                               <option value="pending">待处理</option>
                               <option value="progress">整改中</option>
                               <option value="completed">已完成</option>
                               <option value="overdue">已逾期</option>
                            </select>
                        </div>
                         <div>
                            <label for="modalRectificationRespUnit" class="block text-sm font-medium text-gray-700 mb-1">责任单位 <span class="text-red-500">*</span></label>
                            <div id="modalRectificationOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    :props="{ value: 'name', label: 'name', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                         <div>
                             <label for="modalRectificationRespPerson" class="block text-sm font-medium text-gray-700 mb-1">责任人</label>
                             <input type="text" id="modalRectificationRespPerson" name="dutyBy" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                         </div>
                         <div>
                             <label for="modalRectificationDeadline" class="block text-sm font-medium text-gray-700 mb-1">整改期限 <span class="text-red-500">*</span></label>
                             <div id="modalRectificationDateApp">
                                 <el-date-picker
                                    v-model="deadline"
                                    type="date"
                                    placeholder="选择日期"
                                    class="block w-full"
                                    value-format="YYYY-MM-DD"
                                />
                             </div>
                         </div>
                          <div>
                             <label for="modalRectificationTaskStatus" class="block text-sm font-medium text-gray-700 mb-1">任务状态 <span class="text-red-500">*</span></label>
                             <select id="modalRectificationTaskStatus" name="status" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="0">待处理</option>
                                <option value="1">整改中</option>
                                <option value="2">已完成</option>
                             </select>
                         </div>
                    </div>
                    <div>
                        <label for="modalRectificationRemarks" class="block text-sm font-medium text-gray-700 mb-1">整改进度/备注</label>
                        <textarea id="modalRectificationRemarks" name="remarks" rows="4" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    </div>
                     <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">上传整改附件</label>
                        <input type="file" id="modalRectificationTaskFiles" name="fileUrls" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                        <div id="uploadedRectificationTaskFilesList" class="mt-2 text-sm text-gray-600"></div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-rectification-modal">
                  取消
                </button>
                <button id="btnSaveRectificationTask" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  保存更新
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 (Rectification Task Specific - Renamed ID if different from hazard list's delete modal) -->
    <div id="deleteRectificationConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-start justify-center pt-[100px] hidden">
         <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">确认删除整改任务</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-sm text-gray-700">您确定要删除这条整改任务吗？此操作无法撤销。</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-rectification-modal">
                  取消
                </button>
                <button id="btnConfirmRectificationDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  删除
                </button>
            </div>
        </div>
    </div>
    <!-- Modals related to Rectification Tasks END -->

    <!-- Modals from hazard_check_list.html START -->
    <!-- 添加/编辑隐患模态框 -->
    <div id="hazardModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-start justify-center pt-[100px] hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">添加检查记录</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="hazardForm">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="modalPitfallName" class="block text-sm font-medium text-gray-700 mb-1">隐患名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalPitfallName" name="name" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入" required>
                        </div>
                        <div>
                            <label for="modalCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别 <span class="text-red-500">*</span></label>
                            <select id="modalCategory" name="inspectType" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请选择</option>
                                <optgroup label="风险路段">
                                    <option value="risk_section_flood">山洪淹没区风险路段</option>
                                    <option value="risk_section_geology">地质灾害风险路段</option>
                                </optgroup>
                                <option value="management_mechanism">工作管理机制隐患</option>
                                <optgroup label="基础保障设施隐患">
                                     <option value="basic_facilities_sign">防洪标识</option>
                                     <option value="basic_facilities_trail">检查步道</option>
                                     <option value="basic_facilities_hazard">涉灾隐患点</option>
                                </optgroup>
                                 <optgroup label="涉灾隐患点">
                                    <option value="hazard_points_slope">边坡</option>
                                    <option value="hazard_points_drainage">防洪排水设施</option>
                                    <option value="hazard_points_bridge">桥梁</option>
                                    <option value="hazard_points_tunnel">隧道</option>
                                </optgroup>
                            </select>
                        </div>
                        <div>
                            <label for="modalCity" class="block text-sm font-medium text-gray-700 mb-1">市名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalCity" name="city" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入" required>
                        </div>
                         <div>
                            <label for="modalArea" class="block text-sm font-medium text-gray-700 mb-1">区/县名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalArea" name="district" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入" required>
                        </div>
                        <div>
                            <label for="modalCoordinate" class="block text-sm font-medium text-gray-700 mb-1">坐标地址 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalCoordinate" name="coordinateAddress" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入" required>
                            <input type="hidden" id="modalLat" name="lat">
                            <input type="hidden" id="modalLng" name="lot">
                        </div>
                        <div>
                            <label for="modalOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <div id="modalOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :props="{ value: 'name', label: 'name', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="modalRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">公路编号 <span class="text-red-500">*</span></label>
                            <select id="modalRoadNumber" name="roadNum" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请先选择市/区县</option>
                                <option value="G324">G324</option>
                                <option value="S211">S211</option>
                                <option value="X456">X456</option>
                            </select>
                        </div>
                         <div>
                            <label for="modalStartStake" class="block text-sm font-medium text-gray-700 mb-1">起点桩号</label>
                            <input type="text" id="modalStartStake" name="pileStart" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+200">
                        </div>
                         <div>
                            <label for="modalEndStake" class="block text-sm font-medium text-gray-700 mb-1">止点桩号</label>
                            <input type="text" id="modalEndStake" name="pileEnd" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+500">
                        </div>
                        <div>
                            <label for="modelProject" class="block text-sm font-medium text-gray-700 mb-1">关联项目 <span class="text-red-500">*</span></label>
                            <select id="modelProject" name="projectId" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请先选择项目</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="modalProvincialResp" class="block text-sm font-medium text-gray-700 mb-1">省级责任单位及人员 <span class="text-red-500">*</span></label>
                            <div id="modalProvincialRespApp">
                                <el-tree-select
                                    v-model="selectedProvincialResp"
                                    :data="unitOptions"
                                    :props="{ value: 'name', label: 'name', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalProvincialRespChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="modalReviewRespApp" class="block text-sm font-medium text-gray-700 mb-1">复核责任单位及人员 <span class="text-red-500">*</span></label>
                            <div id="modalReviewRespApp">
                                <el-tree-select
                                    v-model="selectedReviewResp"
                                    :data="unitOptions"
                                    :props="{ value: 'name', label: 'name', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalReviewRespChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="modalInspectRespApp" class="block text-sm font-medium text-gray-700 mb-1">排查责任单位及人员 <span class="text-red-500">*</span></label>
                            <div id="modalInspectRespApp">
                                <el-tree-select
                                    v-model="selectedInspectResp"
                                    :data="unitOptions"
                                    :props="{ value: 'name', label: 'name', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleModalInspectRespChange"
                                />
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                      <label for="modalRiskDescription" class="block text-sm font-medium text-gray-700 mb-1">风险点描述 <span class="text-red-500">*</span></label>
                      <textarea id="modalRiskDescription" name="remakes" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required></textarea>
                    </div>

                    <div class="grid grprojed:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">现场照片/附件</label>
                            <input type="file" id="modalPhotos" name="sceneImg" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            <div id="uploadedFilesList" class="mt-2 text-sm text-gray-600"></div>
                        </div>
                        <div>
                            <label for="modalRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级 <span class="text-red-500">*</span></label>
                            <select id="modalRiskLevel" name="riskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="1">高</option>
                                <option value="2">中</option>
                                <option value="3">低</option>
                                <!-- <option value="none">无风险</option> -->
                            </select>
                        </div>
                         <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">是否已采取措施 <span class="text-red-500">*</span></label>
                            <div class="flex items-center space-x-4 mt-1">
                                <label><input type="radio" name="isMeasure" value="1" class="mr-1"> 是</label>
                                <label><input type="radio" name="isMeasure" value="0" class="mr-1" checked> 否</label>
                                <input type="hidden" name="isMeasure" value="0">
                            </div>
                        </div>
                         <div>
                            <label for="modalMeasures" class="block text-sm font-medium text-gray-700 mb-1">已（拟）采取的措施</label>
                            <textarea id="modalMeasures" name="measures" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="可多选预设措施，或手动填写"></textarea>
                            <input type="file" id="modalMeasureFiles" name="measureFiles" multiple class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100" placeholder="上传方案/报告">
                            <div id="uploadedMeasureFilesList" class="mt-1 text-sm text-gray-600"></div>
                        </div>
                    </div>
                    <!-- <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4 border-t pt-4">
                        <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">省级责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                         <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">复核责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                         <div>
                           <label class="block text-sm font-medium text-gray-700 mb-1">排查责任单位及人员</label>
                           <p class="text-sm text-gray-600">系统自动关联</p>
                        </div>
                    </div> -->
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnSave" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  保存
                </button>
            </div>
        </div>
    </div>

    <!-- 查看详情模态框 -->
    <div id="viewModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-start justify-center pt-[100px] hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">查看检查记录详情</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 pb-4 border-b">
                    <div><strong class="text-gray-600">检查类别:</strong> <span id="view-category"></span></div>
                    <div><strong class="text-gray-600">市/区县:</strong> <span id="view-city_county"></span></div>
                    <div><strong class="text-gray-600">所属单位:</strong> <span id="view-orgUnit"></span></div>
                    <div><strong class="text-gray-600">公路编号:</strong> <span id="view-roadNumber"></span></div>
                    <div><strong class="text-gray-600">起点桩号:</strong> <span id="view-startStake"></span></div>
                    <div><strong class="text-gray-600">止点桩号:</strong> <span id="view-endStake"></span></div>
                    <div><strong class="text-gray-600">风险等级:</strong> <span id="view-riskLevel"></span></div>
                    <div><strong class="text-gray-600">是否隐患点:</strong> <span id="view-isHazard" class="font-semibold"></span></div>
                    <div><strong class="text-gray-600">是否已采取措施:</strong> <span id="view-measuresTaken"></span></div>
                </div>
                 <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">风险点描述:</strong>
                    <p id="view-riskDescription" class="text-gray-800 bg-gray-50 p-2 rounded"></p>
                 </div>
                  <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">已（拟）采取的措施:</strong>
                    <p id="view-measures" class="text-gray-800 bg-gray-50 p-2 rounded"></p>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                     <div>
                         <strong class="text-gray-600 block mb-1">现场照片/附件:</strong>
                         <div id="view-photos" class="text-blue-600"></div>
                     </div>
                      <div>
                         <strong class="text-gray-600 block mb-1">措施附件:</strong>
                         <div id="view-measureFiles" class="text-blue-600"></div>
                     </div>
                 </div>
                 <div class="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                     <div><strong class="text-gray-600">省级责任单位:</strong> <span id="view-provincialResp"></span></div>
                     <div><strong class="text-gray-600">复核责任单位:</strong> <span id="view-reviewResp"></span></div>
                     <div><strong class="text-gray-600">排查责任单位:</strong> <span id="view-inspectResp"></span></div>
                 </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 btn-close-modal">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-start justify-center pt-[100px] hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
            </div>
            <div class="px-6 py-4">
                <p class="text-sm text-gray-700">您确定要删除这条检查记录吗？相关整改任务（如有）将可能受到影响。此操作无法撤销。</p>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnConfirmDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  删除
                </button>
            </div>
        </div>
    </div>
    <!-- Modals from hazard_check_list.html END -->

    <!-- Load Libraries (Vue & Element Plus might be needed later) -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
    <script>
        // 初始化导航栏
        window.NavigationComponent.init('system-management');
    </script>

    <!-- Tab Switching Logic -->
    <script>
        // 添加通用的模态框操作函数
        function openModal(modal) {
            if (modal && modal instanceof HTMLElement) {
                modal.classList.remove('hidden');
            }
        }

        function closeModal(modal) {
            if (modal && modal instanceof HTMLElement) {
                modal.classList.add('hidden');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            window.Http.get('/risk/modifyTask/list').then(response => {
                console.log('获取整改任务列表成功：', response);
                const taskList = response.rows;
                renderRectificationList(taskList); // 添加这行调用渲染函数
            }).catch(error => {
                console.error('获取整改任务列表失败：', error);
            });
            
            
            // 用于存储上传的文件信息
            let uploadedPhotos = [];
            let uploadedMeasureFiles = [];
            let uploadedRectificationFiles = []; // 添加整改任务文件存储数组

            // 处理上传文件的显示和删除
            function handleUploadedFiles(files, listElementId, onDelete) {
                const listElement = document.getElementById(listElementId);
                if (listElement) {
                    const listItem = document.createElement('div');
                    listItem.className = 'upload-list-item';
                    
                    const fileNameSpan = document.createElement('span');
                    fileNameSpan.textContent = files.newFileName;
                    
                    const deleteButton = document.createElement('button');
                    deleteButton.type = 'button';
                    deleteButton.className = 'text-red-500 text-xs';
                    deleteButton.textContent = '删除';
                    
                    deleteButton.addEventListener('click', function() {
                        listItem.remove();
                        if (onDelete) onDelete();
                        const relatedInput = listElementId === 'uploadedFilesList' ? 
                            document.getElementById('modalPhotos') : 
                            document.getElementById('modalMeasureFiles');
                        if (relatedInput) {
                            relatedInput.value = '';
                        }
                    });
                    
                    listItem.appendChild(fileNameSpan);
                    listItem.appendChild(deleteButton);
                    listElement.appendChild(listItem);
                }
            }

            // 清除文件列表
            function clearUploadedFiles(listElementId) {
                const listElement = document.getElementById(listElementId);
                if (listElement) {
                    listElement.innerHTML = '';
                    if (listElementId === 'uploadedFilesList') {
                        uploadedPhotos = [];
                    } else if (listElementId === 'uploadedMeasureFilesList') {
                        uploadedMeasureFiles = [];
                    } else if (listElementId === 'uploadedRectificationTaskFilesList') {
                        uploadedRectificationFiles = [];
                    }
                }
            }

            // 现场照片上传处理
            const modalPhotosInput = document.getElementById('modalPhotos');
            if (modalPhotosInput) {
                modalPhotosInput.addEventListener('change', function(e) {
                    console.log('上传照片', e.target.files);
                    Array.from(e.target.files).forEach(file => {
                        window.Http.upload('/common/upload', file).then(res => {
                            console.log('上传照片成功', res);
                            uploadedPhotos.push(res);
                            handleUploadedFiles(res, 'uploadedFilesList', () => {
                                uploadedPhotos = uploadedPhotos.filter(item => item.newFileName !== res.newFileName);
                            });
                        }).catch(err => {
                            console.error('上传失败:', err);
                        });
                    });
                });
            }

            // 措施文件上传处理
            const modalMeasureFilesInput = document.getElementById('modalMeasureFiles');
            if (modalMeasureFilesInput) {
                modalMeasureFilesInput.addEventListener('change', function(e) {
                    console.log('上传措施照片', e.target.files);
                    Array.from(e.target.files).forEach(file => {
                        window.Http.upload('/common/upload', file).then(res => {
                            console.log('上传措施照片成功', res);
                            uploadedMeasureFiles.push(res);
                            handleUploadedFiles(res, 'uploadedMeasureFilesList', () => {
                                uploadedMeasureFiles = uploadedMeasureFiles.filter(item => item.newFileName !== res.newFileName);
                            });
                        }).catch(err => {
                            console.error('上传失败:', err);
                        });
                    });
                });
            }

            // 整改任务文件上传处理
            const modalRectificationTaskFilesInput = document.getElementById('modalRectificationTaskFiles');
            if (modalRectificationTaskFilesInput) {
                modalRectificationTaskFilesInput.addEventListener('change', function(e) {
                    console.log('上传整改任务文件', e.target.files);
                    Array.from(e.target.files).forEach(file => {
                        window.Http.upload('/common/upload', file).then(res => {
                            console.log('上传整改任务文件成功', res);
                            uploadedRectificationFiles.push(res);
                            handleUploadedFiles(res, 'uploadedRectificationTaskFilesList', () => {
                                uploadedRectificationFiles = uploadedRectificationFiles.filter(item => item.newFileName !== res.newFileName);
                            });
                        }).catch(err => {
                            console.error('上传失败:', err);
                        });
                    });
                });
            }

            // 获取隐患列表
            window.Http.get('/risk/pitfalls/list').then(response => {
                console.log('获取隐患列表成功：', response);
                const pitfallList = response.rows;

                const selectElement = document.getElementById('modalPitfallId');
                if (selectElement && response.rows) {
                    // 保留第一个默认选项
                    selectElement.innerHTML = '<option value="">请选择关联隐患</option>';
                    
                    // 添加项目选项
                    response.rows.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = project.name;
                        selectElement.appendChild(option);
                    });
                }
                
                console.log('pitfallList', pitfallList);
                // 渲染隐患列表到表格
                renderHazardList(pitfallList);
            }).catch(error => {
                console.error('获取隐患列表失败：', error);
            });

            // 添加渲染隐患列表的函数
            function renderHazardList(list) {
                const tableBody = document.getElementById('hazardListTableBody');
                if (!tableBody) return;

                // 清空现有内容
                tableBody.innerHTML = '';

                // 遍历数据并渲染
                list.forEach((item, index) => {
                    const riskLevelMap = {
                        '1': { text: '高', class: 'status-high' },
                        '2': { text: '中', class: 'status-medium' },
                        '3': { text: '低', class: 'status-low' },
                        'none': { text: '无风险', class: 'status-none' }
                    };

                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${index + 1}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.city || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.district || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.units || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.inspectType || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.roadNum || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="status-badge ${riskLevelMap[item.riskLevel]?.class || 'status-none'}">
                                ${riskLevelMap[item.riskLevel]?.text || '未知'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm ${item.isMeasure === 1 ? 'text-red-600 font-semibold' : 'text-gray-500'}">
                            ${item.isMeasure == '1' ? '是' : '否'}
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">${item.remakes || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view" data-id="${item.id}" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit" data-id="${item.id}" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete" data-id="${item.id}" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none ml-2 btn-rectify" data-id="${item.id}" title="生成整改任务">
                                <i class="fas fa-clipboard-list"></i>
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // 更新分页信息
                updatePagination(list.length);
            }

            // 更新分页信息的函数
            function updatePagination(total) {
                const paginationInfo = document.querySelector('.text-sm.text-gray-700');
                if (paginationInfo) {
                    paginationInfo.innerHTML = `显示 <span class="font-medium">1</span> 到 <span class="font-medium">${total}</span> 条，共 <span class="font-medium">${total}</span> 条记录`;
                }
            }
            
            // 获取项目列表并更新下拉框
            window.Http.get('/risk/projects/list').then(response => {
                console.log('获取项目列表成功：', response);
                const selectElement = document.getElementById('modelProject');
                if (selectElement && response.rows) {
                    // 保留第一个默认选项
                    selectElement.innerHTML = '<option value="">请先选择项目</option>';
                    
                    // 添加项目选项
                    response.rows.forEach(project => {
                        const option = document.createElement('option');
                        option.value = project.id;
                        option.textContent = project.projectName;
                        selectElement.appendChild(option);
                    });
                }
            }).catch(error => {
                console.error('获取项目列表失败：', error);
            });

            // 获取检查类别列表
            window.Http.get('/risk/type/list').then(response => {
                const modalCategory = document.getElementById('modalCategory');
                if (modalCategory) {
                    modalCategory.innerHTML = '<option value="">请选择检查类别</option>';
                    response.rows.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        modalCategory.appendChild(option);
                    });
                }
            }).catch(error => {
                console.error('获取检查类别列表失败：', error);
            });

            // 获取公路编号列表
            window.Http.get('/system/marker/list', {
                pageNum: 1,
                pageSize: 9999
            }).then(response => {
                console.log('获取公路编号列表成功：', response);
                const selectElement = document.getElementById('modalRoadNumber');
                if (selectElement && response.rows) {
                    selectElement.innerHTML = '<option value="">请选择公路编号</option>';
                    response.rows.forEach(road => {
                        const option = document.createElement('option');
                        option.value = road.code;
                        option.textContent = road.code;
                        selectElement.appendChild(option);
                    });
                }
            }).catch(error => {
                console.error('获取公路编号列表失败：', error);
            });

            getUnitList()
            async function getUnitList() {
                try {
                    let unitRes = await window.Http.get('/system/organization/tree')
                    console.log('unitRes', unitRes)
                    if (unitRes.code == 200) {
                        
                        // 同时更新 modalProvincialResp 的数据
                        const modalProvincialRespApp = document.getElementById('modalProvincialRespApp')?.__vue_app__?._instance;
                        if (modalProvincialRespApp) {
                            modalProvincialRespApp.data.unitOptions = [...unitRes.data];
                        }
                        // 同时更新 modalReviewResp 的数据
                        const modalReviewRespApp = document.getElementById('modalReviewRespApp')?.__vue_app__?._instance;
                        if (modalReviewRespApp) {
                            modalReviewRespApp.data.unitOptions = [...unitRes.data];
                        }

                        // 同时更新 modalInspectRespApp 的数据
                        const modalInspectRespApp = document.getElementById('modalInspectRespApp')?.__vue_app__?._instance;
                        if (modalInspectRespApp) {
                            modalInspectRespApp.data.unitOptions = [...unitRes.data];
                        }
                        
                        const filterOrgApp = document.getElementById('filterOrgApp')?.__vue_app__?._instance;
                        if (filterOrgApp) {
                            filterOrgApp.data.unitOptions = [...unitRes.data];
                        }

                        // 同时更新 modalRectificationOrgApp 的数据
                        const modalRectificationOrgApp = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                        if (modalRectificationOrgApp) {
                            modalRectificationOrgApp.data.unitOptions = [...unitRes.data];
                        }
                    }
                } catch (error) {
                    console.error('获取单位列表失败:', error);
                }
            }

            // 删除原来的标签页切换逻辑
            // 直接从 Vue 应用初始化开始
            // 模拟单位数据 (应从后端获取)
            const unitData = [{
                value: '1', label: '广西壮族自治区交通运输厅', children: [
                    { value: '1.1', label: '直属事业单位及专项机构', children: [
                            { value: '1.1.1', label: '自治区公路发展中心' },
                            { value: '1.1.2', label: '自治区高速公路发展中心' },
                            { value: '1.1.3', label: '自治区道路运输发展中心' }
                    ]},
                    { value: '1.2', label: '市级交通运输局', children: [
                            { value: '1.2.1', label: '钦州市交通运输局' },
                            { value: '1.2.2', label: '南宁市交通运输局' },
                            { value: '1.2.3', label: '玉林市交通运输局' }
                    ]}
                ]
            }];

            if (document.getElementById('mainVue')) {
                const mainVue = Vue.createApp({
                    data() { 
                        return { 
                            page: 1,
                            pageSize: 10,
                            total: 0,
                            pitfallList: [],
                            activeTab: 'hazard-list-content', // 添加当前激活的tab状态
                            proJectList: []
                        }
                    },
                    mounted() {
                        // this.getPitfallList()
                        // this.getCorrectionList()
                        // this.getProjectList()
                    },
                    methods: {
                        async getProjectList() {
                            let projectRes = await window.Http.get('/risk/projects/list')
                            console.log('projectRes', projectRes)
                            this.proJectList = [...projectRes.rows]
                        },
                        
                        async getPitfallList() {
                            let pitfallRes = await window.Http.get('/risk/pitfalls/list')
                            console.log('pitfallRes', pitfallRes)
                        },

                        async getCorrectionList() {
                            let correctionRes = await window.Http.get('/risk/modifyTask/list')
                            console.log('correctionRes', correctionRes)
                        },
                        
                        // 添加切换tab的方法
                        switchTab(tabId) {
                            this.activeTab = tabId;
                        }
                    }
                }).use(ElementPlus);
                mainVue.mount('#mainVue');
            }

            // Vue 应用 - 用于筛选栏所属单位多选树形选择器
            if (document.getElementById('filterOrgApp')) {
                const filterOrgApp = Vue.createApp({
                    data() { 
                        return { 
                            selectedOrgResp: null, 
                            unitOptions: [] 
                        } 
                    },
                    mounted() {
                        console.log('filterOrgApp mounted');
                    },
                    methods: { 
                        handleFilterOrgChange(value) { 
                            console.log('筛选选中的单位:', value); 
                        }
                    }
                }).use(ElementPlus);
                filterOrgApp.mount('#filterOrgApp');
                // const filterOrgApp = Vue.createApp({
                //     data() { return { selectedOrgResp: null, unitOptions: []  } },
                //     mounted() {
                //         console.log('filterOrgApp mounted');
                //     },
                //     methods: { handleFilterOrgChange(value) { console.log('筛选选中的单位:', value); } }
                // }).use(ElementPlus);
                // filterOrgApp.mount('#filterOrgApp');
            }
            // Vue 应用 - 用于模态框内所属单位单选树形选择器
            if (document.getElementById('modalOrgApp')) {
                const modalOrgApp = Vue.createApp({
                    data() { 
                        return { 
                            selectedUnit: null, 
                            unitOptions: [] 
                        } 
                    },
                    mounted() {
                        this.getUnitList()
                    },
                    methods: { 
                        handleModalOrgChange(value) { 
                            console.log('模态框选中的单位:', value); 
                        },
                        async getUnitList() {
                            try {
                                let unitRes = await window.Http.get('/system/organization/tree')
                                console.log('unitRes', unitRes)
                                if (unitRes.code == 200) {
                                    this.unitOptions = [...unitRes.data]
                                }
                            } catch (error) {
                                console.error('获取单位列表失败:', error);
                            }
                        }
                    }
                }).use(ElementPlus);
                modalOrgApp.mount('#modalOrgApp');
            }

            // 为省级责任单位创建新的 Vue 实例
            if (document.getElementById('modalProvincialRespApp')) {
                const modalProvincialRespApp = Vue.createApp({
                    data() { 
                        return { 
                            selectedProvincialResp: null, 
                            unitOptions: [] 
                        } 
                    },
                    mounted() {
                        console.log('省级责任单位组件已挂载');
                    },
                    methods: { 
                        handleModalProvincialRespChange(value) { 
                            console.log('省级责任单位选中:', value); 
                        }
                    }
                }).use(ElementPlus);
                modalProvincialRespApp.mount('#modalProvincialRespApp');
            }

            // 为复核责任单位创建新的 Vue 实例
            if (document.getElementById('modalReviewRespApp')) {
                const modalReviewRespApp = Vue.createApp({
                    data() { 
                        return { 
                            selectedReviewResp: null, 
                            unitOptions: [] 
                        } 
                    },
                    mounted() {
                        console.log('复核责任单位组件已挂载');
                    },
                    methods: { 
                        handleModalReviewRespChange(value) { 
                            console.log('复核责任单位选中:', value); 
                        }
                    }
                }).use(ElementPlus);
                modalReviewRespApp.mount('#modalReviewRespApp');
            }

            // 为排查责任单位创建新的 Vue 实例
            if (document.getElementById('modalInspectRespApp')) {
                const modalInspectRespApp = Vue.createApp({
                    data() { 
                        return { 
                            selectedInspectResp: null, 
                            unitOptions: [] 
                        } 
                    },
                    mounted() {
                        console.log('排查责任单位组件已挂载');
                    },
                    methods: { 
                        handleModalInspectRespChange(value) { 
                            console.log('排查责任单位选中:', value); 
                        }
                    }
                }).use(ElementPlus);
                modalInspectRespApp.mount('#modalInspectRespApp');
            }

            // 模拟检查类别数据 (应从后端获取或配置)
            const categoryData = [
                { value: 'risk_section', label: '风险路段', children: [
                        { value: 'risk_section_flood', label: '山洪淹没区风险路段' },
                        { value: 'risk_section_geology', label: '地质灾害风险路段' }
                ]},
                { value: 'management_mechanism', label: '工作管理机制隐患' },
                { value: 'basic_facilities', label: '基础保障设施隐患', children: [
                        { value: 'basic_facilities_sign', label: '防洪标识' },
                        { value: 'basic_facilities_trail', label: '检查步道' },
                        { value: 'basic_facilities_hazard', label: '涉灾隐患点' }
                ]},
                { value: 'hazard_points', label: '涉灾隐患点', children: [
                        { value: 'hazard_points_slope', label: '边坡' },
                        { value: 'hazard_points_drainage', label: '防洪排水设施' },
                        { value: 'hazard_points_bridge', label: '桥梁' },
                        { value: 'hazard_points_tunnel', label: '隧道' }
                ]}
            ];

            // Vue 应用 - 用于筛选栏检查类别多选树形选择器
            if (document.getElementById('filterCategoryApp')) {
                const filterCategoryApp = Vue.createApp({
                    data() { return { selectedCategories: [], categoryOptions: categoryData } },
                    methods: { handleFilterCategoryChange(value) { console.log('筛选选中的类别:', value); } }
                }).use(ElementPlus);
                filterCategoryApp.mount('#filterCategoryApp');
            }

            const hazardModal = document.getElementById('hazardModal');
            const viewModal = document.getElementById('viewModal');
            const deleteConfirmModal = document.getElementById('deleteConfirmModal');
            const btnAddHazard = document.getElementById('btnAddHazard');
            const hazardForm = document.getElementById('hazardForm');
            const modalTitle = document.getElementById('modalTitle');
            let currentEditId = null; // 用于存储当前编辑的记录ID
            let hazardIdToDelete = null; // 用于存储待删除的ID

            // --- 模态框控制 ---
            const openModal = (modal) => { if(modal) modal.classList.remove('hidden');}
            const closeModal = (modal) => { if(modal) modal.classList.add('hidden');}

            // 打开新增模态框
            if (btnAddHazard) {
                btnAddHazard.addEventListener('click', () => {
                    currentEditId = null; // 清除编辑ID
                    if(modalTitle) modalTitle.textContent = '添加检查记录';
                    if(hazardForm) hazardForm.reset();
                    const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) {
                        modalOrgAppInstance.data.selectedUnit = null;
                    }
                    clearUploadedFiles('uploadedFilesList');
                    clearUploadedFiles('uploadedMeasureFilesList');
                    openModal(hazardModal);
                });
            }

            // 关闭模态框按钮
            document.querySelectorAll('.btn-close-modal').forEach(button => {
                button.addEventListener('click', (e) => {
                    // Find the closest modal and close it
                    const modalToClose = e.target.closest('.fixed.inset-0.z-50');
                    if (modalToClose) {
                        closeModal(modalToClose);
                    } else { // Fallback for modals not directly parent
                         closeModal(hazardModal);
                         closeModal(viewModal);
                         closeModal(deleteConfirmModal);
                    }
                });
            });

            // --- 列表操作按钮 (Hazard List Specific) ---
            // Ensure table body exists before adding listener
            const hazardListTableBody = document.getElementById('hazardListTableBody');
            if (hazardListTableBody) {
                hazardListTableBody.addEventListener('click', (event) => {
                    const target = event.target.closest('button');
                    if (!target) return;

                    const id = target.getAttribute('data-id');

                    if (target.classList.contains('btn-view')) {
                        console.log('查看 ID:', id);
                        populateViewModal(id);
                        openModal(viewModal);
                    } else if (target.classList.contains('btn-edit')) {
                        console.log('编辑 ID:', id);
                        currentEditId = id;
                        if(modalTitle) modalTitle.textContent = '编辑检查记录';
                        populateEditModal(id);
                        openModal(hazardModal);
                    } else if (target.classList.contains('btn-delete')) {
                        console.log('删除 ID:', id);
                        hazardIdToDelete = id;
                        openModal(deleteConfirmModal);
                    } else if (target.classList.contains('btn-rectify')) {
                        console.log('生成整改任务 ID:', id);
                        alert(`模拟为记录 ${id} 生成整改任务`);
                        // TODO: Navigate or open modal for rectification task creation
                        // This might involve switching to the '整改任务列表' tab and pre-filling data
                        // For now, just an alert.
                    }
                });
            }


            // --- 模态框表单处理 (Hazard Modal Specific) ---
            // 用于存储上传的文件信息
            // let uploadedPhotos = [];
            // let uploadedMeasureFiles = [];

            // 保存按钮处理
            document.getElementById('btnSave')?.addEventListener('click', function(e) {
                e.preventDefault();
                const hazardForm = document.getElementById('hazardForm');
                if (!hazardForm) return;
                
                // 表单验证
                let isValid = true;
                const requiredFields = {
                    'modalPitfallName': '隐患名称',
                    'modalCategory': '检查类别',
                    'modalCity': '市名称',
                    'modalArea': '区/县名称',
                    'modalRoadNumber': '公路编号',
                    'modelProject': '关联项目',
                    'modalRiskDescription': '风险点描述',
                    'modalRiskLevel': '风险等级'
                };

                // 清除所有错误提示
                document.querySelectorAll('.error-message').forEach(el => el.remove());
                document.querySelectorAll('.border-red-500').forEach(el => el.classList.remove('border-red-500'));

                // 检查所属单位（特殊处理）
                const specialValidations = [
                    {
                        appId: 'modalOrgApp',
                        fieldName: 'selectedUnit',
                        label: '所属单位',
                        errorMessage: '请选择所属单位'
                    },
                    {
                        appId: 'modalProvincialRespApp',
                        fieldName: 'selectedProvincialResp',
                        label: '省级责任单位',
                        errorMessage: '请选择省级责任单位'
                    },
                    {
                        appId: 'modalReviewRespApp',
                        fieldName: 'selectedReviewResp',
                        label: '复核责任单位',
                        errorMessage: '请选择复核责任单位'
                    },
                    {
                        appId: 'modalInspectRespApp',
                        fieldName: 'selectedInspectResp',
                        label: '排查责任单位',
                        errorMessage: '请选择排查责任单位'
                    }
                ];

                // 通用的特殊验证处理函数
                function validateSpecialField(validation) {
                    const instance = document.getElementById(validation.appId)?.__vue_app__?._instance;
                    if (!instance?.data[validation.fieldName]) {
                        isValid = false;
                        console.log(`验证失败: ${validation.label}未选择`, {
                            component: instance,
                            value: instance?.data[validation.fieldName]
                        });

                        const container = document.getElementById(validation.appId);
                        if (container) {
                            const treeSelect = container.querySelector('.el-tree-select');
                            if (treeSelect) {
                                treeSelect.classList.add('border-red-500');
                                const errorDiv = document.createElement('div');
                                errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                                errorDiv.textContent = validation.errorMessage;
                                container.appendChild(errorDiv);
                            }
                        }
                        return false;
                    }
                    return true;
                }

                // 执行所有特殊验证
                specialValidations.forEach(validation => {
                    validateSpecialField(validation);
                });

                // 检查其他必填字段
                for (const [fieldId, fieldName] of Object.entries(requiredFields)) {
                    const field = document.getElementById(fieldId);
                    if (!field || !field.value.trim()) {
                        isValid = false;
                        console.log(`验证失败: ${fieldName} 为空`, {
                            fieldId,
                            fieldName,
                            value: field?.value,
                            element: field
                        });
                        field?.classList.add('border-red-500');
                        
                        // 添加错误提示
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                        errorDiv.textContent = `请填写${fieldName}`;
                        field?.parentNode.appendChild(errorDiv);

                        // 添加输入事件监听器，当用户开始输入时移除错误提示
                        field?.addEventListener('input', function() {
                            this.classList.remove('border-red-500');
                            const errorMessage = this.parentNode.querySelector('.error-message');
                            if (errorMessage) {
                                errorMessage.remove();
                            }
                        });
                    }
                }

                // 检查是否已采取措施的单选按钮
                const isMeasureValue = document.querySelector('input[name="isMeasure"]:checked')?.value;
                if (isMeasureValue === undefined) {
                    isValid = false;
                    console.log('验证失败: 未选择是否已采取措施');
                    const measuresTakenContainer = document.querySelector('input[name="isMeasure"]')?.closest('.flex.items-center');
                    if (measuresTakenContainer) {
                        measuresTakenContainer.classList.add('border-red-500', 'p-2', 'rounded');
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                        errorDiv.textContent = '请选择是否已采取措施';
                        measuresTakenContainer.parentElement.appendChild(errorDiv);
                    }
                }

                console.log('表单验证结果:', {
                    isValid,
                    requiredFields: Object.keys(requiredFields),
                    emptyFields: Object.entries(requiredFields)
                        .filter(([fieldId]) => {
                            const field = document.getElementById(fieldId);
                            return !field || !field.value.trim();
                        })
                        .map(([fieldId, fieldName]) => fieldName)
                });

                if (!isValid) {
                    console.log('表单验证不通过，请检查以上错误信息');
                    // 滚动到第一个错误字段
                    const firstError = document.querySelector('.border-red-500');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    return;
                }

                // 处理表单数据
                const formData = new FormData(hazardForm);
                const data = Object.fromEntries(formData.entries());

                // 处理数值类型字段
                data.riskLevel = Number(data.riskLevel);
                data.isMeasure = Number(isMeasureValue || '0');

                // 获取所有Vue实例
                const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                const modalProvincialRespAppInstance = document.getElementById('modalProvincialRespApp')?.__vue_app__?._instance;
                const modalReviewRespAppInstance = document.getElementById('modalReviewRespApp')?.__vue_app__?._instance;
                const modalInspectRespAppInstance = document.getElementById('modalInspectRespApp')?.__vue_app__?._instance;

                // 添加所属单位的值
                if (modalOrgAppInstance?.data.selectedUnit) {
                    data.units = modalOrgAppInstance.data.selectedUnit;
                }

                // 添加省级责任单位的值
                if (modalProvincialRespAppInstance?.data.selectedProvincialResp) {
                    data.provinceUnit = modalProvincialRespAppInstance.data.selectedProvincialResp;
                }

                // 添加复核责任单位的值 
                if (modalReviewRespAppInstance?.data.selectedReviewResp) {
                    data.reviewUnit = modalReviewRespAppInstance.data.selectedReviewResp;
                }

                // 添加排查责任单位的值
                if (modalInspectRespAppInstance?.data.selectedInspectResp) {    
                    data.inspectUnit = modalInspectRespAppInstance.data.selectedInspectResp;
                }

                // 处理文件数据
                try {
                    // 处理现场照片
                    if (uploadedPhotos && uploadedPhotos.length > 0) {
                        data.sceneImg = uploadedPhotos.map(photo => photo.url || '').filter(url => url).join(',');
                    } else {
                        data.sceneImg = '';
                    }

                    // 处理措施文件
                    if (uploadedMeasureFiles && uploadedMeasureFiles.length > 0) {
                        data.measureFiles = uploadedMeasureFiles.map(file => file.url || '').filter(url => url).join(',');
                    } else {
                        data.measureFiles = '';
                    }
                } catch (error) {
                    console.error('处理文件数据时出错:', error);
                    data.sceneImg = '';
                    data.measureFiles = '';
                }

                console.log('表单数据:', data);
                console.log('上传的照片数据:', uploadedPhotos);
                console.log('上传的措施文件数据:', uploadedMeasureFiles);

                // 发起请求
                if (currentEditId) {
                    data.id = currentEditId;
                    window.Http.post('/risk/pitfalls/edit', data)
                        .then(response => {
                            console.log('更新记录成功:', response);
                            alert('更新成功');
                            closeModal(hazardModal);
                            location.reload();
                        })
                        .catch(error => {
                            console.error('更新记录失败:', error);
                            alert('更新失败：' + (error.message || '请稍后重试'));
                        });
                } else {
                    window.Http.post('/risk/pitfalls/add', data)
                        .then(response => {
                            console.log('新增记录成功:', response);
                            alert('添加成功');
                            closeModal(hazardModal);
                            location.reload();
                        })
                        .catch(error => {
                            console.error('新增记录失败:', error);
                            alert('添加失败：' + (error.message || '请稍后重试'));
                        });
                }
            });


            // --- 删除确认 (Hazard Delete Modal Specific) ---
            const btnConfirmDelete = document.getElementById('btnConfirmDelete');
            if (btnConfirmDelete) {
                btnConfirmDelete.addEventListener('click', async () => {
                    if (hazardIdToDelete) {
                        try {
                            console.log('发送删除请求，ID:', hazardIdToDelete);
                            const response = await window.Http.post('/risk/pitfalls/remove', { id: hazardIdToDelete });
                            console.log('删除成功：', response);
                            // 删除成功后移除对应的表格行
                            const row = document.querySelector(`button.btn-delete[data-id="${hazardIdToDelete}"]`).closest('tr');
                            if (row) row.remove();
                            closeModal(deleteConfirmModal);
                            alert('删除成功');
                            hazardIdToDelete = null;
                        } catch (error) {
                            console.error('删除失败：', error);
                            alert('删除失败：' + (error.message || '未知错误'));
                        }
                    }
                });
            }

            // --- 筛选栏处理 (Hazard List Specific) ---
            const btnFilter = document.getElementById('btnFilter');
            if (btnFilter) {
                 btnFilter.addEventListener('click', () => {
                    const filterOrgAppInstance = document.getElementById('filterOrgApp')?.__vue_app__?._instance;
                    const filterCategoryAppInstance = document.getElementById('filterCategoryApp')?.__vue_app__?._instance;

                    const filterData = {
                        city: document.getElementById('filterCity')?.value,
                        county: document.getElementById('filterCounty')?.value,
                        units: filterOrgAppInstance ? filterOrgAppInstance.data.selectedUnits : [],
                        categories: filterCategoryAppInstance ? filterCategoryAppInstance.data.selectedCategories : [],
                        riskLevel: document.getElementById('filterRiskLevel')?.value,
                        isHazard: document.getElementById('filterIsHazard')?.value,
                        roadNumber: document.getElementById('filterRoadNumber')?.value,
                    };
                    console.log('应用筛选:', filterData);
                    alert('应用筛选条件 (模拟)');
                });
            }


            const btnResetFilter = document.getElementById('btnResetFilter');
            if (btnResetFilter) {
                btnResetFilter.addEventListener('click', () => {
                    const filterCity = document.getElementById('filterCity');
                    if(filterCity) filterCity.value = '';
                    const filterCounty = document.getElementById('filterCounty');
                    if(filterCounty) filterCounty.value = '';

                    const filterOrgAppInstance = document.getElementById('filterOrgApp')?.__vue_app__?._instance;
                    if (filterOrgAppInstance) filterOrgAppInstance.data.selectedUnits = [];

                    const filterCategoryAppInstance = document.getElementById('filterCategoryApp')?.__vue_app__?._instance;
                    if (filterCategoryAppInstance) filterCategoryAppInstance.data.selectedCategories = [];

                    const filterRiskLevel = document.getElementById('filterRiskLevel');
                    if(filterRiskLevel) filterRiskLevel.value = '';
                    const filterIsHazard = document.getElementById('filterIsHazard');
                    if(filterIsHazard) filterIsHazard.value = '';
                    const filterRoadNumber = document.getElementById('filterRoadNumber');
                    if(filterRoadNumber) filterRoadNumber.value = '';

                    console.log('重置筛选条件');
                    alert('重置筛选条件 (模拟)');
                });
            }


            // --- 辅助函数 (Copied from hazard_check_list.html) ---
            function populateViewModal(id) {
                console.log(`获取隐患ID=${id}的详情数据`);
                const modal = document.getElementById('viewModal');
                if (!modal) {
                    console.error("#viewModal not found!");
                    return;
                }

                // 调用获取隐患详情接口
                window.Http.get(`/risk/pitfalls/getInfo/${id}`).then(response => {
                    console.log('获取隐患详情成功：', response);
                    const data = response.data;
                    
                    // 映射风险等级显示
                    const riskLevelMap = {
                        '1': { text: '高', class: 'status-high' },
                        '2': { text: '中', class: 'status-medium' },
                        '3': { text: '低', class: 'status-low' },
                        'none': { text: '无风险', class: 'status-none' }
                    };

                    // 渲染数据到视图
                    const categoryEl = modal.querySelector('#view-category');
                    if (categoryEl) categoryEl.textContent = data.inspectType || '-';

                    const cityCountyEl = modal.querySelector('#view-city_county');
                    if (cityCountyEl) cityCountyEl.textContent = `${data.city || '-'} / ${data.district || '-'}`;

                    const orgUnitEl = modal.querySelector('#view-orgUnit');
                    if (orgUnitEl) orgUnitEl.textContent = data.units || '-';

                    const roadNumberEl = modal.querySelector('#view-roadNumber');
                    if (roadNumberEl) roadNumberEl.textContent = data.roadNum || '-';

                    const startStakeEl = modal.querySelector('#view-startStake');
                    if (startStakeEl) startStakeEl.textContent = data.pileStart || '-';

                    const endStakeEl = modal.querySelector('#view-endStake');
                    if (endStakeEl) endStakeEl.textContent = data.pileEnd || '-';

                    const riskLevelEl = modal.querySelector('#view-riskLevel');
                    if (riskLevelEl) {
                        const riskLevel = riskLevelMap[data.riskLevel] || riskLevelMap.none;
                        riskLevelEl.innerHTML = `<span class="status-badge ${riskLevel.class}">${riskLevel.text}</span>`;
                    }

                    const isHazardEl = modal.querySelector('#view-isHazard');
                    if (isHazardEl) {
                        isHazardEl.textContent = data.isMeasure == '1' ? '是' : '否';
                        if(data.isMeasure == '1') {
                            isHazardEl.classList.add('text-red-600');
                            isHazardEl.classList.remove('text-gray-500');
                        } else {
                            isHazardEl.classList.remove('text-red-600');
                            isHazardEl.classList.add('text-gray-500');
                        }
                    }

                    const measuresTakenEl = modal.querySelector('#view-measuresTaken');
                    if (measuresTakenEl) measuresTakenEl.textContent = data.isMeasure == '1' ? '是' : '否';

                    const riskDescriptionEl = modal.querySelector('#view-riskDescription');
                    if (riskDescriptionEl) riskDescriptionEl.textContent = data.remakes || '-';

                    const measuresEl = modal.querySelector('#view-measures');
                    if (measuresEl) measuresEl.textContent = data.measures || '尚未采取措施';

                    // 处理照片附件
                    const photosEl = modal.querySelector('#view-photos');
                    if (photosEl) {
                        if (data.sceneImg) {
                            const photoUrls = data.sceneImg.split(',');
                            photosEl.innerHTML = photoUrls.map(url => 
                                `<a href="${url}" target="_blank" class="hover:underline">${url.split('/').pop()}</a>`
                            ).join(', ');
                        } else {
                            photosEl.innerHTML = '无';
                        }
                    }

                    // 处理措施附件
                    const measureFilesEl = modal.querySelector('#view-measureFiles');
                    if (measureFilesEl) {
                        if (data.measureFiles) {
                            const fileUrls = data.measureFiles.split(',');
                            measureFilesEl.innerHTML = fileUrls.map(url => 
                                `<a href="${url}" target="_blank" class="hover:underline">${url.split('/').pop()}</a>`
                            ).join(', ');
                        } else {
                            measureFilesEl.innerHTML = '无';
                        }
                    }

                    const provincialRespEl = modal.querySelector('#view-provincialResp');
                    if (provincialRespEl) provincialRespEl.textContent = data.provinceUnit || '-';

                    const reviewRespEl = modal.querySelector('#view-reviewResp');
                    if (reviewRespEl) reviewRespEl.textContent = data.reviewUnit || '-';

                    const inspectRespEl = modal.querySelector('#view-inspectResp');
                    if (inspectRespEl) inspectRespEl.textContent = data.inspectUnit || '-';

                }).catch(error => {
                    console.error('获取隐患详情失败：', error);
                    alert('获取隐患详情失败，请稍后重试');
                });
            }

            function populateEditModal(id) {
                console.log(`获取隐患ID=${id}的详情数据用于编辑`);
                const currentHazardForm = document.getElementById('hazardForm');
                if (!currentHazardForm) {
                    console.error("#hazardForm not found!");
                    return;
                }
                currentHazardForm.reset();

                // 调用获取隐患详情接口
                window.Http.get(`/risk/pitfalls/getInfo/${id}`).then(response => {
                    console.log('获取隐患详情成功：', response);
                    const data = response.data;

                    // 填充表单数据
                    const modalCategoryEl = currentHazardForm.querySelector('#modalCategory');
                    if (modalCategoryEl) modalCategoryEl.value = data.inspectType || '';

                    const modalCityEl = currentHazardForm.querySelector('#modalCity');
                    if (modalCityEl) modalCityEl.value = data.city || '';

                    const modalAreaEl = currentHazardForm.querySelector('#modalArea');
                    if (modalAreaEl) modalAreaEl.value = data.district || '';

                    // 设置所属单位
                    const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) {
                        modalOrgAppInstance.data.selectedUnit = data.units || null;
                    }

                    const modalRoadNumberEl = currentHazardForm.querySelector('#modalRoadNumber');
                    if (modalRoadNumberEl) modalRoadNumberEl.value = data.roadNum || '';

                    const modalStartStakeEl = currentHazardForm.querySelector('#modalStartStake');
                    if (modalStartStakeEl) modalStartStakeEl.value = data.pileStart || '';

                    const modalEndStakeEl = currentHazardForm.querySelector('#modalEndStake');
                    if (modalEndStakeEl) modalEndStakeEl.value = data.pileEnd || '';

                    const modalRiskDescriptionEl = currentHazardForm.querySelector('#modalRiskDescription');
                    if (modalRiskDescriptionEl) modalRiskDescriptionEl.value = data.remakes || '';

                    const modalRiskLevelEl = currentHazardForm.querySelector('#modalRiskLevel');
                    if (modalRiskLevelEl) modalRiskLevelEl.value = data.riskLevel || '';

                    // 设置是否已采取措施
                    const isMeasureRadios = currentHazardForm.querySelectorAll('input[name="isMeasure"]');
                    isMeasureRadios.forEach(radio => {
                        if (radio.value === String(data.isMeasure)) {
                            radio.checked = true;
                        }
                    });

                    const modalMeasuresEl = currentHazardForm.querySelector('#modalMeasures');
                    if (modalMeasuresEl) modalMeasuresEl.value = data.measures || '';

                    // 设置省级责任单位
                    const modalProvincialRespAppInstance = document.getElementById('modalProvincialRespApp')?.__vue_app__?._instance;
                    if (modalProvincialRespAppInstance) {
                        modalProvincialRespAppInstance.data.selectedProvincialResp = data.provinceUnit || null;
                    }

                    // 设置复核责任单位
                    const modalReviewRespAppInstance = document.getElementById('modalReviewRespApp')?.__vue_app__?._instance;
                    if (modalReviewRespAppInstance) {
                        modalReviewRespAppInstance.data.selectedReviewResp = data.reviewUnit || null;
                    }

                    // 设置排查责任单位
                    const modalInspectRespAppInstance = document.getElementById('modalInspectRespApp')?.__vue_app__?._instance;
                    if (modalInspectRespAppInstance) {
                        modalInspectRespAppInstance.data.selectedInspectResp = data.inspectUnit || null;
                    }

                    // 设置项目
                    const modelProjectEl = currentHazardForm.querySelector('#modelProject');
                    if (modelProjectEl) modelProjectEl.value = data.projectId || '';

                    // 处理现场照片
                    clearUploadedFiles('uploadedFilesList');
                    if (data.sceneImg) {
                        const filesList = document.getElementById('uploadedFilesList');
                        if (filesList) {
                            data.sceneImg.split(',').forEach(url => {
                                const fileName = url.split('/').pop();
                                filesList.innerHTML += `
                                    <div class="upload-list-item">
                                        <span>${fileName}</span>
                                        <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>
                                    </div>`;
                            });
                        }
                    }

                    // 处理措施附件
                    clearUploadedFiles('uploadedMeasureFilesList');
                    if (data.measureFiles) {
                        const measureFilesList = document.getElementById('uploadedMeasureFilesList');
                        if (measureFilesList) {
                            data.measureFiles.split(',').forEach(url => {
                                const fileName = url.split('/').pop();
                                measureFilesList.innerHTML += `
                                    <div class="upload-list-item">
                                        <span>${fileName}</span>
                                        <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>
                                    </div>`;
                            });
                        }
                    }

                }).catch(error => {
                    console.error('获取隐患详情失败：', error);
                    alert('获取隐患详情失败，请稍后重试');
                });
            }

            function displayUploadedFiles(files, listElementId) {
                const listElement = document.getElementById(listElementId);
                if (listElement) {
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        const listItem = document.createElement('div');
                        listItem.className = 'upload-list-item';
                        listItem.innerHTML = `<span>${file.name}</span> <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>`;
                        listElement.appendChild(listItem);
                    }
                } else {
                    console.error(`${listElementId} not found for displayUploadedFiles`);
                }
            }

            function clearUploadedFiles(listElementId) {
                 const listElement = document.getElementById(listElementId);
                 if (listElement) {
                    listElement.innerHTML = '';
                 } else {
                    console.error(`${listElementId} not found for clearUploadedFiles`);
                 }
            }
            // --- Content from hazard_check_list.html JavaScript END ---

            // --- Content for rectification_task_list.html JavaScript START ---
            // Define unit options (ensure this is available or adapt as needed)
            // If standardUnitOptions is the same as unitData, we can reuse unitData.
            // For now, let's assume they might be distinct for clarity during integration.
            const rectificationUnitOptions = [
                { value: '1', label: '广西壮族自治区交通运输厅', children: [
                    { value: '1.1', label: '直属事业单位及专项机构', children: [
                        { value: '1.1.1', label: '自治区公路发展中心' },
                        { value: '1.1.2', label: '自治区高速公路发展中心' },
                        { value: '1.1.3', label: '自治区道路运输发展中心' }
                    ]},
                    { value: '1.2', label: '市级交通运输局', children: [
                        { value: '1.2.1', label: '钦州市交通运输局' },
                        { value: '1.2.2', label: '南宁市交通运输局' },
                        { value: '1.2.3', label: '玉林市交通运输局' }
                    ]}
                ]}
            ];

            // Vue for Filter Org Unit (Rectification List)
            if (document.getElementById('filterRectificationOrgApp')) {
                const filterRectificationOrgApp = Vue.createApp({
                    data() {
                        return {
                            selectedUnit: null,
                            unitOptions: rectificationUnitOptions
                        }
                     },
                    methods: {
                        handleFilterOrgChange(value) {
                            console.log('Rectification Filter Org Unit:', value);
                        }
                     }
                }).use(ElementPlus);
                filterRectificationOrgApp.mount('#filterRectificationOrgApp');
            }

            // Vue for Filter Date Range (Rectification List)
            if (document.getElementById('filterRectificationDateApp')) {
                const filterRectificationDateApp = Vue.createApp({
                    data() { return { deadlineRange: [] } },
                    methods: { handleDateChange(value) { console.log('Rectification Filter Deadline Range:', value); } }
                }).use(ElementPlus);
                filterRectificationDateApp.mount('#filterRectificationDateApp');
            }

            // Vue for Modal Org Unit (Rectification Task Modal)
            if (document.getElementById('modalRectificationOrgApp')) {
                const modalRectificationOrgApp = Vue.createApp({
                    data() {
                        return {
                            selectedUnit: null,
                            unitOptions: []
                        }
                     },
                    methods: {
                        handleModalOrgChange(value) {
                            console.log('Rectification Modal Org Unit:', value);
                        }
                    }
                }).use(ElementPlus);
                modalRectificationOrgApp.mount('#modalRectificationOrgApp');
            }

            // Vue for Modal Deadline (Rectification Task Modal)
            if (document.getElementById('modalRectificationDateApp')) {
                const modalRectificationDateApp = Vue.createApp({
                    data() { return { deadline: '' } }
                }).use(ElementPlus);
                modalRectificationDateApp.mount('#modalRectificationDateApp');
            }

            const rectificationTaskModal = document.getElementById('rectificationTaskModal');
            const deleteRectificationConfirmModal = document.getElementById('deleteRectificationConfirmModal');
            const rectificationTaskForm = document.getElementById('rectificationTaskForm');
            const rectificationModalTitle = document.getElementById('rectificationModalTitle');
            let currentRectificationTaskId = null;
            let rectificationTaskIdToDelete = null;

            // Open/Close for Rectification Modals (can reuse generic openModal/closeModal if IDs are passed)
            // For clarity, specific close buttons might exist: btn-close-rectification-modal
            document.querySelectorAll('.btn-close-rectification-modal').forEach(button => {
                button.addEventListener('click', () => {
                    // Determine which modal to close based on parent or specific ID
                    if (button.closest('#rectificationTaskModal')) {
                        closeModal(rectificationTaskModal); // Assumes closeModal can take a DOM element
                    }
                    if (button.closest('#deleteRectificationConfirmModal')) {
                        closeModal(deleteRectificationConfirmModal);
                    }
                });
            });

            // Event listener for rectification list table buttons
            const rectificationListTableBody = document.getElementById('rectificationListTableBody');
            if (rectificationListTableBody) {
                rectificationListTableBody.addEventListener('click', (event) => {
                    const target = event.target.closest('button, a');
                    if (!target) return;
                    const id = target.getAttribute('data-id');

                    if (target.classList.contains('btn-view-rectification') || target.classList.contains('btn-edit-rectification')) {
                        console.log('View/Edit Rectification Task ID:', id);
                        currentRectificationTaskId = id;
                        populateRectificationTaskModal(id);
                        openModal(rectificationTaskModal); // Assumes openModal can take a DOM element
                    } else if (target.classList.contains('btn-complete-rectification')) {
                        console.log('Complete Rectification Task ID:', id);
                        if (confirm(`确认将整改任务 ${id} 标记为完成吗?`)) {
                            alert(`任务 ${id} 已标记为完成 (模拟)`);
                            // TODO: Send request to mark task as complete & Refresh list or update row status
                        }
                    } else if (target.classList.contains('btn-delete-rectification')) {
                        console.log('Delete Rectification Task ID:', id);
                        rectificationTaskIdToDelete = id; // 设置要删除的任务ID
                        openModal(deleteRectificationConfirmModal);
                    } else if (target.classList.contains('btn-view-associated-hazard')) {
                        event.preventDefault();
                        const hazardId = target.getAttribute('data-hazard-id');
                        console.log('View Associated Hazard ID:', hazardId);
                        alert(`查看关联的隐患详情 ID: ${hazardId} (功能待实现)`);
                    }
                });
            }

            // Add New Rectification Task Button
            const btnAddRectificationTask = document.getElementById('btnAddRectificationTask');
            if (btnAddRectificationTask) {
                btnAddRectificationTask.addEventListener('click', () => {
                    currentRectificationTaskId = null;
                    if(rectificationModalTitle) rectificationModalTitle.textContent = '添加整改任务';
                    if(rectificationTaskForm) rectificationTaskForm.reset();
                    clearUploadedFiles('uploadedRectificationTaskFilesList');

                    const modalOrgAppInstance = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) modalOrgAppInstance.data.selectedUnit = null;

                    const modalDateAppInstance = document.getElementById('modalRectificationDateApp')?.__vue_app__?._instance;
                    if (modalDateAppInstance) modalDateAppInstance.data.deadline = '';

                    const taskIdSpan = document.getElementById('view-rectificationTaskId');
                    if(taskIdSpan) taskIdSpan.textContent = '系统自动生成';
                    const hazardLink = document.getElementById('view-rectificationHazardLink');
                    if(hazardLink) {
                        hazardLink.textContent = '请在下方选择或关联';
                        hazardLink.removeAttribute('href');
                    }
                    openModal(rectificationTaskModal);
                });
            }

            // Save Rectification Task button
            const btnSaveRectificationTask = document.getElementById('btnSaveRectificationTask');
            if (btnSaveRectificationTask) {
                btnSaveRectificationTask.addEventListener('click', () => {
                    if (!rectificationTaskForm) return;
                    
                    // 获取表单数据
                    const formData = new FormData(rectificationTaskForm);
                    const data = Object.fromEntries(formData.entries());
                    
                    // 获取所属单位
                    const modalOrgAppInstance = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance?.data.selectedUnit) {
                        data.dutyUnit = modalOrgAppInstance.data.selectedUnit;
                    }

                    // 获取截止日期
                    const modalDateAppInstance = document.getElementById('modalRectificationDateApp')?.__vue_app__?._instance;
                    if (modalDateAppInstance?.data.deadline) {
                        data.entTime = modalDateAppInstance.data.deadline;
                    }

                    // 处理上传的文件
                    if (uploadedRectificationFiles && uploadedRectificationFiles.length > 0) {
                        data.fileUrls = uploadedRectificationFiles.map(file => file.url || '').filter(url => url).join(',');
                    } else {
                        data.fileUrls = '';
                    }

                    console.log('保存整改任务数据:', data);

                    // 发送请求
                    const url = currentRectificationTaskId ? '/risk/modifyTask/edit' : '/risk/modifyTask/add';
                    if (currentRectificationTaskId) {
                        data.id = currentRectificationTaskId;
                    }

                    window.Http.post(url, data)
                        .then(response => {
                            console.log('保存整改任务成功:', response);
                            alert('保存成功');
                            closeModal(rectificationTaskModal);
                            location.reload(); // 刷新页面
                        })
                        .catch(error => {
                            console.error('保存整改任务失败:', error);
                            alert('保存失败：' + (error.message || '请稍后重试'));
                        });
                });
            }

            // Confirm Delete (Rectification Task) button
            const btnConfirmRectificationDelete = document.getElementById('btnConfirmRectificationDelete');
            if (btnConfirmRectificationDelete) {
                btnConfirmRectificationDelete.addEventListener('click', async () => {
                    if (rectificationTaskIdToDelete) {
                        try {
                            console.log('发送删除请求，ID:', rectificationTaskIdToDelete);
                            const response = await window.Http.post('/risk/pitfalls/remove', { id: rectificationTaskIdToDelete });
                            console.log('删除成功：', response);
                            // 删除成功后移除对应的表格行
                            const row = document.querySelector(`button.btn-delete-rectification[data-id="${rectificationTaskIdToDelete}"]`).closest('tr');
                            if (row) row.remove();
                            alert('删除成功');
                            rectificationTaskIdToDelete = null;
                            closeModal(deleteRectificationConfirmModal);
                        } catch (error) {
                            console.error('删除失败:', error);
                            alert('删除失败，请稍后重试');
                        }
                    }
                });
            }

            // Filter buttons (Rectification List)
            const btnRectificationFilter = document.getElementById('btnRectificationFilter');
            if (btnRectificationFilter) {
                btnRectificationFilter.addEventListener('click', () => {
                    const filterOrgAppInstance = document.getElementById('filterRectificationOrgApp')?.__vue_app__?._instance;
                    const filterDateAppInstance = document.getElementById('filterRectificationDateApp')?.__vue_app__?._instance;
                    const filterData = {
                        status: document.getElementById('filterTaskStatus')?.value,
                        unit: filterOrgAppInstance ? filterOrgAppInstance.data.selectedUnit : null,
                        deadlineStart: filterDateAppInstance?.deadlineRange ? filterDateAppInstance.deadlineRange[0] : '',
                        deadlineEnd: filterDateAppInstance?.deadlineRange ? filterDateAppInstance.deadlineRange[1] : ''
                    };
                    console.log('Apply Rectification Filter:', filterData);
                    alert('应用整改任务筛选条件 (模拟)');
                    // TODO: Apply filter and refresh list
                });
            }

            const btnRectificationResetFilter = document.getElementById('btnRectificationResetFilter');
            if (btnRectificationResetFilter) {
                btnRectificationResetFilter.addEventListener('click', () => {
                    const filterTaskStatusEl = document.getElementById('filterTaskStatus');
                    if(filterTaskStatusEl) filterTaskStatusEl.value = '';

                    const filterOrgAppInstance = document.getElementById('filterRectificationOrgApp')?.__vue_app__?._instance;
                    if(filterOrgAppInstance) filterOrgAppInstance.data.selectedUnit = null;

                    const filterDateAppInstance = document.getElementById('filterRectificationDateApp')?.__vue_app__?._instance;
                    if(filterDateAppInstance) filterDateAppInstance.deadlineRange = [];

                    console.log('Reset Rectification Filter');
                    alert('重置整改任务筛选条件 (模拟)');
                    // TODO: Reset filter and refresh list
                });
            }

            function populateRectificationTaskModal(taskId) {
                console.log(`获取整改任务ID=${taskId}的详情数据`);
                if(rectificationModalTitle) rectificationModalTitle.textContent = '编辑整改任务';
                if(rectificationTaskForm) rectificationTaskForm.reset();
                clearUploadedFiles('uploadedRectificationTaskFilesList');
                uploadedRectificationFiles = []; // 清空已上传文件数组

                // 获取整改任务详情
                window.Http.get(`/risk/modifyTask/${taskId}`).then(response => {
                    console.log('获取整改任务详情成功：', response);
                    const data = response.data;

                    // 填充表单数据
                    const taskIdInput = document.getElementById('rectificationTaskId');
                    if(taskIdInput) taskIdInput.value = taskId;
                    
                    const viewTaskIdSpan = document.getElementById('view-rectificationTaskId');
                    if(viewTaskIdSpan) viewTaskIdSpan.textContent = taskId;

                    // 设置关联隐患
                    const modalPitfallId = document.getElementById('modalPitfallId');
                    if(modalPitfallId) modalPitfallId.value = data.pitfallsId || '';

                    // 设置责任单位
                    const modalOrgAppInstance = document.getElementById('modalRectificationOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) modalOrgAppInstance.data.selectedUnit = data.dutyUnit || null;

                    // 设置责任人
                    const respPersonInput = document.getElementById('modalRectificationRespPerson');
                    if(respPersonInput) respPersonInput.value = data.dutyBy || '';

                    // 设置截止日期
                    const modalDateAppInstance = document.getElementById('modalRectificationDateApp')?.__vue_app__?._instance;
                    if(modalDateAppInstance) {
                        // 强制更新Vue的响应式数据
                        modalDateAppInstance.data.deadline = null; // 先清空
                        setTimeout(() => {
                            modalDateAppInstance.data.deadline = data.entTime ? data.entTime.split(' ')[0] : '';
                            console.log('设置整改期限:', modalDateAppInstance.data.deadline);
                        }, 0);
                    }

                    // 设置任务状态
                    const taskStatusSelect = document.getElementById('modalRectificationTaskStatus');
                    if(taskStatusSelect) taskStatusSelect.value = data.status || '0';

                    // 设置备注
                    const remarksTextarea = document.getElementById('modalRectificationRemarks');
                    if(remarksTextarea) remarksTextarea.value = data.remarks || '';

                    // 处理已上传的文件 - 修改这部分
                    console.log('整改任务详情数据:', data);
                    const filesList = document.getElementById('uploadedRectificationTaskFilesList');
                    if (filesList) {
                        filesList.innerHTML = ''; // 清空现有内容
                        
                        // 检查fileUrls字段
                        if (data.fileUrls) {
                            console.log('处理整改附件数据:', data.fileUrls);
                            let files = [];
                            
                            try {
                                // 尝试处理不同格式的数据
                                if (typeof data.fileUrls === 'string') {
                                    files = data.fileUrls.split(',');
                                } else if (Array.isArray(data.fileUrls)) {
                                    files = data.fileUrls;
                                }
                                
                                files.forEach(file => {
                                    let fileUrl = file;
                                    let fileName = fileUrl.split('/').pop();
                                    
                                    filesList.innerHTML += `
                                        <div class="upload-list-item">
                                            <span>${fileName}</span>
                                            <button type="button" class="text-red-500 text-xs" onclick="this.parentNode.remove()">删除</button>
                                        </div>`;
                                    
                                    uploadedRectificationFiles.push({
                                        url: fileUrl,
                                        newFileName: fileName
                                    });
                                });
                                
                                console.log('处理后的整改附件数据:', uploadedRectificationFiles);
                            } catch (e) {
                                console.error('处理整改附件数据失败:', e);
                            }
                        }
                    }

                }).catch(error => {
                    console.error('获取整改任务详情失败：', error);
                    alert('获取整改任务详情失败，请稍后重试');
                });
            }

            // 添加渲染整改任务列表的函数
            function renderRectificationList(list) {
                const tableBody = document.getElementById('rectificationListTableBody');
                if (!tableBody) return;

                // 清空现有内容
                tableBody.innerHTML = '';

                // 遍历数据并渲染
                list.forEach((item) => {
                    // 定义状态映射
                    const statusMap = {
                        '0': { text: '待处理', class: 'status-pending' },
                        '1': { text: '整改中', class: 'status-progress' },
                        '2': { text: '已完成', class: 'status-completed' },
                        'overdue': { text: '已逾期', class: 'status-overdue' }
                    };

                    // 检查是否逾期
                    const currentDate = new Date();
                    const deadline = new Date(item.entTime);
                    const status = currentDate > deadline && item.status !== '2' ? 'overdue' : item.status;

                    const row = document.createElement('tr');
                    row.className = 'hover:bg-gray-50';
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.id || '-'}</td>
                        <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs">
                            <a href="#" class="text-blue-600 hover:underline btn-view-associated-hazard" 
                               data-hazard-id="${item.pitfallsId}" 
                               title="ID:${item.pitfallsId}">
                               隐患ID:${item.pitfallsId}
                            </a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.dutyUnit || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.dutyBy || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.createTime || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${item.entTime || '-'}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="status-badge ${statusMap[status]?.class || 'status-pending'}">
                                ${statusMap[status]?.text || '未知'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                            <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-view-rectification" 
                                    data-id="${item.id}" title="查看">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-indigo-600 hover:text-indigo-800 focus:outline-none mr-2 btn-edit-rectification" 
                                    data-id="${item.id}" title="编辑/更新状态">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-800 focus:outline-none mr-2 btn-complete-rectification" 
                                    data-id="${item.id}" title="标记完成">
                                <i class="fas fa-check-circle"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 focus:outline-none btn-delete-rectification" 
                                    data-id="${item.id}" title="删除">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </td>
                    `;
                    tableBody.appendChild(row);
                });

                // 更新分页信息
                const paginationInfo = document.querySelector('.text-sm.text-gray-700');
                if (paginationInfo) {
                    paginationInfo.innerHTML = `显示 <span class="font-medium">1</span> 到 <span class="font-medium">${list.length}</span> 条，共 <span class="font-medium">${list.length}</span> 条记录`;
                }
            }
            // --- Content for rectification_task_list.html JavaScript END ---

            // 添加坐标地址输入框的事件监听器
            document.getElementById('modalCoordinate')?.addEventListener('blur', async function() {
                const address = this.value.trim();
                if (address) {
                    try {
                        const response = await window.Http.post(`/map/getLonAndLatByAddress?address=${address}`);
                        if (response.data) {
                            document.getElementById('modalLat').value = response.data.lat;
                            // 接口返回的是lng，我们需要将它赋值给lot字段
                            document.getElementById('modalLng').value = response.data.lng;
                        }
                    } catch (error) {
                        console.error('获取经纬度失败:', error);
                    }
                }
            });
        });
    </script>
</body>
</html>