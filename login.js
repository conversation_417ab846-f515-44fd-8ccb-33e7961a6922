// 登录页面脚本
const app = Vue.createApp({
    data() {
        return {
            username: 'admin',
            password: 'Tocc@_5852',
            imgUrl: '',
            uuId: '',
            code: '',
        }
    },
    mounted() {
        this.getImgUrl()
    },
    methods: {
        getImgUrl() {
            window.Http.get('/captchaImage')
            .then(res => {
                this.imgUrl = `data:image/gif;base64,${res.img}`
                this.uuId = res.uuid
            })
        },
        login() {
            window.Http.post('/login', {
                username: this.username,
                password: this.password,
                uuid: this.uuId,
                code: this.code
            })
            .then(res => {
                if (res.code === 200) {
                    window.localStorage.setItem('token', res.token)
                    window.location.href= 'risk-map.html'
                } else {
                    alert(res.msg);
                    this.getImgUrl()
                }
            })
        }
    }
});
app.mount('#loginContent');
// document.addEventListener('DOMContentLoaded', function() {
//     // 生成初始验证码
//     generateCaptcha();

//     // 登录表单提交处理
//     const loginForm = document.getElementById('loginForm');
//     if (loginForm) {
//         loginForm.addEventListener('submit', function(e) {
//             e.preventDefault();

//             // 显示登录中提示
//             showMessage('登录中...', 'info');

//             // 模拟网络延迟
//             setTimeout(function() {
//                 // 直接跳转到风险一张图页面
//                 window.location.href = 'risk-map.html';
//             }, 800);
//         });
//     }

//     // 生成随机验证码
//     function generateCaptcha() {
//         const captchaDisplay = document.querySelector('.bg-gray-600');
//         if (captchaDisplay) {
//             const captchaCode = Math.floor(1000 + Math.random() * 9000);
//             captchaDisplay.textContent = captchaCode;

//             // 点击验证码刷新
//             captchaDisplay.addEventListener('click', function() {
//                 const newCaptcha = Math.floor(1000 + Math.random() * 9000);
//                 this.textContent = newCaptcha;
//             });
//         }
//     }

//     // 消息提示函数
//     function showMessage(message, type = 'info') {
//         // 检查是否已存在消息元素
//         let messageElement = document.querySelector('.login-message');

//         if (!messageElement) {
//             // 创建消息元素
//             messageElement = document.createElement('div');
//             messageElement.className = 'login-message fade-in';
//             document.querySelector('.right-section').appendChild(messageElement);
//         }

//         // 设置消息类型样式
//         messageElement.className = 'login-message fade-in';
//         if (type === 'error') {
//             messageElement.classList.add('error');
//         } else if (type === 'success') {
//             messageElement.classList.add('success');
//         } else {
//             messageElement.classList.add('info');
//         }

//         // 设置消息内容
//         messageElement.textContent = message;

//         // 自动隐藏消息
//         setTimeout(function() {
//             messageElement.classList.add('fade-out');
//             setTimeout(function() {
//                 if (messageElement.parentNode) {
//                     messageElement.parentNode.removeChild(messageElement);
//                 }
//             }, 500);
//         }, 3000);
//     }
// });
