<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急物资 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">
    <!-- 顶部导航栏容器 -->
    <div id="navigation-container"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow p-6 bg-gray-100 min-h-screen">
            <!-- 原有的主要内容保持不变 -->
            <div class="py-6">
                <!-- 页面标题部分 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">应急物资</h2>
                        <p class="text-gray-600 mt-1">管理和查看应急物资信息</p>
                    </div>
                </div>

                <!-- 页面标题 -->
                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 for spacing from tab nav -->
                    <div>
                        <h3 class="text-xl font-semibold text-gray-700">仓库列表与维护</h3>
                        <p class="text-gray-500 mt-1 text-sm">管理应急物资存放仓库的基础信息</p>
                    </div>
                    <div>
                        <button id="btnAddWarehouse" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-2"></i> 新增仓库
                        </button>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                        <div>
                            <label for="warehouse_name_filter" class="block text-sm font-medium text-gray-700 mb-1">仓库名称</label>
                            <input type="text" id="warehouse_name_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入仓库名称">
                        </div>
                        <div>
                            <label for="warehouse_org_unit_filter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                            <div id="warehouse-filter-app"> <!-- MODIFIED ID -->
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    @change="handleChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="warehouse_type_filter" class="block text-sm font-medium text-gray-700 mb-1">仓库类型</label>
                            <select id="warehouse_type_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">全部</option>
                                <option value="1">中心库</option>
                                <option value="2">区域库</option>
                                <option value="3">前置库</option>
                            </select>
                        </div>
                        <div>
                            <button type="button" id="btnSearchWarehouse" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                <i class="fas fa-search mr-1"></i> 搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 仓库列表表格 -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">路段编号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起始桩号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库地址</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 表格行示例 -->
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">南宁市应急物资中心库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">南宁市应急管理局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G7211</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K15+200</td>
                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">南宁市青秀区民族大道100号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">中心库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">王主任</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">138xxxxxxxx</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="1" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="1" title="删除"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">钦州港应急物资前置库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">钦州市交通运输局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">G75</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">K2100+000</td>
                                    <td class="px-6 py-4 text-sm text-gray-600 truncate max-w-xs">钦州市钦南区港口路1号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">前置库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">李科长</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">139xxxxxxxx</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900 btn-edit-warehouse" data-id="2" title="编辑"><i class="fas fa-edit"></i></button>
                                        <button class="text-red-600 hover:text-red-900 btn-delete-warehouse" data-id="2" title="删除"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                                <!-- 更多行... -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                        </div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left h-5 w-5"></i>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right h-5 w-5"></i>
                            </a>
                        </nav>
                    </div>
                </div>

                <!-- 添加/编辑仓库 Modal -->
                <div id="warehouse-modal-app"> <!-- MODIFIED ID -->
                    <el-dialog
                        v-model="dialogVisible"
                        :title="isEditMode ? '编辑仓库信息' : '新增仓库'"
                        width="50%"
                        @closed="resetForm"
                        :close-on-click-modal="false"
                    >
                        <el-form :model="warehouseForm" ref="warehouseFormRef" label-width="100px" label-position="right">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="仓库名称" required prop="name">
                                        <el-input v-model="warehouseForm.name" placeholder="请输入仓库名称"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="仓库类型" required prop="type">
                                        <el-select v-model="warehouseForm.type" placeholder="请选择仓库类型" style="width: 100%;">
                                            <el-option label="中心库" value="1"></el-option>
                                            <el-option label="区域库" value="2"></el-option>
                                            <el-option label="前置库" value="3"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item label="所属单位" required prop="orgUnit">
                                <el-tree-select
                                    v-model="warehouseForm.orgUnit"
                                    :data="unitOptions"
                                    :multiple="false"
                                    :check-strictly="true"
                                    placeholder="请选择所属单位"
                                    style="width: 100%;"
                                    clearable
                                />
                            </el-form-item>
                            <el-form-item label="仓库地址" required prop="address">
                                <el-input v-model="warehouseForm.address" placeholder="请输入仓库详细地址"></el-input>
                            </el-form-item>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="负责人" required prop="manager">
                                        <el-input v-model="warehouseForm.manager" placeholder="请输入负责人姓名"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="联系电话" required prop="phone">
                                        <el-input v-model="warehouseForm.phone" placeholder="请输入负责人联系电话"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="路段编号" required prop="roadSection">
                                        <el-select v-model="warehouseForm.roadSection" placeholder="请选择路段编号" style="width: 100%;">
                                            <el-option label="G7211" value="G7211"></el-option>
                                            <el-option label="G75" value="G75"></el-option>
                                            <el-option label="G80" value="G80"></el-option>
                                            <el-option label="其他" value="other"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="起始桩号" required prop="startStake">
                                        <el-input v-model="warehouseForm.startStake" placeholder="例如: K15+200"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-form-item label="备注" prop="remarks">
                                <el-input type="textarea" :rows="3" v-model="warehouseForm.remarks" placeholder="请输入备注信息"></el-input>
                            </el-form-item>
                        </el-form>
                        <template #footer>
                            <span class="dialog-footer">
                                <el-button @click="dialogVisible = false">取消</el-button>
                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                            </span>
                        </template>
                    </el-dialog>
                </div>

                <!-- 页面标题和操作按钮 -->
                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                    <div>
                        <h3 class="text-xl font-semibold text-gray-700">应急物资列表与维护</h3>
                        <p class="text-gray-500 mt-1 text-sm">管理应急物资库存信息，支持搜索、筛选和库存维护</p>
                    </div>
                    <div class="flex">
                        <button id="btnExportMaterials" class="mr-3 bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-file-export mr-2"></i> 导出
                        </button>
                        <button id="btnAddMaterial" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-plus mr-2"></i> 新增物资
                        </button>
                    </div>
                </div>

                <!-- 搜索和筛选区域 -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="material_name_filter" class="block text-sm font-medium text-gray-700 mb-1">物资名称</label>
                            <input type="text" id="material_name_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入物资名称关键字">
                        </div>
                        <div>
                            <label for="material_model_filter" class="block text-sm font-medium text-gray-700 mb-1">规格型号</label>
                            <input type="text" id="material_model_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入规格型号">
                        </div>
                        <div>
                            <label for="material_type_filter" class="block text-sm font-medium text-gray-700 mb-1">物资类别</label>
                            <select id="material_type_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">所有类别</option>
                                <option value="1">防疫物资</option>
                                <option value="2">防洪物资</option>
                                <option value="3">消防物资</option>
                                <option value="4">救灾物资</option>
                                <option value="5">通信设备</option>
                                <option value="6">应急装备</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-4">
                        <div>
                            <label for="material_warehouse_filter" class="block text-sm font-medium text-gray-700 mb-1">所属仓库</label>
                            <select id="material_warehouse_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">所有仓库</option>
                                <option value="1">中心仓库</option>
                                <option value="2">东区仓库</option>
                                <option value="3">南区仓库</option>
                                <option value="4">西区仓库</option>
                                <option value="5">北区仓库</option>
                            </select>
                        </div>
                        <div>
                            <label for="material_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                            <select id="material_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">所有状态</option>
                                <option value="1">正常</option>
                                <option value="2">维修中</option>
                                <option value="3">报废</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <div class="flex justify-end w-full">
                                <button id="btnResetMaterialFilters" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm mr-2 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                    <i class="fas fa-redo mr-1"></i> 重置
                                </button>
                                <button id="btnSearchMaterials" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <i class="fas fa-search mr-1"></i> 查询
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 物资列表表格 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden mb-6">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资类别</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属仓库</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">医用防护口罩</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">N95</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防疫物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中心仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">个</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-12-31</td>
                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="标准N95防护级别">标准N95防护级别</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="1">修改</button>
                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="1">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">救生衣</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">成人通用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防洪物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">东区仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">件</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常</span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2026-06-30</td>
                                    <td class="px-6 py-4 text-sm text-gray-500 truncate max-w-xs" title="符合国标GB25804">符合国标GB25804</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 btn-edit-material" data-id="2">修改</button>
                                        <button class="text-red-600 hover:text-red-800 btn-delete-material" data-id="2">删除</button>
                                    </td>
                                </tr>
                                <!-- 更多物资行... -->
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                        </div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <span class="sr-only">上一页</span>
                                <i class="fas fa-chevron-left h-5 w-5"></i>
                            </a>
                            <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                                <span class="sr-only">下一页</span>
                                <i class="fas fa-chevron-right h-5 w-5"></i>
                            </a>
                        </nav>
                    </div>
                </div>

                <!-- 新增/编辑物资 Modal -->
                <div id="material-modal-app"> <!-- MODIFIED ID -->
                    <el-dialog
                        v-model="dialogVisible"
                        :title="isEditMode ? '编辑物资信息' : '新增物资'"
                        width="60%"
                        @closed="resetForm"
                        :close-on-click-modal="false"
                    >
                        <el-form :model="materialForm" ref="materialFormRef" label-width="100px" label-position="right">
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="物资名称" required prop="name">
                                        <el-input v-model="materialForm.name" placeholder="请输入物资名称"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="规格型号" prop="model">
                                        <el-input v-model="materialForm.model" placeholder="请输入规格型号"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="12">
                                    <el-form-item label="物资类别" required prop="type">
                                        <el-select v-model="materialForm.type" placeholder="请选择物资类别" style="width: 100%;">
                                            <el-option label="防疫物资" value="1"></el-option>
                                            <el-option label="防洪物资" value="2"></el-option>
                                            <el-option label="消防物资" value="3"></el-option>
                                            <el-option label="救灾物资" value="4"></el-option>
                                            <el-option label="通信设备" value="5"></el-option>
                                            <el-option label="应急装备" value="6"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="所属仓库" required prop="warehouseId">
                                        <el-select v-model="materialForm.warehouseId" placeholder="请选择所属仓库" style="width: 100%;">
                                            <el-option label="中心仓库" value="1"></el-option>
                                            <el-option label="东区仓库" value="2"></el-option>
                                            <el-option label="南区仓库" value="3"></el-option>
                                            <el-option label="西区仓库" value="4"></el-option>
                                            <el-option label="北区仓库" value="5"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item label="数量" required prop="quantity">
                                        <el-input-number v-model="materialForm.quantity" :min="0" placeholder="请输入数量" style="width: 100%;"></el-input-number>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="单位" required prop="unit">
                                        <el-input v-model="materialForm.unit" placeholder="例如: 个, 件, 套"></el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="8">
                                    <el-form-item label="状态" required prop="status">
                                        <el-select v-model="materialForm.status" placeholder="请选择状态" style="width: 100%;">
                                            <el-option label="正常" value="1"></el-option>
                                            <el-option label="维修中" value="2"></el-option>
                                            <el-option label="报废" value="3"></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row :gutter="20">
                                <el-col :span="8">
                                    <el-form-item label="有效期" prop="expiryDate">
                                        <el-date-picker
                                            v-model="materialForm.expiryDate"
                                            type="date"
                                            placeholder="选择有效期"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            style="width: 100%;">
                                        </el-date-picker>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="16">
                                    <el-form-item label="备注" prop="remarks">
                                        <el-input type="textarea" :rows="1" v-model="materialForm.remarks" placeholder="请输入备注信息"></el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                        <template #footer>
                            <span class="dialog-footer">
                                <el-button @click="closeModal">取消</el-button>
                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                            </span>
                        </template>
                    </el-dialog>
                </div>
            </div>
        </main>
    </div>

    <!-- Load Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
    <script>
        // 初始化导航栏
        window.NavigationComponent.init('system-management');
    </script>

    <!-- 原有的页面特定脚本保持不变 -->
    // ... 保持原有脚本不变 ...

</body>
</html>
