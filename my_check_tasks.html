<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的检查任务 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
         html, body {
            height: 100%;
            margin: 0;
        }
         body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* Add specific styles if needed */
         .el-tree-select, .el-select, .el-date-picker {
             width: 100% !important;
         }
          .el-upload-list__item, .el-upload--picture-card {
             width: 100px !important;
             height: 100px !important;
             line-height: 100px !important;
         }
    </style>
</head>
<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
<script src="./request/index.js"></script>
<body class="bg-gray-100 flex flex-col min-h-screen">

    <!-- Navbar Start -->
    <!-- <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button active">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header> -->
    <!-- Navbar End -->
     <!-- 顶部导航栏容器 -->
     <div id="navigation-container"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow p-6 bg-gray-100 min-h-screen">
            <div class="py-6">
                <!-- 页面标题部分 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">我的检查任务</h2>
                        <p class="text-gray-600 mt-1">查看分配给您的检查任务并进行填报</p>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查类别</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下发单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200" id="taskTableBody">
                                <!-- 任务列表将通过 JavaScript 动态渲染 -->
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <p class="text-sm text-gray-700">分页占位符</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 风险隐患填报 Modal (Copied from hazard_check_list.html) -->
    <div id="hazardModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-start justify-center pt-[150px] hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">风险隐患填报</h3> <!-- Title changed -->
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="hazardForm">
                    <!-- Hidden input to store task id -->
                    <input type="hidden" id="modalTaskId" name="task_id">
                    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                       <p class="text-sm font-medium text-blue-800">当前检查任务: <span id="modalTaskName" class="font-semibold"></span></p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="modalCategory" class="block text-sm font-medium text-gray-700 mb-1">检查类别 <span class="text-red-500">*</span></label>
                            <select id="modalCategory" name="inspectType" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请选择检查类别</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalCity" class="block text-sm font-medium text-gray-700 mb-1">市名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalCity" name="city" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入市名称" required>
                        </div>
                        <div>
                            <label for="modalArea" class="block text-sm font-medium text-gray-700 mb-1">区/县名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalArea" name="district" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="请输入区/县名称" required>
                        </div>
                        <div>
                            <label for="modalOrgUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <div id="modalOrgApp">
                                <el-tree-select
                                    v-model="selectedUnit"
                                    :data="unitOptions"
                                    :props="{ value: 'name', label: 'name', children: 'children' }"
                                    placeholder="请选择单位"
                                    class="block w-full"
                                    clearable
                                    check-strictly
                                    @change="handleModalOrgChange"
                                />
                            </div>
                        </div>
                        <div>
                            <label for="modalRoadNumber" class="block text-sm font-medium text-gray-700 mb-1">公路编号 <span class="text-red-500">*</span></label>
                            <!-- TODO: 根据市/区县加载 -->
                            <select id="modalRoadNumber" name="roadNumber" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="">请先选择市/区县</option>
                                <option value="G324">G324</option>
                                <option value="S211">S211</option>
                                <option value="X456">X456</option>
                            </select>
                        </div>
                         <div>
                            <label for="modalStartStake" class="block text-sm font-medium text-gray-700 mb-1">起点桩号</label>
                            <input type="text" id="modalStartStake" name="startStake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+200">
                        </div>
                         <div>
                            <label for="modalEndStake" class="block text-sm font-medium text-gray-700 mb-1">止点桩号</label>
                            <input type="text" id="modalEndStake" name="endStake" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="例如: K1500+500">
                        </div>
                    </div>

                    <div class="mt-4">
                      <label for="modalRiskDescription" class="block text-sm font-medium text-gray-700 mb-1">风险点描述 <span class="text-red-500">*</span></label>
                      <textarea id="modalRiskDescription" name="riskDescription" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">现场照片/附件</label>
                            <input type="file" id="modalPhotos" name="photos" multiple class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            <div id="uploadedFilesList" class="mt-2 text-sm text-gray-600"></div>
                        </div>
                        <div>
                            <label for="modalRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">风险等级 <span class="text-red-500">*</span></label>
                            <select id="modalRiskLevel" name="riskLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="high">高</option>
                                <option value="medium">中</option>
                                <option value="low">低</option>
                                <option value="none">无风险</option>
                            </select>
                        </div>
                         <div>
                             <label class="block text-sm font-medium text-gray-700 mb-1">是否隐患点 <span class="text-red-500">*</span></label>
                             <div class="flex items-center space-x-4 mt-1">
                                 <label><input type="radio" name="isHazard" value="1" class="mr-1"> 是</label>
                                 <label><input type="radio" name="isHazard" value="0" class="mr-1" checked> 否</label>
                             </div>
                         </div>
                         <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">是否已采取措施 <span class="text-red-500">*</span></label>
                            <div class="flex items-center space-x-4 mt-1">
                                <label><input type="radio" name="measuresTaken" value="1" class="mr-1"> 是</label>
                                <label><input type="radio" name="measuresTaken" value="0" class="mr-1" checked> 否</label>
                            </div>
                        </div>
                         <div>
                            <label for="modalMeasures" class="block text-sm font-medium text-gray-700 mb-1">已（拟）采取的措施</label>
                            <!-- TODO: 可多选 + 自定义 -->
                            <textarea id="modalMeasures" name="measures" rows="2" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="可多选预设措施，或手动填写"></textarea>
                            <input type="file" id="modalMeasureFiles" name="measureFiles" multiple class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-1 file:px-2 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-gray-50 file:text-gray-700 hover:file:bg-gray-100" placeholder="上传方案/报告">
                            <div id="uploadedMeasureFilesList" class="mt-1 text-sm text-gray-600"></div>
                        </div>
                        <!-- Removed Responsibility fields as they might be auto-filled -->
                    </div>
                     <div class="mt-4">
                         <label for="modalCheckDate" class="block text-sm font-medium text-gray-700 mb-1">检查日期 <span class="text-red-500">*</span></label>
                         <input type="date" id="modalCheckDate" name="checkDate" class="block border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnSave" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  提交填报
                </button>
            </div>
        </div>
    </div>

    <!-- Load Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <!-- <script src="https://unpkg.com/@element-plus/icons-vue"></script> --><!-- Icons not needed for this modal -->

    <!-- Page Specific Scripts -->
    <script>
        // Mock Data (Same as hazard_check_list for consistency in modal)
        const unitData = [
            { value: '1', label: '广西壮族自治区交通运输厅', children: [
                { value: '1.1', label: '直属事业单位及专项机构', children: [
                    { value: '1.1.1', label: '自治区公路发展中心' },
                    { value: '1.1.2', label: '自治区高速公路发展中心' },
                    { value: '1.1.3', label: '自治区道路运输发展中心' }
                ]},
                { value: '1.2', label: '市级交通运输局', children: [
                    { value: '1.2.1', label: '钦州市交通运输局' },
                    { value: '1.2.2', label: '南宁市交通运输局' },
                    { value: '1.2.3', label: '玉林市交通运输局' }
                ]}
            ]}
        ];

        // Vue App - For Org Unit TreeSelect inside the modal
        const modalOrgApp = Vue.createApp({
            data() { return { selectedUnit: null, unitOptions: [] } },
            methods: { handleModalOrgChange(value) { console.log('模态框选中的单位:', value); } }
        }).use(ElementPlus);
        modalOrgApp.mount('#modalOrgApp');

        // 获取组织机构数据
        getUnitList();
        async function getUnitList() {
            try {
                let unitRes = await window.Http.get('/system/organization/tree')
                console.log('获取组织机构数据成功：', unitRes)
                if (unitRes.code == 200) {
                    // 更新所属单位的数据
                    const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                    if (modalOrgAppInstance) {
                        modalOrgAppInstance.data.unitOptions = [...unitRes.data];
                    }
                }
            } catch (error) {
                console.error('获取组织机构数据失败：', error);
            }
        }

        // Vanilla JS for Modal Control and Form Handling (Copied & Adapted)
        document.addEventListener('DOMContentLoaded', function() {
            const hazardModal = document.getElementById('hazardModal');
            const hazardForm = document.getElementById('hazardForm');
            const modalTitle = document.getElementById('modalTitle');
            const modalTaskIdInput = document.getElementById('modalTaskId');
            const modalTaskNameSpan = document.getElementById('modalTaskName');
            let currentTaskId = null; // Store task ID for submission

            // --- Modal Control --- //
            const openModal = (modal) => modal.classList.remove('hidden');
            const closeModal = (modal) => modal.classList.add('hidden');

            // Close modal buttons
            document.querySelectorAll('.btn-close-modal').forEach(button => {
                button.addEventListener('click', () => {
                    closeModal(hazardModal);
                });
            });

            // --- Event Listeners for Task List Buttons --- //
            document.querySelector('#main-content tbody').addEventListener('click', (event) => {
                const button = event.target.closest('button');
                if (!button) return;

                const row = button.closest('tr.task-row');
                const taskId = row ? row.dataset.taskId : null;
                const taskName = row ? row.querySelector('.task-name')?.textContent.trim() : '未知任务';

                if (button.classList.contains('btn-report-hazard')) {
                    if (taskId) {
                        // Open the hazard reporting modal
                        currentTaskId = taskId; // Store the task ID
                        modalTitle.textContent = '风险隐患填报';
                        hazardForm.reset();
                        modalOrgApp._instance.data.selectedUnit = null; // Reset tree select
                        clearUploadedFiles('uploadedFilesList');
                        clearUploadedFiles('uploadedMeasureFilesList');
                        modalTaskIdInput.value = taskId; // Set hidden input value
                        modalTaskNameSpan.textContent = taskName; // Display task name
                         // Set default check date to today
                        document.getElementById('modalCheckDate').value = new Date().toISOString().split('T')[0];
                        openModal(hazardModal);
                    }
                } else if (button.classList.contains('btn-complete-task')) {
                    if (taskId) {
                        if (confirm(`确认直接完结任务 "${taskName}" 吗？\n（请确保该任务确实没有需要填报的风险隐患）`)) {
                            console.log(`Completing task directly: ${taskName} (ID: ${taskId})`);
                            // TODO: Implement API call to mark task as completed by user
                            alert(`任务 "${taskName}" 已直接完结 (模拟)`);
                            // Update UI: Change status, disable buttons
                            const statusCell = row.querySelector('.task-status');
                            const actionCell = button.closest('td');
                            if (statusCell) {
                                statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完结</span>';
                            }
                            if (actionCell) {
                                actionCell.innerHTML = '<span class="text-gray-400">-</span>'; // Clear buttons
                            }
                        }
                    }
                }
            });

            // --- Modal Form Handling --- //
             // 用于存储上传的文件信息
            let uploadedPhotos = [];
            let uploadedMeasureFiles = [];

            // 文件上传处理
            document.getElementById('modalPhotos')?.addEventListener('change', function(e) {
                console.log('上传现场照片', e.target.files);
                Array.from(e.target.files).forEach(file => {
                    window.Http.upload('/common/upload', file).then(res => {
                        console.log('上传现场照片成功', res);
                        uploadedPhotos.push(res);
                        handleUploadedFiles(res, 'uploadedFilesList', () => {
                            uploadedPhotos = uploadedPhotos.filter(item => item.newFileName !== res.newFileName);
                        });
                    }).catch(err => {
                        console.error('上传失败:', err);
                        alert('上传失败，请稍后重试');
                    });
                });
            });

            document.getElementById('modalMeasureFiles')?.addEventListener('change', function(e) {
                console.log('上传措施文件', e.target.files);
                Array.from(e.target.files).forEach(file => {
                    window.Http.upload('/common/upload', file).then(res => {
                        console.log('上传措施文件成功', res);
                        uploadedMeasureFiles.push(res);
                        handleUploadedFiles(res, 'uploadedMeasureFilesList', () => {
                            uploadedMeasureFiles = uploadedMeasureFiles.filter(item => item.newFileName !== res.newFileName);
                        });
                    }).catch(err => {
                        console.error('上传失败:', err);
                        alert('上传失败，请稍后重试');
                    });
                });
            });

            function handleUploadedFiles(fileInfo, listElementId, onDelete) {
                const listElement = document.getElementById(listElementId);
                if (!listElement) return;

                // 确保文件信息存在
                const fileName = fileInfo.newFileName || fileInfo.originalFileName || '未知文件';

                const listItem = document.createElement('div');
                listItem.className = 'upload-list-item text-sm text-gray-600 flex justify-between items-center py-1';
                listItem.innerHTML = `
                    <span>${fileName}</span>
                    <button type="button" class="text-red-500 hover:text-red-700 text-xs ml-2">删除</button>
                `;

                // 添加删除按钮事件
                const deleteButton = listItem.querySelector('button');
                if (deleteButton) {
                    deleteButton.addEventListener('click', () => {
                        listItem.remove();
                        if (onDelete) onDelete();
                    });
                }

                listElement.appendChild(listItem);
            }

            function clearUploadedFiles(listElementId) {
                const listElement = document.getElementById(listElementId);
                if (listElement) {
                    listElement.innerHTML = '';
                    if (listElementId === 'uploadedFilesList') {
                        uploadedPhotos = [];
                    } else if (listElementId === 'uploadedMeasureFilesList') {
                        uploadedMeasureFiles = [];
                    }
                }
            }

             // 保存按钮处理
            document.getElementById('btnSave')?.addEventListener('click', function(e) {
                e.preventDefault();
                const hazardForm = document.getElementById('hazardForm');
                if (!hazardForm) return;
                
                // 表单验证
                let isValid = true;
                const requiredFields = {
                    'modalCategory': '检查类别',
                    'modalCity': '市名称',
                    'modalArea': '区/县名称',
                    'modalRoadNumber': '公路编号',
                    'modalRiskDescription': '风险点描述',
                    'modalRiskLevel': '风险等级'
                };

                // 清除所有错误提示
                document.querySelectorAll('.error-message').forEach(el => el.remove());
                document.querySelectorAll('.border-red-500').forEach(el => el.classList.remove('border-red-500'));

                // 检查必填字段
                for (const [fieldId, fieldName] of Object.entries(requiredFields)) {
                    const field = document.getElementById(fieldId);
                    if (!field || !field.value.trim()) {
                        isValid = false;
                        console.log(`验证失败: ${fieldName} 为空`, {
                            fieldId,
                            fieldName,
                            value: field?.value,
                            element: field
                        });
                        field?.classList.add('border-red-500');
                        
                        // 添加错误提示
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                        errorDiv.textContent = `请填写${fieldName}`;
                        field?.parentNode.appendChild(errorDiv);

                        // 添加输入事件监听器，当用户开始输入时移除错误提示
                        field?.addEventListener('input', function() {
                            this.classList.remove('border-red-500');
                            const errorMessage = this.parentNode.querySelector('.error-message');
                            if (errorMessage) {
                                errorMessage.remove();
                            }
                        });
                    }
                }

                // 检查是否已采取措施的单选按钮
                const measuresTakenValue = document.querySelector('input[name="measuresTaken"]:checked')?.value;
                const isHazardValue = document.querySelector('input[name="isHazard"]:checked')?.value;

                // 由于设置了默认值，这些值应该总是存在的，但为了代码健壮性仍然进行检查
                if (measuresTakenValue === undefined) {
                    isValid = false;
                    console.log('验证失败: 未选择是否已采取措施');
                    const measuresTakenContainer = document.querySelector('input[name="measuresTaken"]')?.closest('.flex.items-center');
                    if (measuresTakenContainer) {
                        measuresTakenContainer.classList.add('border-red-500', 'p-2', 'rounded');
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                        errorDiv.textContent = '请选择是否已采取措施';
                        measuresTakenContainer.parentElement.appendChild(errorDiv);
                    }
                }

                if (isHazardValue === undefined) {
                    isValid = false;
                    console.log('验证失败: 未选择是否隐患点');
                    const isHazardContainer = document.querySelector('input[name="isHazard"]')?.closest('.flex.items-center');
                    if (isHazardContainer) {
                        isHazardContainer.classList.add('border-red-500', 'p-2', 'rounded');
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                        errorDiv.textContent = '请选择是否隐患点';
                        isHazardContainer.parentElement.appendChild(errorDiv);
                    }
                }

                if (!isValid) {
                    console.log('表单验证不通过，请检查以上错误信息');
                    // 滚动到第一个错误字段
                    const firstError = document.querySelector('.border-red-500');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    return;
                }

                // 处理表单数据
                const formData = new FormData(hazardForm);
                const data = Object.fromEntries(formData.entries());

                console.log('表单数据:', data);

                // 处理数值类型字段
                data.riskLevel = Number(data.riskLevel);
                data.isHazard = Number(isHazardValue || '0');
                data.measuresTaken = Number(measuresTakenValue || '0');

                // 获取所属单位
                const modalOrgAppInstance = document.getElementById('modalOrgApp')?.__vue_app__?._instance;
                if (modalOrgAppInstance?.data.selectedUnit) {
                    data.units = modalOrgAppInstance.data.selectedUnit;
                }

                // 处理文件数据
                try {
                    // 处理现场照片
                    if (uploadedPhotos && uploadedPhotos.length > 0) {
                        data.sceneImg = uploadedPhotos.map(photo => photo.url || '').filter(url => url).join(',');
                    } else {
                        data.sceneImg = '';
                    }

                    // 处理措施文件
                    if (uploadedMeasureFiles && uploadedMeasureFiles.length > 0) {
                        data.measureFiles = uploadedMeasureFiles.map(file => file.url || '').filter(url => url).join(',');
                    } else {
                        data.measureFiles = '';
                    }
                } catch (error) {
                    console.error('处理文件数据时出错:', error);
                    data.sceneImg = '';
                    data.measureFiles = '';
                }

                // 添加任务ID
                data.taskId = modalTaskIdInput.value;

                console.log('表单数据:', data);
                console.log('上传的照片数据:', uploadedPhotos);
                console.log('上传的措施文件数据:', uploadedMeasureFiles);

                let params = {
                    content: JSON.stringify(data),
                    id: modalTaskIdInput.value
                }
                // 发起请求
                window.Http.post('/risk/inspectTask/edit', params)
                    .then(response => {
                        console.log('提交成功:', response);
                        alert('提交成功');
                        closeModal(hazardModal);
                        location.reload();
                    })
                    .catch(error => {
                        console.error('提交失败:', error);
                        alert('提交失败：' + (error.message || '请稍后重试'));
                    });
            });

            let typeListArray = []
            // 获取检查类别列表
            window.Http.get('/risk/type/list').then(response => {
                console.log('获取检查类别列表成功：', response);
                typeListArray = response.rows
                const modalCategory = document.getElementById('modalCategory');
                if (modalCategory && response.rows) {
                    modalCategory.innerHTML = '<option value="">请选择检查类别</option>';
                    response.rows.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.id;
                        option.textContent = category.name;
                        modalCategory.appendChild(option);
                    });

                    fetchTaskList()
                }
            }).catch(error => {
                console.error('获取检查类别列表失败：', error);
            });

            // 获取公路编号列表
            window.Http.get('/system/marker/list', {
                pageNum: 1,
                    pageSize: 9999
            }).then(response => {
                console.log('获取公路编号列表成功：', response);
                const selectElement = document.getElementById('modalRoadNumber');
                if (selectElement && response.rows) {
                    selectElement.innerHTML = '<option value="">请选择公路编号</option>';
                    response.rows.forEach(road => {
                        const option = document.createElement('option');
                        option.value = road.code;
                        option.textContent = road.code;
                        selectElement.appendChild(option);
                    });
                }
            }).catch(error => {
                console.error('获取公路编号列表失败：', error);
            });

            // 获取任务列表数据
            function fetchTaskList() {
                window.Http.get('/risk/inspectTask/list')
                    .then(response => {
                        console.log('获取任务列表成功：', response);
                        if (response.code === 200 && response.rows) {
                            renderTaskList(response.rows);
                        }
                    })
                    .catch(error => {
                        console.error('获取任务列表失败：', error);
                    });
            }

            function findNames(arr, str) {
                // 将字符串拆分为 ID 数组并处理空格
                const ids = str.split(',').map(id => id.trim());
                const result = [];
                
                // 使用栈实现迭代 DFS 遍历
                const stack = [...arr]; // 初始节点入栈
                
                while (stack.length > 0) {
                const node = stack.pop();
                
                // 检查当前节点 ID 是否匹配
                if (node.id !== undefined) {
                const idStr = String(node.id); // 统一转为字符串比较
                const index = ids.indexOf(idStr);
                
                // 如果匹配到有效 ID 则记录 name
                if (index !== -1) {
                    result[index] = node.name; // 按原始顺序保存
                    }
                }
                    
                    // 将子节点压入栈（继续遍历）
                    if (node.children && Array.isArray(node.children)) {
                    stack.push(...node.children.slice().reverse()); // 保持原顺序
                    }
                }
                
                // 过滤 undefined 并返回结果
                return result.filter(item => item !== undefined);
            }

            // 渲染任务列表
            function renderTaskList(tasks) {
                const tbody = document.getElementById('taskTableBody');
                if (!tbody) return;

                tbody.innerHTML = tasks.map((task, index) => {
                    // 根据任务状态设置不同的样式
                    let statusClass = '';
                    let statusText = '';
                    switch(task.status) {
                        case 0:
                            statusClass = 'bg-yellow-100 text-yellow-800';
                            statusText = '待处理';
                            break;
                        case 1:
                            statusClass = 'bg-green-100 text-green-800';
                            statusText = '已完结';
                            break;
                        default:
                            statusClass = 'bg-gray-100 text-gray-800';
                            statusText = '未知状态';
                    }
                    console.log('task', task)
                    let typeName = ''

                    if (task.type) {
                        typeName = findNames(typeListArray, task.type)
                    }
                    console.log('typeName', typeName)
                    // 根据任务状态决定是否显示操作按钮
                    const actionButtons = task.status === 0 ? `
                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2 btn-report-hazard" 
                                title="填报风险隐患" 
                                data-task-id="${task.id}" 
                                data-task-name="${task.name}">
                            <i class="fas fa-edit mr-1"></i>填报
                        </button>
                        <button class="text-green-600 hover:text-green-800 focus:outline-none btn-complete-task" 
                                title="直接完成任务（无隐患）">
                            <i class="fas fa-check-circle mr-1"></i>直接完结
                        </button>
                    ` : '<span class="text-gray-400">-</span>';

                    return `
                        <tr class="hover:bg-gray-50 task-row" data-task-id="${task.id}">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${index + 1}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 task-name">${task.name || '-'}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${typeName.join(',') || '-'}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${task.issueUnit || '-'}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${task.endTime || '-'}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm task-status">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                                    ${statusText}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm">
                                ${actionButtons}
                            </td>
                        </tr>
                    `;
                }).join('');
            }

            // 初始加载任务列表
            // fetchTaskList();
        });

    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
    <script>
        // 初始化导航栏
        window.NavigationComponent.init('system-management');
    </script>
</body>
</html>