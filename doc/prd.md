## 应急管理系统 - 产品页面规划 / 信息架构 (应急预案模块优化)

**核心思路:** 借鉴样例预案的标准化结构，将预案的创建、查看、修改界面设计为分章节/分模块的模式，更贴合实际编写习惯，并强化与系统其他模块（如组织机构、资源、附件）的关联。

**应急预案 (Emergency Plans) 模块 - 细化设计:**

1.  **预案库 (Plan Library):** (基本保持不变，根据需求文档补充搜索条件)
    *   功能：集中展示、检索和管理所有预案（我的预案、全区预案）。
    *   列表字段：序号、预案名称、预案类型（例如：低温雨雪冰冻灾害、危化品泄漏）、适用范围（文本概括）、事件级别（如涉及最高级别）、启用状态、编制单位、最新修订时间、操作（查看详情、修改、启用/失效、修订、删除、关联演练）。
    *   搜索与筛选：按`预案名称`、`预案类型`、`适用范围`、`编制单位`、`适用单位`、`启用状态`、`预案正文关键字`进行搜索和筛选。

2.  **预案创建 / 修改 (Plan Creation / Editing):** 
    *   **界面形式:** 采用多标签页 (Tabs) 或分步向导 (Wizard) 的形式，每个标签页/步骤对应预案的一个主要章节。
    *   **章节/标签页设计 (参考样例结构):**
        *   **基本信息 & 总则 (Basic Info & General Principles):**
            *   `预案名称`: 文本输入，必填。
            *   `预案类型`: 下拉选择（关联字典管理），单选，必填。
            *   `编制单位`: 下拉选择（关联组织机构），单选，默认为当前用户所属单位，必填。
            *   `适用单位`: 下拉选择（关联组织机构），支持多选，可搜索。
            *   `编制目的`: 富文本编辑器。
            *   `编制依据`: 富文本编辑器（或支持引用标准文件）。
            *   `适用范围`: 富文本编辑器。 (需求文档中为下拉多选，但样例预案中此项为文本描述，PRD暂定为富文本，允许后续调整为结构化或关联字典)
            *   `事件分级与响应条件`: (核心细化部分)
                *   可添加多个事件级别。
                *   每个级别包含：
                    *   `事件级别`: 下拉选择，单选 (选项：Ⅰ级-特别重大, II级-重大, III级-较大, IV级-一般，或其他根据预案类型定义的级别)，各级别之间互斥。
                    *   `响应启动条件`: 富文本编辑器 (参考样例附件1的表格结构)。
            *   `工作原则`: 富文本编辑器。
        *   **组织体系 (Organizational System):**
            *   `应急指挥机构设置`:
                *   支持添加多个指挥机构/小组。
                *   每个机构/小组包含：
                    *   `机构/小组名称`: 文本输入或下拉选择（如：领导小组, 领导小组办公室, 应急工作组 - 综合协调组, 公路保障组等，可关联预设或组织机构）。
                    *   `主要职责`: 富文本编辑器。
                    *   `关联人员/单位`: 下拉选择（关联组织机构或用户），多选，可搜索。
                    *   `关联事件级别`: 复选框或下拉多选，选择该机构/小组在哪些事件级别下启动工作 (如：I级, II级)。
                *   可参考样例结构预设层级（领导小组、办公室、应急工作组、现场工作组、专家组）。
            *   `下级指挥机构要求`: (可选) 富文本编辑器。
        *   **预防与预警 (Prevention & Early Warning):**
            *   `预防措施`: 富文本编辑器/清单。
            *   `预警信息收集`: 富文本编辑器。
            *   `预警分级标准`:
                *   可添加多个预警级别。
                *   每个级别包含：
                    *   `预警级别`: 下拉选择（如：红、橙、黄、蓝），单选。
                    *   `启动条件`: 富文本编辑器 (参考样例附件2的表格结构)。
            *   `预警发布`: 富文本编辑器。
            *   `防御响应措施`:
                *   按预警级别分别描述对应的防御措施 (富文本编辑器)。
        *   **应急响应 (Emergency Response):** (核心细化部分)
            *   `响应分级启动`: (信息展示，关联总则中的事件分级) 富文本编辑器。
            *   `信息报送`: 富文本编辑器 (可引用附件中的报送模板)。
            *   `处置措施`:
                *   按事件级别（I, II, III, IV）分别描述：
                    *   `应急处置流程`: 富文本编辑器。
                    *   `关联应急资源`: (替代原各保障模块的关联，集中在此处按级别关联)
                        *   `应急物资`:
                            *   提供按钮/链接，弹出物资选择界面。
                            *   可按仓库、类型、名称搜索物资。
                            *   选择物资后，填写所需`数量`。
                            *   支持直接在此处`新增物资类别`或`新增物资`（权限控制）。
                        *   `应急车辆/运力`:
                            *   提供按钮/链接，弹出车辆选择界面。
                            *   可按单位、类型、车牌号搜索车辆。
                            *   选择车辆后，填写所需`数量`。
                            *   支持直接在此处`新增车辆类型`（权限控制）。
            *   `新闻发布`: 富文本编辑器。
            *   `响应调整与终止`: 富文本编辑器。
        *   **后期处置 (Post-Disposal):**
            *   `善后处置`: 富文本编辑器。
            *   `总结评估`: 富文本编辑器。
        *   **应急保障 (Emergency Support):** (此部分内容根据需求文档调整，部分关联移至应急响应处置措施中按级别关联，此处保留总体要求和非按级别区分的保障)
            *   `通信保障`: 富文本编辑器。
            *   `队伍保障`:
                *   描述保障要求 (富文本)。
                *   **关联应急队伍**: 提供按钮/链接，可按类型、名称搜索并添加。
            *   `专家保障`:
                *   描述保障要求 (富文本)。
                *   **关联应急专家**: 提供按钮/链接，可按领域、姓名搜索并添加。
            *   `资金保障`: 富文本编辑器。
            *   `技术保障`: 富文本编辑器。
            *   (物资和运力保障的总体要求可在此描述，具体关联在应急响应处置措施中按级别进行)
        *   **预案管理 (Plan Management):**
            *   `评估与修订`: 富文本编辑器。
            *   `宣传与培训`: 富文本编辑器。
            *   `应急演练`: 富文本编辑器 (可链接到具体的演练记录)。
        *   **附件管理 (Attachments):**
            *   功能：上传、下载、管理与此预案相关的附件（如：流程图、通知模板、信息报送单样式、相关地图、成员名单等，支持Word, PDF, Excel, 图片等格式）。支持上传多个附件。
            *   列表：显示附件名称、格式、上传时间、上传人、操作（下载、删除）。

3.  **预案详情 (Plan Details):**
    *   **界面形式:** 同样采用分章节/标签页的形式展示，与创建/修改界面结构保持一致，只读模式。
    *   **内容:** 清晰、完整地展示预案的所有信息，包括各章节字段和关联的资源/人员信息。
    *   **操作:** 提供"编辑"、"创建修订版"、"启用/失效"、"导出/打印"、"关联演练"等按钮（根据权限显示）。
    *   **版本历史:** (根据需求文档添加) 应能方便地查看和切换不同版本，并可进行版本比对。

4.  **（可选）预案模板管理 (Plan Template Management):**
    *   功能：允许管理员创建和管理预案模板（例如，针对特定灾种或级别的标准化模板），用户在创建新预案时可以选择基于模板创建，以提高效率和规范性。

## 应急资源管理 (Emergency Resource Management) 模块

**核心思路:** 提供对应急仓库、物资、车辆等核心应急资源的集中管理、查询和维护功能，并支持与应急预案的关联调用。

1.  **应急仓库管理 (Warehouse Management):**
    *   功能：管理应急物资存放仓库的基础信息，参考`应急物资统计表示例.md`中的仓库信息。
    *   列表字段：序号、`仓库名称`、`所属单位` (对应示例中的`公司名称`)、`地址` (对应示例中的`应急仓库地点`)、`负责人` (对应示例中的`联系人`)、`联系方式` (对应示例中的`电话`)、操作（修改、删除、查看物资列表）。
    *   搜索与筛选：按`仓库名称`、`所属单位`、`地址关键字`进行搜索。
    *   仓库新增/修改字段：
        *   `仓库名称`: (对应示例`应急仓库名称`) 文本输入，必填。
        *   `所属单位`: (对应示例`公司名称`) 下拉选择（关联组织机构），单选，必填。
        *   `地址`: (对应示例`应急仓库地点`) 文本输入。
        *   `经纬度`: (可选) 地图选点或输入。
        *   `负责人`: (对应示例`联系人`) 下拉选择（关联用户/单位人员），单选。
        *   `联系方式`: (对应示例`电话`) 文本输入。
    *   删除操作：删除前需确认仓库下是否还有物资，若有则提示。 （或提供选项：同时删除/转移关联物资）

2.  **应急物资管理 (Material Management):**
    *   功能：管理具体的应急物资信息，记录库存和状态，参考`应急物资统计表示例.md`中的物资信息。
    *   列表字段：序号、`物资名称`、`规格型号` (共同对应示例中的`主要应急物资、装备、设备名称及型号`)、`物资类别` (示例中未显式列出，但系统需要)、`所属仓库`、`数量`、`单位` (示例中未显式列出，但系统需要)、`状态`、操作（修改、删除、查看变动记录）。
    *   搜索与筛选：按`物资名称`、`规格型号`、`物资类别`、`所属仓库`、`状态`进行搜索。
    *   物资新增/修改字段：
        *   `物资名称`: (对应示例`主要应急物资...名称及型号`中的名称部分) 文本输入或下拉选择（关联预设物资字典），支持搜索和`即时新增`（权限控制）。
        *   `规格型号`: (对应示例`主要应急物资...名称及型号`中的型号部分) 文本输入。
        *   `物资类别`: 文本输入或下拉选择（关联字典），支持`即时新增`（权限控制）。
        *   `所属仓库`: 下拉选择（关联应急仓库），单选，必填。
        *   `数量`: (对应示例`数量`) 数字输入，必填。
        *   `单位`: 文本输入或下拉选择（如：件、套、个、辆、台、吨）。
        *   `状态`: 下拉选择（如：正常、维修中、报废），单选。
    *   `物资变动记录`: 自动记录物资信息的每次修改（特别是数量变更），提供历史追溯。

3.  **应急车辆管理 (Vehicle Management):** (示例中车辆信息较少，主要参考需求文档)
    *   功能：管理用于应急保障的车辆信息。
    *   列表字段：序号、所属单位、车辆类型、车牌号、车辆位置、车辆状态、负责人、联系方式、操作（修改、删除）。
    *   搜索与筛选：按`所属单位`、`车辆类型`、`车牌号`、`车辆状态`进行搜索。
    *   车辆新增/修改字段：
        *   `所属单位`: 下拉选择（关联组织机构），单选，必填。
        *   `车辆类型`: 文本输入或下拉选择（关联字典），支持`即时新增`（权限控制）。
        *   `车牌号`: 文本输入，必填。
        *   `车辆位置`: 文本输入。
        *   `经纬度`: (可选) 地图选点或输入。
        *   `车辆状态`: 下拉选择（如：可用、维修中、任务中），单选。
        *   `负责人`: 下拉选择（关联用户/单位人员），单选。
        *   `联系方式`: 文本输入。

4.  **资源动态维护 (Resource Dynamic Maintenance):** (可选高级功能)
    *   `物资数量阈值提醒`: 可配置规则，当某类物资数量低于设定阈值时，自动发送提醒通知给相关负责人。
    *   `物资效期提醒`: (若物资有有效期) 可配置有效期，在临近过期前自动发送提醒。
    *   `信息更新提醒`: 可配置规则，例如X天未更新仓库/物资/车辆信息时，提醒相关人员进行检查和更新。

## 组织与人员管理 (Organization & Personnel Management) 模块

**核心思路:** 管理系统所涉及的组织架构、内部人员、外部专家及协作方信息，为预案制定、应急响应、资源分配提供人员支持。

1.  **组织机构管理 (Organizational Structure Management):**
    *   功能：维护系统内的单位/部门层级结构。
    *   界面形式：通常采用树状结构展示和管理。
    *   操作：新增部门/单位、修改名称、调整层级关系、删除。
    *   字段：机构ID、机构名称、上级机构、机构类型（如：单位、部门）、负责人（可选，关联用户）等。

2.  **单位人员管理 (Internal Personnel Management):**
    *   功能：管理系统内各组织机构的正式人员信息。
    *   列表字段：序号、姓名、所属单位/部门、职务、联系方式、操作（修改、删除）。
    *   搜索与筛选：按`姓名`、`所属单位/部门`、`职务`进行搜索。
    *   人员新增/修改字段：
        *   `姓名`: 文本输入，必填。
        *   `所属单位/部门`: 下拉选择（关联组织机构），单选，必填。
        *   `职务`: 文本输入。
        *   `联系方式`: 文本输入（手机、办公电话等），必填。
        *   (可选) `用户账号关联`: 如果需要系统登录权限，则关联系统用户账号。

3.  **专家库管理 (Expert Database Management):**
    *   功能：管理可提供应急咨询和技术支持的专家信息。
    *   列表字段：序号、姓名、专业领域/类别、所属单位（可选）、联系方式、最近确认时间（可选）、操作（修改、删除）。
    *   搜索与筛选：按`姓名`、`专业领域/类别`进行搜索。
    *   专家新增/修改字段：
        *   `姓名`: 文本输入，必填。
        *   `专业领域/类别`: 文本输入或下拉选择（关联字典），支持多选或标签化。
        *   `所属单位`: 文本输入（专家可能来自外部机构）。
        *   `职务/职称`: 文本输入。
        *   `联系方式`: 文本输入，必填。
        *   (可选) `简介/特长`: 富文本编辑器。

4.  **三方人员管理 (Third-Party Personnel Management):**
    *   功能：管理应急协作相关的外部单位人员信息。
    *   列表字段：序号、姓名、所属单位/公司、职务、联系方式、协作类型（可选）、操作（修改、删除）。
    *   搜索与筛选：按`姓名`、`所属单位/公司`进行搜索。
    *   人员新增/修改字段：
        *   `姓名`: 文本输入，必填。
        *   `所属单位/公司`: 文本输入，必填。
        *   `职务`: 文本输入。
        *   `联系方式`: 文本输入，必填。
        *   (可选) `协作类型/备注`: 文本输入或下拉选择。

