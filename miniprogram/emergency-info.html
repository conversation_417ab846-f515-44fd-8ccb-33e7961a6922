<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急信息录入</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>应急信息录入</h1>
            <div class="subtitle">录入应急资源信息</div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
            <!-- 信息类型选择 -->
            <div class="info-type-tabs">
                <button class="tab-btn active" onclick="switchInfoType('warehouse')" id="warehouseTab">
                    <i class="fas fa-warehouse"></i>
                    <span>应急仓库</span>
                </button>
                <button class="tab-btn" onclick="switchInfoType('team')" id="teamTab">
                    <i class="fas fa-users"></i>
                    <span>救援队伍</span>
                </button>
                <button class="tab-btn" onclick="switchInfoType('expert')" id="expertTab">
                    <i class="fas fa-user-tie"></i>
                    <span>专家信息</span>
                </button>
            </div>

            <!-- 应急仓库信息录入 -->
            <div id="warehouseInfo" class="info-content active">
                <form id="warehouseForm">
                    <!-- 仓库基本信息 -->
                    <div class="form-section">
                        <h4><i class="fas fa-warehouse"></i> 仓库基本信息</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="warehouseName">仓库名称 <span class="required">*</span></label>
                                <input type="text" id="warehouseName" name="warehouseName" placeholder="请输入仓库名称" required>
                            </div>
                            <div class="form-group">
                                <label for="warehouseUnit">所属单位 <span class="required">*</span></label>
                                <input type="text" id="warehouseUnit" name="warehouseUnit" placeholder="请输入所属单位" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="warehouseRoadCode">路段编号</label>
                                <input type="text" id="warehouseRoadCode" name="warehouseRoadCode" placeholder="请输入路段编号">
                            </div>
                            <div class="form-group">
                                <label for="warehouseStakeNo">起始桩号</label>
                                <input type="text" id="warehouseStakeNo" name="warehouseStakeNo" placeholder="请输入起始桩号">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="warehouseAddress">详细地址 <span class="required">*</span></label>
                                <div class="location-input-group">
                                    <input type="text" id="warehouseAddress" name="warehouseAddress" placeholder="请输入详细地址" required>
                                    <button type="button" class="location-btn" onclick="getCurrentLocationForWarehouse()" title="获取当前位置经纬度">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="warehouseLongitude">经度</label>
                                <input type="number" id="warehouseLongitude" name="warehouseLongitude" step="0.000001" placeholder="点击按钮自动获取" readonly>
                            </div>
                            <div class="form-group">
                                <label for="warehouseLatitude">纬度</label>
                                <input type="number" id="warehouseLatitude" name="warehouseLatitude" step="0.000001" placeholder="点击按钮自动获取" readonly>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <button type="button" class="location-get-btn" onclick="getCurrentLocationForWarehouse()">
                                    <i class="fas fa-crosshairs"></i> 获取当前经纬度
                                </button>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="warehouseManager">负责人 <span class="required">*</span></label>
                                <input type="text" id="warehouseManager" name="warehouseManager" placeholder="请输入负责人姓名" required>
                            </div>
                            <div class="form-group">
                                <label for="warehousePhone">联系方式 <span class="required">*</span></label>
                                <input type="tel" id="warehousePhone" name="warehousePhone" placeholder="请输入联系电话" required>
                            </div>
                        </div>
                    </div>

                    <!-- 仓库物资信息 -->
                    <div class="form-section">
                        <h4>
                            <i class="fas fa-boxes"></i> 仓库物资信息
                            <button type="button" class="add-item-btn" onclick="addWarehouseMaterial()">
                                <i class="fas fa-plus"></i> 添加物资
                            </button>
                        </h4>

                        <div id="warehouseMaterials">
                            <!-- 物资项目将通过JavaScript动态添加 -->
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetWarehouseForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存仓库信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 救援队伍信息录入 -->
            <div id="teamInfo" class="info-content">
                <form id="teamForm">
                    <!-- 队伍基本信息 -->
                    <div class="form-section">
                        <h4><i class="fas fa-users"></i> 救援队伍基本信息</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="teamName">队伍名称 <span class="required">*</span></label>
                                <input type="text" id="teamName" name="teamName" placeholder="请输入队伍名称" required>
                            </div>
                            <div class="form-group">
                                <label for="teamUnit">所属单位 <span class="required">*</span></label>
                                <input type="text" id="teamUnit" name="teamUnit" placeholder="请输入所属单位" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="teamPhone">联系电话 <span class="required">*</span></label>
                                <input type="tel" id="teamPhone" name="teamPhone" placeholder="请输入联系电话" required>
                            </div>
                            <div class="form-group">
                                <label for="teamStatus">状态 <span class="required">*</span></label>
                                <select id="teamStatus" name="teamStatus" required>
                                    <option value="">请选择状态</option>
                                    <option value="available">可用</option>
                                    <option value="busy">忙碌</option>
                                    <option value="maintenance">维护中</option>
                                    <option value="unavailable">不可用</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group full-width">
                                <label for="teamSkills">技能特长 <span class="required">*</span></label>
                                <textarea id="teamSkills" name="teamSkills" placeholder="请详细描述队伍的技能特长和专业能力" required></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- 队伍装备信息 -->
                    <div class="form-section">
                        <h4>
                            <i class="fas fa-tools"></i> 队伍装备信息
                            <button type="button" class="add-item-btn" onclick="addTeamEquipment()">
                                <i class="fas fa-plus"></i> 添加装备
                            </button>
                        </h4>

                        <div id="teamEquipments">
                            <!-- 装备项目将通过JavaScript动态添加 -->
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetTeamForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存队伍信息
                        </button>
                    </div>
                </form>
            </div>

            <!-- 专家信息录入 -->
            <div id="expertInfo" class="info-content">
                <form id="expertForm">
                    <!-- 专家基本信息 -->
                    <div class="form-section">
                        <h4><i class="fas fa-user-tie"></i> 专家基本信息</h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="expertName">姓名 <span class="required">*</span></label>
                                <input type="text" id="expertName" name="expertName" placeholder="请输入专家姓名" required>
                            </div>
                            <div class="form-group">
                                <label for="expertGender">性别 <span class="required">*</span></label>
                                <select id="expertGender" name="expertGender" required>
                                    <option value="">请选择性别</option>
                                    <option value="male">男</option>
                                    <option value="female">女</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="expertBirthDate">出生年月 <span class="required">*</span></label>
                                <input type="month" id="expertBirthDate" name="expertBirthDate" required>
                            </div>
                            <div class="form-group">
                                <label for="expertPhone">联系方式 <span class="required">*</span></label>
                                <input type="tel" id="expertPhone" name="expertPhone" placeholder="请输入联系电话" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="expertWorkUnit">工作单位 <span class="required">*</span></label>
                                <input type="text" id="expertWorkUnit" name="expertWorkUnit" placeholder="请输入工作单位" required>
                            </div>
                            <div class="form-group">
                                <label for="expertPosition">职务 <span class="required">*</span></label>
                                <input type="text" id="expertPosition" name="expertPosition" placeholder="请输入职务" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="expertUniversity">毕业院校</label>
                                <input type="text" id="expertUniversity" name="expertUniversity" placeholder="请输入毕业院校">
                            </div>
                            <div class="form-group">
                                <label for="expertMajor">所学专业</label>
                                <input type="text" id="expertMajor" name="expertMajor" placeholder="请输入所学专业">
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="expertProfession">从事专业 <span class="required">*</span></label>
                                <input type="text" id="expertProfession" name="expertProfession" placeholder="请输入从事专业" required>
                            </div>
                            <div class="form-group">
                                <label for="expertCategory">申报类别 <span class="required">*</span></label>
                                <select id="expertCategory" name="expertCategory" required>
                                    <option value="">请选择申报类别</option>
                                    <option value="traffic">交通运输</option>
                                    <option value="safety">安全生产</option>
                                    <option value="environment">环境保护</option>
                                    <option value="medical">医疗卫生</option>
                                    <option value="construction">工程建设</option>
                                    <option value="geology">地质灾害</option>
                                    <option value="meteorology">气象预报</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetExpertForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存专家信息
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="index.html" class="nav-item active">
                <i class="fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </a>
            <a href="risk-tasks.html" class="nav-item">
                <i class="fas fa-shield-alt"></i>
                <span>风险隐患</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script src="js/miniprogram.js"></script>
</body>
</html>
