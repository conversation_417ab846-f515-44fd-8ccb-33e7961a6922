<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>整改任务处理</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>整改任务处理</h1>
            <div class="subtitle">处理整改任务进度</div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
            <!-- 整改任务列表 -->
            <div id="rectificationTasksList" class="tasks-list">
                <!-- 任务项目将通过JavaScript动态生成 -->
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </a>
            <a href="risk-tasks.html" class="nav-item active">
                <i class="fas fa-shield-alt"></i>
                <span>风险隐患</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script src="js/miniprogram.js"></script>
    <script src="js/rectification-task.js"></script>
</body>
</html>
