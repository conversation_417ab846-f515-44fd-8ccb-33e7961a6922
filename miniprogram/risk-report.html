<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险隐患填报</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>风险隐患填报</h1>
            <div class="subtitle">填报风险隐患检查任务</div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
            <!-- 任务列表 -->
            <div id="tasksList" class="tasks-list">
                <!-- 任务项目将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 查看任务详情模态框 -->
        <div id="viewTaskModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-eye"></i> 任务详情</h3>
                    <button class="close-btn" onclick="closeViewModal()">&times;</button>
                </div>
                <div class="modal-body" id="viewTaskContent">
                    <!-- 任务详情内容 -->
                </div>
            </div>
        </div>

        <!-- 填报任务模态框 -->
        <div id="reportTaskModal" class="modal">
            <div class="modal-content large-modal">
                <div class="modal-header">
                    <h3><i class="fas fa-edit"></i> 风险隐患填报</h3>
                    <button class="close-btn" onclick="closeReportModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="riskReportForm">
                        <div class="form-section">
                            <h4><i class="fas fa-info-circle"></i> 基本信息</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="checkCategory">检查类别 <span class="required">*</span></label>
                                    <select id="checkCategory" name="checkCategory" required>
                                        <option value="">请选择检查类别</option>
                                        <option value="routine">日常检查</option>
                                        <option value="special">专项检查</option>
                                        <option value="emergency">应急检查</option>
                                        <option value="seasonal">季节性检查</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="city">市 <span class="required">*</span></label>
                                    <select id="city" name="city" required onchange="updateDistricts()">
                                        <option value="">请选择市</option>
                                        <option value="beijing">北京市</option>
                                        <option value="shanghai">上海市</option>
                                        <option value="guangzhou">广州市</option>
                                        <option value="shenzhen">深圳市</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="district">区/县名称 <span class="required">*</span></label>
                                    <select id="district" name="district" required>
                                        <option value="">请先选择市</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="belongUnit">所属单位 <span class="required">*</span></label>
                                    <input type="text" id="belongUnit" name="belongUnit" placeholder="请输入所属单位" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="roadNumber">公路编号 <span class="required">*</span></label>
                                    <input type="text" id="roadNumber" name="roadNumber" placeholder="请输入公路编号" required>
                                </div>
                                <div class="form-group">
                                    <label for="checkDate">检查日期 <span class="required">*</span></label>
                                    <input type="date" id="checkDate" name="checkDate" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="startStake">起点桩号 <span class="required">*</span></label>
                                    <input type="text" id="startStake" name="startStake" placeholder="如：K10+000" required>
                                </div>
                                <div class="form-group">
                                    <label for="endStake">止点桩号 <span class="required">*</span></label>
                                    <input type="text" id="endStake" name="endStake" placeholder="如：K15+000" required>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-exclamation-triangle"></i> 风险信息</h4>

                            <div class="form-row">
                                <div class="form-group full-width">
                                    <label for="riskDescription">风险点描述 <span class="required">*</span></label>
                                    <textarea id="riskDescription" name="riskDescription" placeholder="请详细描述发现的风险点情况" required></textarea>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="riskLevel">风险等级 <span class="required">*</span></label>
                                    <select id="riskLevel" name="riskLevel" required>
                                        <option value="">请选择风险等级</option>
                                        <option value="low">低风险</option>
                                        <option value="medium">中风险</option>
                                        <option value="high">高风险</option>
                                        <option value="critical">重大风险</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="isHazardPoint">是否隐患点 <span class="required">*</span></label>
                                    <select id="isHazardPoint" name="isHazardPoint" required>
                                        <option value="">请选择</option>
                                        <option value="yes">是</option>
                                        <option value="no">否</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="hasMeasures">是否已采取措施 <span class="required">*</span></label>
                                    <select id="hasMeasures" name="hasMeasures" required onchange="toggleMeasuresField()">
                                        <option value="">请选择</option>
                                        <option value="yes">是</option>
                                        <option value="no">否</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="sitePhotos">现场照片/附件</label>
                                    <input type="file" id="sitePhotos" name="sitePhotos" multiple accept="image/*,.pdf,.doc,.docx">
                                    <small class="form-hint">支持图片、PDF、Word文档</small>
                                </div>
                            </div>

                            <div class="form-row" id="measuresRow" style="display: none;">
                                <div class="form-group full-width">
                                    <label for="measures">已（拟）采取的措施</label>
                                    <textarea id="measures" name="measures" placeholder="请详细描述已采取或拟采取的措施"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeReportModal()">
                                <i class="fas fa-times"></i> 取消
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 提交填报
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </a>
            <a href="risk-tasks.html" class="nav-item active">
                <i class="fas fa-shield-alt"></i>
                <span>风险隐患</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script src="js/miniprogram.js"></script>
    <script src="js/risk-report.js"></script>
</body>
</html>
