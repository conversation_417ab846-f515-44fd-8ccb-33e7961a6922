<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险隐患 - 管理</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>风险隐患</h1>
            <div class="subtitle">预防为主 · 综合治理 · 安全发展</div>
        </div>
        
        <!-- 功能模块网格 -->
        <div class="function-grid">
            <!-- 风险隐患填报 -->
            <div class="function-card fade-in" data-href="risk-report.html">
                <div class="function-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="function-title">风险隐患填报</div>
                <div class="function-desc">上报风险隐患信息</div>
            </div>
            
            <!-- 整改任务处理 -->
            <div class="function-card fade-in" data-href="rectification-task.html" style="animation-delay: 0.1s;">
                <div class="function-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="function-title">整改任务处理</div>
                <div class="function-desc">处理整改任务</div>
            </div>
            
            <!-- 风险隐患审批 -->
            <div class="function-card fade-in" data-href="risk-approval.html" style="animation-delay: 0.2s;">
                <div class="function-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="function-title">风险隐患审批</div>
                <div class="function-desc">审批风险隐患处理</div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </a>
            <a href="risk-tasks.html" class="nav-item active">
                <i class="fas fa-shield-alt"></i>
                <span>风险隐患</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script src="js/miniprogram.js"></script>
</body>
</html>
