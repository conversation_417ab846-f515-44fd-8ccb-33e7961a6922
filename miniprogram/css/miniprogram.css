/* 小程序通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 容器样式 */
.container {
    max-width: 375px;
    margin: 0 auto;
    background-color: #fff;
    min-height: 100vh;
    position: relative;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 20px 15px 15px;
    text-align: center;
    position: relative;
}

.header h1 {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.header .subtitle {
    font-size: 12px;
    opacity: 0.9;
}

/* 返回按钮 */
.back-btn {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
}

/* 现代化登录页面样式 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
}

.page-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.status-icons {
    display: flex;
    gap: 15px;
    color: #666;
}

.modern-login-container {
    position: relative;
    height: 60vh;
    background: linear-gradient(135deg, #4A90E2 0%, #7B68EE 50%, #9370DB 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

/* 科技感背景 */
.tech-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.circuit-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 1px, transparent 1px),
        linear-gradient(45deg, transparent 40%, rgba(255,255,255,0.05) 50%, transparent 60%);
    background-size: 50px 50px, 30px 30px, 100px 100px;
    animation: float 20s ease-in-out infinite;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255,255,255,0.1);
    animation: float 6s ease-in-out infinite;
}

.element-1 {
    width: 60px;
    height: 60px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.element-2 {
    width: 40px;
    height: 40px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.element-3 {
    width: 80px;
    height: 80px;
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.element-4 {
    width: 30px;
    height: 30px;
    top: 40%;
    right: 30%;
    animation-delay: 1s;
}

/* 应用信息 */
.app-info {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
}

.app-icon {
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.2);
    border-radius: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.app-icon i {
    font-size: 50px;
    color: white;
}

.app-name {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 16px;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    background-color: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 底部登录区域 */
.login-bottom {
    padding: 30px 20px;
    background: white;
    border-radius: 20px 20px 0 0;
    margin-top: -20px;
    position: relative;
    z-index: 3;
}

.phone-login-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.phone-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.phone-login-btn i {
    font-size: 18px;
}

.login-notice {
    text-align: center;
    color: #ff6b6b;
    font-size: 12px;
    margin: 20px 0;
    line-height: 1.4;
}

.other-login {
    margin-top: 30px;
}

.divider {
    text-align: center;
    position: relative;
    margin-bottom: 25px;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e0e0e0;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #999;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.login-methods {
    display: flex;
    justify-content: center;
    gap: 40px;
}

.login-method {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.login-method:hover {
    transform: translateY(-3px);
}

.method-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    color: white;
    font-size: 20px;
}

.email-icon {
    background: linear-gradient(135deg, #5C6BC0 0%, #3F51B5 100%);
}

.sms-icon {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.login-method span {
    font-size: 12px;
    color: #666;
}

/* 首页网格布局 */
.function-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 20px;
}

.function-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #f0f0f0;
}

.function-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.function-icon {
    width: 50px;
    height: 50px;
    margin: 0 auto 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

.function-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.function-desc {
    font-size: 12px;
    color: #999;
}

/* 页面内容样式 */
.page-content {
    padding: 0;
    padding-bottom: 80px;
    min-height: auto;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 20px;
    color: #ddd;
}

.empty-text {
    font-size: 16px;
    margin-bottom: 10px;
}

.empty-desc {
    font-size: 14px;
    color: #ccc;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 375px;
    background: white;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    z-index: 1000;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #999;
    font-size: 12px;
    transition: color 0.3s ease;
}

.nav-item.active {
    color: #667eea;
}

.nav-item i {
    font-size: 20px;
    margin-bottom: 5px;
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-processing {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-rejected {
    background-color: #f8d7da;
    color: #721c24;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .container {
        max-width: 100%;
    }
    
    .function-grid {
        padding: 15px;
        gap: 12px;
    }
    
    .function-card {
        padding: 15px;
    }
    
    .function-icon {
        width: 45px;
        height: 45px;
        font-size: 20px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.3;
    }
    50% {
        opacity: 0.6;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.floating-elements .element {
    animation: float 6s ease-in-out infinite;
}

.circuit-pattern {
    animation: pulse 4s ease-in-out infinite;
}

/* 表单布局样式 */
.form-container {
    padding: 0;
    max-width: 800px;
    margin: 0;
}

.form-section {
    background: white;
    border-radius: 0;
    padding: 20px 15px;
    margin: 0;
    margin-bottom: 10px;
    box-shadow: none;
    border-bottom: 1px solid #f0f0f0;
}

.form-section h4 {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-section h4 i {
    color: #4A90E2;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row:last-child {
    margin-bottom: 0;
}

.form-group {
    flex: 1;
    min-width: 0;
}

.form-group.full-width {
    flex: 1 1 100%;
}

.form-group label {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group .required {
    color: #ff4757;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4A90E2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* 位置输入组样式 */
.location-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.location-input-group input {
    padding-right: 50px;
}

.location-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: #4A90E2;
    color: white;
    border: none;
    border-radius: 6px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.location-btn:hover {
    background: #357ABD;
    transform: translateY(-50%) scale(1.05);
}

.location-btn i {
    font-size: 14px;
}

/* 获取经纬度按钮 */
.location-get-btn {
    width: 100%;
    padding: 12px 20px;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.location-get-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.location-get-btn i {
    font-size: 16px;
}

/* 信息类型选择标签页 */
.info-type-tabs {
    display: flex;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    margin: 0;
    padding: 0;
}

.tab-btn {
    flex: 1;
    padding: 15px 10px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    border-bottom: 3px solid transparent;
}

.tab-btn.active {
    color: #4A90E2;
    border-bottom-color: #4A90E2;
    background: #f8f9ff;
}

.tab-btn i {
    font-size: 18px;
}

.tab-btn span {
    font-size: 12px;
    font-weight: 500;
}

/* 信息内容区域 */
.info-content {
    display: none;
    padding: 0;
}

.info-content.active {
    display: block;
}

/* 添加项目按钮 */
.add-item-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: auto;
}

.add-item-btn:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
}

/* 动态物资/装备项目 */
.material-item, .equipment-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
}

.material-item h5, .equipment-item h5 {
    color: #495057;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.remove-item-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.remove-item-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

/* 风险隐患填报页面样式 */
.tasks-list {
    padding: 0;
}

.task-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #4A90E2;
}

.task-item.completed {
    border-left-color: #28a745;
    opacity: 0.8;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.task-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    flex: 1;
}

.task-status {
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 12px;
}

.task-info {
    margin-bottom: 16px;
}

.task-info-row {
    display: flex;
    margin-bottom: 6px;
    font-size: 14px;
}

.info-label {
    color: #666;
    min-width: 80px;
    font-weight: 500;
}

.info-value {
    color: #333;
    flex: 1;
}

.task-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-info {
    background: #17a2b8;
    color: white;
    border: none;
}

.btn-info:hover {
    background: #138496;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
}

.large-modal {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

/* 任务详情样式 */
.task-detail {
    font-size: 14px;
}

.detail-section {
    margin-bottom: 20px;
}

.detail-section h4 {
    color: #333;
    font-size: 16px;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.detail-row {
    display: flex;
    margin-bottom: 8px;
    line-height: 1.5;
}

.detail-label {
    color: #666;
    min-width: 100px;
    font-weight: 500;
}

.detail-value {
    color: #333;
    flex: 1;
}

.attachments-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.attachment-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
}

.attachment-item i {
    color: #666;
}

.btn-link {
    background: none;
    border: none;
    color: #4A90E2;
    text-decoration: underline;
    cursor: pointer;
    padding: 0;
}

.btn-link:hover {
    color: #357abd;
}

/* 表单提示文字 */
.form-hint {
    color: #666;
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

/* 任务详情页面样式 */
.task-detail-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.task-header-info {
    background: linear-gradient(135deg, #4A90E2 0%, #357abd 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.task-name {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.task-status {
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
}

.task-status.completed {
    background: rgba(40,167,69,0.2);
    border-color: rgba(40,167,69,0.3);
}

.detail-section {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.detail-section:last-child {
    border-bottom: none;
}

.detail-section h4 {
    color: #333;
    font-size: 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-label {
    color: #666;
    font-size: 13px;
    font-weight: 500;
}

.detail-value {
    color: #333;
    font-size: 14px;
    line-height: 1.4;
}

.attachments-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.attachment-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.attachment-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #4A90E2;
    color: white;
    border-radius: 6px;
    font-size: 16px;
}

.attachment-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.attachment-name {
    color: #333;
    font-size: 14px;
    font-weight: 500;
}

.download-btn {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.action-buttons {
    padding: 20px;
    display: flex;
    gap: 12px;
}

.btn-large {
    flex: 1;
    padding: 12px 20px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-large:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* 整改任务样式 */
.rectification-task-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #4A90E2;
}

.rectification-task-item.completed {
    border-left-color: #28a745;
    opacity: 0.9;
}

.rectification-task-item.in-progress {
    border-left-color: #17a2b8;
}

.rectification-task-item.overdue {
    border-left-color: #dc3545;
}

.progress-section {
    margin: 12px 0;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.progress-label {
    font-size: 13px;
    font-weight: 500;
    color: #666;
}

.progress-percent {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.overdue-text {
    color: #dc3545 !important;
    font-weight: 600;
}

/* 整改任务详情页样式 */
.rectification-detail-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.progress-section-large {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.progress-bar-large {
    width: 100%;
    height: 12px;
    background: #e9ecef;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.detail-content {
    padding: 12px 0;
}

.detail-content p {
    margin: 0;
    line-height: 1.6;
    color: #333;
}

/* 整改任务编辑页样式 */
.task-basic-info {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 16px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #666;
    min-width: 80px;
    font-weight: 500;
}

.info-value {
    color: #333;
    flex: 1;
}

.progress-input-group {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-input-group input[type="range"] {
    flex: 1;
    height: 6px;
    background: #ddd;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.progress-input-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: #4A90E2;
    border-radius: 50%;
    cursor: pointer;
}

.progress-input-group input[type="range"]::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #4A90E2;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.progress-input-group input[type="number"] {
    width: 60px;
    text-align: center;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 6px;
}

.progress-unit {
    color: #666;
    font-size: 14px;
    font-weight: 500;
}

/* 审批任务样式 */
.approval-task-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #4A90E2;
}

.approval-task-item.approved {
    border-left-color: #28a745;
    opacity: 0.9;
}

.approval-task-item.rejected {
    border-left-color: #dc3545;
    opacity: 0.9;
}

.task-badges {
    display: flex;
    gap: 8px;
    margin-bottom: 12px;
    flex-wrap: wrap;
}

.risk-badge, .urgency-badge {
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    white-space: nowrap;
}

/* 审批详情页样式 */
.approval-detail-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.status-badges {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

/* 审批表单样式 */
.info-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.info-header h5 {
    margin: 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    flex: 1;
}

.info-badges {
    display: flex;
    gap: 6px;
    margin-left: 12px;
    flex-wrap: wrap;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.info-grid .info-item.full-width {
    grid-column: 1 / -1;
}

/* 审批按钮样式 */
.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
}

.btn-danger:hover {
    background: #c82333;
}

.action-buttons .btn-large {
    min-width: 120px;
}

/* 风险隐患填报页面样式 */
.tasks-list {
    padding: 0;
}

.task-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #4A90E2;
}

.task-item.completed {
    border-left-color: #28a745;
    opacity: 0.8;
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.task-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    flex: 1;
}

.task-status {
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 12px;
}

.task-info {
    margin-bottom: 16px;
}

.task-info-row {
    display: flex;
    margin-bottom: 6px;
    font-size: 14px;
}

.info-label {
    color: #666;
    min-width: 80px;
    font-weight: 500;
}

.info-value {
    color: #333;
    flex: 1;
}

.task-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
}

.btn-info {
    background: #17a2b8;
    color: white;
    border: none;
}

.btn-info:hover {
    background: #138496;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 20px 15px;
    margin-top: 0;
    background: white;
    border-top: 1px solid #f0f0f0;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .form-container {
        padding: 0;
    }

    .form-section {
        padding: 15px;
    }

    .form-actions {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .btn {
        width: 100%;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
