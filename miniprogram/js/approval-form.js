// 审批表单页面功能

// 模拟审批任务数据（与其他文件保持一致）
const mockApprovalTasks = [
    {
        id: 1,
        title: "G75兰海高速K1234+500护栏损坏隐患",
        submitter: "张三",
        submitterUnit: "市公路养护中心",
        submitDate: "2024-06-20",
        riskLevel: "high",
        riskLevelText: "高风险",
        location: "G75兰海高速K1234+500",
        description: "高速公路护栏因车辆撞击导致严重变形，存在安全隐患，需要立即整改",
        checkCategory: "special",
        checkCategoryText: "专项检查",
        city: "广州市",
        district: "天河区",
        belongUnit: "市公路养护中心",
        roadNumber: "G75",
        startStake: "K1234+400",
        endStake: "K1234+600",
        isHazardPoint: "yes",
        hasMeasures: "no",
        measures: "",
        attachments: ["现场照片1.jpg", "现场照片2.jpg"],
        status: "pending",
        statusText: "待审批",
        urgency: "high"
    },
    {
        id: 2,
        title: "S210省道K45+200排水沟堵塞",
        submitter: "李四",
        submitterUnit: "县交通运输局",
        submitDate: "2024-06-18",
        riskLevel: "medium",
        riskLevelText: "中风险",
        location: "S210省道K45+200",
        description: "省道排水沟被泥沙堵塞，雨季易积水影响行车安全",
        checkCategory: "routine",
        checkCategoryText: "日常检查",
        city: "深圳市",
        district: "宝安区",
        belongUnit: "县交通运输局",
        roadNumber: "S210",
        startStake: "K45+100",
        endStake: "K45+300",
        isHazardPoint: "yes",
        hasMeasures: "yes",
        measures: "已设置警示标志，安排人员定期巡查",
        attachments: ["排水沟照片.jpg", "检查报告.pdf"],
        status: "pending",
        statusText: "待审批",
        urgency: "medium"
    }
];

let currentTask = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTaskInfo();
    initializeForm();
    setDefaultDate();
});

// 加载任务基本信息
function loadTaskInfo() {
    // 从URL参数获取任务ID
    const urlParams = new URLSearchParams(window.location.search);
    const taskId = parseInt(urlParams.get('id'));
    
    currentTask = mockApprovalTasks.find(t => t.id === taskId);
    
    if (!currentTask) {
        showToast('审批任务不存在', 'error');
        setTimeout(() => {
            history.back();
        }, 1500);
        return;
    }
    
    // 显示任务基本信息
    const basicInfoHtml = `
        <div class="task-basic-info">
            <div class="info-header">
                <h5>${currentTask.title}</h5>
                <div class="info-badges">
                    <span class="risk-badge" style="background-color: ${getRiskLevelColor(currentTask.riskLevel)}">
                        ${currentTask.riskLevelText}
                    </span>
                    <span class="urgency-badge" style="background-color: ${getUrgencyColor(currentTask.urgency)}">
                        ${getUrgencyText(currentTask.urgency)}
                    </span>
                </div>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">申请人：</span>
                    <span class="info-value">${currentTask.submitter}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">申请单位：</span>
                    <span class="info-value">${currentTask.submitterUnit}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">申请时间：</span>
                    <span class="info-value">${currentTask.submitDate}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">风险位置：</span>
                    <span class="info-value">${currentTask.location}</span>
                </div>
                <div class="info-item full-width">
                    <span class="info-label">风险描述：</span>
                    <span class="info-value">${currentTask.description}</span>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('taskBasicInfo').innerHTML = basicInfoHtml;
}

// 初始化表单
function initializeForm() {
    const form = document.getElementById('approvalForm');
    if (form) {
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            showToast('正在提交审批结果...', 'info');
            
            setTimeout(() => {
                showToast('审批结果提交成功', 'success');
                console.log('审批数据:', data);
                
                // 提交成功后返回审批列表
                setTimeout(() => {
                    window.location.href = 'risk-approval.html';
                }, 1000);
            }, 1500);
        });
    }
}

// 设置默认日期
function setDefaultDate() {
    const today = new Date().toISOString().split('T')[0];
    const approvalDateInput = document.getElementById('approvalDate');
    if (approvalDateInput) {
        approvalDateInput.value = today;
    }
}

// 切换审批字段显示
function toggleApprovalFields() {
    const result = document.getElementById('approvalResult').value;
    const rejectionSection = document.getElementById('rejectionReasonSection');
    const conditionsSection = document.getElementById('approvalConditionsSection');
    const instructionsSection = document.getElementById('returnInstructionsSection');
    
    // 隐藏所有条件字段
    rejectionSection.style.display = 'none';
    conditionsSection.style.display = 'none';
    instructionsSection.style.display = 'none';
    
    // 根据选择显示对应字段
    switch (result) {
        case 'approved':
            conditionsSection.style.display = 'block';
            break;
        case 'rejected':
            rejectionSection.style.display = 'block';
            break;
        case 'returned':
            instructionsSection.style.display = 'block';
            break;
    }
}

// 获取风险等级颜色
function getRiskLevelColor(riskLevel) {
    switch (riskLevel) {
        case 'critical':
            return '#dc3545';
        case 'high':
            return '#fd7e14';
        case 'medium':
            return '#ffc107';
        default:
            return '#28a745';
    }
}

// 获取紧急程度颜色
function getUrgencyColor(urgency) {
    switch (urgency) {
        case 'critical':
            return '#dc3545';
        case 'high':
            return '#fd7e14';
        case 'medium':
            return '#ffc107';
        default:
            return '#6c757d';
    }
}

// 获取紧急程度文字
function getUrgencyText(urgency) {
    switch (urgency) {
        case 'critical':
            return '紧急';
        case 'high':
            return '重要';
        case 'medium':
            return '一般';
        default:
            return '普通';
    }
}
