// 整改任务详情页面功能

// 模拟整改任务数据（与rectification-task.js保持一致）
const mockRectificationTasks = [
    {
        id: 1,
        relatedHazard: "G75兰海高速K1234+500路段护栏损坏",
        responsibleUnit: "市公路养护中心",
        responsiblePerson: "张三",
        createDate: "2024-06-15",
        deadline: "2024-06-30",
        status: "pending",
        statusText: "待处理",
        progress: 0,
        description: "高速公路护栏因车辆撞击导致严重变形，存在安全隐患",
        currentProgress: "已安排人员现场勘查，制定整改方案中",
        attachments: []
    },
    {
        id: 2,
        relatedHazard: "S210省道K45+200排水沟堵塞",
        responsibleUnit: "县交通运输局",
        responsiblePerson: "李四",
        createDate: "2024-06-10",
        deadline: "2024-06-25",
        status: "in_progress",
        statusText: "整改中",
        progress: 60,
        description: "省道排水沟被泥沙堵塞，雨季易积水影响行车安全",
        currentProgress: "已清理60%的堵塞物，预计3天内完成全部清理工作",
        attachments: ["现场照片1.jpg", "清理进度报告.pdf"]
    },
    {
        id: 3,
        relatedHazard: "G72泉南高速K890+100标志牌缺失",
        responsibleUnit: "高速公路管理处",
        responsiblePerson: "王五",
        createDate: "2024-06-05",
        deadline: "2024-06-20",
        status: "completed",
        statusText: "已完成",
        progress: 100,
        description: "高速公路限速标志牌丢失，需要重新安装",
        currentProgress: "已完成新标志牌的采购和安装，通过验收",
        attachments: ["安装完成照片.jpg", "验收报告.pdf"]
    },
    {
        id: 4,
        relatedHazard: "国道G107K2156+800边坡滑坡风险",
        responsibleUnit: "省公路管理局",
        responsiblePerson: "赵六",
        createDate: "2024-06-12",
        deadline: "2024-07-10",
        status: "overdue",
        statusText: "已逾期",
        progress: 30,
        description: "国道边坡出现裂缝，雨季存在滑坡风险",
        currentProgress: "已完成地质勘探，正在制定加固方案，因技术复杂导致进度延缓",
        attachments: ["地质勘探报告.pdf"]
    }
];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadRectificationDetail();
});

// 加载整改任务详情
function loadRectificationDetail() {
    // 从URL参数获取任务ID
    const urlParams = new URLSearchParams(window.location.search);
    const taskId = parseInt(urlParams.get('id'));
    
    const task = mockRectificationTasks.find(t => t.id === taskId);
    
    if (!task) {
        showToast('任务不存在', 'error');
        setTimeout(() => {
            history.back();
        }, 1500);
        return;
    }
    
    const statusColor = getStatusColor(task.status);
    const progressColor = getProgressColor(task.progress);
    
    const content = `
        <div class="rectification-detail-card">
            <div class="task-header-info">
                <h2 class="task-name">${task.relatedHazard}</h2>
                <span class="task-status" style="background-color: ${statusColor}">
                    ${task.statusText}
                </span>
            </div>
            

            
            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> 基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">关联隐患</span>
                        <span class="detail-value">${task.relatedHazard}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">责任单位</span>
                        <span class="detail-value">${task.responsibleUnit}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">责任人</span>
                        <span class="detail-value">${task.responsiblePerson}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">创建日期</span>
                        <span class="detail-value">${task.createDate}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">整改期限</span>
                        <span class="detail-value ${task.status === 'overdue' ? 'overdue-text' : ''}">${task.deadline}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">任务状态</span>
                        <span class="detail-value">${task.statusText}</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-exclamation-triangle"></i> 隐患描述</h4>
                <div class="detail-content">
                    <p>${task.description}</p>
                </div>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-tasks"></i> 当前进度</h4>
                <div class="detail-content">
                    <p>${task.currentProgress}</p>
                </div>
            </div>
            
            ${task.attachments.length > 0 ? `
                <div class="detail-section">
                    <h4><i class="fas fa-paperclip"></i> 相关附件</h4>
                    <div class="attachments-grid">
                        ${task.attachments.map(file => `
                            <div class="attachment-card">
                                <div class="attachment-icon">
                                    <i class="fas fa-file-${getFileIcon(file)}"></i>
                                </div>
                                <div class="attachment-info">
                                    <span class="attachment-name">${file}</span>
                                    <button class="download-btn" onclick="downloadFile('${file}')">
                                        <i class="fas fa-download"></i> 下载
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${task.status !== 'completed' ? `
                <div class="action-buttons">
                    <button class="btn btn-primary btn-large" onclick="goToEdit(${task.id})">
                        <i class="fas fa-edit"></i> 更新进度
                    </button>
                </div>
            ` : ''}
        </div>
    `;
    
    document.getElementById('rectificationDetailContent').innerHTML = content;
}

// 获取状态颜色
function getStatusColor(status) {
    switch (status) {
        case 'completed':
            return '#28a745';
        case 'in_progress':
            return '#17a2b8';
        case 'overdue':
            return '#dc3545';
        default:
            return '#ffc107';
    }
}

// 获取进度条颜色
function getProgressColor(progress) {
    if (progress >= 80) return '#28a745';
    if (progress >= 50) return '#ffc107';
    if (progress >= 20) return '#17a2b8';
    return '#dc3545';
}

// 获取文件图标
function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    switch (ext) {
        case 'pdf':
            return 'pdf';
        case 'doc':
        case 'docx':
            return 'word';
        case 'xls':
        case 'xlsx':
            return 'excel';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return 'image';
        default:
            return 'alt';
    }
}

// 下载文件
function downloadFile(filename) {
    showToast(`正在下载 ${filename}`, 'info');
    // 这里可以添加实际的下载逻辑
}

// 跳转到编辑页面
function goToEdit(taskId) {
    window.location.href = `rectification-edit.html?id=${taskId}`;
}
