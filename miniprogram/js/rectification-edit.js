// 整改任务编辑页面功能

// 模拟整改任务数据（与其他文件保持一致）
const mockRectificationTasks = [
    {
        id: 1,
        relatedHazard: "G75兰海高速K1234+500路段护栏损坏",
        responsibleUnit: "市公路养护中心",
        responsiblePerson: "张三",
        createDate: "2024-06-15",
        deadline: "2024-06-30",
        status: "pending",
        statusText: "待处理",
        progress: 0,
        description: "高速公路护栏因车辆撞击导致严重变形，存在安全隐患",
        currentProgress: "已安排人员现场勘查，制定整改方案中",
        attachments: []
    },
    {
        id: 2,
        relatedHazard: "S210省道K45+200排水沟堵塞",
        responsibleUnit: "县交通运输局",
        responsiblePerson: "李四",
        createDate: "2024-06-10",
        deadline: "2024-06-25",
        status: "in_progress",
        statusText: "整改中",
        progress: 60,
        description: "省道排水沟被泥沙堵塞，雨季易积水影响行车安全",
        currentProgress: "已清理60%的堵塞物，预计3天内完成全部清理工作",
        attachments: ["现场照片1.jpg", "清理进度报告.pdf"]
    },
    {
        id: 3,
        relatedHazard: "G72泉南高速K890+100标志牌缺失",
        responsibleUnit: "高速公路管理处",
        responsiblePerson: "王五",
        createDate: "2024-06-05",
        deadline: "2024-06-20",
        status: "completed",
        statusText: "已完成",
        progress: 100,
        description: "高速公路限速标志牌丢失，需要重新安装",
        currentProgress: "已完成新标志牌的采购和安装，通过验收",
        attachments: ["安装完成照片.jpg", "验收报告.pdf"]
    },
    {
        id: 4,
        relatedHazard: "国道G107K2156+800边坡滑坡风险",
        responsibleUnit: "省公路管理局",
        responsiblePerson: "赵六",
        createDate: "2024-06-12",
        deadline: "2024-07-10",
        status: "overdue",
        statusText: "已逾期",
        progress: 30,
        description: "国道边坡出现裂缝，雨季存在滑坡风险",
        currentProgress: "已完成地质勘探，正在制定加固方案，因技术复杂导致进度延缓",
        attachments: ["地质勘探报告.pdf"]
    }
];

let currentTask = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTaskInfo();
    initializeForm();
});

// 加载任务基本信息
function loadTaskInfo() {
    // 从URL参数获取任务ID
    const urlParams = new URLSearchParams(window.location.search);
    const taskId = parseInt(urlParams.get('id'));
    
    currentTask = mockRectificationTasks.find(t => t.id === taskId);
    
    if (!currentTask) {
        showToast('任务不存在', 'error');
        setTimeout(() => {
            history.back();
        }, 1500);
        return;
    }
    
    // 显示任务基本信息
    const basicInfoHtml = `
        <div class="task-basic-info">
            <div class="info-item">
                <span class="info-label">关联隐患：</span>
                <span class="info-value">${currentTask.relatedHazard}</span>
            </div>
            <div class="info-item">
                <span class="info-label">责任单位：</span>
                <span class="info-value">${currentTask.responsibleUnit}</span>
            </div>
            <div class="info-item">
                <span class="info-label">责任人：</span>
                <span class="info-value">${currentTask.responsiblePerson}</span>
            </div>
            <div class="info-item">
                <span class="info-label">整改期限：</span>
                <span class="info-value">${currentTask.deadline}</span>
            </div>
        </div>
    `;
    
    document.getElementById('taskBasicInfo').innerHTML = basicInfoHtml;
    
    // 设置当前值
    document.getElementById('taskStatus').value = currentTask.status;
    document.getElementById('progressDescription').value = currentTask.currentProgress;
    
    // 根据状态显示/隐藏完成相关字段
    toggleCompletionFields();
}

// 初始化表单
function initializeForm() {
    const form = document.getElementById('rectificationEditForm');
    if (form) {
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            showToast('正在保存更新...', 'info');
            
            setTimeout(() => {
                showToast('整改进度更新成功', 'success');
                console.log('更新数据:', data);
                
                // 更新成功后返回任务列表
                setTimeout(() => {
                    window.location.href = 'rectification-task.html';
                }, 1000);
            }, 1500);
        });
    }
    
    // 监听状态变化
    const statusSelect = document.getElementById('taskStatus');
    if (statusSelect) {
        statusSelect.addEventListener('change', toggleCompletionFields);
    }
}



// 切换完成相关字段显示
function toggleCompletionFields() {
    const status = document.getElementById('taskStatus').value;
    const completionSection = document.getElementById('completionSection');
    const completionRemarkSection = document.getElementById('completionRemarkSection');
    const completionDate = document.getElementById('completionDate');
    
    if (status === 'completed') {
        completionSection.style.display = 'block';
        completionRemarkSection.style.display = 'block';
        completionDate.required = true;
        
        // 设置默认完成日期为今天
        if (!completionDate.value) {
            const today = new Date().toISOString().split('T')[0];
            completionDate.value = today;
        }
        

    } else {
        completionSection.style.display = 'none';
        completionRemarkSection.style.display = 'none';
        completionDate.required = false;
    }
}
