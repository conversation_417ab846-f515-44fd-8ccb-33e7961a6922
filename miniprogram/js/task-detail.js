// 任务详情页面功能

// 模拟任务数据（与risk-report.js保持一致）
const mockTasks = [
    {
        id: 1,
        checkName: "春季道路安全检查",
        issueUnit: "市交通运输局",
        checkField: "道路桥梁",
        deadline: "2024-06-30",
        status: "pending",
        statusText: "待填报",
        checkMethod: "现场检查",
        checkContent: "对辖区内主要道路进行全面安全检查，重点关注路面状况、护栏设施、标志标线等",
        workRequirement: "按照《公路安全检查规范》执行，确保检查全覆盖、无遗漏",
        remark: "雨季前重点检查排水设施",
        attachments: ["检查标准.pdf", "检查表格.xlsx"]
    },
    {
        id: 2,
        checkName: "桥梁结构专项检查",
        issueUnit: "省公路管理局",
        checkField: "桥梁工程",
        deadline: "2024-07-15",
        status: "pending",
        statusText: "待填报",
        checkMethod: "专业检测",
        checkContent: "对重点桥梁进行结构安全检测，包括桥面、桥墩、支座等关键部位",
        workRequirement: "委托具有相应资质的检测机构进行专业检测",
        remark: "重点关注使用年限超过15年的桥梁",
        attachments: ["技术规范.pdf"]
    },
    {
        id: 3,
        checkName: "隧道安全隐患排查",
        issueUnit: "区安全监督局",
        checkField: "隧道工程",
        deadline: "2024-06-25",
        status: "completed",
        statusText: "已完成",
        checkMethod: "综合检查",
        checkContent: "对隧道内照明、通风、消防、监控等设施进行全面检查",
        workRequirement: "确保隧道运营安全，及时发现和处理安全隐患",
        remark: "夏季高温期间加强通风系统检查",
        attachments: ["检查指南.pdf", "应急预案.doc"]
    }
];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTaskDetail();
});

// 加载任务详情
function loadTaskDetail() {
    // 从URL参数获取任务ID
    const urlParams = new URLSearchParams(window.location.search);
    const taskId = parseInt(urlParams.get('id'));
    
    const task = mockTasks.find(t => t.id === taskId);
    
    if (!task) {
        showToast('任务不存在', 'error');
        setTimeout(() => {
            history.back();
        }, 1500);
        return;
    }
    
    const content = `
        <div class="task-detail-card">
            <div class="task-header-info">
                <h2 class="task-name">${task.checkName}</h2>
                <span class="task-status ${task.status === 'completed' ? 'completed' : 'pending'}">
                    ${task.statusText}
                </span>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-info-circle"></i> 基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <span class="detail-label">检查名称</span>
                        <span class="detail-value">${task.checkName}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">下发单位</span>
                        <span class="detail-value">${task.issueUnit}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">检查领域</span>
                        <span class="detail-value">${task.checkField}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">截止时间</span>
                        <span class="detail-value">${task.deadline}</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-tasks"></i> 检查要求</h4>
                <div class="detail-grid">
                    <div class="detail-item full-width">
                        <span class="detail-label">检查方式</span>
                        <span class="detail-value">${task.checkMethod}</span>
                    </div>
                    <div class="detail-item full-width">
                        <span class="detail-label">检查内容</span>
                        <span class="detail-value">${task.checkContent}</span>
                    </div>
                    <div class="detail-item full-width">
                        <span class="detail-label">工作要求</span>
                        <span class="detail-value">${task.workRequirement}</span>
                    </div>
                    <div class="detail-item full-width">
                        <span class="detail-label">备注</span>
                        <span class="detail-value">${task.remark}</span>
                    </div>
                </div>
            </div>
            
            <div class="detail-section">
                <h4><i class="fas fa-paperclip"></i> 附件</h4>
                <div class="attachments-grid">
                    ${task.attachments.map(file => `
                        <div class="attachment-card">
                            <div class="attachment-icon">
                                <i class="fas fa-file-${getFileIcon(file)}"></i>
                            </div>
                            <div class="attachment-info">
                                <span class="attachment-name">${file}</span>
                                <button class="download-btn" onclick="downloadFile('${file}')">
                                    <i class="fas fa-download"></i> 下载
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            ${task.status === 'pending' ? `
                <div class="action-buttons">
                    <button class="btn btn-primary btn-large" onclick="goToReport(${task.id})">
                        <i class="fas fa-edit"></i> 开始填报
                    </button>
                    <button class="btn btn-success btn-large" onclick="completeTask(${task.id})">
                        <i class="fas fa-check"></i> 直接完结
                    </button>
                </div>
            ` : ''}
        </div>
    `;
    
    document.getElementById('taskDetailContent').innerHTML = content;
}

// 获取文件图标
function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    switch (ext) {
        case 'pdf':
            return 'pdf';
        case 'doc':
        case 'docx':
            return 'word';
        case 'xls':
        case 'xlsx':
            return 'excel';
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
            return 'image';
        default:
            return 'alt';
    }
}

// 下载文件
function downloadFile(filename) {
    showToast(`正在下载 ${filename}`, 'info');
    // 这里可以添加实际的下载逻辑
}

// 跳转到填报页面
function goToReport(taskId) {
    window.location.href = `risk-form.html?taskId=${taskId}`;
}

// 完结任务
function completeTask(taskId) {
    if (confirm('确认要完结此任务吗？完结后将无法再次填报。')) {
        showToast('正在完结任务...', 'info');
        
        setTimeout(() => {
            showToast('任务已完结', 'success');
            // 返回任务列表
            setTimeout(() => {
                window.location.href = 'risk-report.html';
            }, 1000);
        }, 1500);
    }
}
