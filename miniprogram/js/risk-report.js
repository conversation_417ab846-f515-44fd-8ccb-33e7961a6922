// 风险隐患填报功能

// 模拟任务数据
const mockTasks = [
    {
        id: 1,
        checkName: "春季道路安全检查",
        issueUnit: "市交通运输局",
        checkField: "道路桥梁",
        deadline: "2024-06-30",
        status: "pending",
        statusText: "待填报",
        checkMethod: "现场检查",
        checkContent: "对辖区内主要道路进行全面安全检查，重点关注路面状况、护栏设施、标志标线等",
        workRequirement: "按照《公路安全检查规范》执行，确保检查全覆盖、无遗漏",
        remark: "雨季前重点检查排水设施",
        attachments: ["检查标准.pdf", "检查表格.xlsx"]
    },
    {
        id: 2,
        checkName: "桥梁结构专项检查",
        issueUnit: "省公路管理局",
        checkField: "桥梁工程",
        deadline: "2024-07-15",
        status: "pending",
        statusText: "待填报",
        checkMethod: "专业检测",
        checkContent: "对重点桥梁进行结构安全检测，包括桥面、桥墩、支座等关键部位",
        workRequirement: "委托具有相应资质的检测机构进行专业检测",
        remark: "重点关注使用年限超过15年的桥梁",
        attachments: ["技术规范.pdf"]
    },
    {
        id: 3,
        checkName: "隧道安全隐患排查",
        issueUnit: "区安全监督局",
        checkField: "隧道工程",
        deadline: "2024-06-25",
        status: "completed",
        statusText: "已完成",
        checkMethod: "综合检查",
        checkContent: "对隧道内照明、通风、消防、监控等设施进行全面检查",
        workRequirement: "确保隧道运营安全，及时发现和处理安全隐患",
        remark: "夏季高温期间加强通风系统检查",
        attachments: ["检查指南.pdf", "应急预案.doc"]
    }
];

// 区县数据映射
const districtData = {
    beijing: [
        { value: "dongcheng", text: "东城区" },
        { value: "xicheng", text: "西城区" },
        { value: "chaoyang", text: "朝阳区" },
        { value: "fengtai", text: "丰台区" },
        { value: "haidian", text: "海淀区" }
    ],
    shanghai: [
        { value: "huangpu", text: "黄浦区" },
        { value: "xuhui", text: "徐汇区" },
        { value: "changning", text: "长宁区" },
        { value: "jingan", text: "静安区" },
        { value: "putuo", text: "普陀区" }
    ],
    guangzhou: [
        { value: "yuexiu", text: "越秀区" },
        { value: "liwan", text: "荔湾区" },
        { value: "haizhu", text: "海珠区" },
        { value: "tianhe", text: "天河区" },
        { value: "baiyun", text: "白云区" }
    ],
    shenzhen: [
        { value: "futian", text: "福田区" },
        { value: "luohu", text: "罗湖区" },
        { value: "nanshan", text: "南山区" },
        { value: "yantian", text: "盐田区" },
        { value: "baoan", text: "宝安区" }
    ]
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTasks();
    initializeForm();
});

// 加载任务列表
function loadTasks() {
    const tasksList = document.getElementById('tasksList');
    
    if (!tasksList) return;
    
    let tasksHtml = '';
    
    mockTasks.forEach(task => {
        const statusClass = task.status === 'completed' ? 'completed' : 'pending';
        const statusColor = task.status === 'completed' ? '#28a745' : '#ffc107';
        
        tasksHtml += `
            <div class="task-item ${statusClass}">
                <div class="task-header">
                    <h3 class="task-title">${task.checkName}</h3>
                    <span class="task-status" style="background-color: ${statusColor}">
                        ${task.statusText}
                    </span>
                </div>
                
                <div class="task-info">
                    <div class="task-info-row">
                        <span class="info-label">下发单位：</span>
                        <span class="info-value">${task.issueUnit}</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">检查领域：</span>
                        <span class="info-value">${task.checkField}</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">截止时间：</span>
                        <span class="info-value">${task.deadline}</span>
                    </div>
                </div>
                
                <div class="task-actions">
                    <button class="btn btn-info btn-sm" onclick="viewTask(${task.id})">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    ${task.status === 'pending' ? `
                        <button class="btn btn-primary btn-sm" onclick="reportTask(${task.id})">
                            <i class="fas fa-edit"></i> 填报
                        </button>
                        <button class="btn btn-success btn-sm" onclick="completeTask(${task.id})">
                            <i class="fas fa-check"></i> 完结
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    });
    
    tasksList.innerHTML = tasksHtml;
}

// 查看任务详情
function viewTask(taskId) {
    // 跳转到任务详情页面
    window.location.href = `task-detail.html?id=${taskId}`;
}

// 填报任务
function reportTask(taskId) {
    // 跳转到填报页面
    window.location.href = `risk-form.html?taskId=${taskId}`;
}

// 完结任务
function completeTask(taskId) {
    if (confirm('确认要完结此任务吗？完结后将无法再次填报。')) {
        const task = mockTasks.find(t => t.id === taskId);
        if (task) {
            task.status = 'completed';
            task.statusText = '已完成';
            loadTasks();
            showToast('任务已完结', 'success');
        }
    }
}


