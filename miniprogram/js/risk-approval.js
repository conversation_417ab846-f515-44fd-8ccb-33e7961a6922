// 风险隐患审批功能

// 模拟审批任务数据
const mockApprovalTasks = [
    {
        id: 1,
        title: "G75兰海高速K1234+500护栏损坏隐患",
        submitter: "张三",
        submitterUnit: "市公路养护中心",
        submitDate: "2024-06-20",
        riskLevel: "high",
        riskLevelText: "高风险",
        location: "G75兰海高速K1234+500",
        description: "高速公路护栏因车辆撞击导致严重变形，存在安全隐患，需要立即整改",
        checkCategory: "special",
        checkCategoryText: "专项检查",
        city: "广州市",
        district: "天河区",
        belongUnit: "市公路养护中心",
        roadNumber: "G75",
        startStake: "K1234+400",
        endStake: "K1234+600",
        isHazardPoint: "yes",
        hasMeasures: "no",
        measures: "",
        attachments: ["现场照片1.jpg", "现场照片2.jpg"],
        status: "pending",
        statusText: "待审批",
        urgency: "high"
    },
    {
        id: 2,
        title: "S210省道K45+200排水沟堵塞",
        submitter: "李四",
        submitterUnit: "县交通运输局",
        submitDate: "2024-06-18",
        riskLevel: "medium",
        riskLevelText: "中风险",
        location: "S210省道K45+200",
        description: "省道排水沟被泥沙堵塞，雨季易积水影响行车安全",
        checkCategory: "routine",
        checkCategoryText: "日常检查",
        city: "深圳市",
        district: "宝安区",
        belongUnit: "县交通运输局",
        roadNumber: "S210",
        startStake: "K45+100",
        endStake: "K45+300",
        isHazardPoint: "yes",
        hasMeasures: "yes",
        measures: "已设置警示标志，安排人员定期巡查",
        attachments: ["排水沟照片.jpg", "检查报告.pdf"],
        status: "pending",
        statusText: "待审批",
        urgency: "medium"
    },
    {
        id: 3,
        title: "G72泉南高速K890+100标志牌缺失",
        submitter: "王五",
        submitterUnit: "高速公路管理处",
        submitDate: "2024-06-15",
        riskLevel: "low",
        riskLevelText: "低风险",
        location: "G72泉南高速K890+100",
        description: "高速公路限速标志牌丢失，需要重新安装",
        checkCategory: "routine",
        checkCategoryText: "日常检查",
        city: "北京市",
        district: "朝阳区",
        belongUnit: "高速公路管理处",
        roadNumber: "G72",
        startStake: "K890+000",
        endStake: "K890+200",
        isHazardPoint: "no",
        hasMeasures: "yes",
        measures: "已临时设置移动标志牌",
        attachments: ["缺失位置照片.jpg"],
        status: "approved",
        statusText: "已通过",
        urgency: "low",
        approvalDate: "2024-06-16",
        approvalComment: "同意处理方案，请尽快完成标志牌安装"
    },
    {
        id: 4,
        title: "国道G107K2156+800边坡滑坡风险",
        submitter: "赵六",
        submitterUnit: "省公路管理局",
        submitDate: "2024-06-12",
        riskLevel: "critical",
        riskLevelText: "重大风险",
        location: "国道G107K2156+800",
        description: "国道边坡出现裂缝，雨季存在滑坡风险，威胁行车安全",
        checkCategory: "emergency",
        checkCategoryText: "应急检查",
        city: "上海市",
        district: "浦东新区",
        belongUnit: "省公路管理局",
        roadNumber: "G107",
        startStake: "K2156+700",
        endStake: "K2156+900",
        isHazardPoint: "yes",
        hasMeasures: "yes",
        measures: "已设置安全警示区域，限制车辆通行",
        attachments: ["边坡裂缝照片.jpg", "地质勘探报告.pdf", "应急处置方案.doc"],
        status: "rejected",
        statusText: "已拒绝",
        urgency: "critical",
        approvalDate: "2024-06-13",
        approvalComment: "处置方案不够完善，需要补充详细的地质加固方案后重新提交"
    }
];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadApprovalTasks();
});

// 加载审批任务列表
function loadApprovalTasks() {
    const tasksList = document.getElementById('approvalTasksList');
    
    if (!tasksList) return;
    
    let tasksHtml = '';
    
    mockApprovalTasks.forEach(task => {
        const statusClass = getStatusClass(task.status);
        const statusColor = getStatusColor(task.status);
        const urgencyColor = getUrgencyColor(task.urgency);
        const riskLevelColor = getRiskLevelColor(task.riskLevel);
        
        tasksHtml += `
            <div class="approval-task-item ${statusClass}">
                <div class="task-header">
                    <h3 class="task-title">${task.title}</h3>
                    <span class="task-status" style="background-color: ${statusColor}">
                        ${task.statusText}
                    </span>
                </div>
                
                <div class="task-badges">
                    <span class="risk-badge" style="background-color: ${riskLevelColor}">
                        ${task.riskLevelText}
                    </span>
                    <span class="urgency-badge" style="background-color: ${urgencyColor}">
                        ${getUrgencyText(task.urgency)}
                    </span>
                </div>
                
                <div class="task-info">
                    <div class="task-info-row">
                        <span class="info-label">提交人：</span>
                        <span class="info-value">${task.submitter} (${task.submitterUnit})</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">位置：</span>
                        <span class="info-value">${task.location}</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">提交时间：</span>
                        <span class="info-value">${task.submitDate}</span>
                    </div>
                    ${task.approvalDate ? `
                        <div class="task-info-row">
                            <span class="info-label">审批时间：</span>
                            <span class="info-value">${task.approvalDate}</span>
                        </div>
                    ` : ''}
                </div>
                
                <div class="task-actions">
                    <button class="btn btn-info btn-sm" onclick="viewApprovalTask(${task.id})">
                        <i class="fas fa-eye"></i> 查看详情
                    </button>
                    ${task.status === 'pending' ? `
                        <button class="btn btn-success btn-sm" onclick="approveTask(${task.id})">
                            <i class="fas fa-check"></i> 审批
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    });
    
    tasksList.innerHTML = tasksHtml;
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 'approved':
            return 'approved';
        case 'rejected':
            return 'rejected';
        default:
            return 'pending';
    }
}

// 获取状态颜色
function getStatusColor(status) {
    switch (status) {
        case 'approved':
            return '#28a745';
        case 'rejected':
            return '#dc3545';
        default:
            return '#ffc107';
    }
}

// 获取紧急程度颜色
function getUrgencyColor(urgency) {
    switch (urgency) {
        case 'critical':
            return '#dc3545';
        case 'high':
            return '#fd7e14';
        case 'medium':
            return '#ffc107';
        default:
            return '#6c757d';
    }
}

// 获取风险等级颜色
function getRiskLevelColor(riskLevel) {
    switch (riskLevel) {
        case 'critical':
            return '#dc3545';
        case 'high':
            return '#fd7e14';
        case 'medium':
            return '#ffc107';
        default:
            return '#28a745';
    }
}

// 获取紧急程度文字
function getUrgencyText(urgency) {
    switch (urgency) {
        case 'critical':
            return '紧急';
        case 'high':
            return '重要';
        case 'medium':
            return '一般';
        default:
            return '普通';
    }
}

// 查看审批任务详情
function viewApprovalTask(taskId) {
    // 跳转到审批任务详情页面
    window.location.href = `approval-detail.html?id=${taskId}`;
}

// 审批任务
function approveTask(taskId) {
    // 跳转到审批页面
    window.location.href = `approval-form.html?id=${taskId}`;
}
