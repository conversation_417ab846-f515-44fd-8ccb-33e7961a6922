// 小程序通用JavaScript功能

// 页面导航功能
function navigateTo(page) {
    window.location.href = page;
}

// 返回上一页
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        navigateTo('index.html');
    }
}

// 显示提示信息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加toast样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        animation: slideDown 0.3s ease-out;
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动移除
    setTimeout(() => {
        toast.style.animation = 'slideUp 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 添加toast动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// 手机号快捷登录
function phoneLogin() {
    showToast('正在获取手机号...', 'info');

    // 模拟微信授权获取手机号
    setTimeout(() => {
        showToast('登录成功', 'success');
        setTimeout(() => {
            navigateTo('index.html');
        }, 1000);
    }, 1500);
}

// 账号登录
function emailLogin() {
    showToast('跳转到账号登录页面', 'info');
    // 这里可以跳转到传统的账号密码登录页面
}

// 短信登录
function smsLogin() {
    showToast('跳转到短信验证登录页面', 'info');
    // 这里可以跳转到短信验证码登录页面
}

// 传统登录功能（保留用于其他页面）
function login() {
    const username = document.getElementById('username')?.value;
    const password = document.getElementById('password')?.value;

    if (!username || !password) {
        showToast('请输入用户名和密码', 'error');
        return;
    }

    // 模拟登录验证
    if (username === 'admin' && password === '123456') {
        showToast('登录成功', 'success');
        setTimeout(() => {
            navigateTo('index.html');
        }, 1000);
    } else {
        showToast('用户名或密码错误', 'error');
    }
}

// 获取当前位置经纬度
function getCurrentLocation() {
    const longitudeInput = document.getElementById('longitude');
    const latitudeInput = document.getElementById('latitude');

    if (!navigator.geolocation) {
        showToast('您的浏览器不支持地理位置获取', 'error');
        return;
    }

    showToast('正在获取位置信息...', 'info');

    navigator.geolocation.getCurrentPosition(
        function(position) {
            const longitude = position.coords.longitude.toFixed(6);
            const latitude = position.coords.latitude.toFixed(6);

            longitudeInput.value = longitude;
            latitudeInput.value = latitude;

            showToast('位置获取成功', 'success');
        },
        function(error) {
            let errorMessage = '位置获取失败';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = '用户拒绝了位置请求';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = '位置信息不可用';
                    break;
                case error.TIMEOUT:
                    errorMessage = '位置请求超时';
                    break;
            }
            showToast(errorMessage, 'error');

            // 提供手动输入提示
            showToast('您可以手动输入经纬度坐标', 'info');
            longitudeInput.removeAttribute('readonly');
            latitudeInput.removeAttribute('readonly');
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// 重置应急事件表单
function resetEmergencyForm() {
    const form = document.getElementById('emergencyForm');
    if (form) {
        form.reset();
        // 重新设置经纬度为只读
        const longitudeInput = document.getElementById('longitude');
        const latitudeInput = document.getElementById('latitude');
        if (longitudeInput) longitudeInput.setAttribute('readonly', 'readonly');
        if (latitudeInput) latitudeInput.setAttribute('readonly', 'readonly');

        showToast('表单已重置', 'info');
    }
}

// 切换事故类型专项字段显示
function toggleAccidentFields() {
    const eventType = document.getElementById('eventType').value;
    const trafficFields = document.getElementById('trafficAccidentFields');

    console.log('事件类型选择:', eventType);

    // 隐藏所有专项字段
    if (trafficFields) trafficFields.style.display = 'none';

    // 根据事件类型显示对应的专项字段
    switch(eventType) {
        case 'traffic-accident':
            if (trafficFields) {
                trafficFields.style.display = 'block';
                console.log('显示道路交通事故专项字段');
            }
            break;
        // 可以在这里添加其他事件类型的专项字段
        default:
            // 其他类型暂不显示专项字段
            break;
    }
}

// 应急信息录入相关功能
let materialCounter = 0;
let equipmentCounter = 0;

// 切换信息类型标签页
function switchInfoType(type) {
    // 移除所有活动状态
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.info-content').forEach(content => content.classList.remove('active'));

    // 激活选中的标签页
    document.getElementById(type + 'Tab').classList.add('active');
    document.getElementById(type + 'Info').classList.add('active');
}

// 添加仓库物资
function addWarehouseMaterial() {
    materialCounter++;
    const materialsContainer = document.getElementById('warehouseMaterials');

    const materialHtml = `
        <div class="material-item" id="material-${materialCounter}">
            <h5>
                <span><i class="fas fa-box"></i> 物资组 ${materialCounter}</span>
                <div class="item-actions">
                    <button type="button" class="add-sub-item-btn" onclick="addSubMaterial(${materialCounter})">
                        <i class="fas fa-plus"></i> 添加物资
                    </button>
                    <button type="button" class="remove-item-btn" onclick="removeMaterial(${materialCounter})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </h5>

            <div class="sub-materials" id="subMaterials-${materialCounter}">
                <!-- 子物资项目将在这里添加 -->
            </div>
        </div>
    `;

    materialsContainer.insertAdjacentHTML('beforeend', materialHtml);

    // 自动添加第一个物资项目
    addSubMaterial(materialCounter);
}

// 添加子物资项目
function addSubMaterial(parentId) {
    const subMaterialsContainer = document.getElementById(`subMaterials-${parentId}`);
    const subMaterialCount = subMaterialsContainer.children.length + 1;
    const subMaterialId = `${parentId}_${subMaterialCount}`;

    const subMaterialHtml = `
        <div class="sub-material-item" id="subMaterial-${subMaterialId}">
            <div class="sub-material-header">
                <span class="sub-material-title">物资 ${subMaterialCount}</span>
                <button type="button" class="remove-sub-item-btn" onclick="removeSubMaterial('${subMaterialId}')">
                    <i class="fas fa-minus"></i>
                </button>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>物资名称 <span class="required">*</span></label>
                    <input type="text" name="materialName_${subMaterialId}" placeholder="请输入物资名称" required>
                </div>
                <div class="form-group">
                    <label>规格型号</label>
                    <input type="text" name="materialSpec_${subMaterialId}" placeholder="请输入规格型号">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>物资类别 <span class="required">*</span></label>
                    <select name="materialCategory_${subMaterialId}" required>
                        <option value="">请选择类别</option>
                        <option value="emergency-supplies">应急物资</option>
                        <option value="emergency-equipment">应急装备</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>数量 <span class="required">*</span></label>
                    <input type="number" name="materialQuantity_${subMaterialId}" min="1" placeholder="请输入数量" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>单位 <span class="required">*</span></label>
                    <input type="text" name="materialUnit_${subMaterialId}" placeholder="如：个、台、箱" required>
                </div>
                <div class="form-group">
                    <label>状态 <span class="required">*</span></label>
                    <select name="materialStatus_${subMaterialId}" required>
                        <option value="">请选择状态</option>
                        <option value="available">可用</option>
                        <option value="in-use">使用中</option>
                        <option value="maintenance">维护中</option>
                        <option value="damaged">损坏</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>有效期</label>
                    <input type="date" name="materialExpiry_${subMaterialId}">
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <input type="text" name="materialRemark_${subMaterialId}" placeholder="请输入备注信息">
                </div>
            </div>
        </div>
    `;

    subMaterialsContainer.insertAdjacentHTML('beforeend', subMaterialHtml);
}

// 移除子物资项目
function removeSubMaterial(subId) {
    const subMaterialElement = document.getElementById(`subMaterial-${subId}`);
    if (subMaterialElement) {
        subMaterialElement.remove();

        // 重新编号剩余的子物资
        const parentId = subId.split('_')[0];
        const subMaterialsContainer = document.getElementById(`subMaterials-${parentId}`);
        const subMaterials = subMaterialsContainer.querySelectorAll('.sub-material-item');

        subMaterials.forEach((item, index) => {
            const newNumber = index + 1;
            const titleElement = item.querySelector('.sub-material-title');
            if (titleElement) {
                titleElement.textContent = `物资 ${newNumber}`;
            }
        });
    }
}

// 移除物资项目
function removeMaterial(id) {
    const materialElement = document.getElementById(`material-${id}`);
    if (materialElement) {
        materialElement.remove();
    }
}

// 添加队伍装备
function addTeamEquipment() {
    equipmentCounter++;
    const equipmentsContainer = document.getElementById('teamEquipments');

    const equipmentHtml = `
        <div class="equipment-item" id="equipment-${equipmentCounter}">
            <h5>
                <span><i class="fas fa-tools"></i> 装备组 ${equipmentCounter}</span>
                <div class="item-actions">
                    <button type="button" class="add-sub-item-btn" onclick="addSubEquipment(${equipmentCounter})">
                        <i class="fas fa-plus"></i> 添加装备
                    </button>
                    <button type="button" class="remove-item-btn" onclick="removeEquipment(${equipmentCounter})">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </h5>

            <div class="sub-equipments" id="subEquipments-${equipmentCounter}">
                <!-- 子装备项目将在这里添加 -->
            </div>
        </div>
    `;

    equipmentsContainer.insertAdjacentHTML('beforeend', equipmentHtml);

    // 自动添加第一个装备项目
    addSubEquipment(equipmentCounter);
}

// 添加子装备项目
function addSubEquipment(parentId) {
    const subEquipmentsContainer = document.getElementById(`subEquipments-${parentId}`);
    const subEquipmentCount = subEquipmentsContainer.children.length + 1;
    const subEquipmentId = `${parentId}_${subEquipmentCount}`;

    const subEquipmentHtml = `
        <div class="sub-equipment-item" id="subEquipment-${subEquipmentId}">
            <div class="sub-material-header">
                <span class="sub-material-title">装备 ${subEquipmentCount}</span>
                <button type="button" class="remove-sub-item-btn" onclick="removeSubEquipment('${subEquipmentId}')">
                    <i class="fas fa-minus"></i>
                </button>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>装备名称 <span class="required">*</span></label>
                    <input type="text" name="equipmentName_${subEquipmentId}" placeholder="请输入装备名称" required>
                </div>
                <div class="form-group">
                    <label>规格型号</label>
                    <input type="text" name="equipmentSpec_${subEquipmentId}" placeholder="请输入规格型号">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>装备类别 <span class="required">*</span></label>
                    <select name="equipmentCategory_${subEquipmentId}" required>
                        <option value="">请选择类别</option>
                        <option value="emergency-supplies">应急物资</option>
                        <option value="emergency-equipment">应急装备</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>数量 <span class="required">*</span></label>
                    <input type="number" name="equipmentQuantity_${subEquipmentId}" min="1" placeholder="请输入数量" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>单位 <span class="required">*</span></label>
                    <input type="text" name="equipmentUnit_${subEquipmentId}" placeholder="如：个、台、套" required>
                </div>
                <div class="form-group">
                    <label>状态 <span class="required">*</span></label>
                    <select name="equipmentStatus_${subEquipmentId}" required>
                        <option value="">请选择状态</option>
                        <option value="available">可用</option>
                        <option value="in-use">使用中</option>
                        <option value="maintenance">维护中</option>
                        <option value="damaged">损坏</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>有效期</label>
                    <input type="date" name="equipmentExpiry_${subEquipmentId}">
                </div>
                <div class="form-group">
                    <label>备注</label>
                    <input type="text" name="equipmentRemark_${subEquipmentId}" placeholder="请输入备注信息">
                </div>
            </div>
        </div>
    `;

    subEquipmentsContainer.insertAdjacentHTML('beforeend', subEquipmentHtml);
}

// 移除子装备项目
function removeSubEquipment(subId) {
    const subEquipmentElement = document.getElementById(`subEquipment-${subId}`);
    if (subEquipmentElement) {
        subEquipmentElement.remove();

        // 重新编号剩余的子装备
        const parentId = subId.split('_')[0];
        const subEquipmentsContainer = document.getElementById(`subEquipments-${parentId}`);
        const subEquipments = subEquipmentsContainer.querySelectorAll('.sub-equipment-item');

        subEquipments.forEach((item, index) => {
            const newNumber = index + 1;
            const titleElement = item.querySelector('.sub-material-title');
            if (titleElement) {
                titleElement.textContent = `装备 ${newNumber}`;
            }
        });
    }
}

// 移除装备项目
function removeEquipment(id) {
    const equipmentElement = document.getElementById(`equipment-${id}`);
    if (equipmentElement) {
        equipmentElement.remove();
    }
}

// 获取仓库位置经纬度
function getCurrentLocationForWarehouse() {
    if (navigator.geolocation) {
        showToast('正在获取位置信息...', 'info');
        navigator.geolocation.getCurrentPosition(
            function(position) {
                document.getElementById('warehouseLongitude').value = position.coords.longitude.toFixed(6);
                document.getElementById('warehouseLatitude').value = position.coords.latitude.toFixed(6);
                showToast('位置信息获取成功', 'success');
            },
            function(error) {
                showToast('位置信息获取失败，请手动输入', 'error');
                console.error('位置获取错误:', error);
            }
        );
    } else {
        showToast('浏览器不支持位置获取功能', 'error');
    }
}

// 获取救援队伍位置经纬度
function getCurrentLocationForTeam() {
    if (navigator.geolocation) {
        showToast('正在获取位置信息...', 'info');
        navigator.geolocation.getCurrentPosition(
            function(position) {
                document.getElementById('teamLongitude').value = position.coords.longitude.toFixed(6);
                document.getElementById('teamLatitude').value = position.coords.latitude.toFixed(6);
                showToast('位置信息获取成功', 'success');
            },
            function(error) {
                showToast('位置信息获取失败，请手动输入', 'error');
                console.error('位置获取错误:', error);
            }
        );
    } else {
        showToast('浏览器不支持位置获取功能', 'error');
    }
}

// 重置表单
function resetWarehouseForm() {
    document.getElementById('warehouseForm').reset();
    document.getElementById('warehouseMaterials').innerHTML = '';
    materialCounter = 0;
}

function resetTeamForm() {
    document.getElementById('teamForm').reset();
    document.getElementById('teamEquipments').innerHTML = '';
    equipmentCounter = 0;
}

function resetExpertForm() {
    document.getElementById('expertForm').reset();
}

// 应急事件表单提交处理
function handleEmergencyFormSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // 验证必填字段
    const requiredFields = ['eventTitle', 'eventType', 'eventTime', 'eventLevel', 'eventLocation', 'eventDescription', 'impactScope'];
    const missingFields = requiredFields.filter(field => !data[field] || data[field].trim() === '');

    if (missingFields.length > 0) {
        showToast('请填写所有必填字段', 'error');
        return;
    }

    // 模拟提交
    showToast('正在提交事件报告...', 'info');

    setTimeout(() => {
        showToast('事件报告提交成功', 'success');
        console.log('提交的数据:', data);

        // 可以在这里添加实际的API调用
        // submitToAPI(data);
    }, 1500);
}

// 页面加载完成后绑定表单提交事件
document.addEventListener('DOMContentLoaded', function() {
    // 绑定仓库表单提交
    const warehouseForm = document.getElementById('warehouseForm');
    if (warehouseForm) {
        warehouseForm.addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            showToast('正在保存仓库信息...', 'info');
            setTimeout(() => {
                showToast('仓库信息保存成功', 'success');
                console.log('仓库数据:', data);
            }, 1500);
        });
    }

    // 绑定队伍表单提交
    const teamForm = document.getElementById('teamForm');
    if (teamForm) {
        teamForm.addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            showToast('正在保存队伍信息...', 'info');
            setTimeout(() => {
                showToast('队伍信息保存成功', 'success');
                console.log('队伍数据:', data);
            }, 1500);
        });
    }

    // 绑定专家表单提交
    const expertForm = document.getElementById('expertForm');
    if (expertForm) {
        expertForm.addEventListener('submit', function(event) {
            event.preventDefault();
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            showToast('正在保存专家信息...', 'info');
            setTimeout(() => {
                showToast('专家信息保存成功', 'success');
                console.log('专家数据:', data);
            }, 1500);
        });
    }
});

// 模拟表单提交
function submitForm(formType) {
    showToast('提交成功', 'success');
    setTimeout(() => {
        goBack();
    }, 1500);
}

// 模拟审批操作
function approveItem(action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';
    showToast(`审批${actionText}成功`, 'success');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面淡入效果
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.3s ease-in';
        document.body.style.opacity = '1';
    }, 100);
    
    // 为所有功能卡片添加点击事件
    const functionCards = document.querySelectorAll('.function-card');
    functionCards.forEach(card => {
        card.addEventListener('click', function() {
            const href = this.getAttribute('data-href');
            if (href) {
                navigateTo(href);
            }
        });
    });
    
    // 为返回按钮添加事件
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', goBack);
    }
    
    // 为登录按钮添加事件
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.addEventListener('click', login);
    }
    
    // 为表单输入框添加回车事件
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const loginBtn = document.querySelector('.login-btn');
                if (loginBtn) {
                    login();
                }
            }
        });
    });

    // 为应急事件表单添加提交事件
    const emergencyForm = document.getElementById('emergencyForm');
    if (emergencyForm) {
        emergencyForm.addEventListener('submit', handleEmergencyFormSubmit);
    }

    // 设置当前时间为默认值
    const eventTimeInput = document.getElementById('eventTime');
    if (eventTimeInput) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        eventTimeInput.value = localDateTime;
    }
});

// 工具函数：格式化日期
function formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 工具函数：格式化时间
function formatDateTime(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 模拟数据加载
function loadData(callback) {
    // 显示加载状态
    const loadingElement = document.querySelector('.loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }
    
    // 模拟网络延迟
    setTimeout(() => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        if (callback) {
            callback();
        }
    }, 1000);
}

// 底部导航激活状态管理
function setActiveNav(activeIndex) {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        if (index === activeIndex) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}
