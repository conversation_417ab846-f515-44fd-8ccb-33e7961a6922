// 小程序通用JavaScript功能

// 页面导航功能
function navigateTo(page) {
    window.location.href = page;
}

// 返回上一页
function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        navigateTo('index.html');
    }
}

// 显示提示信息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加toast样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 12px 24px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        animation: slideDown 0.3s ease-out;
    `;
    
    document.body.appendChild(toast);
    
    // 3秒后自动移除
    setTimeout(() => {
        toast.style.animation = 'slideUp 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 添加toast动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        to {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
        }
    }
`;
document.head.appendChild(style);

// 手机号快捷登录
function phoneLogin() {
    showToast('正在获取手机号...', 'info');

    // 模拟微信授权获取手机号
    setTimeout(() => {
        showToast('登录成功', 'success');
        setTimeout(() => {
            navigateTo('index.html');
        }, 1000);
    }, 1500);
}

// 账号登录
function emailLogin() {
    showToast('跳转到账号登录页面', 'info');
    // 这里可以跳转到传统的账号密码登录页面
}

// 短信登录
function smsLogin() {
    showToast('跳转到短信验证登录页面', 'info');
    // 这里可以跳转到短信验证码登录页面
}

// 传统登录功能（保留用于其他页面）
function login() {
    const username = document.getElementById('username')?.value;
    const password = document.getElementById('password')?.value;

    if (!username || !password) {
        showToast('请输入用户名和密码', 'error');
        return;
    }

    // 模拟登录验证
    if (username === 'admin' && password === '123456') {
        showToast('登录成功', 'success');
        setTimeout(() => {
            navigateTo('index.html');
        }, 1000);
    } else {
        showToast('用户名或密码错误', 'error');
    }
}

// 获取当前位置经纬度
function getCurrentLocation() {
    const longitudeInput = document.getElementById('longitude');
    const latitudeInput = document.getElementById('latitude');

    if (!navigator.geolocation) {
        showToast('您的浏览器不支持地理位置获取', 'error');
        return;
    }

    showToast('正在获取位置信息...', 'info');

    navigator.geolocation.getCurrentPosition(
        function(position) {
            const longitude = position.coords.longitude.toFixed(6);
            const latitude = position.coords.latitude.toFixed(6);

            longitudeInput.value = longitude;
            latitudeInput.value = latitude;

            showToast('位置获取成功', 'success');
        },
        function(error) {
            let errorMessage = '位置获取失败';
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    errorMessage = '用户拒绝了位置请求';
                    break;
                case error.POSITION_UNAVAILABLE:
                    errorMessage = '位置信息不可用';
                    break;
                case error.TIMEOUT:
                    errorMessage = '位置请求超时';
                    break;
            }
            showToast(errorMessage, 'error');

            // 提供手动输入提示
            showToast('您可以手动输入经纬度坐标', 'info');
            longitudeInput.removeAttribute('readonly');
            latitudeInput.removeAttribute('readonly');
        },
        {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 60000
        }
    );
}

// 重置应急事件表单
function resetEmergencyForm() {
    const form = document.getElementById('emergencyForm');
    if (form) {
        form.reset();
        // 重新设置经纬度为只读
        const longitudeInput = document.getElementById('longitude');
        const latitudeInput = document.getElementById('latitude');
        if (longitudeInput) longitudeInput.setAttribute('readonly', 'readonly');
        if (latitudeInput) latitudeInput.setAttribute('readonly', 'readonly');

        showToast('表单已重置', 'info');
    }
}

// 应急事件表单提交处理
function handleEmergencyFormSubmit(event) {
    event.preventDefault();

    const formData = new FormData(event.target);
    const data = Object.fromEntries(formData.entries());

    // 验证必填字段
    const requiredFields = ['eventTitle', 'eventType', 'eventTime', 'eventLevel', 'eventLocation', 'eventDescription', 'impactScope'];
    const missingFields = requiredFields.filter(field => !data[field] || data[field].trim() === '');

    if (missingFields.length > 0) {
        showToast('请填写所有必填字段', 'error');
        return;
    }

    // 模拟提交
    showToast('正在提交事件报告...', 'info');

    setTimeout(() => {
        showToast('事件报告提交成功', 'success');
        console.log('提交的数据:', data);

        // 可以在这里添加实际的API调用
        // submitToAPI(data);
    }, 1500);
}

// 模拟表单提交
function submitForm(formType) {
    showToast('提交成功', 'success');
    setTimeout(() => {
        goBack();
    }, 1500);
}

// 模拟审批操作
function approveItem(action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';
    showToast(`审批${actionText}成功`, 'success');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面淡入效果
    document.body.style.opacity = '0';
    setTimeout(() => {
        document.body.style.transition = 'opacity 0.3s ease-in';
        document.body.style.opacity = '1';
    }, 100);
    
    // 为所有功能卡片添加点击事件
    const functionCards = document.querySelectorAll('.function-card');
    functionCards.forEach(card => {
        card.addEventListener('click', function() {
            const href = this.getAttribute('data-href');
            if (href) {
                navigateTo(href);
            }
        });
    });
    
    // 为返回按钮添加事件
    const backBtn = document.querySelector('.back-btn');
    if (backBtn) {
        backBtn.addEventListener('click', goBack);
    }
    
    // 为登录按钮添加事件
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.addEventListener('click', login);
    }
    
    // 为表单输入框添加回车事件
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
        input.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const loginBtn = document.querySelector('.login-btn');
                if (loginBtn) {
                    login();
                }
            }
        });
    });

    // 为应急事件表单添加提交事件
    const emergencyForm = document.getElementById('emergencyForm');
    if (emergencyForm) {
        emergencyForm.addEventListener('submit', handleEmergencyFormSubmit);
    }

    // 设置当前时间为默认值
    const eventTimeInput = document.getElementById('eventTime');
    if (eventTimeInput) {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        eventTimeInput.value = localDateTime;
    }
});

// 工具函数：格式化日期
function formatDate(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 工具函数：格式化时间
function formatDateTime(date) {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 模拟数据加载
function loadData(callback) {
    // 显示加载状态
    const loadingElement = document.querySelector('.loading');
    if (loadingElement) {
        loadingElement.style.display = 'inline-block';
    }
    
    // 模拟网络延迟
    setTimeout(() => {
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
        if (callback) {
            callback();
        }
    }, 1000);
}

// 底部导航激活状态管理
function setActiveNav(activeIndex) {
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        if (index === activeIndex) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}
