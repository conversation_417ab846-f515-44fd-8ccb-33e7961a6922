// 风险隐患填报表单功能

// 区县数据映射
const districtData = {
    beijing: [
        { value: "dongcheng", text: "东城区" },
        { value: "xicheng", text: "西城区" },
        { value: "chaoyang", text: "朝阳区" },
        { value: "fengtai", text: "丰台区" },
        { value: "haidian", text: "海淀区" }
    ],
    shanghai: [
        { value: "huangpu", text: "黄浦区" },
        { value: "xuhui", text: "徐汇区" },
        { value: "changning", text: "长宁区" },
        { value: "jingan", text: "静安区" },
        { value: "putuo", text: "普陀区" }
    ],
    guangzhou: [
        { value: "yuexiu", text: "越秀区" },
        { value: "liwan", text: "荔湾区" },
        { value: "haizhu", text: "海珠区" },
        { value: "tianhe", text: "天河区" },
        { value: "baiyun", text: "白云区" }
    ],
    shenzhen: [
        { value: "futian", text: "福田区" },
        { value: "luohu", text: "罗湖区" },
        { value: "nanshan", text: "南山区" },
        { value: "yantian", text: "盐田区" },
        { value: "baoan", text: "宝安区" }
    ]
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    setDefaultDate();
});

// 初始化表单
function initializeForm() {
    const form = document.getElementById('riskReportForm');
    if (form) {
        form.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());
            
            showToast('正在提交填报信息...', 'info');
            
            setTimeout(() => {
                showToast('风险隐患填报提交成功', 'success');
                console.log('填报数据:', data);
                
                // 提交成功后返回任务列表
                setTimeout(() => {
                    window.location.href = 'risk-report.html';
                }, 1000);
            }, 1500);
        });
    }
}

// 设置默认日期
function setDefaultDate() {
    const today = new Date().toISOString().split('T')[0];
    const checkDateInput = document.getElementById('checkDate');
    if (checkDateInput) {
        checkDateInput.value = today;
    }
}

// 更新区县选项
function updateDistricts() {
    const citySelect = document.getElementById('city');
    const districtSelect = document.getElementById('district');
    
    const selectedCity = citySelect.value;
    
    // 清空区县选项
    districtSelect.innerHTML = '<option value="">请选择区/县</option>';
    
    if (selectedCity && districtData[selectedCity]) {
        districtData[selectedCity].forEach(district => {
            const option = document.createElement('option');
            option.value = district.value;
            option.textContent = district.text;
            districtSelect.appendChild(option);
        });
    }
}

// 切换措施字段显示
function toggleMeasuresField() {
    const hasMeasures = document.getElementById('hasMeasures').value;
    const measuresRow = document.getElementById('measuresRow');
    const measuresInput = document.getElementById('measures');
    
    if (hasMeasures === 'yes') {
        measuresRow.style.display = 'block';
        measuresInput.required = true;
    } else {
        measuresRow.style.display = 'none';
        measuresInput.required = false;
        measuresInput.value = '';
    }
}
