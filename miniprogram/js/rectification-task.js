// 整改任务处理功能

// 模拟整改任务数据
const mockRectificationTasks = [
    {
        id: 1,
        relatedHazard: "G75兰海高速K1234+500路段护栏损坏",
        responsibleUnit: "市公路养护中心",
        responsiblePerson: "张三",
        createDate: "2024-06-15",
        deadline: "2024-06-30",
        status: "pending",
        statusText: "待处理",
        progress: 0,
        description: "高速公路护栏因车辆撞击导致严重变形，存在安全隐患",
        currentProgress: "已安排人员现场勘查，制定整改方案中",
        attachments: []
    },
    {
        id: 2,
        relatedHazard: "S210省道K45+200排水沟堵塞",
        responsibleUnit: "县交通运输局",
        responsiblePerson: "李四",
        createDate: "2024-06-10",
        deadline: "2024-06-25",
        status: "in_progress",
        statusText: "整改中",
        progress: 60,
        description: "省道排水沟被泥沙堵塞，雨季易积水影响行车安全",
        currentProgress: "已清理60%的堵塞物，预计3天内完成全部清理工作",
        attachments: ["现场照片1.jpg", "清理进度报告.pdf"]
    },
    {
        id: 3,
        relatedHazard: "G72泉南高速K890+100标志牌缺失",
        responsibleUnit: "高速公路管理处",
        responsiblePerson: "王五",
        createDate: "2024-06-05",
        deadline: "2024-06-20",
        status: "completed",
        statusText: "已完成",
        progress: 100,
        description: "高速公路限速标志牌丢失，需要重新安装",
        currentProgress: "已完成新标志牌的采购和安装，通过验收",
        attachments: ["安装完成照片.jpg", "验收报告.pdf"]
    },
    {
        id: 4,
        relatedHazard: "国道G107K2156+800边坡滑坡风险",
        responsibleUnit: "省公路管理局",
        responsiblePerson: "赵六",
        createDate: "2024-06-12",
        deadline: "2024-07-10",
        status: "overdue",
        statusText: "已逾期",
        progress: 30,
        description: "国道边坡出现裂缝，雨季存在滑坡风险",
        currentProgress: "已完成地质勘探，正在制定加固方案，因技术复杂导致进度延缓",
        attachments: ["地质勘探报告.pdf"]
    }
];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadRectificationTasks();
});

// 加载整改任务列表
function loadRectificationTasks() {
    const tasksList = document.getElementById('rectificationTasksList');
    
    if (!tasksList) return;
    
    let tasksHtml = '';
    
    mockRectificationTasks.forEach(task => {
        const statusClass = getStatusClass(task.status);
        const statusColor = getStatusColor(task.status);
        const progressColor = getProgressColor(task.progress);
        
        tasksHtml += `
            <div class="rectification-task-item ${statusClass}">
                <div class="task-header">
                    <h3 class="task-title">${task.relatedHazard}</h3>
                    <span class="task-status" style="background-color: ${statusColor}">
                        ${task.statusText}
                    </span>
                </div>
                
                <div class="task-info">
                    <div class="task-info-row">
                        <span class="info-label">责任单位：</span>
                        <span class="info-value">${task.responsibleUnit}</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">责任人：</span>
                        <span class="info-value">${task.responsiblePerson}</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">创建日期：</span>
                        <span class="info-value">${task.createDate}</span>
                    </div>
                    <div class="task-info-row">
                        <span class="info-label">整改期限：</span>
                        <span class="info-value ${task.status === 'overdue' ? 'overdue-text' : ''}">${task.deadline}</span>
                    </div>
                </div>
                

                
                <div class="task-actions">
                    <button class="btn btn-info btn-sm" onclick="viewRectificationTask(${task.id})">
                        <i class="fas fa-eye"></i> 查看
                    </button>
                    ${task.status !== 'completed' ? `
                        <button class="btn btn-primary btn-sm" onclick="editRectificationTask(${task.id})">
                            <i class="fas fa-edit"></i> 更新进度
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    });
    
    tasksList.innerHTML = tasksHtml;
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case 'completed':
            return 'completed';
        case 'in_progress':
            return 'in-progress';
        case 'overdue':
            return 'overdue';
        default:
            return 'pending';
    }
}

// 获取状态颜色
function getStatusColor(status) {
    switch (status) {
        case 'completed':
            return '#28a745';
        case 'in_progress':
            return '#17a2b8';
        case 'overdue':
            return '#dc3545';
        default:
            return '#ffc107';
    }
}

// 获取进度条颜色
function getProgressColor(progress) {
    if (progress >= 80) return '#28a745';
    if (progress >= 50) return '#ffc107';
    if (progress >= 20) return '#17a2b8';
    return '#dc3545';
}

// 查看整改任务详情
function viewRectificationTask(taskId) {
    // 跳转到整改任务详情页面
    window.location.href = `rectification-detail.html?id=${taskId}`;
}

// 编辑整改任务
function editRectificationTask(taskId) {
    // 跳转到整改任务编辑页面
    window.location.href = `rectification-edit.html?id=${taskId}`;
}
