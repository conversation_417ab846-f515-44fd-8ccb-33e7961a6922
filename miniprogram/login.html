<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 应急管理小程序</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 顶部状态栏 -->
        <div class="status-bar">
            <button class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </button>
            <span class="page-title">用户登录</span>
            <div class="status-icons">
                <i class="fas fa-ellipsis-h"></i>
                <i class="fas fa-question-circle"></i>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="modern-login-container">
            <!-- 背景图案 -->
            <div class="tech-background">
                <div class="circuit-pattern"></div>
                <div class="floating-elements">
                    <div class="element element-1"></div>
                    <div class="element element-2"></div>
                    <div class="element element-3"></div>
                    <div class="element element-4"></div>
                </div>
            </div>

            <!-- 应用图标和名称 -->
            <div class="app-info">
                <div class="app-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="app-name">广西公路水路安全畅通与应急处置系统</div>
            </div>
        </div>

        <!-- 底部登录区域 -->
        <div class="login-bottom">
            <!-- 主要登录按钮 -->
            <button class="phone-login-btn" onclick="phoneLogin()">
                <i class="fas fa-mobile-alt"></i>
                手机号快捷登录
            </button>

            <!-- 提示文字 -->
            <div class="login-notice">
                * 本程序仅供智慧应急云平台用户使用，不对公众开放
            </div>

            <!-- 其他登录方式 -->
            <div class="other-login">
                <div class="divider">
                    <span>其他登录方式</span>
                </div>

                <div class="login-methods">
                    <div class="login-method" onclick="emailLogin()">
                        <div class="method-icon email-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <span>账号</span>
                    </div>

                    <div class="login-method" onclick="smsLogin()">
                        <div class="method-icon sms-icon">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <span>短信</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/miniprogram.js"></script>
</body>
</html>
