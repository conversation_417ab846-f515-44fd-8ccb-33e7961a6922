<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险隐患审批</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>风险隐患审批</h1>
            <div class="subtitle">审批风险隐患填报和处理</div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="empty-text">风险隐患审批</div>
                <div class="empty-desc">此页面用于审批风险隐患处理<br>功能正在开发中...</div>
                
                <!-- 示例审批列表 -->
                <div style="margin-top: 40px; text-align: left; max-width: 320px; margin-left: auto; margin-right: auto;">
                    <h3 style="margin-bottom: 20px; color: #333;">待审批事项</h3>
                    
                    <!-- 审批项1 -->
                    <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #333;">桥梁裂缝处理方案</span>
                            <span class="status-badge status-pending">待审批</span>
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                            申请人：张工程师
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                            位置：某某大桥主跨中部
                        </div>
                        <div style="color: #999; font-size: 12px; margin-bottom: 15px;">
                            申请时间：2025-01-20 09:30
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button style="flex: 1; padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="approveItem('approve')">
                                通过
                            </button>
                            <button style="flex: 1; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="approveItem('reject')">
                                拒绝
                            </button>
                        </div>
                    </div>
                    
                    <!-- 审批项2 -->
                    <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #333;">边坡加固工程</span>
                            <span class="status-badge status-pending">待审批</span>
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                            申请人：李主管
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                            位置：G75高速K1200段
                        </div>
                        <div style="color: #999; font-size: 12px; margin-bottom: 15px;">
                            申请时间：2025-01-19 16:20
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <button style="flex: 1; padding: 8px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="approveItem('approve')">
                                通过
                            </button>
                            <button style="flex: 1; padding: 8px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="approveItem('reject')">
                                拒绝
                            </button>
                        </div>
                    </div>
                    
                    <!-- 已审批项 -->
                    <div style="background: white; border-radius: 8px; padding: 15px; margin-bottom: 15px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                            <span style="font-weight: bold; color: #333;">路面修补工程</span>
                            <span class="status-badge status-completed">已通过</span>
                        </div>
                        <div style="color: #666; font-size: 14px; margin-bottom: 10px;">
                            申请人：王工程师
                        </div>
                        <div style="color: #999; font-size: 12px;">
                            审批时间：2025-01-19 14:15
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </a>
            <a href="risk-tasks.html" class="nav-item active">
                <i class="fas fa-shield-alt"></i>
                <span>风险隐患</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script src="js/miniprogram.js"></script>
</body>
</html>
