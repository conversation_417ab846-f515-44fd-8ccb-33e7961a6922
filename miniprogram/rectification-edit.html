<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新整改进度</title>
    <link rel="stylesheet" href="css/miniprogram.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1>更新整改进度</h1>
            <div class="subtitle">更新整改任务状态和进度</div>
        </div>
        
        <!-- 页面内容 -->
        <div class="page-content">
            <form id="rectificationEditForm">
                <div class="form-section">
                    <h4><i class="fas fa-info-circle"></i> 任务信息</h4>
                    <div id="taskBasicInfo">
                        <!-- 基本信息将通过JavaScript动态生成 -->
                    </div>
                </div>

                <div class="form-section">
                    <h4><i class="fas fa-tasks"></i> 进度更新</h4>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="taskStatus">任务状态 <span class="required">*</span></label>
                            <select id="taskStatus" name="taskStatus" required>
                                <option value="">请选择状态</option>
                                <option value="pending">待处理</option>
                                <option value="in_progress">整改中</option>
                                <option value="completed">已完成</option>
                                <option value="overdue">已逾期</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="progressDescription">进度说明 <span class="required">*</span></label>
                            <textarea id="progressDescription" name="progressDescription" placeholder="请详细描述当前整改进度、已完成的工作和下一步计划" required></textarea>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group full-width">
                            <label for="attachments">附件上传</label>
                            <input type="file" id="attachments" name="attachments" multiple accept="image/*,.pdf,.doc,.docx">
                            <small class="form-hint">支持图片、PDF、Word文档，可选择多个文件</small>
                        </div>
                    </div>

                    <div class="form-row" id="completionSection" style="display: none;">
                        <div class="form-group full-width">
                            <label for="completionDate">完成日期</label>
                            <input type="date" id="completionDate" name="completionDate">
                        </div>
                    </div>

                    <div class="form-row" id="completionRemarkSection" style="display: none;">
                        <div class="form-group full-width">
                            <label for="completionRemark">完成备注</label>
                            <textarea id="completionRemark" name="completionRemark" placeholder="请描述整改完成情况、效果评估等"></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="history.back()">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> 保存更新
                    </button>
                </div>
            </form>
        </div>
        
        <!-- 底部导航 -->
        <div class="bottom-nav">
            <a href="index.html" class="nav-item">
                <i class="fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </a>
            <a href="risk-tasks.html" class="nav-item active">
                <i class="fas fa-shield-alt"></i>
                <span>风险隐患</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="nav-item" onclick="showToast('功能开发中', 'info')">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>
    
    <script src="js/miniprogram.js"></script>
    <script src="js/rectification-edit.js"></script>
</body>
</html>
