# 应急管理小程序展示页面

这是一个应急管理系统的小程序展示页面，包含了完整的功能模块框架和用户界面。

## 功能模块

### 1. 登录页面 (`login.html`)
- 用户名密码登录
- 测试账号：admin / 123456
- 响应式设计，适配移动端

### 2. 首页导航 (`index.html`)
- 功能模块网格布局
- 6个主要功能入口
- 底部导航栏

### 3. 应急事件接报 (`emergency-report.html`)
- 事件类型选择
- 事件等级分类
- 事件描述录入
- 表单提交功能

### 4. 应急信息录入 (`emergency-info.html`)
- 处置措施记录
- 现场情况描述
- 人员伤亡统计
- 经济损失评估
- 后续计划制定

### 5. 风险隐患填报 (`risk-report.html`)
- 隐患类型分类
- 风险等级评估
- 位置信息录入
- 隐患详细描述
- 处理建议提交

### 6. 整改任务处理 (`rectification-task.html`)
- 待处理任务列表
- 任务状态管理
- 处理进度跟踪
- 完成确认功能

### 7. 风险隐患审批 (`risk-approval.html`)
- 待审批事项列表
- 审批通过/拒绝
- 审批历史记录
- 状态标签显示

## 技术特点

### 响应式设计
- 适配移动端屏幕（375px宽度）
- 灵活的网格布局
- 触摸友好的交互设计

### 用户体验
- 渐变色彩搭配
- 平滑动画效果
- 直观的图标设计
- 清晰的状态反馈

### 代码结构
- 模块化CSS样式
- 通用JavaScript功能
- 统一的页面布局
- 可扩展的组件设计

## 文件结构

```
miniprogram/
├── css/
│   └── miniprogram.css          # 小程序通用样式
├── js/
│   └── miniprogram.js           # 小程序通用脚本
├── login.html                   # 登录页面
├── index.html                   # 首页导航
├── emergency-report.html        # 应急事件接报
├── emergency-info.html          # 应急信息录入
├── risk-report.html             # 风险隐患填报
├── rectification-task.html      # 整改任务处理
├── risk-approval.html           # 风险隐患审批
└── README.md                    # 说明文档
```

## 使用方法

1. **启动页面**
   - 直接在浏览器中打开 `login.html`
   - 或者使用本地服务器访问

2. **登录系统**
   - 用户名：admin
   - 密码：123456
   - 点击登录按钮

3. **功能导航**
   - 登录成功后自动跳转到首页
   - 点击功能卡片进入对应模块
   - 使用返回按钮或底部导航切换页面

4. **表单操作**
   - 填写相应的表单信息
   - 点击提交按钮完成操作
   - 系统会显示操作结果提示

## 样式特色

### 色彩方案
- 主色调：蓝紫渐变 (#667eea → #764ba2)
- 背景色：浅灰色 (#f5f5f5)
- 卡片背景：纯白色
- 文字颜色：深灰色 (#333)

### 交互效果
- 悬停动画：卡片上浮效果
- 点击反馈：按钮颜色变化
- 页面切换：淡入淡出动画
- 状态提示：Toast消息提醒

### 状态标签
- 待处理：黄色背景
- 处理中：蓝色背景
- 已完成：绿色背景
- 已拒绝：红色背景

## 扩展说明

当前页面为展示框架，具体功能逻辑需要根据实际需求进行开发：

1. **后端接口对接**
2. **数据库设计**
3. **文件上传功能**
4. **地图定位集成**
5. **消息推送机制**
6. **权限管理系统**

## 浏览器兼容性

- Chrome 60+
- Safari 12+
- Firefox 55+
- Edge 79+
- 移动端浏览器

## 注意事项

- 页面使用了Font Awesome图标库，需要网络连接
- 部分功能为演示效果，实际使用需要后端支持
- 建议在移动设备或开发者工具的移动模式下查看效果
