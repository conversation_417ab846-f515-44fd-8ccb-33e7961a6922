<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应急通讯录 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
    </style>
</head>
<body class="bg-gray-100 flex flex-col min-h-screen">
    <!-- 顶部导航栏容器 -->
    <div id="navigation-container"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow p-6 bg-gray-100 min-h-screen">
            <!-- 原有的主要内容保持不变 -->
            <div class="py-6">
                <!-- 页面标题部分 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">应急通讯录</h2>
                        <p class="text-gray-600 mt-1">管理和查看应急联系人信息</p>
                    </div>
                </div>

                <!-- Global Filters -->
                <div id="global-filters-app" class="bg-white p-4 rounded-lg shadow-sm mb-6">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">通用筛选</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-x-6 gap-y-4 items-end">
                        <div>
                            <label for="global_city_filter_select" class="block text-sm font-medium text-gray-700 mb-1">广西地市</label>
                            <el-select v-model="selectedCity" placeholder="请选择地市" clearable class="block w-full" @change="handleCityChange" id="global_city_filter_select">
                                <el-option v-for="city in cityOptions" :key="city.value" :label="city.label" :value="city.value"></el-option>
                            </el-select>
                        </div>
                        <div>
                            <label for="global_org_unit_filter_tree_select" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                            <el-tree-select
                                v-model="selectedOrgUnit"
                                :data="filteredOrgUnitOptions"
                                :props="{ value: 'value', label: 'label', children: 'children' }"
                                placeholder="请选择单位"
                                clearable
                                check-strictly
                                class="block w-full"
                                @change="handleOrgChange"
                                id="global_org_unit_filter_tree_select"
                            />
                        </div>
                        <div class="flex space-x-2">
                            <button @click="applyGlobalFilters" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm w-full sm:w-auto flex-grow sm:flex-grow-0 flex items-center justify-center">
                              <i class="fas fa-filter mr-2"></i> 应用筛选
                            </button>
                            <button @click="resetGlobalFilters" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm w-full sm:w-auto flex-grow sm:flex-grow-0 flex items-center justify-center">
                              <i class="fas fa-undo mr-2"></i> 重置筛选
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Main Tab 导航 -->
                <div class="mb-6 border-b border-gray-300">
                    <nav class="-mb-px flex space-x-2" aria-label="MainContactsTabs">
                        <button class="main-tab-btn active" data-main-tab="personnel-section">
                            <i class="fas fa-users mr-2"></i>单位人员
                        </button>
                        <button class="main-tab-btn" data-main-tab="expert-section">
                            <i class="fas fa-user-tie mr-2"></i>专家库
                        </button>
                        <button class="main-tab-btn" data-main-tab="third-party-section">
                            <i class="fas fa-handshake mr-2"></i>救援队伍
                        </button>
                    </nav>
                </div>

                <!-- Main Tab 内容区域 -->
                <div id="main-tab-content-container">
                    <!-- 单位人员内容 -->
                    <div id="personnel-section" class="main-tab-content active">
                        <!-- Content for Personnel List will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <button id="btnAddPersonnel" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                      <i class="fas fa-plus mr-2"></i>添加人员
                                                    </button>
                                                  </div>

                                                  <!-- 过滤栏 -->
                                                  <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                                      <div>
                                                        <label for="personnel_position_filter" class="block text-sm font-medium text-gray-700 mb-1">职位</label>
                                                        <select id="personnel_position_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部职位</option>
                                                          <option value="director">主任</option>
                                                          <option value="deputy">副主任</option>
                                                          <option value="chief">科长</option>
                                                          <option value="staff">工作人员</option>
                                                          <!-- Add more positions -->
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="personnel_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                        <select id="personnel_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部状态</option>
                                                          <option value="active">正常</option>
                                                          <option value="leave">停用</option>
                                                          <!-- Add other statuses if needed -->
                                                        </select>
                                                      </div>
                                                      <div class="flex items-end space-x-2">
                                                        <button id="btnFilterPersonnel" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                          <i class="fas fa-filter mr-1"></i> 筛选
                                                        </button>
                                                        <button id="btnResetFilterPersonnel" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                          <i class="fas fa-undo mr-1"></i> 重置
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>

                                                  <!-- 数据表格 -->
                                                  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                      <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                          <tr>
                                                            <th scope="col" class="w-16 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                                            <th scope="col" class="w-16 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                                                            <th scope="col" class="w-40 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                                            <th scope="col" class="w-24 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                            <th scope="col" class="w-32 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                          </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                          <tr>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">1</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李明</td>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">男</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">科长</td>
                                                            <td class="w-40 px-6 py-4 text-sm text-gray-900">13900139001</td>
                                                            <td class="w-24 px-6 py-4 whitespace-nowrap text-center">
                                                               <span class="status-badge active">正常</span>
                                                            </td>
                                                            <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-personnel" data-id="1">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-personnel" data-id="1">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <tr>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王芳</td>
                                                            <td class="w-16 px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-center">女</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">安监科</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900">科员</td>
                                                            <td class="w-40 px-6 py-4 text-sm text-gray-900">13800138002</td>
                                                             <td class="w-24 px-6 py-4 whitespace-nowrap text-center">
                                                               <span class="status-badge leave">停用</span>
                                                            </td>
                                                            <td class="w-32 px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-personnel" data-id="2">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-personnel" data-id="2">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <!-- More rows -->
                                                        </tbody>
                                                      </table>
                                                    </div>

                                                    <!-- 分页控件 -->
                                                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                                         <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                            <div>
                                                                <p class="text-sm text-gray-700">
                                                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">上一页</span><i class="fas fa-chevron-left"></i></a>
                                                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">下一页</span><i class="fas fa-chevron-right"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                  </div>

                                                  <!-- 添加/编辑人员 Modal -->
                                                  <div id="personnel-modal-app"> <!-- MODIFIED ID -->
                                                      <el-dialog
                                                          v-model="dialogVisible"
                                                          :title="isEditMode ? '编辑人员' : '新增人员'"
                                                          width="60%"
                                                          @closed="resetForm"
                                                          :close-on-click-modal="false"
                                                      >
                                                        <el-form :model="personnelForm" ref="personnelFormRef" label-width="100px">
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="姓名" required prop="name">
                                                                        <el-input v-model="personnelForm.name" placeholder="请输入姓名"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="性别" required prop="gender">
                                                                        <el-radio-group v-model="personnelForm.gender">
                                                                            <el-radio label="male">男</el-radio>
                                                                            <el-radio label="female">女</el-radio>
                                                                        </el-radio-group>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-form-item label="所属单位" required prop="orgUnit">
                                                                <el-tree-select
                                                                    v-model="personnelForm.orgUnit"
                                                                    :data="orgOptions"
                                                                    :multiple="false"
                                                                    :check-strictly="true"
                                                                    placeholder="请选择所属单位"
                                                                    clearable
                                                                    style="width: 100%;"
                                                                />
                                                            </el-form-item>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="部门" prop="department">
                                                                        <el-input v-model="personnelForm.department" placeholder="请输入部门"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                    <el-form-item label="职位" prop="position">
                                                                        <el-input v-model="personnelForm.position" placeholder="请输入职位"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                            <el-row :gutter="20">
                                                                <el-col :span="12">
                                                                    <el-form-item label="联系电话" required prop="phone">
                                                                        <el-input v-model="personnelForm.phone" placeholder="请输入联系电话"></el-input>
                                                                    </el-form-item>
                                                                </el-col>
                                                                <el-col :span="12">
                                                                     <el-form-item label="状态" required prop="status">
                                                                          <el-select v-model="personnelForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                              <el-option label="正常" value="active"></el-option>
                                                                              <el-option label="停用" value="leave"></el-option>
                                                                          </el-select>
                                                                     </el-form-item>
                                                                </el-col>
                                                            </el-row>
                                                        </el-form>
                                                        <template #footer>
                                                            <span class="dialog-footer">
                                                                <el-button @click="closeModal">取消</el-button>
                                                                <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                            </span>
                                                        </template>
                                                      </el-dialog>
                                                  </div>
                    </div>

                    <!-- 专家库内容 -->
                    <div id="expert-section" class="main-tab-content">
                        <!-- Content for Expert List will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <button id="btnAddExpert" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                      <i class="fas fa-plus mr-2"></i>添加专家
                                                    </button>
                                                  </div>

                                                  <!-- 过滤栏 -->
                                                  <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                                      <div>
                                                        <label for="expert_expertise_filter" class="block text-sm font-medium text-gray-700 mb-1">专业领域</label>
                                                        <select id="expert_expertise_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部领域</option>
                                                          <option value="fire">消防救援</option>
                                                          <option value="medical">医疗救护</option>
                                                          <option value="chemical">危化品处置</option>
                                                          <option value="geology">地质灾害</option>
                                                          <option value="flood">洪涝灾害</option>
                                                          <!-- Add more domains -->
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="expert_level_filter" class="block text-sm font-medium text-gray-700 mb-1">专家级别</label>
                                                        <select id="expert_level_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部级别</option>
                                                          <option value="national">国家级</option>
                                                          <option value="provincial">省级</option>
                                                          <option value="city">市级</option>
                                                          <option value="county">县级</option>
                                                          <!-- Add more levels -->
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="expert_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                        <select id="expert_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部状态</option>
                                                          <option value="normal">正常</option>
                                                          <option value="inactive">停用</option>
                                                        </select>
                                                      </div>
                                                      <div class="flex items-end space-x-2">
                                                        <button id="btnFilterExperts" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                          <i class="fas fa-filter mr-1"></i> 筛选
                                                        </button>
                                                        <button id="btnResetExpertFilters" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                          <i class="fas fa-undo mr-1"></i> 重置
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>

                                                  <!-- 数据表格 -->
                                                  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                      <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                          <tr>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业领域</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专家级别</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最近一次确认时间</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                          </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                          <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">刘建国</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">男</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省消防总队</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">消防救援</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13900139001</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-26</td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                              <span class="status-badge normal">正常</span>
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium"> <!-- Changed text-right to text-center -->
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-expert" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-expert" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                           <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张美华</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">女</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市第一人民医院</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医疗救护</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138002</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-15</td>
                                                            <td class="px-6 py-4 whitespace-nowrap">
                                                              <span class="status-badge inactive">停用</span> <!-- Example changed status -->
                                                            </td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-expert" data-id="2">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-expert" data-id="2">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <!-- More expert rows -->
                                                        </tbody>
                                                      </table>
                                                    </div>

                                                    <!-- 分页控件 -->
                                                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                                         <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                            <div>
                                                                <p class="text-sm text-gray-700">
                                                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                     <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">上一页</span><i class="fas fa-chevron-left"></i></a>
                                                                     <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                     <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">下一页</span><i class="fas fa-chevron-right"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                  </div>

                                                  <!-- 添加/编辑专家 Modal -->
                                                   <!-- Note: Original file used ID "addPersonnelModal", changed to "expert-modal-app" -->
                                                  <div id="expert-modal-app"> <!-- MODIFIED ID -->
                                                      <el-dialog
                                                          v-model="dialogVisible"
                                                          :title="modalTitle"
                                                          width="60%"
                                                          @closed="resetForm"
                                                          :close-on-click-modal="false"
                                                      >
                                                          <el-form :model="expertForm" ref="expertFormRef" label-width="100px">
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="姓名" required prop="name">
                                                                            <el-input v-model="expertForm.name" placeholder="请输入专家姓名"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="性别" required prop="gender">
                                                                            <el-radio-group v-model="expertForm.gender">
                                                                                <el-radio label="male">男</el-radio>
                                                                                <el-radio label="female">女</el-radio>
                                                                            </el-radio-group>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-form-item label="所属单位" required prop="organizationId">
                                                                  <el-tree-select
                                                                        v-model="expertForm.organizationId"
                                                                      :data="orgOptions"
                                                                        :multiple="false"
                                                                        check-strictly
                                                                      :props="{ value: 'value', label: 'label', children: 'children' }"
                                                                        placeholder="请选择所属单位"
                                                                        class="w-full"
                                                                        style="width: 100%;"
                                                                    />
                                                                </el-form-item>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="专业领域" required prop="expertise">
                                                                            <el-select v-model="expertForm.expertise" placeholder="请选择专业领域" style="width: 100%;">
                                                                                <el-option label="消防救援" value="fire"></el-option>
                                                                                <el-option label="医疗救护" value="medical"></el-option>
                                                                                <el-option label="危化品处置" value="chemical"></el-option>
                                                                                <el-option label="地质灾害" value="geology"></el-option>
                                                                                <el-option label="洪涝灾害" value="flood"></el-option>
                                                                                <el-option label="其他" value="other"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="级别" required prop="level">
                                                                            <el-select v-model="expertForm.level" placeholder="请选择专家级别" style="width: 100%;">
                                                                                <el-option label="国家级" value="national"></el-option>
                                                                                <el-option label="省级" value="provincial"></el-option>
                                                                                <el-option label="市级" value="municipal"></el-option> <!-- Changed from city -->
                                                                                <el-option label="县级" value="county"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="联系电话" required prop="phone">
                                                                            <el-input v-model="expertForm.phone" placeholder="请输入联系电话"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="状态" required prop="status">
                                                                            <el-select v-model="expertForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                                <el-option label="正常" value="normal"></el-option>
                                                                                <el-option label="停用" value="inactive"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                            </el-form>
                                                            <template #footer>
                                                                <span class="dialog-footer">
                                                                    <el-button @click="closeModal">取消</el-button>
                                                                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                                </span>
                                                            </template>
                                                      </el-dialog>
                                                  </div>

                                                  <!-- 删除确认模态框 -->
                                                   <!-- Note: Original file used ID "deleteConfirmModal", changed to "expert-delete-confirm-modal" -->
                                                  <div id="expert-delete-confirm-modal" class="fixed inset-0 z-[100] hidden"> <!-- MODIFIED ID -->
                                                      <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
                                                      <div class="fixed inset-0 z-10 overflow-y-auto">
                                                          <div class="flex min-h-full items-center justify-center p-4">
                                                              <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                                                                  <div class="px-6 py-4 border-b border-gray-200">
                                                                      <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
                                                                  </div>
                                                                  <div class="px-6 py-4">
                                                                      <p class="text-sm text-gray-700">您确定要删除该专家信息吗？此操作无法撤销。</p>
                                                                  </div>
                                                                  <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                                                                      <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-expert-delete-modal"> <!-- MODIFIED CLASS -->
                                                                          取消
                                                                      </button>
                                                                      <button id="btnConfirmDeleteExpert" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"> <!-- MODIFIED ID -->
                                                                          删除
                                                                      </button>
                                                                  </div>
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                    </div>

                    <!-- 救援队伍内容 -->
                    <div id="third-party-section" class="main-tab-content">
                        <!-- Content for Third Party List will be added here -->
                                                <!-- 页面标题 -->
                                                <div class="flex justify-between items-center mb-6 pt-4"> <!-- Added pt-4 -->
                                                    <button id="btnAddThirdParty" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                      <i class="fas fa-plus mr-2"></i>添加应急救援人员
                                                    </button>
                                                  </div>

                                                  <!-- 过滤栏 -->
                                                  <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
                                                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                                      <div>
                                                        <label for="third_party_filterName" class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                                        <input type="text" id="third_party_filterName" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="输入姓名关键词">
                                                      </div>
                                                      <div>
                                                        <label for="third_party_filterCompany" class="block text-sm font-medium text-gray-700 mb-1">所属三方公司</label>
                                                        <select id="third_party_filterCompany" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部公司</option>
                                                          <option value="companyA">XX消防技术服务公司</option>
                                                          <option value="companyB">XX医疗急救中心</option>
                                                          <option value="companyC">XX电力工程公司</option>
                                                          <option value="companyD">XX运输有限公司</option>
                                                          <option value="companyE">XX环境监测评估公司</option>
                                                        </select>
                                                      </div>
                                                      <div>
                                                        <label for="personnel_status_filter" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                                                        <select id="personnel_status_filter" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                                          <option value="">全部状态</option>
                                                          <option value="active">正常</option>
                                                          <option value="leave">停用</option>
                                                          <!-- Add other statuses if needed -->
                                                        </select>
                                                      </div>
                                                      <div class="flex items-end space-x-2 col-start-5"> <!-- Adjusted col-start -->
                                                        <button id="btnFilterThirdParty" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                          <i class="fas fa-search mr-1"></i> 查询
                                                        </button>
                                                        <button id="btnResetThirdPartyFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                          <i class="fas fa-undo mr-1"></i> 重置
                                                        </button>
                                                      </div>
                                                    </div>
                                                  </div>

                                                  <!-- 数据表格 -->
                                                  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                                                    <div class="overflow-x-auto">
                                                      <table class="min-w-full divide-y divide-gray-200">
                                                        <thead class="bg-gray-50">
                                                          <tr>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属三方公司</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技能特长</th>
                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                                          </tr>
                                                        </thead>
                                                        <tbody class="bg-white divide-y divide-gray-200">
                                                          <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王军</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">广西壮族自治区交通运输厅</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">XX消防技术服务公司</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">现场负责人</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13900139001</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="灭火救援, 现场指挥">灭火救援, 现场指挥</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center"> <span class="status-badge available">可用</span></td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-third-party" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-third-party" data-id="1"> <!-- MODIFIED CLASS -->
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <tr>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李红</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自治区公路发展中心</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">XX医疗急救中心</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医护队长</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138002</td>
                                                            <td class="px-6 py-4 text-sm text-gray-900 truncate max-w-xs" title="急救处理, 包扎">急救处理, 包扎</td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center"> <span class="status-badge on_mission">任务中</span></td>
                                                            <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                                                              <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit-third-party" data-id="2">
                                                                <i class="fas fa-edit"></i> 编辑
                                                              </button>
                                                              <button class="text-red-600 hover:text-red-900 btn-delete-third-party" data-id="2">
                                                                <i class="fas fa-trash-alt"></i> 删除
                                                              </button>
                                                            </td>
                                                          </tr>
                                                          <!-- More rows -->
                                                        </tbody>
                                                      </table>
                                                    </div>

                                                    <!-- 分页控件 -->
                                                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                                                         <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                                            <div>
                                                                <p class="text-sm text-gray-700">
                                                                    显示第 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 条，共 <span class="font-medium">2</span> 条记录
                                                                </p>
                                                            </div>
                                                            <div>
                                                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">上一页</span><i class="fas fa-chevron-left"></i></a>
                                                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                                                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"><span class="sr-only">下一页</span><i class="fas fa-chevron-right"></i></a>
                                                                </nav>
                                                            </div>
                                                        </div>
                                                    </div>
                                                  </div>

                                                   <!-- 添加/编辑三方人员模态框 -->
                                                   <!-- Note: Original file used ID "addThirdPartyModal", changed to "third-party-modal-app" -->
                                                  <div id="third-party-modal-app"> <!-- MODIFIED ID -->
                                                      <el-dialog
                                                          v-model="dialogVisible"
                                                          :title="modalTitle"
                                                          width="60%"
                                                          @closed="resetForm"
                                                          :close-on-click-modal="false"
                                                      >
                                                          <el-form :model="thirdPartyForm" ref="thirdPartyFormRef" label-width="120px">
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="姓名" required prop="name">
                                                                            <el-input v-model="thirdPartyForm.name" placeholder="请输入人员姓名"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="性别" required prop="gender">
                                                                            <el-radio-group v-model="thirdPartyForm.gender">
                                                                                <el-radio label="male">男</el-radio>
                                                                                <el-radio label="female">女</el-radio>
                                                                            </el-radio-group>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="所属单位" prop="organizationId">
                                                                            <el-tree-select
                                                                                v-model="thirdPartyForm.organizationId"
                                                                                :data="orgOptions"
                                                                                :multiple="false"
                                                                                check-strictly
                                                                                placeholder="请选择所属单位 (非必填)"
                                                                                class="w-full"
                                                                                style="width: 100%;"
                                                                                clearable
                                                                            />
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="所属三方公司" required prop="company">
                                                                            <el-select v-model="thirdPartyForm.company" placeholder="请选择所属公司" style="width: 100%;">
                                                                                <el-option label="XX消防技术服务公司" value="companyA"></el-option>
                                                                                <el-option label="XX医疗急救中心" value="companyB"></el-option>
                                                                                <el-option label="XX电力工程公司" value="companyC"></el-option>
                                                                                <el-option label="XX运输有限公司" value="companyD"></el-option>
                                                                                <el-option label="XX环境监测评估公司" value="companyE"></el-option>
                                                                                <el-option label="其他" value="other"></el-option>
                                                                            </el-select>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                <el-row :gutter="20">
                                                                    <el-col :span="12">
                                                                        <el-form-item label="职务/岗位" prop="position">
                                                                            <el-input v-model="thirdPartyForm.position" placeholder="请输入职务或岗位"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                    <el-col :span="12">
                                                                        <el-form-item label="联系电话" required prop="phone">
                                                                            <el-input v-model="thirdPartyForm.phone" placeholder="请输入联系电话"></el-input>
                                                                        </el-form-item>
                                                                    </el-col>
                                                                </el-row>
                                                                 <el-form-item label="技能特长" prop="skills">
                                                                     <el-input type="textarea" :rows="3" v-model="thirdPartyForm.skills" placeholder="请输入人员的技能或特长，多个请用逗号分隔"></el-input>
                                                                 </el-form-item>
                                                                 <el-form-item label="状态" required prop="status">
                                                                     <el-select v-model="thirdPartyForm.status" placeholder="请选择状态" style="width: 100%;">
                                                                         <el-option label="可用" value="available"></el-option>
                                                                         <el-option label="任务中" value="on_mission"></el-option>
                                                                         <el-option label="不可用" value="unavailable"></el-option>
                                                                     </el-select>
                                                                 </el-form-item>
                                                            </el-form>
                                                            <template #footer>
                                                                <span class="dialog-footer">
                                                                    <el-button @click="closeModal">取消</el-button>
                                                                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                                                                </span>
                                                            </template>
                                                      </el-dialog>
                                                  </div>

                                                  <!-- 删除确认模态框 -->
                                                  <!-- Note: Original file used ID "deleteConfirmModal", changed to "third-party-delete-confirm-modal" -->
                                                  <div id="third-party-delete-confirm-modal" class="fixed inset-0 z-[100] hidden"> <!-- MODIFIED ID -->
                                                      <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
                                                      <div class="fixed inset-0 z-10 overflow-y-auto">
                                                          <div class="flex min-h-full items-center justify-center p-4">
                                                              <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
                                                                  <div class="px-6 py-4 border-b border-gray-200">
                                                                      <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
                                                                  </div>
                                                                  <div class="px-6 py-4">
                                                                      <p class="text-sm text-gray-700">您确定要删除该应急救援人员信息吗？此操作无法撤销。</p>
                                                                  </div>
                                                                  <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                                                                      <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-third-party-delete-modal"> <!-- MODIFIED CLASS -->
                                                                          取消
                                                                      </button>
                                                                      <button id="btnConfirmDeleteThirdParty" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"> <!-- MODIFIED ID -->
                                                                          删除
                                                                      </button>
                                                                  </div>
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Load Libraries -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
    <script>
        // 初始化导航栏
        window.NavigationComponent.init('system-management');
    </script>

    <!-- 原有的页面特定脚本保持不变 -->
    // ... 保持原有脚本不变 ...

</body>
</html>
