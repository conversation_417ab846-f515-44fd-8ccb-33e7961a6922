<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>资源预警设置 - 应急管理系统</title>
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        html, body {
            height: 100%;
            margin: 0;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        /* Keep other specific styles if needed */
    </style>
</head>
<body class="bg-gray-100 min-h-screen">

    <!-- Navbar Placeholder -->
    <div id="navbar-placeholder"></div>
            
    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>
            
        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
        <div class="py-6">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800">资源预警设置</h2>
                    <p class="text-gray-600 mt-1">配置物资数量阈值提醒、物资效期提醒和信息更新提醒等规则</p>
                </div>
            </div>
            
            <!-- 标签页导航 -->
            <div class="bg-white rounded-t-lg shadow-sm mb-0">
                <div class="p-4 sm:px-6">
                    <nav class="flex space-x-4 overflow-x-auto pb-1">
                        <button class="tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none" data-tab="threshold">
                            <i class="fas fa-tachometer-alt mr-2"></i> 物资数量阈值提醒
                        </button>
                    </nav>
                </div>
            </div>
            
            <!-- 标签页内容 -->
            <div class="bg-white rounded-b-lg shadow-md p-6">
                <!-- 物资数量阈值提醒 -->
                <div id="threshold" class="tab-content active">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">物资数量阈值规则</h3>
                        <button class="inline-flex items-center px-3 py-1.5 border border-blue-600 text-sm font-medium rounded text-blue-600 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-1"></i> 新增规则
                        </button>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资类别</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物资名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预警阈值</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知人员</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">医疗防护物资预警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防疫物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">所有</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中心仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李四、王五</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3 focus:outline-none">编辑</button>
                                        <button class="text-red-600 hover:text-red-900 focus:outline-none">停用</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">防洪物资预警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防洪物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">防洪沙袋</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">所有仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张三、赵六</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3 focus:outline-none">编辑</button>
                                        <button class="text-red-600 hover:text-red-900 focus:outline-none">停用</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">应急照明设备预警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">应急装备</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">应急照明灯</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">南区仓库</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王五</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已启用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3 focus:outline-none">编辑</button>
                                        <button class="text-red-600 hover:text-red-900 focus:outline-none">停用</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">消防物资预警</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">消防物资</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">所有</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">所有仓库</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">100</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李四</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">已停用</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3 focus:outline-none">编辑</button>
                                        <button class="text-green-600 hover:text-green-900 focus:outline-none">启用</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                        <!-- 分页 -->
                        <div class="px-4 py-3 border-t border-gray-200 sm:px-6">
                            <div class="flex justify-between items-center">
                                <div class="text-sm text-gray-700">
                                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">4</span> 条记录
                                </div>
                            <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">上一页</span>
                                            <i class="fas fa-chevron-left"></i>
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600 hover:bg-blue-100">
                                            1
                                        </a>
                                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                            <span class="sr-only">下一页</span>
                                            <i class="fas fa-chevron-right"></i>
                                        </a>
                                    </nav>
                            </div>
                            </div>
                        </div>
                            </div>
                            
                    <!-- 物资效期提醒 (示例，保持隐藏) -->
                    <div id="expiry" class="tab-content">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">物资效期提醒规则</h3>
                        <p class="text-gray-600">此功能暂未启用。</p>
                        </div>
                        
                    <!-- 信息更新提醒 (示例，保持隐藏) -->
                    <div id="update" class="tab-content">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">信息更新提醒规则</h3>
                        <p class="text-gray-600">此功能暂未启用。</p>
                            </div>
                            
                    <!-- 通知设置 (示例，保持隐藏) -->
                    <div id="notification" class="tab-content">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">通知设置</h3>
                        <p class="text-gray-600">此功能暂未启用。</p>
                    </div>
                </div>
            </div>
        </main>
        </div>

    <!-- Keep tab switching logic -->
    <script>
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

        // Ensure initial state if needed (e.g., first tab active)
        if (tabButtons.length > 0 && tabContents.length > 0) {
             if (!document.querySelector('.tab-btn.text-blue-600')) {
                 tabButtons[0].classList.add('text-blue-600', 'border-blue-600');
                 tabButtons[0].classList.remove('text-gray-500', 'border-transparent');
            }
             if (!document.querySelector('.tab-content.active')) {
                 const firstTabId = tabButtons[0].getAttribute('data-tab');
                 const firstTabContent = document.getElementById(firstTabId);
                 if(firstTabContent) firstTabContent.classList.add('active');
            }
        }
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                const tabId = button.getAttribute('data-tab');

                // Update button styles
                    tabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600');
                        btn.classList.add('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                    });
                button.classList.add('text-blue-600', 'border-blue-600');
                    button.classList.remove('text-gray-500', 'border-transparent', 'hover:text-gray-700', 'hover:border-gray-300');
                    
                // Show/hide tab content
                    tabContents.forEach(content => {
                    if (content.id === tabId) {
                        content.classList.add('active');
                    } else {
                        content.classList.remove('active');
                    }
                });
            });
        });

         // Add placeholder logic for table buttons if needed
         document.addEventListener('click', function(event){
            const editButton = event.target.closest('button.text-blue-600');
             const actionButton = event.target.closest('button.text-red-600, button.text-green-600');
             if (editButton && editButton.textContent === '编辑') {
                 console.log('Edit rule clicked');
                 alert('触发编辑规则操作 (模拟)');
            }
             if (actionButton) {
                 const action = actionButton.textContent;
                 if (confirm(`确定要${action}此规则吗？`)) {
                    console.log(`${action} rule clicked`);
                     alert(`触发${action}规则操作 (模拟)`);
                     // Toggle button text/color if needed
                }
            }
        });

    </script>
    <!-- Load component HTML first -->
    <script src="js/navbarComponent.js"></script>
    <script src="js/sidebarComponent_emergency.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>
</body>
</html> 