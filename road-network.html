<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路网运行 - 广西交通运输应急管理系统</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- 引入公共样式 -->
    <link rel="stylesheet" href="css/emergency-common.css">
    <link rel="stylesheet" href="new_style.css">
    <link rel="stylesheet" href="alert-styles.css">
    <link rel="stylesheet" href="css/road-network.css">

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏容器 -->
        <div id="navigation-container"></div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="tab-content-container">
                <!-- 路网运行内容 -->
                <div id="road-network-content" class="tab-content road-network-container" style="display: flex;">
                    <!-- 左侧资源目录 -->
                    <aside class="left-sidebar">
                        <div class="resource-filter">
                            <h3>资源目录</h3>
                            <div class="resource-filter-container">
                                <!-- 1. 资源类型选择器 -->
                                <div class="resource-type-selector">
                                    <h4>资源类型</h4>

                                    <!-- 筛选面板 -->
                                    <div class="filter-panel">
                                        <!-- 全选复选框 -->
                                        <div class="filter-row">
                                            <div class="filter-item checkbox-item">
                                                <input type="checkbox" id="select-all-congestion" class="select-all-checkbox" checked>
                                                <label for="select-all-congestion">全选</label>
                                            </div>
                                        </div>

                                        <!-- 当前拥堵状态 -->
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="congestion-status">当前拥堵状态：</label>
                                                <select id="congestion-status" class="filter-select">
                                                    <option value="all">所有状态</option>
                                                    <option value="severe">严重拥堵</option>
                                                    <option value="moderate">中度拥堵</option>
                                                    <option value="light">轻度拥堵</option>
                                                    <option value="smooth">畅通</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- 拥堵趋势预测 -->
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="congestion-trend">拥堵趋势预测：</label>
                                                <select id="congestion-trend" class="filter-select">
                                                    <option value="all">所有趋势</option>
                                                    <option value="severe-soon">即将严重拥堵</option>
                                                    <option value="moderate-soon">即将中度拥堵</option>
                                                    <option value="none">无趋势变化</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- 发生时段 -->
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="congestion-time">发生时段：</label>
                                                <select id="congestion-time" class="filter-select">
                                                    <option value="all">所有时段</option>
                                                    <option value="morning">早高峰</option>
                                                    <option value="evening">晚高峰</option>
                                                    <option value="normal">平峰</option>
                                                    <option value="night">夜间时段</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- 持续时间 -->
                                        <div class="filter-row">
                                            <div class="filter-item">
                                                <label for="congestion-duration">持续时间：</label>
                                                <select id="congestion-duration" class="filter-select">
                                                    <option value="all">所有时长</option>
                                                    <option value="short">短期（30分钟内）</option>
                                                    <option value="medium">中期（30分钟-2小时）</option>
                                                    <option value="long">长期（超过2小时）</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 2. 资源条件筛选 -->
                                <div class="resource-condition-filter">
                                    <div class="filter-tabs">
                                        <button class="filter-tab-button active" data-tab="unit" onclick="switchFilterTab(this, 'unit')">按单位划分</button>
                                        <button class="filter-tab-button" data-tab="road" onclick="switchFilterTab(this, 'road')">按路段划分</button>
                                    </div>

                                    <!-- 2.1 按单位划分内容 -->
                                    <div id="filter-by-unit-content" class="filter-tab-content active">
                                        <ul class="collapsible-tree">
                                            <li>
                                                <span class="tree-toggler">+</span><input type="checkbox" id="road-network-unit-gxtyt" value="gxtyt"><label for="road-network-unit-gxtyt">广西交通运输厅</label>
                                                <ul>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-nn" value="gxtyt-nn"><label for="road-network-unit-gxtyt-nn">南宁市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-lz" value="gxtyt-lz"><label for="road-network-unit-gxtyt-lz">柳州市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-gl" value="gxtyt-gl"><label for="road-network-unit-gxtyt-gl">桂林市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-wz" value="gxtyt-wz"><label for="road-network-unit-gxtyt-wz">梧州市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-bh" value="gxtyt-bh"><label for="road-network-unit-gxtyt-bh">北海市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-fcg" value="gxtyt-fcg"><label for="road-network-unit-gxtyt-fcg">防城港市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-qz" value="gxtyt-qz"><label for="road-network-unit-gxtyt-qz">钦州市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-gg" value="gxtyt-gg"><label for="road-network-unit-gxtyt-gg">贵港市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-yl" value="gxtyt-yl"><label for="road-network-unit-gxtyt-yl">玉林市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-bs" value="gxtyt-bs"><label for="road-network-unit-gxtyt-bs">百色市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-hz" value="gxtyt-hz"><label for="road-network-unit-gxtyt-hz">贺州市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-hc" value="gxtyt-hc"><label for="road-network-unit-gxtyt-hc">河池市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-lb" value="gxtyt-lb"><label for="road-network-unit-gxtyt-lb">来宾市交通运输局</label></li>
                                                    <li><input type="checkbox" id="road-network-unit-gxtyt-cz" value="gxtyt-cz"><label for="road-network-unit-gxtyt-cz">崇左市交通运输局</label></li>
                                                    <li>
                                                        <span class="tree-toggler">+</span><input type="checkbox" id="road-network-unit-gxtyt-zs" value="gxtyt-zs"><label for="road-network-unit-gxtyt-zs">直属单位</label>
                                                        <ul>
                                                            <li><input type="checkbox" id="road-network-unit-gxtyt-zs-glfz" value="gxtyt-zs-glfz"><label for="road-network-unit-gxtyt-zs-glfz">广西公路发展中心</label></li>
                                                            <li><input type="checkbox" id="road-network-unit-gxtyt-zs-gsfz" value="gxtyt-zs-gsfz"><label for="road-network-unit-gxtyt-zs-gsfz">广西高速公路发展中心</label></li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li>
                                                <input type="checkbox" id="road-network-unit-qy" value="qy"><label for="road-network-unit-qy">企业</label>
                                            </li>
                                        </ul>
                                    </div>

                                    <!-- 2.2 按路段划分内容 -->
                                    <div id="filter-by-road-content" class="filter-tab-content">
                                        <ul class="collapsible-tree">
                                            <li>
                                                <span class="tree-toggler">+</span><input type="checkbox" id="road-network-road-gl" value="gl"><label for="road-network-road-gl">公路</label>
                                                <ul>
                                                    <li>
                                                        <span class="tree-toggler">+</span><input type="checkbox" id="road-network-road-gl-gs" value="gl-gs"><label for="road-network-road-gl-gs">高速公路</label>
                                                        <ul>
                                                            <li><input type="checkbox" id="road-network-road-gl-gs-g72" value="gl-gs-g72"><label for="road-network-road-gl-gs-g72">G72</label></li>
                                                            <li><input type="checkbox" id="road-network-road-gl-gs-g80" value="gl-gs-g80"><label for="road-network-road-gl-gs-g80">G80</label></li>
                                                        </ul>
                                                    </li>
                                                    <li>
                                                        <span class="tree-toggler">+</span><input type="checkbox" id="road-network-road-gl-gsgd" value="gl-gsgd"><label for="road-network-road-gl-gsgd">国省干道</label>
                                                        <ul>
                                                            <li><input type="checkbox" id="road-network-road-gl-gsgd-s201" value="gl-gsgd-s201"><label for="road-network-road-gl-gsgd-s201">S201</label></li>
                                                        </ul>
                                                    </li>
                                                    <li>
                                                        <span class="tree-toggler">+</span><input type="checkbox" id="road-network-road-gl-ncgl" value="gl-ncgl"><label for="road-network-road-gl-ncgl">农村公路</label>
                                                        <ul>
                                                            <li><input type="checkbox" id="road-network-road-gl-ncgl-x001" value="gl-ncgl-x001"><label for="road-network-road-gl-ncgl-x001">X001</label></li>
                                                            <li><input type="checkbox" id="road-network-road-gl-ncgl-y002" value="gl-ncgl-y002"><label for="road-network-road-gl-ncgl-y002">Y002</label></li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <li>
                                                <span class="tree-toggler">+</span><input type="checkbox" id="road-network-road-sl" value="sl"><label for="road-network-road-sl">水路</label>
                                                <ul>
                                                    <li>
                                                        <input type="checkbox" id="road-network-road-sl-xj" value="sl-xj"><label for="road-network-road-sl-xj">西江航道</label>
                                                    </li>
                                                </ul>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- 告警信息列表 -->
                            <div class="alert-list-container">
                                <h4>告警信息 <i class="fas fa-bell" style="color: #dc3545; margin-left: 5px;"></i></h4>
                                <div class="alert-tabs">
                                    <button class="alert-tab-button active" data-tab="congestion-sections" onclick="switchRoadNetworkAlertTab(this, 'congestion-sections')">拥堵路段</button>
                                    <button class="alert-tab-button" data-tab="congestion-warnings" onclick="switchRoadNetworkAlertTab(this, 'congestion-warnings')">拥堵预警</button>
                                </div>

                                <!-- 拥堵路段内容 -->
                                <div id="alert-congestion-sections-content" class="alert-tab-content active">
                                    <ul class="alert-list">
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 10:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level">交通中断</span>
                                                <span class="alert-text">G72泉南高速吴家屯隧道出口（柳州端）山体塌方</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 10:35</div>
                                            <div class="alert-content">
                                                <span class="alert-level">交通中断</span>
                                                <span class="alert-text">G72泉南高速K1500+200至K1501+500段双向封闭</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 10:40</div>
                                            <div class="alert-content">
                                                <span class="alert-level">严重拥堵</span>
                                                <span class="alert-text">G72泉南高速K1495+000至K1500+000段（桂林方向）</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 10:45</div>
                                            <div class="alert-content">
                                                <span class="alert-level">严重拥堵</span>
                                                <span class="alert-text">G72泉南高速K1501+500至K1505+000段（柳州方向）</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 11:00</div>
                                            <div class="alert-content">
                                                <span class="alert-level">中度拥堵</span>
                                                <span class="alert-text">G72泉南高速K1490+000桂林南收费站出口</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 11:15</div>
                                            <div class="alert-content">
                                                <span class="alert-level">中度拥堵</span>
                                                <span class="alert-text">G72泉南高速K1510+000柳江收费站出口</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 11:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level">中度拥堵</span>
                                                <span class="alert-text">S20省道桂林至柳州段K50+000至K55+000</span>
                                            </div>
                                        </li>
                                        <li class="alert-item low">
                                            <div class="alert-time">2023-04-20 12:00</div>
                                            <div class="alert-content">
                                                <span class="alert-level">轻度拥堵</span>
                                                <span class="alert-text">S20省道桂林至柳州段K30+000至K35+000</span>
                                            </div>
                                        </li>
                                        <li class="alert-item low">
                                            <div class="alert-time">2023-04-20 12:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level">轻度拥堵</span>
                                                <span class="alert-text">S20省道桂林至柳州段K70+000至K75+000</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 13:00</div>
                                            <div class="alert-content">
                                                <span class="alert-level">交通管制</span>
                                                <span class="alert-text">G72泉南高速K1490+000至K1510+000段实施交通管制</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>

                                <!-- 拥堵预警内容 -->
                                <div id="alert-congestion-warnings-content" class="alert-tab-content">
                                    <ul class="alert-list">
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 10:45</div>
                                            <div class="alert-content">
                                                <span class="alert-level">交通管制预警</span>
                                                <span class="alert-text">G72泉南高速K1480+000至K1520+000段预计将实施交通管制</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 10:50</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将严重拥堵</span>
                                                <span class="alert-text">S20省道桂林至柳州段预计30分钟内出现严重拥堵</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 11:00</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将中度拥堵</span>
                                                <span class="alert-text">G72泉南高速K1485+000桂林南收费站预计20分钟内出现中度拥堵</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 11:10</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将中度拥堵</span>
                                                <span class="alert-text">G72泉南高速K1515+000柳江收费站预计25分钟内出现中度拥堵</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 11:20</div>
                                            <div class="alert-content">
                                                <span class="alert-level">危险路段预警</span>
                                                <span class="alert-text">G72泉南高速吴家屯隧道附近山体存在二次塌方风险</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 11:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将中度拥堵</span>
                                                <span class="alert-text">S20省道K40+000至K60+000段预计40分钟内出现中度拥堵</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 11:45</div>
                                            <div class="alert-content">
                                                <span class="alert-level">危险品泄漏预警</span>
                                                <span class="alert-text">G72泉南高速吴家屯隧道事故现场槽罐车粗苯泄漏风险增加</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 12:00</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将中度拥堵</span>
                                                <span class="alert-text">桂林市区至机场高速预计35分钟内出现中度拥堵</span>
                                            </div>
                                        </li>
                                        <li class="alert-item high">
                                            <div class="alert-time">2023-04-20 12:15</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将严重拥堵</span>
                                                <span class="alert-text">柳州市区至柳江县道路预计30分钟内出现严重拥堵</span>
                                            </div>
                                        </li>
                                        <li class="alert-item medium">
                                            <div class="alert-time">2023-04-20 12:30</div>
                                            <div class="alert-content">
                                                <span class="alert-level">即将中度拥堵</span>
                                                <span class="alert-text">G72泉南高速周边县道预计55分钟内出现中度拥堵</span>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </aside>

                    <!-- 地图显示区域 -->
                    <section class="map-display-area">
                        <!-- 最新告警提示框 -->
                        <div class="latest-alert-container" id="road-network-alert">
                            <div class="latest-alert high">
                                <div class="alert-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="alert-content">
                                    <div class="alert-title">最新告警 <span class="alert-time">2023-04-20 10:30</span></div>
                                    <div class="alert-message">
                                        <span class="alert-level">交通中断</span>
                                        <span class="alert-text">G72泉南高速吴家屯隧道出口（柳州端）山体塌方</span>
                                    </div>
                                </div>
                                <div class="alert-actions">
                                    <button class="alert-more-btn" onclick="document.getElementById('alert-congestion-sections-content').scrollIntoView({behavior: 'smooth'})">
                                        查看更多
                                    </button>
                                    <button class="alert-close-btn" onclick="document.getElementById('road-network-alert').style.display='none'">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <img src="lib/map_new.png" alt="广西地图" id="road-network-map-image">

                        <!-- 图例 -->
                        <div class="map-legend">
                            <div class="legend-section">
                                <div class="legend-title">拥堵等级</div>
                                <div class="legend-items">
                                    <div class="legend-item">
                                        <div class="legend-color" style="background-color: rgba(220, 53, 69, 0.9);"></div>
                                        <div class="legend-text">严重拥堵</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background-color: rgba(255, 153, 0, 0.9);"></div>
                                        <div class="legend-text">中度拥堵</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background-color: rgba(255, 193, 7, 0.9);"></div>
                                        <div class="legend-text">轻度拥堵</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background-color: rgba(220, 53, 69, 0.6); border: 1px dashed rgba(220, 53, 69, 0.9);"></div>
                                        <div class="legend-text">即将严重拥堵</div>
                                    </div>
                                    <div class="legend-item">
                                        <div class="legend-color" style="background-color: rgba(255, 153, 0, 0.6); border: 1px dashed rgba(255, 153, 0, 0.9);"></div>
                                        <div class="legend-text">即将中度拥堵</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 右侧信息面板 -->
                    <aside class="right-sidebar">
                        <div class="statistics-panel">
                            <h3>统计分析</h3>
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <div class="stat-label">交通中断</div>
                                    <div class="stat-value" id="severe-congestion-count">2</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">严重拥堵</div>
                                    <div class="stat-value" id="moderate-congestion-count">3</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">中度拥堵</div>
                                    <div class="stat-value" id="light-congestion-count">3</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-label">轻度拥堵</div>
                                    <div class="stat-value" id="soon-congestion-count">2</div>
                                </div>
                            </div>
                        </div>
                        <div class="risk-details-list">
                            <h3>小时流量</h3>
                            <table class="details-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>地市</th>
                                        <th>路段</th>
                                        <th>流量</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1</td>
                                        <td>桂林市</td>
                                        <td>G72泉南高速K1490+000</td>
                                        <td>0辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>2</td>
                                        <td>桂林市</td>
                                        <td>G72泉南高速K1495+000</td>
                                        <td>0辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>3</td>
                                        <td>桂林市</td>
                                        <td>G72泉南高速K1500+000</td>
                                        <td>0辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>4</td>
                                        <td>柳州市</td>
                                        <td>G72泉南高速K1501+500</td>
                                        <td>0辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>5</td>
                                        <td>柳州市</td>
                                        <td>G72泉南高速K1505+000</td>
                                        <td>0辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>6</td>
                                        <td>柳州市</td>
                                        <td>G72泉南高速K1510+000</td>
                                        <td>0辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>7</td>
                                        <td>桂林市</td>
                                        <td>S20省道K30+000</td>
                                        <td>920辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>8</td>
                                        <td>桂林市</td>
                                        <td>S20省道K50+000</td>
                                        <td>870辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>9</td>
                                        <td>柳州市</td>
                                        <td>S20省道K70+000</td>
                                        <td>780辆/小时</td>
                                    </tr>
                                    <tr>
                                        <td>10</td>
                                        <td>桂林市</td>
                                        <td>桂林市区至机场高速</td>
                                        <td>1260辆/小时</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </aside>
                </div>
            </div>
        </main>
    </div>

    <!-- 拥堵详情模态框 -->
    <div id="congestion-detail-modal" class="modal-overlay" style="display: none;">
        <div class="modal-container">
            <div class="modal-header">
                <h3 id="congestion-modal-title">拥堵详情</h3>
                <button class="modal-close-btn" onclick="closeCongestionModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <!-- 基本信息 -->
                <div class="info-section">
                    <h4><i class="fas fa-info-circle"></i> 基本信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">路段编号：</span>
                            <span class="info-value" id="road-section-code">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">起止桩号：</span>
                            <span class="info-value" id="road-section-range">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">拥堵等级：</span>
                            <span class="info-value congestion-level" id="congestion-level">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">开始时间：</span>
                            <span class="info-value" id="congestion-start-time">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">持续时间：</span>
                            <span class="info-value" id="congestion-duration">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">车流量：</span>
                            <span class="info-value" id="traffic-flow">-</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">平均车速：</span>
                            <span class="info-value" id="average-speed">-</span>
                        </div>
                    </div>
                </div>

                <!-- 建议绕行方案 -->
                <div class="info-section">
                    <h4><i class="fas fa-route"></i> 建议绕行方案</h4>
                    <div class="detour-routes" id="detour-routes">
                        <!-- 绕行方案将通过JavaScript动态添加 -->
                    </div>
                </div>

                <!-- 附近监控视频 -->
                <div class="info-section">
                    <h4><i class="fas fa-video"></i> 附近监控视频</h4>
                    <div class="video-grid" id="monitoring-videos">
                        <!-- 监控视频将通过JavaScript动态添加 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入公共JavaScript -->
    <script src="js/emergency-common.js"></script>

    <!-- 路网运行专用JavaScript -->
    <script src="js/road-network.js"></script>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('路网运行页面已加载');

            // 初始化导航高亮
            if (typeof initializeNavigation === 'function') {
                initializeNavigation('road-network');
            }
        });

        // 初始化导航栏
        NavigationComponent.init('road-network');
    </script>
</body>
</html>