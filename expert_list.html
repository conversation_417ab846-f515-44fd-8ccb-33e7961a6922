<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>专家库管理 - 应急管理系统</title>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="css/tailwind.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
  <style>
    html, body {
        height: 100%;
        margin: 0;
    }
    body {
      font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
    }
    .sidebar-menu-item {
      display: flex;
      align-items: center;
      padding: 0.5rem 1rem;
      color: #4b5563;
      transition: all 0.2s;
    }
    
    .sidebar-menu-item:hover, .sidebar-menu-item.active {
      background-color: #f3f4f6;
      color: #1f2937;
    }
    
    .sidebar-menu-item.active {
      border-left: 3px solid #2563eb;
    }
    
    .main-content {
      transition: margin-left 0.3s;
    }
    
    .sidebar {
      width: 250px;
      transition: all 0.3s;
      z-index: 40;
    }
    
    .sidebar.collapsed {
      width: 0px;
      transform: translateX(-100%);
    }
    
    body.sidebar-expanded .main-content {
      margin-left: 250px;
    }
    
    body.sidebar-collapsed .main-content {
      margin-left: 0;
    }
    
    @media (max-width: 768px) {
      body .main-content {
        margin-left: 0;
      }
      
      .sidebar {
        transform: translateX(-100%);
      }
      
      .sidebar.expanded {
        transform: translateX(0);
      }
    }
    
    /* 表格和状态标签样式 */
    .status-badge {
      padding: 0.25rem 0.5rem;
      border-radius: 9999px;
      font-size: 0.75rem;
      font-weight: 600;
      display: inline-block;
    }
    
    .status-badge.normal { /* 正常状态 */
      background-color: #DEF7EC;
      color: #03543E;
    }
    
    .status-badge.inactive { /* 停用状态 */
      background-color: #FEE2E2;
      color: #991B1B;
    }
    /* 旧的状态样式可以保留或删除 */
    .status-badge.active { background-color: #DEF7EC; color: #03543E; } /* 可暂时保留，或统一为 normal */
    .status-badge.leave { background-color: #FEF3C7; color: #92400E; }
    .status-badge.retired { background-color: #FEE2E2; color: #991B1B; } /* 可暂时保留，或统一为 inactive */

    /* 自定义 Tree Select 样式 */
    .el-tree-select {
        width: 100% !important;
    }
    .el-select-dropdown__wrap {
        max-height: 400px;
    }
    .el-tree-node__content {
        height: 32px;
    }
    .el-tree-node__label {
        font-size: 14px;
    }
  </style>
</head>
<body class="bg-gray-100 min-h-screen">
  <!-- Removed old sidebar and header -->

  <!-- Navbar Placeholder -->
  <div id="navbar-placeholder"></div>

  <!-- Flex Container for Sidebar and Main Content -->
  <div class="flex-container h-full" style="display: flex;">
      <!-- Sidebar Placeholder -->
      <div id="sidebar-placeholder"></div>

      <!-- Main Content Area -->
      <main id="main-content" class="flex-grow pb-8 px-6 bg-gray-100">
          <!-- Removed old wrapper -->
          <div class="py-6">
      <!-- 页面标题 -->
      <div class="flex justify-between items-center mb-6">
        <div>
          <h2 class="text-2xl font-semibold text-gray-800">专家列表</h2>
          <p class="text-sm text-gray-500 mt-1">管理系统内的专业领域专家信息</p>
        </div>
        <button id="btnAdd" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
          <i class="fas fa-plus mr-2"></i>添加专家
        </button>
      </div>

      <!-- 过滤栏 -->
      <div class="bg-white p-4 rounded-lg shadow-sm mb-6">
        <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label for="orgFilter" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
            <div id="orgFilterApp">
                <el-tree-select
                    v-model="selectedOrgs"
                    :data="orgOptions"
                    multiple
                    show-checkbox
                    :props="{ value: 'value', label: 'label', children: 'children' }"
                    placeholder="请选择单位"
                    class="block w-full"
                    @change="handleOrgChange"
                />
            </div>
          </div>
          <div>
            <label for="filterExpertise" class="block text-sm font-medium text-gray-700 mb-1">专业领域</label>
            <select id="filterExpertise" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="">全部领域</option>
              <option value="fire">消防救援</option>
              <option value="medical">医疗救护</option>
              <option value="chemical">危化品处置</option>
              <option value="geology">地质灾害</option>
              <option value="flood">洪涝灾害</option>
            </select>
          </div>
          <div>
            <label for="filterLevel" class="block text-sm font-medium text-gray-700 mb-1">专家级别</label>
            <select id="filterLevel" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="">全部级别</option>
              <option value="national">国家级</option>
              <option value="provincial">省级</option>
              <option value="city">市级</option>
              <option value="county">县级</option>
            </select>
          </div>
          <div>
            <label for="filterStatus" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
            <select id="filterStatus" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="">全部状态</option>
              <option value="normal">正常</option>
              <option value="inactive">停用</option>
            </select>
          </div>
          <div class="flex items-end space-x-2">
            <button id="btnFilter" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <i class="fas fa-filter mr-1"></i> 筛选
            </button>
            <button id="btnResetFilter" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
              <i class="fas fa-undo mr-1"></i> 重置
            </button>
          </div>
        </div>
      </div>

      <!-- 数据表格 -->
      <div class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业领域</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专家级别</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最近一次确认时间</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">刘建国</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">男</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省消防总队</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">消防救援</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13900139001</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-26</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="status-badge normal">正常</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="1">
                    <i class="fas fa-edit"></i> 编辑
                  </button>
                  <button class="text-red-600 hover:text-red-900 btn-delete" data-id="1">
                    <i class="fas fa-trash-alt"></i> 删除
                  </button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张美华</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">女</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市第一人民医院</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医疗救护</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138002</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-15</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="status-badge normal">正常</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="2">
                    <i class="fas fa-edit"></i> 编辑
                  </button>
                  <button class="text-red-600 hover:text-red-900 btn-delete" data-id="2">
                    <i class="fas fa-trash-alt"></i> 删除
                  </button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">陈杰</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">男</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">应急管理局危化处</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">危化品处置</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138003</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-01</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="status-badge inactive">停用</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="3">
                    <i class="fas fa-edit"></i> 编辑
                  </button>
                  <button class="text-red-600 hover:text-red-900 btn-delete" data-id="3">
                    <i class="fas fa-trash-alt"></i> 删除
                  </button>
                </td>
              </tr>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李岩</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">男</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">地质勘查院</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">地质灾害</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138004</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-01</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="status-badge normal">正常</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="4">
                    <i class="fas fa-edit"></i> 编辑
                  </button>
                  <button class="text-red-600 hover:text-red-900 btn-delete" data-id="4">
                    <i class="fas fa-trash-alt"></i> 删除
                  </button>
                </td>
              </tr>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王慧</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">女</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">水利设计院</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">洪涝灾害</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">13800138005</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-20</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="status-badge inactive">停用</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3 btn-edit" data-id="5">
                    <i class="fas fa-edit"></i> 编辑
                  </button>
                  <button class="text-red-600 hover:text-red-900 btn-delete" data-id="5">
                    <i class="fas fa-trash-alt"></i> 删除
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 分页控件 -->
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    上一页
                </a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    下一页
                </a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">12</span> 条记录
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">上一页</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            1
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            2
                        </a>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            3
                        </a>
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                        <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            8
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">下一页</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
      </div>
          </div> <!-- Close py-6 -->
      </main>
  </div> <!-- Close flex-container -->
  
  <!-- Modals outside main content -->
  <!-- 添加专家模态框 -->
  <div id="addPersonnelModal">
      <el-dialog
          v-model="dialogVisible"
          :title="modalTitle"
          width="60%"
          @closed="resetForm"
          :close-on-click-modal="false"
      >
          <el-form :model="expertForm" ref="expertFormRef" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="姓名" required prop="name">
                            <el-input v-model="expertForm.name" placeholder="请输入专家姓名"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="性别" required prop="gender">
                             <el-radio-group v-model="expertForm.gender">
                                <el-radio label="male">男</el-radio>
                                <el-radio label="female">女</el-radio>
                             </el-radio-group>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="所属单位" required prop="organizationId">
                  <el-tree-select
                        v-model="expertForm.organizationId"
                      :data="orgOptions"
                        :multiple="false"
                        check-strictly
                      :props="{ value: 'value', label: 'label', children: 'children' }"
                        placeholder="请选择所属单位"
                        class="w-full"
                     />
                </el-form-item>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="专业领域" required prop="expertise">
                             <el-select v-model="expertForm.expertise" placeholder="请选择专业领域" class="w-full">
                                <el-option label="医疗卫生" value="medical"></el-option>
                                <el-option label="工程抢险" value="engineering"></el-option>
                                <el-option label="危化品处置" value="chemical"></el-option>
                                <el-option label="消防救援" value="firefighting"></el-option>
                                <el-option label="气象水文" value="meteorology"></el-option>
                                <el-option label="其他" value="other"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="级别" required prop="level">
                            <el-select v-model="expertForm.level" placeholder="请选择专家级别" class="w-full">
                                <el-option label="国家级" value="national"></el-option>
                                <el-option label="省级" value="provincial"></el-option>
                                <el-option label="市级" value="municipal"></el-option>
                                <el-option label="县级" value="county"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="联系电话" required prop="phone">
                            <el-input v-model="expertForm.phone" placeholder="请输入联系电话"></el-input>
                        </el-form-item>
                    </el-col>
                     <el-col :span="12">
                        <el-form-item label="状态" required prop="status">
                            <el-select v-model="expertForm.status" placeholder="请选择状态" class="w-full">
                                <el-option label="正常" value="normal"></el-option>
                                <el-option label="停用" value="inactive"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="closeModal">取消</el-button>
                    <el-button type="primary" @click="submitForm">{{ isEditMode ? '保存' : '添加' }}</el-button>
                </span>
            </template>
      </el-dialog>
  </div>
  
  <!-- 删除确认模态框 -->
  <div id="deleteConfirmModal" class="fixed inset-0 z-[100] hidden"> <!-- Increased z-index -->
    <!-- ... Delete Modal Content ... -->
        <div class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="fixed inset-0 z-10 overflow-y-auto">
            <div class="flex min-h-full items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-800">确认删除</h3>
      </div>
      <div class="px-6 py-4">
        <p class="text-sm text-gray-700">您确定要删除该专家信息吗？此操作无法撤销。</p>
      </div>
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
        <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
          取消
        </button>
        <button id="btnConfirmDelete" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
          删除
        </button>
                    </div>
                </div>
      </div>
    </div>
  </div>
  
  <!-- Load Libraries First -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
  <script src="https://unpkg.com/element-plus"></script>

  <!-- Page Specific Scripts -->
  <script>
    // Define unit options globally for reuse
    const standardUnitOptions = [{
                    value: '1',
                    label: '广西壮族自治区交通运输厅',
                    children: [{
                        value: '1.1',
                        label: '直属事业单位及专项机构',
            children: [{ value: '1.1.1', label: '自治区公路发展中心' }, { value: '1.1.2', label: '自治区高速公路发展中心' }, { value: '1.1.3', label: '自治区道路运输发展中心' }]
                    }, {
                        value: '1.2',
                        label: '市级交通运输局',
            children: [{ value: '1.2.1', label: '钦州市交通运输局' }, { value: '1.2.2', label: '南宁市交通运输局' }, { value: '1.2.3', label: '玉林市交通运输局' }]
        }]
     },{
                    value: '2',
                    label: '省消防总队'
                 },{ 
                    value: '3', 
                    label: '市第一人民医院'
                 },{ 
                    value: '4', 
                    label: '应急管理局危化处'
                 },{ 
                    value: '5', 
                    label: '地质勘查院'
                 },{ 
                    value: '6', 
                    label: '水利设计院'
     }];

    // Vue App for Org Filter in search bar
    const OrgFilterApp = {
        data() {
            return {
                selectedOrgs: [],
                orgOptions: standardUnitOptions // Use global data
            }
        },
        methods: {
            handleOrgChange(value) {
                console.log('选中的单位:', value);
            }
        }
    };

    // Vue App for Expert Modal
    const ExpertModalApp = {
        data() {
            return {
                dialogVisible: false,
                isEditMode: false,
                modalTitle: '添加专家',
                expertForm: {
                    id: null,
                    name: '',
                    gender: 'male',
                    organizationId: null,
                    expertise: '',
                    level: '',
                    phone: '',
                    status: 'normal'
                },
                 orgOptions: standardUnitOptions // Use global data for modal too
            };
        },
        methods: {
             openModal(isEdit = false, expertData = null) {
                this.isEditMode = isEdit;
                this.modalTitle = isEdit ? '编辑专家' : '添加专家';
                if (isEdit && expertData) {
                    this.expertForm = { ...this.expertForm, ...expertData };
                } else {
                    this.resetForm();
                }
                this.dialogVisible = true;
            },
            closeModal() {
                this.dialogVisible = false;
            },
            submitForm() {
                 console.log('Submitting expert form...');
                 // Add validation logic here if needed
                 if (!this.expertForm.name || !this.expertForm.organizationId /*...*/) {
                     ElementPlus.ElMessage.error('请填写所有必填项!');
                     return;
                 }
                 console.log('Form Submitted:', this.expertForm);
                 // TODO: Add actual save/update logic here
                 this.closeModal();
                 ElementPlus.ElMessage.success(this.isEditMode ? '专家信息更新成功！' : '专家添加成功！');
                 // Optionally refresh table
            },
            resetForm() {
                this.expertForm = {
                    id: null, name: '', gender: 'male', organizationId: null,
                    expertise: '', level: '', phone: '', status: 'normal'
                };
                 // if (this.$refs.expertFormRef) { ... }
            }
        }
    };

    const expertModalVm = Vue.createApp(ExpertModalApp);
    expertModalVm.use(ElementPlus);
    // Mount the modal app to its container
    const mountedExpertModal = expertModalVm.mount('#addPersonnelModal'); 

    // Mount Org Filter App
    const orgFilterVm = Vue.createApp(OrgFilterApp);
    orgFilterVm.use(ElementPlus);
    orgFilterVm.mount('#orgFilterApp');

    // --- Event Listeners --- 
    document.addEventListener('DOMContentLoaded', function() {
        // Removed old sidebar logic

        // Add expert button
      const btnAdd = document.getElementById('btnAdd');
      if (btnAdd) {
            btnAdd.addEventListener('click', () => {
                mountedExpertModal.openModal(false);
            });
        }

        // Close modals
        const deleteConfirmModal = document.getElementById('deleteConfirmModal');
        document.querySelectorAll('.btn-close-modal').forEach(button => {
            button.addEventListener('click', () => {
                // Assuming modal components handle their own visibility via v-model
                // We just need to handle the delete confirmation modal hide
                 if(deleteConfirmModal) deleteConfirmModal.classList.add('hidden');
        });
      });
      
        // Table button actions (delegation)
        const tableBody = document.querySelector('tbody');
        if (tableBody) {
            tableBody.addEventListener('click', (event) => {
                const button = event.target.closest('button');
                if (!button) return;
                const expertId = button.dataset.id;

                if (button.classList.contains('btn-edit')) {
                    console.log('Edit expert:', expertId);
                     // Mock data for editing
                     const mockData = {
                        id: expertId, name: '王五', gender: 'male', organizationId: '1.1.1', 
                        expertise: 'engineering', level: 'provincial', phone: '13700001111', status: 'normal'
                     };
                    mountedExpertModal.openModal(true, mockData);
                } else if (button.classList.contains('btn-delete')) {
                     console.log('Delete expert:', expertId);
                     if(deleteConfirmModal) {
                         // Store ID for confirmation modal
                         deleteConfirmModal.dataset.deleteId = expertId; 
                         deleteConfirmModal.classList.remove('hidden');
                     }
                }
            });
        }
        
        // Confirm Delete button
      const btnConfirmDelete = document.getElementById('btnConfirmDelete');
        if (btnConfirmDelete && deleteConfirmModal) {
            btnConfirmDelete.addEventListener('click', () => {
                const expertIdToDelete = deleteConfirmModal.dataset.deleteId;
                if (expertIdToDelete) {
                    console.log('Confirm delete expert:', expertIdToDelete);
                     // TODO: Add actual delete API call
                     ElementPlus.ElMessage.success('专家删除成功！');
                     deleteConfirmModal.classList.add('hidden');
                     delete deleteConfirmModal.dataset.deleteId; // Clear stored ID
                     // Optionally refresh table
                 }
            });
        }
        
        // Filter/Reset Buttons (Keep or add logic)
        // const btnFilter = document.getElementById('btnFilter'); ...
        // const btnResetFilter = document.getElementById('btnResetFilter'); ...
    });
  </script>

  <!-- Load component HTML first -->
  <script src="js/navbarComponent.js"></script>
  <script src="js/sidebarComponent.js"></script>
  <!-- Then load the script to inject them and highlight links -->
  <script src="js/loadComponents.js"></script>
</body>
</html> 