body, html {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    background-color: #f4f7fa;
    color: #333;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh; /* 使容器占满整个视口高度 */
}

/* 顶部导航栏 */
.top-nav {
    display: flex;
    justify-content: flex-start; /* 使系统名称和标签卡靠左对齐 */
    align-items: center; /* 垂直居中对齐 */
    padding: 6px 15px; /* 减小内边距 */
    background-color: #0056b3; /* 深蓝色背景 */
    color: white;
    flex-shrink: 0; /* 防止header被压缩 */
    min-height: 40px; /* 设置最小高度 */
}

.system-title {
    font-size: 1.2em; /* 减小字体 */
    font-weight: bold;
    margin-right: 20px; /* 减小右侧间距 */
    white-space: nowrap; /* 防止换行 */
}

.tab-navigation {
    display: flex;
    flex-wrap: wrap; /* 允许在必要时换行 */
    align-items: center;
}

.tab-button {
    background-color: transparent;
    color: white;
    border: 1px solid white; /* 轻微边框 */
    padding: 5px 8px; /* 减小内边距 */
    margin-left: 5px; /* 减小左侧间距 */
    margin-bottom: 5px; /* 添加底部间距，以防换行 */
    cursor: pointer;
    font-size: 0.8em; /* 减小字体 */
    border-radius: 3px; /* 减小圆角 */
    transition: background-color 0.3s, color 0.3s;
    white-space: nowrap; /* 防止按钮内文字换行 */
}

.tab-button:hover {
    background-color: white;
    color: #0056b3;
}

.tab-button.active {
    background-color: #e9ecef; /* 激活标签的背景色 */
    color: #0056b3;
    border-color: #e9ecef;
}

/* 主要内容区域 */
.main-content {
    flex-grow: 1; /* 占据剩余空间 */
    display: flex; /* 用于控制内部tab-content的显示 */
    overflow-y: auto; /* 如果内容过多则显示滚动条 */
    height: calc(100vh - 52px); /* 减去顶部导航栏的高度，调整为新的高度 */
}

.tab-content {
    display: none; /* 默认隐藏所有标签内容 */
    width: 100%;
    padding: 15px;
    box-sizing: border-box;
    height: 100%; /* 确保高度填满容器 */
}

.tab-content.active, #risk-map-content {
    display: flex; /* 当前激活的标签页内容为flex布局 */
    flex-direction: row; /* 内部元素横向排列：左侧栏、地图、右侧栏 */
    gap: 15px; /* 各区域之间的间距 */
    height: 100%; /* 确保高度填满容器 */
}

/* 路网运行标签页样式 */
#road-network-content {
    display: none; /* 默认隐藏路网运行标签页 */
}

#road-network-content.active {
    display: flex; /* 当激活时显示为flex布局 */
    flex-direction: row; /* 内部元素横向排列：左侧栏、地图、右侧栏 */
    gap: 15px; /* 各区域之间的间距 */
    height: 100%; /* 确保高度填满容器 */
}

/* 左侧资源目录筛选框 */
.left-sidebar {
    flex: 0 0 280px; /* Slightly wider sidebar */
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    font-size: 14px; /* 增大基础字体大小 */
}

.resource-filter h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0056b3;
    font-size: 17px;
}

.resource-filter h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0056b3;
    font-size: 15px;
}

.resource-filter .filter-group {
    margin-bottom: 15px;
}

.resource-filter label {
    display: block;
    margin-bottom: 5px;
    cursor: pointer;
}

.resource-filter-container h4 {
    font-size: 15px;
    color: #0056b3; /* Match other titles */
    margin-top: 0;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e0e0e0;
}

/* Resource Type Selector */
.resource-type-selector {
    margin-bottom: 20px;
}

.resource-type-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    background-color: #f8f9fa; /* Light background for items */
    padding: 8px 10px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.resource-type-item input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
    transform: scale(1.1); /* Slightly larger checkbox */
}

.resource-type-item label {
    font-size: 14px; /* 增大字体大小 */
    color: #333;
    cursor: pointer;
    flex-grow: 1;
}

/* 防汛防台筛选面板样式 */
.filter-panel {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 10px; /* 减小内边距 */
    margin-bottom: 10px; /* 减小下边距 */
    border: 1px solid #e9ecef;
}

.select-all-checkbox {
    margin-right: 8px;
    cursor: pointer;
    transform: scale(1.2);
}

.select-all-checkbox + label {
    font-size: 15px;
    font-weight: 500;
    color: #0d6efd;
    cursor: pointer;
}

.checkbox-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px; /* 减小下边距 */
}

.checkbox-item input[type="checkbox"] {
    margin-right: 8px;
    cursor: pointer;
}

.checkbox-item label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.filter-date {
    width: 120px; /* 设置固定宽度 */
    padding: 5px 8px; /* 减小内边距 */
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    background-color: #fff;
}

/* 资源类型标签卡样式 */
.resource-type-tabs {
    display: flex;
    margin: 15px 0;
    border-bottom: 2px solid #0056b3;
    width: 100%;
}

.resource-tab-button {
    padding: 8px 12px;
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    border-bottom: none;
    cursor: pointer;
    font-size: 0.95em;
    color: #495057;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 5px;
    position: relative;
    bottom: -1px;
    transition: background-color 0.2s, color 0.2s;
    flex-grow: 1;
    text-align: center;
}

.resource-tab-button.active {
    background-color: #0056b3;
    color: white;
    border-color: #0056b3;
    border-bottom: 1px solid #0056b3;
    z-index: 1;
}

.resource-tab-button:not(.active):hover {
    background-color: #d1d9e0;
}

.resource-content-container {
    margin-bottom: 20px;
}

.resource-tab-content {
    display: none;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 4px 4px;
}

.resource-tab-content.active {
    display: block;
}

/* 风险隐患类型和等级筛选样式 */
/* 筛选区域样式 */
.filter-section {
    margin-bottom: 15px;
}

.filter-row {
    display: flex;
    margin-bottom: 6px; /* 减小下边距 */
    gap: 8px; /* 减小间距 */
}

.filter-item {
    display: flex;
    align-items: center;
    flex: 1;
    margin-bottom: 6px; /* 减小间距 */
}

.filter-item label {
    font-size: 14px;
    color: #333;
    margin-right: 10px; /* 增加间距 */
    white-space: nowrap;
    min-width: 80px; /* 增加宽度 */
    font-weight: 500; /* 稍微加粗 */
}

.filter-select {
    flex: 1;
    padding: 8px 12px; /* 增加内边距 */
    border: 1px solid #c0d3eb; /* 更浅的蓝色边框 */
    border-radius: 6px;
    font-size: 14px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 120px; /* 进一步减小宽度 */
    max-width: 120px; /* 进一步减小最大宽度 */
    appearance: none; /* 移除默认的下拉箭头 */
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 12px;
    cursor: pointer;
}

.filter-select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
}

/* 美化下拉框选项 */
.filter-select option {
    padding: 8px;
    font-size: 14px;
}

/* 风险隐患类型列表结构 */
.risk-type-list-container {
    flex-direction: column;
    align-items: flex-start;
}

.risk-type-list-container > label {
    margin-bottom: 8px;
    font-weight: 500;
}

.risk-type-list {
    width: 100%;
    max-height: none;
    overflow: visible;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    background-color: #f8f9fa;
}

.risk-type-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    padding: 4px 0;
}

.risk-type-item:last-child {
    margin-bottom: 0;
}

.risk-type-item input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.risk-type-item label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    margin: 0;
    user-select: none;
}

.risk-type-item:hover {
    background-color: #e9ecef;
    border-radius: 3px;
}

/* 在建项目类型列表结构 - 和风险类型一样的样式 */
.project-type-list-container {
    flex-direction: column;
    align-items: flex-start;
}

.project-type-list-container > label {
    margin-bottom: 8px;
    font-weight: 500;
}

.project-type-list {
    width: 100%;
    max-height: none;
    overflow: visible;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    background-color: #f8f9fa;
}

.project-type-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    padding: 4px 0;
}

.project-type-item:last-child {
    margin-bottom: 0;
}

.project-type-item input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.project-type-item label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    margin: 0;
    user-select: none;
}

.project-type-item:hover {
    background-color: #e9ecef;
    border-radius: 3px;
}

/* 其他资源类型列表结构 */
.other-type-list-container {
    flex-direction: column;
    align-items: flex-start;
}

.other-type-list-container > label {
    margin-bottom: 8px;
    font-weight: 500;
}

.other-type-list {
    width: 100%;
    max-height: none;
    overflow: visible;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 8px;
    background-color: #f8f9fa;
}

.other-type-item {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    padding: 4px 0;
}

.other-type-item:last-child {
    margin-bottom: 0;
}

.other-type-item input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.other-type-item label {
    font-size: 14px;
    color: #333;
    cursor: pointer;
    margin: 0;
    user-select: none;
}

.other-type-item:hover {
    background-color: #e9ecef;
    border-radius: 3px;
}

/* 高风险、中风险、低风险标签样式 */
.high-risk-label {
    color: #dc3545;
    font-weight: 500;
}

.medium-risk-label {
    color: #ff9900;
    font-weight: 500;
}

.low-risk-label {
    color: #28a745;
    font-weight: 500;
}

/* 项目搜索框样式 */
.search-box {
    display: flex;
    flex: 1;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    max-width: 180px; /* 限制搜索框的最大宽度 */
}

.search-box input {
    flex-grow: 1;
    padding: 5px 8px; /* 减小内边距 */
    border: 1px solid #dee2e6;
    border-right: none;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    width: 120px; /* 设置固定宽度 */
}

.search-box input:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-box button {
    background-color: #0056b3;
    color: white;
    border: none;
    padding: 5px 8px; /* 减小内边距 */
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    transition: background-color 0.15s ease-in-out;
}

.search-box button:hover {
    background-color: #004494;
}

.search-box button:active {
    background-color: #003d7a;
}

/* 统计面板中高风险数量的特殊样式 */
.risk-high-count {
    color: #dc3545 !important;
}

/* Resource Condition Filter */
.filter-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 2px solid #0056b3; /* Active tab indicator line */
}

.filter-tab-button {
    padding: 8px 12px; /* Adjust padding for smaller font */
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    border-bottom: none; /* Remove bottom border for inactive tabs */
    cursor: pointer;
    font-size: 0.95em; /* Was 0.9em, now relative to 12px -> 11.4px */
    color: #495057;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 5px;
    position: relative;
    bottom: -1px; /* Align with the border-bottom of .filter-tabs */
    transition: background-color 0.2s, color 0.2s;
}

.filter-tab-button.active {
    background-color: #0056b3;
    color: white;
    border-color: #0056b3;
    border-bottom: 1px solid #0056b3; /* Ensure active tab visually merges with content area */
    z-index: 1; /* Bring active tab to front */
}

.filter-tab-button:not(.active):hover {
    background-color: #d1d9e0;
}

.filter-tab-content {
    display: none; /* Hide inactive tab content */
    padding: 10px;
    border: 1px solid #dee2e6; /* Border for content area */
    border-top: none; /* Remove top border as tabs handle it */
    border-radius: 0 0 4px 4px; /* Rounded bottom corners */
}

.filter-tab-content.active {
    display: block;
}

/* Collapsible Tree Styles */
.collapsible-tree {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.collapsible-tree li {
    padding: 3px 0;
    margin-left: 0; /* MODIFIED: Control indent via ul padding */
    position: relative;
}

.collapsible-tree li > ul {
    list-style: none;
    padding-left: 5px; /* 大幅减少缩进距离 */
    display: none; /* Hidden by default */
}

.collapsible-tree .tree-toggler {
    cursor: pointer;
    display: inline-block;
    width: 15px; /* Adjust size */
    height: 15px; /* Adjust size */
    line-height: 13px; /* Adjust for new height */
    text-align: center;
    border: 1px solid #ccc;
    border-radius: 3px;
    margin-right: 5px;
    font-weight: bold;
    color: #555;
    background-color: #f0f0f0;
    user-select: none; /* Prevent text selection */
    position: absolute;
    left: 0; /* Position toggler to the left of the item */
    top: 2px; /* Re-align if needed */
    font-size: 1em; /* Make toggler icon itself smaller, relative to 12px */
    z-index: 5; /* 确保在复选框上方 */
}

.collapsible-tree li.open > .tree-toggler {
    content: "-"; /* Change to minus when open - JS will handle adding class */
}

.collapsible-tree li {
    position: relative;
    padding-left: 20px;
    margin-bottom: 8px;
}

.collapsible-tree li > ul {
    margin-top: 8px;
    margin-left: 0;
    padding-left: 5px; /* 大幅减少缩进距离 */
    display: none;
}

.collapsible-tree li.open > ul {
    display: block;
}

.collapsible-tree input[type="checkbox"] {
    position: absolute;
    left: 20px;
    top: 3px;
    margin-right: 6px;
    vertical-align: middle;
}

/* 为第二级内容的复选框设置特殊样式 */
.collapsible-tree li > ul > li input[type="checkbox"] {
    left: 10px; /* 减少第二级复选框的左边距 */
}

/* 为第三级内容的复选框设置特殊样式 */
.collapsible-tree li > ul > li > ul > li input[type="checkbox"] {
    left: 5px; /* 减少第三级复选框的左边距 */
}

.collapsible-tree label {
    margin-left: 20px; /* 减少标签左边距 */
    font-size: 14px; /* 增大字体大小 */
    cursor: pointer;
    vertical-align: middle;
    display: inline-block;
}

/* 为第二级内容的标签设置特殊样式 */
.collapsible-tree li > ul > li label {
    margin-left: 15px; /* 减少第二级标签的左边距 */
}

/* 为第三级内容的标签设置特殊样式 */
.collapsible-tree li > ul > li > ul > li label {
    margin-left: 10px; /* 减少第三级标签的左边距 */
}

/* 地图展示区域 */
.map-display-area {
    flex-grow: 1; /* 占据中间的主要空间 */
    position: relative; /* 用于绝对定位标注点 */
    background-color: #e0e0e0; /* 地图区域背景色，实际会被图片覆盖 */
    border-radius: 5px;
    display: flex; /* 使图片居中 */
    justify-content: center;
    align-items: center;
    overflow: hidden; /* 防止图片溢出 */
    height: 100%; /* 确保高度填满容器 */
}

/* 风险隐患管理按钮容器 */
.management-button-container {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 10; /* 确保按钮在地图上方 */
    display: flex;
    gap: 10px;
}

.management-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #0056b3;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.management-button:hover {
    background-color: #004494;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
}

.management-button i {
    font-size: 16px;
}

#map-image, #monitor-map-image, #flood-map-image, #road-network-map-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain; /* 保持图片比例 */
    width: 100%; /* 确保图片宽度填满容器 */
    height: auto; /* 保持图片比例 */
}

/* 地图标注点基础样式 */
.map-marker {
    position: absolute;
    padding: 6px;
    background-color: rgba(255, 0, 0, 0.7); /* 示例红色 */
    color: white;
    border-radius: 50%; /* 圆形 */
    font-size: 0.7em;
    cursor: pointer;
    border: 1px solid white;
    box-shadow: 0 1px 2px rgba(0,0,0,0.3);
    transform: scale(0.85); /* 整体缩小 */
    width: 16px;
    height: 16px;
    display: block; /* 改为block，确保显示 */
    justify-content: center;
    align-items: center;
    z-index: 10; /* 确保在地图上方 */
}

/* 不同类型的标注点可以有不同颜色 */
/* 风险一张图标记点 - 风险隐患点 */
.risk-marker {
    padding: 6px;
}
.risk-marker i {
    font-size: 0.9em;
}
.risk-high { background-color: rgba(220, 53, 69, 0.9); } /* 高风险：红色 */
.risk-medium { background-color: rgba(255, 153, 0, 0.9); } /* 中风险：橙色 */
.risk-low { background-color: rgba(40, 167, 69, 0.9); } /* 低风险：绿色 */

/* 风险一张图标记点 - 在建项目 */
.project-marker {
    padding: 6px;
}
.project-marker i {
    font-size: 0.9em;
}
.project-risk { background-color: rgba(220, 53, 69, 0.9); } /* 存在风险：红色 */
.project-safe { background-color: rgba(108, 117, 125, 0.9); } /* 无风险：灰色 */

/* 监测预警标记点 */
.event-marker {
    background-color: rgba(220, 53, 69, 0.8); /* 红色 */
    padding: 6px;
}
.rescue-marker, .supply-marker, .supplies-marker, .medical-marker, .vehicle-marker {
    padding: 6px;
}
.rescue-marker {
    background-color: rgba(13, 110, 253, 0.8); /* 蓝色 */
}
.supply-marker, .supplies-marker {
    background-color: rgba(255, 193, 7, 0.8); /* 黄色 */
}
.medical-marker {
    background-color: rgba(25, 135, 84, 0.8); /* 绿色 */
}
.vehicle-marker {
    background-color: rgba(108, 117, 125, 0.8); /* 灰色 */
}
.camera-marker {
    background-color: rgba(108, 117, 125, 0.8); /* 灰色 */
    padding: 6px;
}
.weather-marker {
    background-color: rgba(13, 202, 240, 0.8); /* 青色 */
    padding: 6px;
}
/* 应急一张图筛选内容样式 */
.resource-filter-content {
    display: none;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    margin-bottom: 15px;
    background-color: #f8f9fa;
}

.resource-filter-content.active {
    display: block;
}

/* 日期范围和数量范围选择器 */
.date-range-picker, .quantity-range {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.date-input, .quantity-input {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9em;
}

/* 多选下拉框样式 */
select[multiple] {
    height: auto;
    min-height: 80px;
}

/* 应急事件模态框样式 */
.emergency-event-modal-content,
.emergency-supplies-modal-content,
.rescue-forces-modal-content,
.detail-modal-content {
    width: 80%;
    max-width: 900px;
    max-height: 90vh;
    overflow-y: auto;
}

/* 确保模态框正确显示 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

/* 风险一张图通用详情模态框宽度调整 */
#details-modal .modal-content {
    max-width: 1200px !important;
    width: 90% !important;
    margin: 2% auto !important;
}

/* 搜索隐藏的标记点 */
.search-hidden {
    display: none !important;
}

/* 应急事件模态框新增样式 */
.plan-activation-info, .decision-support {
    margin-top: 10px;
}

.activation-description, .decision-description {
    line-height: 1.6;
}

.activation-description p, .decision-description p {
    margin-bottom: 10px;
    text-align: justify;
}

.decision-description ul {
    margin: 10px 0;
    padding-left: 20px;
}

.decision-description li {
    margin-bottom: 5px;
}

.plan-status {
    color: #28a745;
    font-weight: 500;
    margin-top: 5px;
}

.organization-structure {
    margin-top: 10px;
}

.org-item {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 15px;
    background-color: #f9f9f9;
}

.org-name {
    font-size: 16px;
    font-weight: 600;
    color: #0056b3;
    margin-bottom: 8px;
}

.org-commander {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
}

.org-structure {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-top: 10px;
}

.org-group {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
}

.org-group h4 {
    font-size: 14px;
    color: #0056b3;
    margin: 0 0 5px 0;
}

.org-group p {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

.experts-container, .supplies-container, .rescue-teams-container,
.medical-units-container, .fire-units-container, .operation-units-container {
    margin-top: 10px;
}

.expert-item, .supplies-item, .rescue-item, .medical-item, .fire-item, .operation-item {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
}

.expert-name, .supplies-name, .rescue-name, .medical-name, .fire-name, .operation-name {
    font-size: 14px;
    font-weight: 600;
    color: #0056b3;
    margin-bottom: 5px;
}

.expert-title, .expert-address, .expert-distance, .expert-contact,
.supplies-detail, .supplies-contact, .rescue-detail, .rescue-contact,
.medical-detail, .medical-contact, .fire-detail, .fire-contact,
.operation-detail, .operation-contact {
    font-size: 13px;
    color: #666;
    margin-bottom: 3px;
    line-height: 1.4;
}

.supplies-subsection, .rescue-subsection {
    margin-bottom: 15px;
}

.subsection-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    padding-bottom: 3px;
    border-bottom: 1px solid #ddd;
}

.monitoring-container {
    margin-top: 10px;
}

.camera-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.camera-item {
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    background-color: #f9f9f9;
}

.camera-placeholder {
    width: 100%;
    height: 80px;
    background-color: #e9ecef;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.camera-placeholder i {
    font-size: 24px;
    color: #6c757d;
}

.camera-label {
    font-size: 12px;
    color: #333;
    margin-bottom: 3px;
    font-weight: 500;
}

.camera-status {
    font-size: 11px;
    color: #28a745;
}

.info-section {
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8f9fa;
}

.section-title {
    font-size: 1.2em;
    margin-bottom: 15px;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 8px;
}

.subsection-title {
    font-size: 1em;
    margin-bottom: 10px;
    color: #495057;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.info-row {
    display: flex;
    gap: 20px;
}

.info-item {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.info-item.full-width {
    flex: 2;
}

.info-label {
    font-weight: 500;
    color: #6c757d;
    margin-bottom: 5px;
    font-size: 0.9em;
}

.info-value {
    color: #212529;
}

.event-level {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    color: white;
}

.event-level.special {
    background-color: #dc3545;
}

.event-level.major {
    background-color: #fd7e14;
}

.event-level.large {
    background-color: #ffc107;
    color: #212529;
}

.event-level.normal {
    background-color: #28a745;
}

.response-conditions {
    display: flex;
    gap: 20px;
}

.condition-box {
    flex: 2;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
}

.condition-box p {
    margin-bottom: 10px;
}

.condition-box ul {
    padding-left: 20px;
}

.event-level-selector {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.emergency-plans {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.plan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
}

.plan-info {
    flex: 1;
}

.plan-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.plan-scope {
    font-size: 0.9em;
    color: #6c757d;
}

.view-plan-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
}

.view-plan-btn:hover {
    background-color: #0069d9;
}

.resources-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.resource-subsection {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
}

.resource-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.resource-item {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.resource-item:last-child {
    border-bottom: none;
}

.resource-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.resource-detail {
    font-size: 0.9em;
    color: #6c757d;
}

.supplies-table,
.equipment-table,
.dispatch-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.supplies-table th,
.equipment-table th,
.dispatch-table th {
    background-color: #f8f9fa;
    padding: 8px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    font-weight: 500;
}

.supplies-table td,
.equipment-table td,
.dispatch-table td {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
}

/* 防汛防台标记点 */
.danger-marker {
    background-color: rgba(220, 53, 69, 0.8); /* 红色 */
    padding: 6px;
}
.shelter-marker {
    background-color: rgba(25, 135, 84, 0.8); /* 绿色 */
    padding: 6px;
}
.flood-supplies-marker {
    background-color: rgba(255, 193, 7, 0.8); /* 黄色 */
    padding: 6px;
}
.team-marker {
    background-color: rgba(13, 110, 253, 0.8); /* 蓝色 */
    padding: 6px;
}
.water-marker {
    background-color: rgba(13, 202, 240, 0.8); /* 青色 */
    padding: 6px;
}

/* 图例样式 */
.map-legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.25);
    z-index: 10;
    font-size: 14px;
    width: auto;
    display: flex;
    flex-direction: column;
    max-width: 250px;
}

.legend-section {
    margin-bottom: 10px;
}

.legend-section:last-child {
    margin-bottom: 0;
}

.legend-title {
    font-weight: bold;
    font-size: 15px;
    color: #0056b3;
    margin-bottom: 6px;
    border-bottom: 1px solid rgba(0,0,0,0.15);
    padding-bottom: 3px;
}

.legend-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 6px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-size: 10px;
    background-color: rgba(108, 117, 125, 0.8);
}

.legend-color {
    width: 14px;
    height: 14px;
    margin-right: 8px;
    border-radius: 2px;
}

.legend-line {
    width: 22px;
    height: 4px;
    margin-right: 8px;
    border-radius: 0;
}

.legend-text {
    font-size: 13px;
    color: #333;
    white-space: nowrap;
}

/* 态势标绘面板样式 */
.situation-plotting-panel {
    position: absolute;
    top: 60px;
    right: 15px;
    width: 300px;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    z-index: 100;
    padding: 15px;
    display: none; /* 默认隐藏 */
    max-height: 80vh;
    overflow-y: auto;
}

.plotting-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.plotting-panel-header h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.close-plotting-panel {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
}

.close-plotting-panel:hover {
    color: #333;
}

.plotting-icons-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.plotting-icon-row {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.plotting-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 80px;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.plotting-icon:hover {
    background-color: #f0f0f0;
}

.plotting-icon.selected {
    background-color: #e6f7ff;
    border: 1px solid #91d5ff;
}

/* 简单图形图标样式 */
.shape-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.circle-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #ff5252;
    border: 2px solid #c50e29;
}

.triangle-icon {
    width: 0;
    height: 0;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-bottom: 28px solid #4caf50;
}

.square-icon {
    width: 28px;
    height: 28px;
    background-color: #2196f3;
    border: 2px solid #0c7cd5;
}

.plotting-icon span {
    font-size: 12px;
    text-align: center;
    color: #666;
}

.plotting-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.plotting-control-btn {
    padding: 6px 12px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.2s;
}

.plotting-control-btn:hover {
    background-color: #e6e6e6;
}

/* 标绘图标在地图上的样式 */
.plotting-marker {
    position: absolute;
    z-index: 5;
    cursor: move;
    width: 32px;
    height: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* 地图上的圆形标记 */
.plotting-marker.circle-marker {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #ff5252;
    border: 2px solid #c50e29;
}

/* 地图上的三角形标记 */
.plotting-marker.triangle-marker {
    width: 0;
    height: 0;
    border-left: 16px solid transparent;
    border-right: 16px solid transparent;
    border-bottom: 28px solid #4caf50;
}

/* 地图上的方形标记 */
.plotting-marker.square-marker {
    width: 28px;
    height: 28px;
    background-color: #2196f3;
    border: 2px solid #0c7cd5;
}


/* 右侧信息展示区 */
.right-sidebar {
    flex: 0 0 350px; /* 进一步增加宽度 */
    background-color: #fff;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column; /* 上下排列统计面板和详情列表 */
    gap: 15px;
    overflow-y: auto;
    font-size: 14px; /* 增大字体大小 */
}

.statistics-panel, .risk-details-list {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: 1px solid #dee2e6;
}

.statistics-panel h3, .risk-details-list h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #0056b3;
    font-size: 16px;
    font-weight: 600;
    position: relative;
    padding-left: 10px;
    display: inline-block;
}

.statistics-panel h3::before, .risk-details-list h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 14px;
    background: linear-gradient(to bottom, #4285F4, #0056b3);
    border-radius: 2px;
}

.statistics-panel {
    font-size: 12px;
}

.stat-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 8px;
}

.stat-item {
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    padding: 10px 8px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item:hover {
    background-color: #f8f9fa;
}

.stat-label {
    color: #333;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.stat-value {
    color: #0056b3;
    font-size: 26px;
    font-weight: bold;
    line-height: 1.2;
}

/* 统一的统计项样式 */
.stat-item:nth-child(1) .stat-value,
.stat-item:nth-child(2) .stat-value,
.stat-item:nth-child(3) .stat-value,
.stat-item:nth-child(4) .stat-value {
    color: #0056b3;
}

.risk-details-list {
    font-size: 14px;
}

.risk-details-list table,
.risk-details-list th,
.risk-details-list td {
    font-size: 14px;
}

/* 详情列表标签卡样式 */
.details-tabs {
    display: flex;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 15px;
}

.details-tab-button {
    background-color: transparent;
    border: none;
    padding: 7px 12px;
    cursor: pointer;
    font-weight: 400;
    font-size: 14px;
    color: #6c757d;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.details-tab-button:hover {
    color: #0056b3;
}

.details-tab-button.active {
    color: #0056b3;
    border-bottom: 2px solid #0056b3;
}

.details-tab-content {
    display: none;
}

.details-tab-content.active {
    display: block !important;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #dee2e6;
    margin-top: 8px;
    table-layout: auto; /* 自动调整表格布局 */
}

/* 设置各列宽度 */
.details-table th:nth-child(1),
.details-table td:nth-child(1) {
    width: 8%; /* 序号列 */
}

.details-table th:nth-child(2),
.details-table td:nth-child(2) {
    width: 15%; /* 地市列 */
}

.details-table th:nth-child(3),
.details-table td:nth-child(3) {
    width: 42%; /* 风险点名称列 */
}

.details-table th:nth-child(4),
.details-table td:nth-child(4) {
    width: 15%; /* 风险等级列 */
}

.details-table th:nth-child(5),
.details-table td:nth-child(5) {
    width: 20%; /* 上报时间列 */
}

.details-table thead {
    background-color: #0056b3;
}

.details-table th {
    color: white;
    font-weight: 500;
    padding: 6px 8px;
    text-align: left;
    font-size: 14px;
    border: 1px solid #dee2e6;
}

.details-table td {
    padding: 6px 8px;
    border: 1px solid #dee2e6;
    font-size: 14px;
    color: #333;
    vertical-align: middle;
    word-break: normal; /* 允许在必要时换行 */
}

.details-table tbody tr {
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.details-table tbody tr:hover {
    background-color: #f8f9fa;
}

.details-table tbody tr:last-child td {
    border-bottom: none;
}

/* 风险等级样式 */
.risk-level, .event-level {
    display: inline-block;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    min-width: 20px;
}

.risk-level.high, .event-level.high {
    background-color: #ffebee;
    color: #d32f2f;
}

.risk-level.medium, .event-level.medium {
    background-color: #fff8e1;
    color: #ff8f00;
}

.risk-level.low, .event-level.low {
    background-color: #e8f5e9;
    color: #388e3c;
}

.event-level.normal {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* 预警级别样式 */
.warning-level {
    display: inline-block;
    padding: 2px 4px;
    border-radius: 2px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    min-width: 20px;
}

.warning-level.red {
    background-color: #ffebee;
    color: #d32f2f;
}

.warning-level.orange {
    background-color: #fff3e0;
    color: #e65100;
}

.warning-level.yellow {
    background-color: #fffde7;
    color: #fbc02d;
}

.warning-level.blue {
    background-color: #e3f2fd;
    color: #1976d2;
}

/* 物资状态样式 */
.supplies-status {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.8em;
    font-weight: 500;
    text-align: center;
}

.supplies-status.available {
    background-color: #e8f5e9;
    color: #388e3c;
}

.supplies-status.allocated {
    background-color: #fff8e1;
    color: #ff8f00;
}

/* 救援力量表格特殊样式 */
.rescue-forces-table {
    font-size: 11px; /* 稍微减小字体大小 */
}

.rescue-forces-table th,
.rescue-forces-table td {
    padding: 4px 6px; /* 减小内边距 */
}

.rescue-forces-table th:nth-child(1),
.rescue-forces-table td:nth-child(1) {
    width: 5%; /* 序号列 */
}

.rescue-forces-table th:nth-child(2),
.rescue-forces-table td:nth-child(2) {
    width: 10%; /* 地市列 */
}

.rescue-forces-table th:nth-child(3),
.rescue-forces-table td:nth-child(3) {
    width: 25%; /* 救援队伍名称列 */
}

.rescue-forces-table th:nth-child(4),
.rescue-forces-table td:nth-child(4) {
    width: 15%; /* 救援类型列 */
}

.rescue-forces-table th:nth-child(5),
.rescue-forces-table td:nth-child(5) {
    width: 8%; /* 人数列 */
}

.rescue-forces-table th:nth-child(6),
.rescue-forces-table td:nth-child(6) {
    width: 12%; /* 联系人列 */
}

.rescue-forces-table th:nth-child(7),
.rescue-forces-table td:nth-child(7) {
    width: 15%; /* 联系方式列 */
}

/* 其他资源筛选样式 */
.filter-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    color: #0056b3;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 5px;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 5px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 5px;
    font-size: 14px;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 4px;
}

.range-inputs {
    display: flex;
    align-items: center;
    gap: 5px;
}

.range-input {
    width: 60px;
    padding: 4px 6px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
}

.unit-select {
    width: 50px;
    padding: 4px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
}

/* 医疗点专科能力样式 */
.specialty-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 15px;
}

.specialty-item {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
}

.specialty-name {
    font-weight: bold;
    color: #0056b3;
    margin-bottom: 5px;
    font-size: 14px;
}

.specialty-level {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
}

.specialty-desc {
    color: #212529;
    font-size: 14px;
    line-height: 1.4;
}

/* 医疗资源表格样式 */
.medical-resources-table,
.vehicle-equipment-table,
.vehicle-dispatch-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    font-size: 14px;
}

.medical-resources-table th,
.vehicle-equipment-table th,
.vehicle-dispatch-table th {
    background-color: #0056b3;
    color: white;
    font-weight: 500;
    padding: 8px;
    text-align: left;
    border: 1px solid #dee2e6;
}

.medical-resources-table td,
.vehicle-equipment-table td,
.vehicle-dispatch-table td {
    padding: 8px;
    border: 1px solid #dee2e6;
    vertical-align: middle;
}

.medical-resources-table tr:nth-child(even),
.vehicle-equipment-table tr:nth-child(even),
.vehicle-dispatch-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* 医疗点和救援车辆模态框样式 - 已改为使用救援力量模态框的样式 */

/* 应急事件模态框样式 */
.event-modal-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 应急事件模态框字体大小统一设置 */
#emergency-event-modal .modal-header h3 {
    font-size: 24px !important;
}

#emergency-event-modal .modal-body {
    font-size: 16px;
}

#emergency-event-modal .modal-body h4 {
    font-size: 20px;
}

#emergency-event-modal .modal-body h5 {
    font-size: 18px;
}

#emergency-event-modal .modal-body .info-grid div,
#emergency-event-modal .modal-body .operation-name,
#emergency-event-modal .modal-body .plan-name,
#emergency-event-modal .modal-body .supplies-name,
#emergency-event-modal .modal-body .medical-name {
    font-size: 16px !important;
}

#emergency-event-modal .modal-body .operation-detail,
#emergency-event-modal .modal-body .plan-scope,
#emergency-event-modal .modal-body .supplies-detail,
#emergency-event-modal .modal-body .medical-detail {
    font-size: 15px !important;
}

#emergency-event-modal .modal-body .operation-contact,
#emergency-event-modal .modal-body .supplies-contact,
#emergency-event-modal .modal-body .medical-contact {
    font-size: 14px !important;
}

#emergency-event-modal .modal-body button {
    font-size: 16px !important;
}

/* 救援车辆模块字体大小 */
#emergency-event-modal .rescue-vehicles-grid > div > div:first-child {
    font-size: 16px !important;
}

#emergency-event-modal .rescue-vehicles-grid > div > div:last-child {
    font-size: 14px !important;
}

/* 专家、救援队伍、消防单位等模块字体大小 */
#emergency-event-modal .expert-name,
#emergency-event-modal .rescue-name,
#emergency-event-modal .fire-name {
    font-size: 16px !important;
}

#emergency-event-modal .expert-title,
#emergency-event-modal .rescue-detail,
#emergency-event-modal .fire-detail {
    font-size: 15px !important;
}

#emergency-event-modal .expert-distance,
#emergency-event-modal .expert-contact,
#emergency-event-modal .rescue-contact,
#emergency-event-modal .fire-contact {
    font-size: 14px !important;
}

/* 监控点字体大小 */
#emergency-event-modal .camera-label {
    font-size: 16px !important;
}

#emergency-event-modal .camera-status {
    font-size: 14px !important;
}

/* 应急物资点表格样式 */
#emergency-event-modal .supplies-table {
    font-size: 14px !important;
}

#emergency-event-modal .supplies-table th {
    font-size: 14px !important;
    font-weight: bold;
}

#emergency-event-modal .supplies-table td {
    font-size: 14px !important;
}

#emergency-event-modal .supplies-header {
    margin-bottom: 10px !important;
}

#emergency-event-modal .supplies-name {
    font-size: 16px !important;
}

#emergency-event-modal .supplies-distance {
    font-size: 15px !important;
}

#emergency-event-modal .supplies-contact {
    font-size: 14px !important;
}

/* 救援车辆模块样式 */
#emergency-event-modal .vehicle-item {
    margin-bottom: 10px !important;
}

#emergency-event-modal .vehicle-name {
    font-size: 16px !important;
    font-weight: bold !important;
}

#emergency-event-modal .vehicle-details {
    font-size: 14px !important;
}

/* 组织机构字体大小 */
#emergency-event-modal .org-tree {
    font-size: 16px !important;
}

#emergency-event-modal .org-tree .org-section {
    font-size: 18px !important;
}

#emergency-event-modal .org-tree .org-subsection > div:first-child {
    font-size: 16px !important;
}

/* 救援力量模态框字体大小统一设置 */
#rescue-forces-modal .modal-header h3 {
    font-size: 24px !important;
}

#rescue-forces-modal .modal-body {
    font-size: 18px;
}

#rescue-forces-modal .resource-subsection-title {
    font-size: 20px !important;
}

#rescue-forces-modal .basic-info-grid .info-label {
    font-size: 18px !important;
}

#rescue-forces-modal .basic-info-grid .info-value {
    font-size: 18px !important;
}

#rescue-forces-modal .equipment-content,
#rescue-forces-modal .supplies-content {
    font-size: 18px !important;
}

#emergency-event-modal .org-tree div[style*="color: #ecf0f1"] {
    font-size: 15px !important;
}

#emergency-event-modal .org-tree div[style*="color: #95a5a6"] {
    font-size: 14px !important;
}

.event-section {
    margin-bottom: 20px;
    padding: 20px;
    border: 1px solid #555c66;
    border-radius: 8px;
    background-color: #31353d;
    box-shadow: 0 3px 6px rgba(0,0,0,0.25);
}

.event-section h5 {
    color: #a0d8ef;
    margin-top: 0;
    margin-bottom: 12px;
    border-bottom: 1px solid #5f6773;
    padding-bottom: 8px;
    font-size: 1.1em;
}

/* 预案标题容器样式 */
.plan-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    border-bottom: 1px solid #5f6773;
    padding-bottom: 8px;
}

.plan-title-container h5 {
    margin: 0;
    padding: 0;
    border-bottom: none;
}

.view-plan-button {
    background-color: #0056b3;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.85em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.view-plan-button:hover {
    background-color: #004494;
}

.event-section h6 {
    color: #bac8d3;
    margin-top: 10px;
    margin-bottom: 8px;
    font-size: 0.95em;
}

/* 两列布局 */
.event-columns-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    width: 100%;
}

.event-column {
    display: flex;
    flex-direction: column;
}

.event-basic-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.event-basic-info-grid .info-label,
.event-section .info-label {
    color: #bac8d3;
    display: inline-block;
    min-width: 90px;
    font-weight: bold;
    margin-bottom: 5px;
}

.event-basic-info-grid .info-value,
.event-section .info-value {
    color: #e0e0e0;
    display: block;
    margin-left: 10px;
}

.plan-details-grid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 10px 15px;
    margin-bottom: 15px;
    align-items: start;
}

.plan-details-grid strong {
    color: #bac8d3;
    white-space: nowrap;
}

.plan-details-grid span {
    color: #e0e0e0;
}

/* 响应等级行样式 */
.level-row {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    grid-column: 1 / -1;
}

.level-item {
    flex: 0 0 auto;
}

.condition-item {
    flex: 1;
}

.condition-reference {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    padding: 10px;
    margin-top: 5px;
    max-height: 150px;
    overflow-y: auto;
}

.condition-text p {
    margin-top: 0;
    margin-bottom: 8px;
    font-weight: bold;
}

.condition-text ul {
    margin: 0;
    padding-left: 20px;
}

.condition-text li {
    margin-bottom: 5px;
}

/* 事件等级选择样式 */
.level-select-container {
    grid-column: 1 / -1;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 4px;
}

.level-select-label {
    color: #bac8d3;
    font-weight: bold;
    min-width: 100px;
}

.level-select {
    flex: 1;
    background-color: #2c3034;
    color: #e0e0e0;
    border: 1px solid #495057;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.95em;
}

.level-confirm-button {
    background-color: #0056b3;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.95em;
    transition: background-color 0.2s;
}

.level-confirm-button:hover {
    background-color: #004494;
}

.response-steps-list {
    margin: 0;
    padding-left: 20px;
    color: #e0e0e0;
}

.response-steps-list li {
    margin-bottom: 8px;
}

/* 指挥部列表样式 */
.command-list li {
    margin-bottom: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 4px;
    color: #e0e0e0;
}

.command-list li strong {
    color: #a0d8ef;
}

/* 资源信息样式 */
.resource-info {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 5px;
    color: #e0e0e0;
    font-size: 0.95em;
    line-height: 1.5;
    margin-bottom: 10px;
}

.experts-info {
    margin-top: 10px;
}

.expert-item {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 5px;
    color: #e0e0e0;
    font-size: 0.95em;
    line-height: 1.5;
}

/* 资源子版块样式 */
.resource-subsection-title {
    background-color: #3a4a5d;
    color: #fff;
    padding: 5px 10px;
    margin-top: 15px;
    margin-bottom: 10px;
    border-radius: 3px;
    font-weight: bold;
}

.resource-item {
    background-color: rgba(255, 255, 255, 0.05);
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
    border-left: 3px solid #4a89dc;
    color: #e0e0e0;
    font-size: 0.95em;
    line-height: 1.5;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .event-columns-grid {
        grid-template-columns: 1fr;
    }
}

.details-button {
    padding: 6px 12px;
    font-size: 12px;
    background-color: #0056b3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 500;
    display: inline-block;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
}

.details-button:hover {
    background-color: #004494;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

/* 模态框样式 (沿用或略作调整) */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    padding: 20px; /* Padding for the backdrop, allowing modal to not touch edges */
    box-sizing: border-box;
    overflow: auto; /* 允许内容溢出时滚动 */
}

.modal-content {
    background-color: #343a40;
    color: #f8f9fa;
    width: auto; /* Let width be determined by content up to max-width */
    min-width: 400px; /* A minimum width to avoid being too squished */
    max-width: 900px; /* 增加最大宽度以适应两列布局 */
    margin: 5% auto; /* 上下间距5%，左右自动居中 */
    padding: 20px; /* 增加内边距 */
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    position: relative;
    overflow-y: auto; /* Scroll for content overflow */
    max-height: 90vh; /* Set a max-height relative to viewport height */
    font-size: 16px;
    display: flex; /* Use flex here to manage children better */
    flex-direction: column; /* Stack title and body vertically */
}

/* 表单样式 */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-group {
    flex: 1;
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.btn-cancel,
.btn-confirm {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
}

.btn-cancel {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
}

.btn-confirm {
    background-color: #3b82f6;
    color: white;
    border: none;
}

/* 演练资料和复盘报告按钮样式 */
.drill-action-button {
    background-color: #4caf50;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
    margin-right: 5px;
    min-width: 40px;
    text-align: center;
}

.drill-action-button.submit-button {
    background-color: #4caf50;
}

.drill-action-button.view-button {
    background-color: #3b82f6;
}

.drill-action-button.more-button {
    background-color: #f0f0f0;
    color: #333;
    border: 1px solid #ddd;
}

.drill-action-button i {
    margin-right: 3px;
}

.no-report {
    color: #999;
    display: inline-block;
    margin: 0 5px;
}

/* 演练资料提交模态框样式 */
.form-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.form-full-width {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-group input[type="text"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.file-upload-container {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.file-upload-container input[type="file"] {
    display: none;
}

.file-upload-label {
    background-color: #3b82f6;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 10px;
}

.file-name {
    color: #666;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.photo-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.photo-preview-placeholder {
    width: 120px;
    height: 90px;
    border: 1px dashed #ccc;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
}

.photo-preview-placeholder i {
    font-size: 24px;
    margin-bottom: 5px;
}

/* 演练资料查看模态框样式 */
.detail-section {
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.detail-section:last-child {
    border-bottom: none;
}

.section-title {
    font-size: 18px;
    color: #0056b3;
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 15px 30px;
}

.detail-full-width {
    width: 100%;
}

.detail-item {
    margin-bottom: 12px;
}

.detail-label {
    font-weight: 500;
    color: #555;
    display: block;
    margin-bottom: 5px;
    font-size: 15px;
}

.detail-value {
    color: #333;
    font-size: 15px;
}

.detail-value p {
    margin: 8px 0;
    font-size: 15px;
    line-height: 1.5;
}

.file-link {
    color: #0056b3;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    font-size: 15px;
}

.file-link i {
    margin-right: 5px;
    font-size: 16px;
}

.file-link:hover {
    text-decoration: underline;
}

.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.photo-item {
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.drill-photo {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
}

.photo-caption {
    padding: 8px;
    text-align: center;
    background-color: #f8f9fa;
    font-size: 15px;
    color: #333;
}

#modal-title {
    color: #f8f9fa;
    padding-bottom: 10px;
    border-bottom: 1px solid #495057;
    margin-bottom: 10px; /* Reduced margin */
    font-size: 1.5em;
    flex-shrink: 0; /* Prevent title from shrinking */
}

#modal-body {
    overflow-y: auto; /* If body itself needs to scroll independently */
    flex-grow: 1; /* Allow body to take available space */
    padding-right: 5px; /* Add a little padding for scrollbar if it appears */
}

.modal-details-grid .info-item {
    padding: 4px 0; /* Fine-tune padding */
    margin-bottom: 4px; /* Fine-tune margin */
}

.modal-details-grid .top-info-container .info-item {
    background-color: #495057;
    padding: 6px 10px; /* Fine-tune padding */
    margin-bottom: 6px; /* Added margin for spacing */
}

.modal-details-grid .info-label {
    font-size: 1.2em;
    color: #adb5bd;
    font-weight: bold;
    margin-bottom: 2px; /* Reduced space */
    display: inline; /* Default to inline, can be overridden */
    margin-right: 5px;
}

.modal-details-grid .info-value {
    font-size: 1.2em;
    color: #f0f0f0;
    display: inline; /* Default to inline */
    word-break: break-word; /* Break long words/strings */
}

/* Adjustments for specific sections if they need block display for label/value */
.risk-point-panel .description-item .info-label,
.risk-point-panel .description-item .info-value,
.rectification-panel .measures-item .info-label,
.rectification-panel .measures-item .info-value {
    display: block; /* For long descriptions/measures, make them block */
    padding-left: 0; /* Reset padding for block items */
}
.risk-point-panel .description-item .info-label,
.rectification-panel .measures-item .info-label {
    margin-bottom: 3px;
}

.modal-details-grid a {
    color: #79c0ff;
    text-decoration: none;
}

.top-info-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); /* Adjust minmax for tighter fit */
    gap: 10px;
    margin-bottom: 10px;
}

.middle-info-container,
.bottom-info-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 10px;
}

.info-column {
    background-color: #3E444A;
    padding: 10px;
    border-radius: 4px;
}

.info-column h5 {
    font-size: 1.3em; /* Reduced size */
    margin-top: 0;
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid #495057;
}

.info-column ul {
    padding-left: 0;
    list-style-type: none;
    margin-top: 5px;
}

.info-column ul li {
    background-color: #495057;
    padding: 4px 6px;
    border-radius: 3px;
    margin-bottom: 3px;
}

.rescue-team-item {
    border: 1px solid #495057;
    border-radius: 4px;
    padding: 8px;
    margin-bottom: 8px;
}
.rescue-team-item .info-item {
    border-bottom: none;
    padding: 2px 0;
    margin-bottom: 0;
}

.close-button {
    color: #adb5bd;
    position: absolute;
    top: 10px; /* Adjusted for new padding */
    right: 10px; /* Adjusted for new padding */
    font-size: 28px; /* Slightly smaller */
    line-height: 1;
    padding: 5px;
    background: transparent;
    border: none;
    cursor: pointer;
}

/* Styles for Supply Point Modal */
.modal-supply-details .supply-subtitle {
    font-size: 1.3em; /* Relative to modal base font-size */
    color: #e0e0e0;
    margin-top: 5px; /* Space after main title */
    margin-bottom: 15px;
    text-align: left;
    font-weight: normal;
}

.supply-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.supply-info-grid .info-item {
    background-color: #495057; /* Same as risk top items */
    padding: 8px 12px;
    border-radius: 4px;
}
/* .info-label and .info-value styles from .modal-details-grid will apply here too */

.supply-item-list-title {
    font-size: 1.4em; /* Relative to modal base */
    color: #f8f9fa;
    margin-top: 20px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #495057;
}

.supply-table-container {
    overflow-x: auto; /* Allow horizontal scroll for table if needed on small screens */
}

.supply-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
    font-size: 1.1em; /* Slightly smaller font for table data if needed */
}

.supply-table th,
.supply-table td {
    border: 1px solid #5a6268; /* Darker border for table cells */
    padding: 8px 10px;
    text-align: left;
    vertical-align: middle;
}

.supply-table th {
    background-color: #495057; /* Header background */
    color: #f0f0f0;
    font-weight: bold;
}

.supply-table td {
    background-color: #3E444A; /* Cell background */
    color: #e0e0e0;
}

.supply-table tr:nth-child(even) td {
    background-color: #41484e; /* Slightly different for even rows if desired */
}

.supply-table .status-good {
    color: #28a745; /* Green for good status */
    font-weight: bold;
}

.supply-table .status-needs-attention {
    color: #ffc107; /* Yellow for needs attention */
    font-weight: bold;
}

/* Styles for Rescue Team Modal */
.modal-rescue-details {
    padding-top: 10px; /* Add some space from modal title */
}

.rescue-team-name-block {
    background-color: #3E444A; /* Match panel background */
    padding: 12px 15px !important; /* Override generic .info-item padding if needed */
    margin-bottom: 10px;
    border-bottom: 1px solid #495057; /* Separator line as in image */
}

.team-name-prominent {
    font-size: 1.25em; /* Larger font for team name */
    font-weight: bold;
    color: #f8f9fa; /* Bright text for team name */
    display: block; /* Ensure it takes full width */
}

.rescue-details-container {
    background-color: #3E444A; /* Match panel background */
    padding: 15px;
    border-radius: 4px; /* Consistent with other panels */
}

.rescue-detail-item {
    padding: 8px 0 !important; /* Vertical padding for each detail line */
    border-bottom: 1px solid #495057; /* Separator line for each item */
    margin-bottom: 0 !important; /* Remove bottom margin from generic info-item */
}

.rescue-detail-item:last-child {
    border-bottom: none; /* No border for the last item */
}

.rescue-detail-item .info-label {
    /* Styles for label (e.g., color, font-weight) will be inherited or can be specified */
     min-width: 80px; /* Ensure labels have some minimum width for alignment */
     display: inline-block; /* Allow inline display but with width */
}

.rescue-detail-item .info-value {
    /* Styles for value */
}

.equipment-detail .info-value {
    white-space: pre-wrap; /* Allow line breaks in equipment list */
}

/* General info item styling for all modal types for label/value distinction */
.info-item .info-label {
    font-weight: bold;
    color: #adb5bd; /* Lighter gray for labels */
    margin-right: 8px; /* Space between label and value */
    display: inline; /* Default to inline */
}

.info-item .info-value {
    color: #f0f0f0; /* Slightly brighter white for values */
    display: inline; /* Default to inline */
    word-break: break-word;
}

/* Override for block display where needed (e.g., risk descriptions, measures) */
.description-item .info-label,
.measures-item .info-label {
    display: block;
    margin-bottom: 3px;
}
.description-item .info-value,
.measures-item .info-value {
    display: block;
    padding-left: 0; /* No indent if label is block */
}


/* Styles for RISK POINT MODAL - modal-details-grid */
.modal-details-grid .info-label {
    /* Already covered by general .info-item .info-label if specificity is managed */
    /* Or can add specific overrides if needed */
}
.modal-details-grid .info-value {
    /* Already covered by general .info-item .info-value */
}


/* Styles for SUPPLY POINT MODAL - modal-supply-details */
.supply-info-grid .info-item .info-label {
    /* General .info-item .info-label applies */
    /* Example of an override IF NEEDED: min-width: 90px; display: inline-block; */
}
.supply-info-grid .info-item .info-value {
    /* General .info-item .info-value applies */
}

/* Styles for RESCUE TEAM MODAL - modal-rescue-details */
.rescue-detail-item .info-label {
    /* General .info-item .info-label applies */
    min-width: 80px; /* Ensure labels have some minimum width for alignment */
    display: inline-block; /* Allow inline display but with width */
     /* margin-right: 5px; from previous specific rule, check if general is better */
}

.rescue-detail-item .info-value {
    /* General .info-item .info-value applies */
}

/* Ensure the specific structure of team name block doesn't get unwanted label styling */
.rescue-team-name-block .info-label { /* This span might not exist or should be empty */
    display: none;
}

.team-name-prominent { /* This is essentially the value for the team name block */
    font-size: 1.4em;
    font-weight: bold;
    color: #f8f9fa;
    display: block;
}

/* Styles for Project Under Construction Modal */
.modal-project-details {
    /* General padding/spacing for the project modal content area */
}

.project-panel h5 {
    font-size: 1.8em; /* Consistent with other panel titles */
    color: #f0f0f0;
    margin-top: 0;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid #495057;
}

.project-basic-info-panel {
    background-color: #3E444A; /* Panel background */
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
}

.project-basic-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr)); /* Responsive 2-column layout */
    gap: 8px 15px; /* Row and column gap */
}

.project-basic-info-grid .info-item {
    padding: 4px 0; /* Minimal padding for grid items */
}

.project-basic-info-grid .full-width-item {
    grid-column: 1 / -1; /* Make item span all columns */
}

/* Styles for the linked items section */
.project-linked-items-panel {
    display: grid;
    grid-template-columns: 1fr 1fr; /* Two main columns for risks and support */
    gap: 15px;
}

.linked-section {
    background-color: #3E444A; /* Panel background for each section */
    padding: 15px;
    border-radius: 4px;
}

.linked-section h5 {
    font-size: 1.6em;
    color: #f0f0f0;
    margin-top: 0;
    margin-bottom: 10px;
    padding-bottom: 6px;
    border-bottom: 1px solid #495057;
}

.linked-support-wrapper {
    display: flex;
    flex-direction: column;
    gap: 15px; /* Space between supplies and rescue sections */
}

.linked-item-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #495057;
}

.linked-item-entry:last-child {
    border-bottom: none;
}

.linked-item-name {
    color: #e0e0e0;
    flex-grow: 1;
    margin-right: 10px;
    font-size: 1.2em;
}

.view-details-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.4em; /* Match other button fonts if any */
    flex-shrink: 0;
    transition: background-color 0.2s ease-in-out;
}

.view-details-button:hover {
    background-color: #0056b3;
}

.no-linked-data {
    color: #adb5bd;
    font-style: italic;
    padding: 10px 0;
    font-size: 1.2em;
}

/* Ensure general label/value distinction applies */
.project-basic-info-grid .info-label {
    /* font-weight: bold; color: #adb5bd; etc. from general .info-item .info-label */
    font-size: 1.2em;
}
.project-basic-info-grid .info-value {
    /* color: #f0f0f0; from general .info-item .info-value */
    font-size: 1.2em;
}

/* Marker style for projects (add if you haven't already) */
.project-marker {
    background-color: rgba(108, 117, 125, 0.8); /* Example: Gray marker */
}

/* Style for the Back Button */
.back-to-previous-button {
    display: block; /* Or inline-block if preferred next to something */
    margin-bottom: 15px; /* Space below the back button */
    padding: 8px 15px;
    background-color: #6c757d; /* A neutral gray, or another color */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1.4em;
    text-align: left;
    transition: background-color 0.2s ease-in-out;
}

.back-to-previous-button:hover {
    background-color: #5a6268;
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px 24px;
    justify-content: center;
    max-width: 320px;
    margin: 0 auto;
}

/* 详情列表标签卡样式 */
.details-tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 2px solid #0056b3;
    width: 100%;
}

.details-tab-button {
    padding: 7px 12px;
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    border-bottom: none;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 5px;
    position: relative;
    bottom: -1px;
    transition: background-color 0.2s, color 0.2s;
    flex-grow: 1;
    text-align: center;
}

.details-tab-button.active {
    background-color: #0056b3;
    color: white;
    border-color: #0056b3;
    border-bottom: 1px solid #0056b3;
    z-index: 1;
}

.details-tab-button:not(.active):hover {
    background-color: #d1d9e0;
}

.details-tab-content {
    display: none;
    padding: 10px 0;
}

.details-tab-content.active {
    display: block;
}
.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 8px;
    padding: 12px 0 10px 0;
    box-shadow: 0 1px 2px rgba(0,0,0,0.03);
    min-width: 90px;
    min-height: 60px;
}
.stat-label {
    color: #333;
    font-size: 12px;
    margin-bottom: 4px;
    text-align: center;
    font-weight: 400;
    line-height: 1.2;
    word-break: break-all;
}
.stat-value {
    color: #1756b3;
    font-size: 22px;
    font-weight: bold;
    margin-top: 2px;
    text-align: center;
    line-height: 1.1;
}

.statistics-panel h3 {
    margin-top: 0;
    margin-bottom: 12px;
    color: #0056b3;
    text-align: left;
}

/* 气象预警模态框样式 */
.modal-warning-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.warning-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 10px;
    border-bottom: 2px solid #495057;
    margin-bottom: 10px;
}

.warning-header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.warning-header-right {
    display: flex;
    align-items: center;
}

.notify-button {
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 16px; /* 增大按钮字体 */
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.notify-button:hover {
    background-color: #218838;
}

.warning-name {
    font-size: 22px; /* 增大预警名称字体 */
    color: #f8f9fa;
    margin: 0;
}

.warning-level {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    color: white;
    font-size: 16px; /* 增大预警等级字体 */
}

.warning-level.红色 {
    background-color: #dc3545;
}

.warning-level.橙色 {
    background-color: #fd7e14;
}

.warning-level.黄色 {
    background-color: #ffc107;
    color: #333;
}

.warning-level.蓝色 {
    background-color: #0d6efd;
}

.warning-columns-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 10px;
}

.warning-column {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.warning-basic-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    background-color: #3E444A;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #495057;
}

.warning-basic-info .info-item {
    margin-bottom: 8px;
}

.warning-basic-info .info-label {
    color: #adb5bd;
    font-weight: bold;
    display: block;
    margin-bottom: 4px;
}

.warning-basic-info .info-value {
    color: #f8f9fa;
}

.warning-details-section,
.warning-impact-section,
.warning-precautions-section {
    background-color: #3E444A;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #495057;
    height: 100%;
}

.warning-details-section h5,
.warning-impact-section h5,
.warning-precautions-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #a0d8ef;
    font-size: 18px; /* 增大小标题字体 */
    padding-bottom: 5px;
    border-bottom: 1px solid #495057;
}

.warning-details-text {
    line-height: 1.5;
    margin: 0;
    color: #f8f9fa;
}

.impact-list,
.precautions-list {
    margin: 0;
    padding-left: 20px;
    color: #f8f9fa;
    font-size: 16px; /* 增大列表字体 */
}

.impact-list li,
.precautions-list li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.precautions-list {
    counter-reset: item;
}

.precautions-list li {
    counter-increment: item;
}

.impact-list li strong {
    color: #a0d8ef;
}

/* 通知模态框样式 */
.notify-modal-content {
    max-width: 600px;
}

#notify-modal-title {
    margin-bottom: 20px;
}

.notify-warning-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #3E444A;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #495057;
}

.notify-warning-name {
    font-size: 1.2em;
    font-weight: bold;
    color: #f8f9fa;
}

.notify-warning-level {
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    color: white;
}

.notify-units-container {
    background-color: #3E444A;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #495057;
}

.notify-units-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.notify-units-header h4 {
    margin: 0;
    color: #a0d8ef;
    font-size: 1.1em;
}

.notify-select-actions {
    display: flex;
    gap: 10px;
}

.select-action-button {
    background-color: #495057;
    color: #f8f9fa;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 0.9em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.select-action-button:hover {
    background-color: #5a6268;
}

.notify-units-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background-color: #343a40;
    border-radius: 4px;
}

.notify-unit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background-color: #495057;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.notify-unit-item:hover {
    background-color: #5a6268;
}

.notify-unit-item input[type="checkbox"] {
    cursor: pointer;
}

.notify-unit-item label {
    color: #f8f9fa;
    cursor: pointer;
    font-size: 0.95em;
}

.notify-message-container {
    background-color: #3E444A;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    border: 1px solid #495057;
}

.notify-message-container h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #a0d8ef;
    font-size: 1.1em;
}

.notify-message-textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    background-color: #343a40;
    border: 1px solid #495057;
    border-radius: 4px;
    color: #f8f9fa;
    font-size: 0.95em;
    resize: vertical;
}

.notify-message-textarea:focus {
    outline: none;
    border-color: #0d6efd;
}

.notify-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.send-notify-button {
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.send-notify-button:hover {
    background-color: #218838;
}

.cancel-notify-button {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 1em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.cancel-notify-button:hover {
    background-color: #5a6268;
}

/* 拥堵路段曲线样式 */
.congestion-line {
    position: absolute;
    z-index: 5;
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    height: 8px !important; /* 固定高度为细线 */
    border-radius: 50px; /* 大幅增加圆角，使其两端为半圆形 */
    transform: rotate(-30deg); /* 稍微旋转以模拟道路走向 */
}

.congestion-line.severe {
    background-color: rgba(220, 53, 69, 0.7);
    border: 2px solid rgba(220, 53, 69, 0.9);
}

.congestion-line.moderate {
    background-color: rgba(255, 153, 0, 0.7);
    border: 2px solid rgba(255, 153, 0, 0.9);
}

.congestion-line.light {
    background-color: rgba(255, 193, 7, 0.7);
    border: 2px solid rgba(255, 193, 7, 0.9);
}

.congestion-line.severe-soon {
    background-color: rgba(220, 53, 69, 0.5);
    border: 2px dashed rgba(220, 53, 69, 0.9);
}

.congestion-line.moderate-soon {
    background-color: rgba(255, 153, 0, 0.5);
    border: 2px dashed rgba(255, 153, 0, 0.9);
}

/* 拥堵路段模态框样式 - 已移至内联样式 */

#traffic-jam-title {
    color: #f8f9fa;
    padding-bottom: 10px;
    border-bottom: 1px solid #495057;
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.3em;
    font-weight: 500;
}

.traffic-jam-info {
    padding: 5px;
}

.section-title {
    color: #8ab4f8;
    border-bottom: 1px solid #3d4354;
    padding-bottom: 8px;
    margin-top: 20px;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.info-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.info-row {
    display: flex;
    gap: 10px;
}

.info-item {
    flex: 1;
    display: flex;
}

.info-label {
    min-width: 80px;
    color: #adb5bd;
}

.info-value {
    font-weight: 500;
}

.camera-grid {
    display: flex;
    gap: 20px;
    margin-top: 15px;
}

.camera-item {
    flex: 1;
    text-align: center;
}

.camera-placeholder {
    background-color: #2c3142;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    margin-bottom: 8px;
    border: 1px solid #495057;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.camera-placeholder:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.camera-placeholder i {
    font-size: 2.5rem;
    color: #adb5bd;
}

.camera-label {
    font-size: 0.9rem;
    color: #adb5bd;
}

.detour-plan {
    background-color: #2c3142;
    padding: 15px;
    border-radius: 4px;
    margin-top: 15px;
    line-height: 1.6;
}

/* 拥堵路段曲线样式 */
.congestion-line {
    position: absolute;
    z-index: 5;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 6px !important; /* 更细的线条 */
    border-radius: 0; /* 移除圆角 */
    transform: none; /* 移除旋转 */
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
    /* 使用clip-path创建弯曲效果 */
    clip-path: path('M0,3 Q25,10 50,3 Q75,-4 100,3');
}

.congestion-line:hover {
    transform: scale(1.05);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* 拥堵路段标点样式 */
.congestion-marker {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    z-index: 5;
    cursor: pointer;
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.congestion-marker:hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* 严重拥堵路段 */
.congestion-marker.severe {
    background-color: rgba(220, 53, 69, 0.9);
    border: 2px solid rgba(220, 53, 69, 1);
}

/* 中度拥堵路段 */
.congestion-marker.moderate {
    background-color: rgba(255, 153, 0, 0.9);
    border: 2px solid rgba(255, 153, 0, 1);
}

/* 轻度拥堵路段 */
.congestion-marker.light {
    background-color: rgba(255, 193, 7, 0.9);
    border: 2px solid rgba(255, 193, 7, 1);
}

/* 即将严重拥堵路段 */
.congestion-marker.severe-soon {
    background-color: rgba(220, 53, 69, 0.7);
    border: 2px dashed rgba(220, 53, 69, 1);
}

/* 即将中度拥堵路段 */
.congestion-marker.moderate-soon {
    background-color: rgba(255, 153, 0, 0.7);
    border: 2px dashed rgba(255, 153, 0, 1);
}

/* 响应式布局 */
@media (max-width: 768px) {
    .warning-columns-grid {
        grid-template-columns: 1fr;
    }

    .notify-units-list {
        grid-template-columns: 1fr;
    }

    .info-row {
        flex-direction: column;
        gap: 10px;
    }

    .camera-grid {
        flex-direction: column;
    }
}

/* 应急救援圈功能样式 */
.rescue-circle-btn {
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rescue-circle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.rescue-circle-controls {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.rescue-circle-controls label {
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: color 0.2s ease;
}

.rescue-circle-controls label:hover {
    color: #3498db !important;
}

.rescue-circle-controls input[type="checkbox"] {
    margin-right: 6px;
    cursor: pointer;
    transform: scale(1.1);
}

.rescue-circle-apply-btn:hover {
    background-color: #2980b9 !important;
    transform: translateY(-1px);
}

.rescue-circle-exit-btn:hover {
    background-color: #7f8c8d !important;
    transform: translateY(-1px);
}

/* 救援圈SVG样式增强 */
#rescue-circles-svg {
    transition: opacity 0.3s ease;
}

#rescue-circles-svg circle {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    animation: circleAppear 0.5s ease-out;
}

@keyframes circleAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

#rescue-circles-svg text {
    font-family: 'Arial', sans-serif;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    animation: labelFadeIn 0.6s ease-out 0.2s both;
}

@keyframes labelFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 救援圈模式下的标点样式增强 */
.map-marker {
    transition: all 0.3s ease;
}

.map-marker[style*="border"] {
    border-radius: 50% !important;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    animation: markerHighlight 0.5s ease-out;
}

@keyframes markerHighlight {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(0, 0, 0, 0);
    }
    50% {
        transform: scale(1.2);
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }
}

/* 选中事件标点的特殊样式 */
.map-marker[style*="scale(1.5)"] {
    border-radius: 50% !important;
    box-shadow: 0 0 20px rgba(231, 76, 60, 0.6) !important;
    animation: selectedEventPulse 2s ease-in-out infinite;
}

@keyframes selectedEventPulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(231, 76, 60, 0.6);
    }
    50% {
        box-shadow: 0 0 30px rgba(231, 76, 60, 0.8);
    }
}

/* 救援圈控制面板的响应式设计 */
@media (max-width: 768px) {
    .rescue-circle-controls {
        padding: 10px;
        font-size: 11px;
    }

    .rescue-circle-resource-types {
        grid-template-columns: 1fr !important;
    }

    .rescue-circle-actions {
        flex-direction: column;
        gap: 8px !important;
    }

    .rescue-circle-apply-btn,
    .rescue-circle-exit-btn {
        width: 100%;
    }
}

/* 气象预警模态框额外字体设置 */
.modal-warning-details {
    font-size: 16px; /* 设置基础字体大小 */
}

.modal-warning-details .info-field {
    font-size: 16px; /* 增大信息字段字体 */
}

.modal-warning-details .info-label {
    font-size: 16px; /* 增大标签字体 */
}

.modal-warning-details .info-value {
    font-size: 16px; /* 增大值字体 */
}

.modal-warning-details .tracking-table {
    font-size: 16px; /* 增大表格字体 */
}

.modal-warning-details .tracking-table th {
    font-size: 16px; /* 增大表头字体 */
}

.modal-warning-details .tracking-table td {
    font-size: 15px; /* 增大表格数据字体 */
}

.urge-button {
    background-color: #ff9800;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px; /* 增大按钮字体 */
}

.urge-button:hover {
    background-color: #e68900;
}

/* 气象预警模态框标题字体增大 */
.modal-warning-details h5 {
    font-size: 20px !important; /* 强制增大标题字体 */
}

/* 气象预警模态框基本信息字体增大 */
.modal-warning-details .warning-basic-info .info-label,
.modal-warning-details .warning-basic-info .info-value {
    font-size: 16px !important; /* 强制增大基本信息字体 */
}