// 全局变量，用于跟踪当前显示的标点类型
let currentMarkerType = 'all'; // 'events', 'supplies', 'teams', 'others'
let map; // 高德地图实例
let markers = []; // 存储所有标记点
let levelDict = {
  "1": "I级(特别重大)",
  "2": "II级(重大)",
  "3": "III级(较大)",
  "4": "IV级(一般)"
}
let teamTypeDict = {
  "professional-rescue": "专业救援队",
  "fire-rescue": "消防救援队",
  "traffic-rescue": "交通救援队",
  "medical-rescue": "医疗救援队"
}
let supplyTypeDict = {
  "rescue-equipment": "救援装备",
  "medical-supplies": "医疗用品",
  "living-supplies": "生活物资",
  "communication-equipment": "通信设备"
}

// 初始化高德地图
function initAMap() {
  // 加载高德地图JS API
  const key = 'c149d16ec64fa406fbaafe432f12c7c9';
  const script = document.createElement('script');
  script.src = `https://webapi.amap.com/maps?v=1.4.15&key=${key}&plugin=AMap.MarkerClusterer`;
  script.onload = function () {
    // 地图初始化
    map = new AMap.Map('map-container', {
      viewMode: '2D', // 默认使用2D地图
      zoom: 8, // 初始化缩放级别
      center: [109.4, 24.3], // 柳州中心坐标
      mapStyle: 'amap://styles/blue' // 设置地图样式
    });
    // 自定义聚合点样式

    // 创建点聚合实例
    window.markerClusterer = new AMap.MarkerClusterer(map, [], {
      gridSize: 40, // 聚合网格像素大小
      maxZoom: 8, // 最大聚合级别，超过该级别就不聚合
      averageCenter: true, // 聚合点是否居中
      styles: [{
        // 默认聚合样式
        url: 'https://a.amap.com/jsapi_demos/static/images/blue.png',
        size: new AMap.Size(32, 32),
        offset: new AMap.Pixel(-16, -16)
      }]
    });

    // 加载地图控件
    AMap.plugin([
                  'AMap.ToolBar',
                  'AMap.Scale',
                  'AMap.HawkEye',
                  'AMap.MapType',
                  'AMap.Geolocation'
                ], function () {
      // 添加地图控件
      map.addControl(new AMap.ToolBar());
      map.addControl(new AMap.Scale());
      map.addControl(new AMap.HawkEye());
      map.addControl(new AMap.MapType());

      // 添加定位控件
      const geolocation = new AMap.Geolocation({
                                                 showButton: true,
                                                 buttonPosition: 'RB'
                                               });
      map.addControl(geolocation);
    });

    // 初始加载标记点
    loadMarkers();
  };
  document.head.appendChild(script);
}

// 模拟从后端获取标记点数据
async function fetchMarkers(type = 'all', filters = {}) {
  console.log(`获取标记点数据，类型: ${type}, 筛选条件:`, filters);

  // 初始化mockData结构
  const mockData = {
    events: [],
    supplies: [],
    teams: [],
    others: []
  };

  try {
    // 创建所有API请求的Promise数组
    const apiRequests = [];
    let option = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    }
    // 如果请求所有类型或特定类型，则添加对应的API请求
    if (type === 'all' || type === 'events') {
      apiRequests.push(
        fetch(API_BASE_URL + '/emergency/event/list', option)
        .then(response => response.json())
        .then(data => {
          mockData.events = data?.rows?.map(item => ({
            ...item,
            resourceType: "events",
            id: item.eventId,
            name: item.eventTitle || '未知事件',
            type: item.eventType || "1",
            level: item.eventLevel || "4",
            status: item.status || "1",
            location: item.administrativeArea || '未知位置',
            position: [item.longitude, item.latitude] || [109.42, 24.33],
            time: item.occurTime ? new Date(item.occurTime * 1000).toLocaleString() : new Date().toLocaleString(),
            description: item.eventDescription || '无描述信息'
          })) ?? [];
        })
        .catch(error => {
          console.error('获取应急事件数据失败:', error);
          return []; // 返回空数组避免影响其他请求
        })
      );
    }

    // 预留supplies接口位置
    if (type === 'all' || type === 'supplies') {
      apiRequests.push(
        fetch(`${API_BASE_URL}/warehouse/list`, option)
        .then(response => response.json())
        .then(data => {
          // 转换数据格式以匹配前端需求
          mockData.supplies = data?.rows?.map(item => ({
            resourceType: "supplies",
            id: item.id,
            name: item.warehouseName || '未知仓库',
            type: item.warehouseType || "1", // 仓库类型
            location: item.address || '未知位置',
            position: [parseFloat(item.longitude), parseFloat(item.latitude)] || [109.4, 24.3],
            manager: item.principal || '未知负责人',
            contact: item.contactPhone || '未知联系方式',
            roadCode: item.roadCode || '未知路段',
            stake: item.stake || '未知桩号',
            totalCount: item.totalMaterialCount || 0,
            status: 1, // 默认状态正常
            statusName: '正常',
            materials: [
              ...(item.materials?.map(material => ({
                id: material.id,
                name: material.materialName,
                type: "应急物资", // materialType="0"
                specModel: material.specModel,
                quantity: material.quantity,
                unit: material.unit,
                expiryDate: material.expiryDate,
                status: material.status,
                statusName: material.statusName
              })) || [])
            ],
            equipments: [
              ...(item.equipments?.map(equipment => ({
                id: equipment.id,
                name: equipment.materialName,
                type: "应急装备", // materialType="1"
                specModel: equipment.specModel,
                quantity: equipment.quantity,
                unit: equipment.unit,
                expiryDate: equipment.expiryDate,
                status: equipment.status,
                statusName: equipment.statusName
              })) || [])
            ],
            updateTime: item.updateTime || '未知更新时间'
          })) || [];
        })
        .catch(error => {
          console.error('获取应急物资仓库数据失败:', error);
          return []; // 返回空数组避免影响其他请求
        })
      );
    }
    // 预留teams接口位置
    if (type === 'all' || type === 'teams') {
      apiRequests.push(
        fetch(`${API_BASE_URL}/rescue/team/list?pageNum=1&pageSize=100`, option)
        .then(response => response.json())
        .then(data => {
          function getTeamTypeCode(typeName) {
            return teamTypeDict[typeName] || "专业救援队";
          }

          mockData.teams = data?.rows?.map(item => ({
            resourceType: "teams",
            id: item.id,
            name: item.teamName || '未知队伍',
            type: getTeamTypeCode(item.teamTypeName), // 转换类型为前端使用的代码
            location: item.address || '未知位置',
            position: [item.longitude, item.latitude] || [109.4, 24.3],
            manager: item.leaderName || '未知负责人',
            contact: item.leaderPhone || '未知联系方式',
            members: item.teamSize || 0,
            specialties: item.specialties || '无专业特长',
            status: item.status || 1,
            statusName: item.statusName || '正常',
            materials: item.materials || [], // 物资列表
            equipments: item.equipments || [], // 装备列表
            updateTime: item.updateTime || '未知更新时间'
          })) ?? [];
        })
        .catch(error => {
          console.error('获取救援队伍数据失败:', error);
          return []; // 返回空数组避免影响其他请求
        })
      );
      // 添加辅助函数用于转换团队类型

    }

    // 预留others接口位置
    if (type === 'all' || type === 'others') {
      apiRequests.push(
        new Promise(resolve => {
          // 这里预留others接口位置
          // 未来可以替换为真实API调用:
          // fetch('/dev-api/emergency/others/list').then(...)
          console.log('others接口待实现');
          resolve();
        })
      );
    }

    // 等待所有API请求完成
    await Promise.all(apiRequests);

    if (mockData.others.length === 0 && (type === 'all' || type === 'others')) {
      mockData.others = [
        {
          resourceType: "others",
          id: 'vehicle1',
          name: '应急救援车队A',
          type: 'rescueVehicle',
          location: '柳州市城中区',
          position: [109.43, 24.34],
          manager: '张队长',
          contact: '13800138011',
          capacity: 10
        },
        {
          resourceType: "others",
          id: 'vehicle1',
          name: '医疗点B',
          type: 'medicalPoint',
          location: '柳州市城中区',
          position: [109.43, 24.17],
          manager: '张队长',
          contact: '13800138011',
          capacity: 10
        },
        {
          resourceType: "others",
          id: 'vehicle1',
          name: '消防点C',
          type: 'firePoint',
          location: '柳州市城中区',
          position: [109.43, 24.00],
          manager: '张队长',
          contact: '13800138011',
          capacity: 10
        },
        // 其他资源数据...
      ]
    }

    // 根据类型和筛选条件过滤数据
    let data = type === 'all' ? [...mockData.events, ...mockData.supplies, ...mockData.teams, ...mockData.others]
                              : mockData[type];

    switch (type) {
      case 'all':
        break;
      case 'events':
        data = data.filter(item =>
                             (filters.eventType === 'all' || item.type === filters.eventType) &&
                             (filters.eventLevel === 'all' || item.level === filters.eventLevel) &&
                             (filters.eventStatus === 'all' || item.status === filters.eventStatus)
        );
        break;
      case 'supplies':
        data = data.filter(item =>
                             (filters.supplyType === 'all' || item.type === filters.supplyType) &&
                             (filters.supplyStatus === 'all' || item.status === filters.supplyStatus) &&
                             (filters.supplyLevel === 'all' || item.level === filters.supplyLevel)
        );
        break;
      case 'teams':
        data = data.filter(item =>
                             (filters.teamType === 'all' || item.type === filters.teamType) &&
                             (filters.teamStatus === 'all' || item.status === filters.teamStatus) &&
                             (filters.teamLevel === 'all' || item.level === filters.teamLevel)
        );
        break;
      case 'others':
        data = data.filter(item =>
                             ((item.type === 'rescueVehicle' && filters.otherTypeRescueVehicle) ||
                              (item.type === 'medicalPoint' && filters.otherTypeMedicalPoint) ||
                              (item.type === 'firePoint' && filters.otherTypeFirePoint)) &&
                             (filters.vehicleType === 'all' || item.vehicleType === filters.vehicleType) &&
                             (filters.facilityStatus === 'all' || item.status === filters.facilityStatus)
        );
        break;
    }

    return data;
  } catch (error) {
    console.error('获取标记点数据失败:', error);
    // 如果API请求失败，返回空数组
    return [];
  }
}

// 加载并显示标记点
async function loadMarkers() {
  // 获取当前筛选条件
  const filters = getCurrentFilters();

  // 获取标记点数据
  const markerData = await fetchMarkers(currentMarkerType, filters);

  // 清除现有标记
  clearMarkers();

  // 创建标记点
  const newMarkers = markerData.map(item => createMarker(item));
  markers = newMarkers;

  // 使用点聚合器添加标记点
  markerClusterer.clearMarkers();
  markerClusterer.addMarkers(markers);

  // 设置合适的视野范围
  map.setFitView(markerClusterer);
}

// 创建单个标记点
function createMarker(data) {
  // 根据不同类型设置不同的图标和样式
  let icon, size;

  switch (data.resourceType) {
    case 'events':
      icon = 'image/emergency-map/events_' + (data.level || "4") + '.png';
      size = new AMap.Size(32, 32);
      break;
    case 'supplies':
      icon = 'image/emergency-map/supplies.png';
      size = new AMap.Size(32, 32);
      break;
    case 'teams':
      icon = 'image/emergency-map/teams.png';
      size = new AMap.Size(32, 32);
      break;
    case 'others':
      icon = 'image/emergency-map/others_' + (data.type || "default") + '.png';
      size = new AMap.Size(32, 32);
      break;
    default:
      icon = 'image/emergency-map/default.png';
      size = new AMap.Size(32, 32);
  }

  // 创建标记点
  const marker = new AMap.Marker({
                                   position: data.position,
                                   icon: new AMap.Icon({
                                                         image: icon,
                                                         size: size
                                                       }),
                                   offset: new AMap.Pixel(-16, -32),
                                   extData: data // 将原始数据存储在标记点中
                                 });

  function createInfoWindowContent(data) {
    let content = '';
    const levelColor = {
      '1': '#ff4d4f',
      '2': '#fa8c16',
      '3': '#faad14',
      '4': '#52c41a'
    };

    const baseStyle = `
    <style>
      .info-window1 {
        min-width: 200px;
        padding: 10px;
        font-family: 'Microsoft YaHei', sans-serif;
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.15);
      }
      .info-title1 {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
        display: flex;
        align-items: center;
      }
      .info-level1 {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        margin-left: 8px;
        color: white;
      }
      .info-item1 {
        font-size: 13px;
        margin-bottom: 6px;
        color: #666;
        display: flex;
      }
      .info-label1 {
        width: 60px;
        color: #999;
      }
      .info-value1 {
        flex: 1;
        color:#000 ;
      }
      .info-divider1 {
        height: 1px;
        background: #f0f0f0;
        margin: 8px 0;
      }
    </style>
  `;
    switch (data.resourceType) {
      case 'events':
        content = `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">
            ${data.name}
            <span class="info-level1" style="background: ${levelColor[data.level] || '#999'}">
              ${levelDict[data.level] || '未知等级'}
            </span>
          </div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">时间</span>
            <span class="info-value1">${data.time || '未知时间'}</span>
          </div>
          <div class="info-divider1"></div>
          <div class="info-item1">
            <span class="info-value1">${data.description || '无描述信息'}</span>
          </div>
        </div>
      `;
        break;

      case 'supplies':
        content = `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">${data.name || '未知仓库'}</div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">负责人</span>
            <span class="info-value1">${data.manager || '未知'}</span>
          </div>
          <div class="info-divider1"></div>
          <div class="info-item1">
            <span class="info-label1">物资数量</span>
            <span class="info-value1">${data.totalCount || 0}种</span>
          </div>
        </div>
      `;
        break;

      case 'teams':
        content = `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">${data.name || '未知队伍'}</div>
          <div class="info-item1">
            <span class="info-label1">类型</span>
            <span class="info-value1">${teamTypeDict[data.type] || '未知类型'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">人数</span>
            <span class="info-value1">${data.members || 0}人</span>
          </div>
          <div class="info-divider1"></div>
          <div class="info-item1">
            <span class="info-label1">特长</span>
            <span class="info-value1">${data.specialties || '无'}</span>
          </div>
        </div>
      `;
        break;

      default:
        content = `
        ${baseStyle}
        <div class="info-window1">
          <div class="info-title1">${data.name || '未知资源'}</div>
          <div class="info-item1">
            <span class="info-label1">位置</span>
            <span class="info-value1">${data.location || '未知位置'}</span>
          </div>
          <div class="info-item1">
            <span class="info-label1">状态</span>
            <span class="info-value1">${data.statusName || '正常'}</span>
          </div>
        </div>
      `;
    }

    return content;
  }

  // 创建信息窗口
  const infoWindow = new AMap.InfoWindow({
                                           offset: new AMap.Pixel(0, -30),
                                           content: createInfoWindowContent(data),
                                           closeWhenClickMap: true,
                                           autoMove: true
                                         });

  // 添加鼠标经过事件
  marker.on('mouseover', function (e) {
    infoWindow.open(map, marker.getPosition());
  });

  // 添加鼠标移出事件
  marker.on('mouseout', function (e) {
    infoWindow.close();
  });

  // 添加点击事件
  marker.on('click', function (e) {
    showMarkerInfo(e.target.getExtData());
  });

  return marker;
}

// 清除所有标记点
function clearMarkers() {
  if (map && markers.length > 0) {
    markerClusterer.clearMarkers();
    markers = [];
  }
}

// 显示标记点信息
function showMarkerInfo(data) {
  console.log('显示标记点信息:', data);
  // 根据不同类型显示不同的模态框
  if (data.resourceType === 'events') {
    // 应急事件详情模态框
    openEmergencyEventModal(data);
  } else if (data.resourceType === 'supplies') {
    // 应急物资详情模态框
    openSupplyModal(data);
  } else if (data.resourceType === 'teams') {
    // 救援队伍详情模态框
    openTeamModal(data);
  } else if (data.resourceType === 'others' && data.type === 'firePoint') {
    // 消防点详情模态框
    openFireStationModal(data);
  } else if (data.resourceType === 'others' && data.type === 'medicalPoint') {
    // 医疗点详情模态框
    openMedicalStationModal(data);
  } else if (data.resourceType === 'others' && data.type === 'rescueVehicle') {
    // 救援车辆详情模态框
    openRescueVehicleModal(data);
  }
}

// 全选/全不选复选框的处理函数
function toggleAllMarkers(checkbox) {
  const isChecked = checkbox.checked;
  console.log('全选/全不选复选框状态:', isChecked);

  // 获取所有资源类型按钮
  const resourceButtons = document.querySelectorAll('.resource-tab-button');

  if (isChecked) {
    // 全选时移除所有按钮的active类
    resourceButtons.forEach(button => {
      button.classList.remove('active');
    });
    // 加载所有类型的标记点
    currentMarkerType = 'all';
    loadMarkers();
  } else {
    // 全不选时默认选中第一个按钮
    if (resourceButtons.length > 0) {
      resourceButtons[0].classList.add('active');
      currentMarkerType = resourceButtons[0].getAttribute('data-type');
      loadMarkers();
    }
  }
}

// 选择特定资源类型
function selectResourceType(button) {
  // 取消全选复选框
  document.getElementById('res-type-all').checked = false;

  // 移除所有按钮的active类
  const resourceButtons = document.querySelectorAll('.resource-tab-button');
  resourceButtons.forEach(btn => btn.classList.remove('active'));

  // 为当前按钮添加active类
  button.classList.add('active');

  // 更新当前资源类型并加载标记点
  currentMarkerType = button.getAttribute('data-type');
  loadMarkers();

  // 显示对应的筛选内容
  showTabContent(`emergency-${currentMarkerType}-content`);
}

// 修改initResourceTypeSelector函数
function initResourceTypeSelector() {
  document.getElementById('res-type-all').checked = true;

  // 初始化全选复选框事件
  document.getElementById('res-type-all').addEventListener('change', function () {
    toggleAllMarkers(this);
  });

  // 初始化资源类型按钮事件
  const typeButtons = document.querySelectorAll('.resource-tab-button');
  typeButtons.forEach(button => {
    button.addEventListener('click', function () {
      selectResourceType(this);
    });
  });
}

// 更新标签卡按钮状态
function updateTabButtons(activeType) {
  const buttons = document.querySelectorAll('.resource-tab-button');
  const typeMap = {
    'events': 0,
    'supplies': 1,
    'teams': 2,
    'others': 3
  };

  buttons.forEach((btn, index) => {
    if (index === typeMap[activeType]) {
      btn.classList.add('active');
    } else {
      btn.classList.remove('active');
    }
  });
}

// 初始化筛选条件
function initFilters() {
  // 为所有筛选条件添加事件监听
  const filterInputs = document.querySelectorAll('.filter-select');

  filterInputs.forEach(input => {
    input.addEventListener('change', function () {
      // 当筛选条件变化时重新加载标记点
      loadMarkers();
    });
  });
  const filterCheckboxs = document.querySelectorAll('.filter-checkbox');

  filterCheckboxs.forEach(input => {
    input.addEventListener('change', function () {
      // 当筛选条件变化时重新加载标记点
      loadMarkers();
    });
  });
}

// 获取当前筛选条件
function getCurrentFilters() {
  const filters = {};

  // 根据当前资源类型获取不同的筛选条件
  switch (currentMarkerType) {
    case "all":
      break;
    case 'events':
      filters.eventType = document.getElementById('event-type-select').value;
      filters.eventLevel = document.getElementById('event-level-select').value;
      filters.eventStatus = document.getElementById('event-status-select').value;
      break;
    case 'supplies':
      filters.supplyType = document.getElementById('supply-type-select').value;
      filters.supplyStatus = document.getElementById('supply-status-select').value;
      filters.supplyLevel = document.getElementById('supply-level-select').value;
      break;
    case 'teams':
      filters.teamType = document.getElementById('team-type-select').value;
      filters.teamStatus = document.getElementById('team-status-select').value;
      filters.teamLevel = document.getElementById('team-level-select').value;
      break;
    case 'others':
      filters.otherTypeRescueVehicle = document.getElementById('other-type-rescue-vehicle').checked;
      filters.otherTypeMedicalPoint = document.getElementById('other-type-medical-point').checked;
      filters.otherTypeFirePoint = document.getElementById('other-type-fire-point').checked;

      filters.vehicleType = document.getElementById('vehicle-type-select').value;
      filters.facilityStatus = document.getElementById('facility-status-select').value;

      break;
  }

  return filters;
}

// 显示对应的筛选内容
function showTabContent(activeContentId) {
  const contents = document.querySelectorAll('.resource-tab-content');

  contents.forEach(content => {
    if (content.id === activeContentId) {
      content.style.display = 'block';
      content.classList.add('active');
    } else {
      content.style.display = 'none';
      content.classList.remove('active');
    }
  });
}

// 模拟获取告警信息
// 模拟获取告警信息
async function fetchAlerts(tabType = 'emergency-events', pageNum = 1, pageSize = 10) {
  console.log(`获取告警信息，类型: ${tabType}, 页码: ${pageNum}, 每页大小: ${pageSize}`);

  try {
    let apiUrl;
    const option = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    };

    // 根据不同类型调用不同的API
    if (tabType === 'emergency-events') {
      // 应急事件告警
      apiUrl = `${API_BASE_URL}/alarm/info/list?pageNum=${pageNum}&pageSize=${pageSize}&alarmType=2&alarm_subtype=2`;
    } else if (tabType === 'verification-overdue') {
      // 应急信息更新超时告警
      apiUrl =
        `${API_BASE_URL}/alarm/info/list?pageNum=${pageNum}&pageSize=${pageSize}&alarmType=2&alarm_subtype=3,4,5,7`;
    }

    const response = await fetch(apiUrl, option);

    if (!response.ok) {
      throw new Error('获取告警信息失败');
    }

    const data = await response.json();

    // 转换数据格式以匹配前端显示需求
    return data.rows.map(item => {
      // 根据不同类型设置不同的描述
      let description = item.alarmContent || '无描述信息';

      if (tabType === 'emergency-events') {
        // 应急事件告警
        description = (item.administrativeArea || '未知位置') + description;
      } else if (tabType === 'verification-overdue') {
        // 对于超时告警，使用特定的描述格式
        description = `${item.alarmTitle || ''}。${item.alarmContent || ''}`;
      }

      return {
        id: item.id,
        level: item.alarmLevel || "4",
        time: item.alarmTime ?
              (tabType === 'verification-overdue' ?
               `${new Date(item.alarmTime).toLocaleDateString()} (超时${calculateOverdueDays(item.alarmTime)}天)` :
               new Date(item.alarmTime).toLocaleString()) :
              '未知时间',
        description: description
      };
    });

  } catch (error) {
    console.error('获取告警信息失败:', error);
    // 如果API请求失败，使用默认数据
    return getDefaultAlerts(tabType);
  }
}

// 计算超时天数
function calculateOverdueDays(alarmTime) {
  if (!alarmTime) {
    return 0;
  }

  const alarmDate = new Date(alarmTime);
  const currentDate = new Date();
  const diffTime = currentDate - alarmDate;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
}

// 获取默认告警数据
function getDefaultAlerts(tabType) {
  const mockData = {
    'emergency-events': [
      {
        id: '1',
        level: "1",
        location: '南宁市G72高速',
        time: '2024-05-20 09:15',
        description: 'K1499+500处发生交通事故，需紧急处置'
      },
      {
        id: '2',
        level: "2",
        location: '柳州市S201省道',
        time: '2024-05-19 14:30',
        description: 'K45+200处山体滑坡，道路中断'
      },
      {
        id: '3',
        level: "1",
        location: ' 桂林市灵川县X001',
        time: '2024-05-19 08:45',
        description: '县道K8+300处桥梁被洪水冲毁'
      },
      {
        id: '4',
        level: "4",
        location: '梧州市G80高速',
        time: '2024-05-18 16:20',
        description: 'K890+100处车辆故障，占用应急车道'
      },
      {
        id: '5',
        level: "3",
        location: '北海市合浦县',
        time: '2024-05-18 16:20',
        description: 'Y002乡道K5+800处路面积水严重'
      },
      // 待处理告警数据...
    ],
    'verification-overdue': [
      {
        id: '1',
        level: "1",
        location: '贺州市八步区G72高速公路',
        time: '2024-05-10 (超时10天)',
        description: '交通事故应急预案'
      },
      {
        id: '2',
        level: "4",
        location: ' 河池市金城江区',
        time: '2024-05-08 (超时12天)',
        description: '应急救援物资库存校验'
      },
      {
        id: '3',
        level: "4",
        location: '来宾市兴宾区',
        time: '2024-05-05 (超时15天)',
        description: '应急救援队伍联系方式更新'
      },
      {
        id: '4',
        level: "2",
        location: '崇左市江州区X012县道',
        time: '2024-05-03 (超时17天)',
        description: '水毁应急预案'
      },
      {
        id: '5',
        level: "4",
        location: '南宁市邕宁区G80高速',
        time: '2024-04-30 (超时20天)',
        description: '应急救援装备检查'
      },
      // 超时告警数据...
    ]
  };

  return mockData[tabType] || [];
}

// 初始化告警标签页
function initAlertTabs() {
  const tabButtons = document.querySelectorAll('.alert-tab-button');

  tabButtons.forEach(button => {
    button.addEventListener('click', function () {
      const tabType = this.getAttribute('data-tab');

      // 更新标签页状态
      updateAlertTabButtons(this);

      // 重置分页状态
      currentAlertPage = 1;
      hasMoreAlerts = true;

      // 加载对应类型的告警数据
      loadAlerts(tabType);
    });
  });

  // 默认加载全部告警
  loadAlerts('emergency-events');

  // 添加滚动事件监听
  const alertListContainer = document.querySelector('#alert-table');
  if (alertListContainer) {
    alertListContainer.addEventListener('scroll', handleAlertListScroll);
  }
}

// 处理告警列表滚动事件
function handleAlertListScroll() {
  const container = this;
  const scrollTop = container.scrollTop;
  const scrollHeight = container.scrollHeight;
  const clientHeight = container.clientHeight;

  // 检查是否滚动到底部
  if (scrollTop + clientHeight >= scrollHeight - 50 && !isLoadingAlerts && hasMoreAlerts) {
    loadAlerts(currentAlertTab, currentAlertPage + 1, true);
  }
}

// 更新告警标签页按钮状态
function updateAlertTabButtons(activeButton) {
  const buttons = document.querySelectorAll('.alert-tab-button');

  buttons.forEach(btn => {
    btn.classList.remove('active');
    btn.style.borderBottom = '2px solid transparent';
    btn.style.color = '#666';
    btn.style.fontWeight = 'normal';
  });

  activeButton.classList.add('active');
  activeButton.style.borderBottom = '2px solid #ff6b35';
  activeButton.style.color = '#333';
  activeButton.style.fontWeight = 'bold';
}

// 全局变量，用于跟踪当前告警状态
let currentAlertTab = 'emergency-events';
let currentAlertPage = 1;
const alertPageSize = 10;
let isLoadingAlerts = false;
let hasMoreAlerts = true;

// 加载告警数据
async function loadAlerts(tabType, page = 1, isAppend = false) {
  if (isLoadingAlerts) {
    return;
  }

  isLoadingAlerts = true;
  currentAlertTab = tabType;
  currentAlertPage = page;

  // 显示加载中状态
  const alertList = document.querySelector('#alert-table ul');
  if (!isAppend) {
    alertList.innerHTML = '<li style="padding: 15px; text-align: center; color: #666;">加载中...</li>';
  } else {
    const loadingItem = document.createElement('li');
    loadingItem.style.cssText = 'padding: 15px; text-align: center; color: #666;';
    loadingItem.textContent = '加载更多...';
    alertList.appendChild(loadingItem);
  }

  try {
    const alerts = await fetchAlerts(tabType, page, alertPageSize);

    // 检查是否还有更多数据
    hasMoreAlerts = alerts.length >= alertPageSize;

    // 如果是追加数据，移除加载中的提示
    if (isAppend) {
      const loadingItems = alertList.querySelectorAll('li');
      if (loadingItems.length > 0) {
        alertList.removeChild(loadingItems[loadingItems.length - 1]);
      }
    } else {
      alertList.innerHTML = '';
    }

    // 如果没有数据且不是追加模式
    if (alerts.length === 0 && !isAppend) {
      const noDataItem = document.createElement('li');
      noDataItem.style.cssText = 'padding: 15px; text-align: center; color: #666;';
      noDataItem.textContent = '暂无告警信息';
      alertList.appendChild(noDataItem);
      return;
    }

    // 填充表格数据
    alerts.forEach(alert => {
      const row = document.createElement('li');
      row.classList.add('alert-item');

      let color = '';
      // 根据告警级别设置行样式
      if (alert.level == "1") {
        row.classList.add('high');
        color = '#dc3545';
      } else if (alert.level == '2' || alert.level == '3') {
        row.classList.add('medium');
        color = '#ffc107';
      } else if (alert.level == '4') {
        row.classList.add('low');
        color = '#28a745';
      }

      row.innerHTML = `
        <div class="alert-time" style="font-size: 12px; color: #666; margin-bottom: 5px;">${alert.time}</div>
        <div class="alert-content">
            <span class="alert-level" style="display: inline-block; padding: 2px 8px; background: ${color}; color: white; border-radius: 3px; margin-right: 8px;">${levelDict[alert.level]}</span>
            <span class="alert-text" style="font-size: 14px; color: #333;">${alert.description}</span>
        </div>
      `;

      alertList.appendChild(row);
    });
  } catch (error) {
    console.error('加载告警信息失败:', error);
    if (!isAppend) {
      alertList.innerHTML = '<li style="padding: 15px; text-align: center; color: #dc3545;">加载告警信息失败</li>';
    }
  } finally {
    isLoadingAlerts = false;
  }
}

// 模拟获取统计数据
async function fetchStatistics() {
  console.log('获取统计数据');

  // 使用您现有的假数据
  const mockData = {
    eventCount: 15,
    teamCount: 8,
    supplyCount: 12,
    expertCount: 5,
    eventTypes: {
      traffic: 6,
      safety: 4,
      natural: 3,
      public: 2
    },
    eventStatus: {
      pending: 3,
      processing: 5,
      resolved: 7
    }
  };

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return mockData;
}

// 加载并显示统计数据
async function loadStatistics() {
  const stats = await fetchStatistics();

  // 更新统计卡片
  document.getElementById('event-count').textContent = stats.eventCount;
  document.getElementById('team-count').textContent = stats.teamCount;
  document.getElementById('supply-count').textContent = stats.supplyCount;
  document.getElementById('overdue-count').textContent = stats.expertCount;

}

// 获取详情列表数据
async function fetchDetails(tabType = 'events') {
  console.log(`获取详情列表数据，类型: ${tabType}`);

  try {
    const option = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
      }
    };

    // 根据不同类型调用不同的API
    switch (tabType) {
      case 'events':
        // 调用应急事件列表API
        const eventsResponse = await fetch(`${API_BASE_URL}/emergency/event/list`, option);
        const eventsData = await eventsResponse.json();
        return eventsData?.rows?.map((item, index) => ({
          id: item.eventId,
          location: item.administrativeArea || '未知位置',
          name: item.eventTitle || '未知事件',
          level: item.eventLevel || "4",
          time: item.occurTime ? new Date(item.occurTime * 1000).toLocaleString() : '未知时间'
        })) || [];

      case 'teams':
        // 调用救援队伍列表API
        const teamsResponse = await fetch(`${API_BASE_URL}/rescue/team/list?pageNum=1&pageSize=1000`, option);
        const teamsData = await teamsResponse.json();
        return teamsData?.rows?.map((item, index) => ({
          id: item.id,
          location: item.address || '未知位置',
          name: item.teamName || '未知队伍',
          teamType: teamTypeDict[item.teamType] || "专业救援队",
          members: item.teamSize || 0
        })) || [];

      case 'supplies':
        // 调用物资仓库列表API
        const suppliesResponse = await fetch(`${API_BASE_URL}/warehouse/list`, option);
        const suppliesData = await suppliesResponse.json();

        // 转换物资数据格式
        return suppliesData?.rows?.flatMap(warehouse => {
          // 合并物资和装备
          const allItems = [
            ...(warehouse.materials?.map(material => ({
              id: material.id,
              location: warehouse.address || '未知位置',
              name: material.materialName || '未知物资',
              supplyType: 'medical-supplies', // 根据实际情况可能需要调整
              supplyCount: `${material.quantity || 0}${material.unit || '个'}`
            })) || []),
            ...(warehouse.equipments?.map(equipment => ({
              id: equipment.id,
              location: warehouse.address || '未知位置',
              name: equipment.materialName || '未知装备',
              supplyType: 'rescue-equipment', // 根据实际情况可能需要调整
              supplyCount: `${equipment.quantity || 0}${equipment.unit || '个'}`
            })) || [])
          ];
          return allItems;
        }) || [];

      case 'experts':
        // 专家暂无接口，保持原样
        return [
          {id: '1', location: '南宁市', name: '李明', expertiseAreas: '桥梁工程', workplace: '广西交通设计院'},
          // 其他专家数据...
        ];

      default:
        return [];
    }
  } catch (error) {
    console.error(`获取${tabType}详情数据失败:`, error);

    // 如果API请求失败，返回默认数据
    const mockData = {
      events: [
        {id: '1', location: '南宁市', name: 'G72高速多车相撞', level: "2", time: '2024-05-20 09:15'},
        // 其他事件数据...
      ],
      teams: [
        {id: '1', location: '南宁市', name: '南宁交通应急救援队', teamType: 'professional-rescue', members: '45人'},
        // 其他队伍数据...
      ],
      supplies: [
        {id: '1', location: '南宁市', name: '救生衣', supplyType: 'rescue-equipment', supplyCount: '500件'},
        // 其他物资数据...
      ],
      experts: [
        {id: '1', location: '南宁市', name: '李明', expertiseAreas: '桥梁工程', workplace: '广西交通设计院'},
        // 其他专家数据...
      ]
    };

    return mockData[tabType] || [];
  }
}

// 初始化详情标签页
function initDetailTabs() {
  const tabButtons = document.querySelectorAll('.details-tab-button');

  tabButtons.forEach(button => {
    button.addEventListener('click', function () {
      const tabType = this.getAttribute('data-tab');

      // 更新标签页状态
      updateDetailTabButtons(this);

      // 加载对应类型的详情数据
      loadDetails(tabType);
    });
  });

  // 默认加载应急事件详情
  loadDetails('events');
}

// 更新详情标签页按钮状态
function updateDetailTabButtons(activeButton) {
  const buttons = document.querySelectorAll('.details-tab-button');
  const activeIndex = Array.from(buttons).indexOf(activeButton);

  buttons.forEach((btn, index) => {
    if (index === activeIndex) {
      btn.classList.add('active');
    } else {
      btn.classList.remove('active');
    }
  });
}

// 加载详情数据
async function loadDetails(tabType) {
  const details = await fetchDetails(tabType);
  const tableHead = document.querySelector('#details-table thead');
  const tableBody = document.querySelector('#details-table tbody');

  // 清空表格
  tableHead.innerHTML = '';
  tableBody.innerHTML = '';
  let trRow = document.createElement('tr');

  // 根据不同类型生成不同的表格行
  switch (tabType) {
    case 'events':
      trRow.innerHTML = `
          <th>序号</th>
          <th>地市</th>
          <th>事件名称</th>
          <th>事件等级</th>
          <th>发生时间</th>
        `;
      tableHead.appendChild(trRow);

      details.forEach((detail, index) => {
        let level = 'low';
        if (detail.level == "1") {
          level = 'high';
        } else if (detail.level == '2' || detail.level == '3') {
          level = 'medium';
        } else if (detail.level == '4') {
          level = 'low';
        }
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${index + 1}</td>
          <td>${detail.location}</td>
          <td>${detail.name}</td>
          <td class="risk-level ${level}" style="width:48px">${levelDict[detail.level]}</td>
          <td>${detail.time}</td>
        `;
        tableBody.appendChild(row);
      });
      break;
    case 'teams':
      trRow.innerHTML = `
          <th>序号</th>
          <th>地市</th>
          <th>队伍名称</th>
          <th>队伍类型</th>
          <th>人数</th>
        `;
      tableHead.appendChild(trRow);

      details.forEach((detail, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${index + 1}</td>
          <td>${detail.location}</td>
          <td>${detail.name}</td>
          <td style="width:48px">${teamTypeDict[detail.teamType]}</td>
          <td>${detail.members}</td>
        `;
        tableBody.appendChild(row);
      });
      break;
    case 'supplies':
      trRow.innerHTML = `
          <th>序号</th>
          <th>地市</th>
          <th>物资名称</th>
          <th>物资类型</th>
          <th>数量</th>
        `;
      tableHead.appendChild(trRow);
      details.forEach((detail, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${index + 1}</td>
          <td>${detail.location}</td>
          <td>${detail.name}</td>
          <td style="width:48px">${supplyTypeDict[detail.supplyType]}</td>
          <td>${detail.supplyCount}</td>
        `;
        tableBody.appendChild(row);
      });
      break;
    case 'experts':
      trRow.innerHTML = `
          <th>序号</th>
          <th>地市</th>
          <th>姓名</th>
          <th>专业领域</th>
          <th>单位</th>
        `;
      tableHead.appendChild(trRow);
      details.forEach((detail, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
          <td>${index + 1}</td>
          <td>${detail.location}</td>
          <td>${detail.name}</td>
          <td style="width:48px">${detail.expertiseAreas}</td>
          <td>${detail.workplace}</td>
        `;
        tableBody.appendChild(row);
      });
      break;
  }
}

// 页面加载完成后初始化所有功能
document.addEventListener('DOMContentLoaded', function () {
  console.log('应急一张图页面已加载');

  // 初始化高德地图
  initAMap();

  // 初始化资源类型选择器
  initResourceTypeSelector();

  // 初始化筛选条件
  initFilters();

  // 初始化告警标签页
  initAlertTabs();

  // 加载统计数据
  loadStatistics();

  // 初始化详情标签页
  initDetailTabs();
});

// 筛选标签切换函数
function switchFilterTab(button, tabType) {
  // 移除所有按钮的active类
  const parentContainer = button.closest('.filter-tabs');
  if (parentContainer) {
    const siblingButtons = parentContainer.querySelectorAll('.filter-tab-button');
    siblingButtons.forEach(btn => btn.classList.remove('active'));
  }

  // 添加当前按钮的active类
  button.classList.add('active');

  // 获取目标内容ID
  const targetTabContentId = `filter-by-${tabType}-content`;

  // 获取相关的内容元素
  const parentSection = button.closest('.resource-condition-filter');
  if (parentSection) {
    const contentElements = parentSection.querySelectorAll('.filter-tab-content');

    contentElements.forEach(content => {
      if (content.id === targetTabContentId) {
        content.classList.add('active');
      } else {
        content.classList.remove('active');
      }
    });
  }
}
