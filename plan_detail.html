<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预案详情 - 应急管理系统</title>
    <!-- 直接引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <style>
        .sidebar-menu-item {
            display: flex;
            align-items: center;
            padding: 0.5rem 1rem;
            color: #4b5563;
            transition: all 0.2s;
        }

        .sidebar-menu-item:hover, .sidebar-menu-item.active {
            background-color: #f3f4f6;
            color: #1f2937;
        }

        .sidebar-menu-item.active {
            border-left: 3px solid #2563eb;
        }

        .main-content {
            transition: margin-left 0.3s;
        }

        .sidebar {
            width: 250px;
            transition: all 0.3s;
            z-index: 40;
        }

        .sidebar.collapsed {
            width: 0px;
            transform: translateX(-100%);
        }

        body.sidebar-expanded .main-content {
            margin-left: 250px;
        }

        body.sidebar-collapsed .main-content {
            margin-left: 0;
        }

        @media (max-width: 768px) {
            body .main-content {
                margin-left: 0;
            }

            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.expanded {
                transform: translateX(0);
            }
        }

        /* 精简版/完整版切换样式 */
        /* .full-mode-only {
            display: none;
        } */

        .mode-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 9999px;
            margin-left: 0.5rem;
        }

        .simple-mode-badge {
            background-color: #E5EDFF;
            color: #3B82F6;
        }

        .full-mode-badge {
            background-color: #FEF3C7;
            color: #D97706;
        }

        /* 工具提示样式 */
        .tooltip {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .tooltip:hover .tooltip-content {
            display: block;
            position: absolute;
            z-index: 100;
            top: 100%;
            right: 0;
            width: 200px;
            margin-top: 5px;
        }

        /* 新增：精简模式下的子标签页样式 */
        body.simple-mode #details .border-b.border-gray-200.mb-6 { /* 隐藏子标签导航 */
            display: none;
        }
        body.simple-mode #details .sub-tab-content-container > .sub-tab-content.full-mode-only { /* 确保隐藏完整版内容 */
            display: none !important;
        }

        /* Added rule to hide all full-mode-only elements in simple mode */
        body.simple-mode .full-mode-only {
            display: none !important;
        }
    </style>
</head>
<body class="bg-gray-100 sidebar-expanded flex flex-col min-h-screen">
    <!-- 顶部导航栏 -->
    <header>
        <h1>广西公路水路安全畅通与应急处置系统</h1>
        <nav class="tab-navigation">
            <a href="index.html" class="tab-button">总览</a>
            <a href="my_check_tasks.html" class="tab-button">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button active">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header>

    <!-- 左侧菜单占位符 -->
    <div id="sidebar-container-placeholder"></div>

    <!-- 主内容区域 -->
    <main class="main-content pt-16 pb-8 px-6 bg-gray-100 min-h-screen">
        <div class="py-6">
            <!-- 页面标题部分 -->
            <div class="flex justify-between items-center mb-6">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800">预案详情</h2>
                    <p class="text-gray-600 mt-1">查看预案详细信息</p>
                </div>
                <div class="space-x-2">
                    <a href="plan_list.html" class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-arrow-left mr-2"></i> 返回列表
                    </a>
                    <a href="edit_plan.html" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-edit mr-2"></i> 编辑预案
                    </a>
                    <button class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                        <i class="fas fa-power-off mr-2"></i> 停用预案
                    </button>
                    <a href="plan_version_history.html" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-history mr-2"></i> 版本历史
                    </a>
                </div>
            </div>

            <!-- 预案名称与基础信息 -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h1 class="text-2xl font-bold text-center text-gray-800 mb-4">广西壮族自治区公路交通突发事件应急预案</h1>
                <div class="flex justify-center items-center mb-6">
                    <div class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium mr-4">
                        <i class="fas fa-check-circle mr-1"></i> 已启用
                    </div>
                    <div class="text-sm text-gray-500">
                        <span class="mr-4"><i class="fas fa-building mr-1"></i> 编制单位: 广西交通运输厅</span>
                        <span class="mr-4"><i class="fas fa-calendar-alt mr-1"></i> 编制时间: 2023-11-15</span>
                        <span><i class="fas fa-tags mr-1"></i> 预案类型: 公路交通类</span>
                    </div>
                </div>
            </div>

            <!-- 标签页导航 -->
            <div class="bg-white rounded-t-lg shadow-sm mb-0">
                <div class="p-4 sm:px-6">
                    <div class="border-b border-gray-200">
                        <nav class="flex -mb-px">
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-target="overview">
                                概览
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-target="details">
                                预案详情
                            </button>
                            <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-target="attachments">
                                附件管理
                            </button>
                            <!-- <button class="tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-target="appendix">
                                附录
                            </button> -->
                        </nav>
                    </div>
                </div>
            </div>

            <!-- 标签页内容 -->
            <div class="bg-white rounded-b-lg shadow-md p-6">
                <!-- 概览 -->
                <div id="overview" class="tab-content">
                    <!-- Restored Basic Info section -->
                    <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">基本信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <p class="text-sm font-medium text-gray-500 mb-1">预案名称</p>
                                <p class="text-base text-gray-800">广西壮族自治区公路交通突发事件应急预案</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500 mb-1">编制单位</p>
                                <p class="text-base text-gray-800">广西交通运输厅</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500 mb-1">适用单位</p>
                                <div class="text-base text-gray-800 flex flex-wrap gap-1">
                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">地市交通运输局</span>
                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">各直属单位</span>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500 mb-1">适用范围</p>
                                <div class="text-base text-gray-800 flex flex-wrap gap-1">
                                     <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">公路交通突发事件</span>
                                    <!-- Add other scopes if needed -->
                                </div>
                            </div>
                        </div>
                    </div>

                     <!-- Restored Event Level Overview section -->
                     <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">事件分级与响应概览</h3>

                        <!-- 一级响应概览卡片 -->
                        <div class="border border-gray-200 rounded-lg mb-6 overflow-hidden">
                            <div class="bg-red-50 px-4 py-3 border-b border-gray-200 rounded-t-lg">
                                <h4 class="text-base font-medium text-red-800">Ⅰ级（特别重大）响应</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 mb-1"><i class="fas fa-bullseye mr-1 text-gray-400"></i>响应启动条件</p>
                                    <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700">
                                        有下列情形之一的，启动I级应急响应：<br>
●造成高速公路、普通国道交通中断，出现大量车辆积压，并影响到周边省域高速公路、普通国道正常运行，且抢修、处置时间预计在48小时以上的。<br>
●造成国道、省道特大桥梁、特长隧道垮塌，或者造成公路桥梁、隧道、边坡等构造物垮塌并导致30人以上死亡或者失踪的s。<br>
●因重要物资缺乏等原因严重影响全国或者大片区经济整体运行和人民正常生活，需要紧急安排跨省域公路应急通行保障的。<br>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 mb-1"><i class="fas fa-tasks mr-1 text-gray-400"></i>应急处置流程</p>
                                    <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700">
                                        <ol class="space-y-2 pl-4 list-decimal">
                                            <li>当自治区人民政府或交通运输部启动I级应急响应时，领导小组按照自治区人民政府或交通运输部指令开展应急处置。</li>
                                        </ol>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 mb-2"><i class="fas fa-sitemap mr-1 text-gray-400"></i>响应单位</p>
                                    <div class="bg-gray-50 p-4 rounded-md text-sm text-gray-700 shadow">
                                        <ul class="space-y-3">
                                            <!-- 示例：领导小组 -->
                                            <li>
                                                <div class="flex items-center">
                                                    <i class="fas fa-users mr-2 text-blue-500"></i>
                                                    <span class="font-semibold text-gray-800">领导小组</span>
                                                </div>
                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                    <li>
                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">单位:</span> 自治区交通运输厅</div>
                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                            <li>
                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">部门:</span> 应急指挥中心</div>
                                                                <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-100">
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 主任</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>张三 (<i class="fas fa-phone-alt mr-1"></i>13800138000)</li>
                                                                        </ul>
                                                                    </li>
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 副主任</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>王五 (<i class="fas fa-phone-alt mr-1"></i>13700137000)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- 示例：应急工作组 - 公路抢险组 -->
                                            <li>
                                                <div class="flex items-center">
                                                    <i class="fas fa-hard-hat mr-2 text-yellow-500"></i>
                                                    <span class="font-semibold text-gray-800">应急工作组 - 公路抢险组</span>
                                                </div>
                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                    <li>
                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">单位:</span> 自治区公路发展中心</div>
                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                            <li>
                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">部门:</span> 养护保通科</div>
                                                                <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-100">
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 科长</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>李四 (<i class="fas fa-phone-alt mr-1"></i>13900139000)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- 示例：应急工作组 - 道路运输保障组 -->
                                            <li>
                                                <div class="flex items-center">
                                                    <i class="fas fa-bus-alt mr-2 text-green-500"></i>
                                                    <span class="font-semibold text-gray-800">应急工作组 - 道路运输保障组</span>
                                                </div>
                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                   <li>
                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">单位:</span> 自治区道路运输发展中心</div>
                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                            <li>
                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">部门:</span> 应急运输科</div>
                                                                <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-100">
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 负责人</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>赵六 (<i class="fas fa-phone-alt mr-1"></i>13600136000)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- 注释：以上为示例结构，实际内容需根据数据动态生成 -->
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 二级响应概览卡片 -->
                        <div class="border border-gray-200 rounded-lg mb-6 overflow-hidden">
                            <div class="bg-orange-50 px-4 py-3 border-b border-gray-200 rounded-t-lg">
                                <h4 class="text-base font-medium text-orange-800">Ⅱ级（重大）响应</h4>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-500 mb-1"><i class="fas fa-bullseye mr-1 text-gray-400"></i>响应启动条件</p>
                                    <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700">
                                        有下列情形之一的，启动Ⅱ级应急响应：<br> 
●造成国道、省道交通中断，出现大量车辆积压，且抢修、处置时间预计在24小时以上的。<br>
●造成国道、省道大桥、中长隧道发生垮塌，或者造成公路桥梁、隧道、边坡等构造物垮塌并导致10人以上30人以下死亡或者失踪的。 <br>
●因重要物资缺乏等原因严重影响省域内经济整体运行和人民正常 生活，需要紧急安排跨市域公路应急通行保障的。<br>
●其他需要由自治区交通运输厅提供公路交通应急保障的。
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 mb-1"><i class="fas fa-tasks mr-1 text-gray-400"></i>应急处置流程</p>
                                    <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700">
                                        <ol class="space-y-2 pl-4 list-decimal">
                                            <li>当领导小组启动Ⅱ级应急响应时，应报告自治区人民政府和 通运输部，并开展以下应急处置工作：</li>
                                            <li>领导小组办公室安排相关人员24小时值班值守，及时跟踪监测、研判信息，并及时向领导小组报告信息。</li>
                                            <li>组织相关单位进行会商，研究解决应急处置的重大事项，研判灾害发展趋势，安排部署低温雨雪冰冻灾害应急处置工作，并连线相关市、县交通运输主管部门及厅直属相关单位，全面掌握事态发生、发展全过程及相关情况，控制事态发展，防止事态蔓延。视情况派出现场工作组或专家组给予指导;</li>
                                            <li>组织做好应急通信保障工作，确保领导小组与现场指挥部的信息畅通，确保信息上报与接收无误;</li>
                                            <li>组织做好抢通保通以及有关抢险作业工作，协调、调度应急救援队伍、物资、装备等开展应急处置;</li>
                                            <li>低温雨雪冰冻灾害发生后，协调组织力量及时清除路面结冰积雪;</li>
                                            <li>加强灾区公路桥梁、隧道、高边坡等重点部位和薄弱环节的风险监测和隐患排查，防范次生、衍生灾害发生；</li>
                                            <li>领导小组办公室应与自治区人民政府相关部门、事发地人民政府及交通运输主管部门保持联系，准确掌握应急处置工作动态，及时报告上级应急指挥机构；</li>
                                            <li>当超出厅本级处置能力时，报请上级应急指挥机构请求支援。</li>
                                        </ol>
                                    </div>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-500 mb-2"><i class="fas fa-sitemap mr-1 text-gray-400"></i>响应单位</p>
                                    <div class="bg-gray-50 p-4 rounded-md text-sm text-gray-700 shadow">
                                        <ul class="space-y-3">
                                            <!-- 示例：领导小组 -->
                                            <li>
                                                <div class="flex items-center">
                                                    <i class="fas fa-users mr-2 text-blue-500"></i>
                                                    <span class="font-semibold text-gray-800">领导小组</span>
                                                </div>
                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                    <li>
                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">单位:</span> 自治区交通运输厅</div>
                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                            <li>
                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">部门:</span> 应急指挥中心</div>
                                                                <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-100">
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 主任</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>张三 (<i class="fas fa-phone-alt mr-1"></i>13800138000)</li>
                                                                        </ul>
                                                                    </li>
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 副主任</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>王五 (<i class="fas fa-phone-alt mr-1"></i>13700137000)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- 示例：应急工作组 - 公路抢险组 -->
                                            <li>
                                                <div class="flex items-center">
                                                    <i class="fas fa-hard-hat mr-2 text-yellow-500"></i>
                                                    <span class="font-semibold text-gray-800">应急工作组 - 公路抢险组</span>
                                                </div>
                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                    <li>
                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">单位:</span> 自治区公路发展中心</div>
                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                            <li>
                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">部门:</span> 养护保通科</div>
                                                                <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-100">
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 科长</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>李四 (<i class="fas fa-phone-alt mr-1"></i>13900139000)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- 示例：应急工作组 - 道路运输保障组 -->
                                            <li>
                                                <div class="flex items-center">
                                                    <i class="fas fa-bus-alt mr-2 text-green-500"></i>
                                                    <span class="font-semibold text-gray-800">应急工作组 - 道路运输保障组</span>
                                                </div>
                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                   <li>
                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">单位:</span> 自治区道路运输发展中心</div>
                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                            <li>
                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">部门:</span> 应急运输科</div>
                                                                <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-100">
                                                                    <li>
                                                                        <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 负责人</div>
                                                                        <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                            <li><i class="fas fa-user-circle mr-1"></i>赵六 (<i class="fas fa-phone-alt mr-1"></i>13600136000)</li>
                                                                        </ul>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- 注释：以上为示例结构，实际内容需根据数据动态生成 -->
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                         <!-- ... (Add cards for III and IV if they exist here in overview) ... -->
                    </div>
                </div>

                <!-- 预案详情 (Keep the modified structure) -->
                 <div id="details" class="tab-content" style="display: block;">
                     <div class="mb-8">
                         <!-- 子标签导航 -->
                         <div class="border-b border-gray-200 mb-6">
                             <nav class="flex space-x-4 overflow-x-auto pb-1">
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600 focus:outline-none" data-sub-tab="basic-info">
                                     基本信息 & 总则
                                 </button>
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-sub-tab="organization">
                                     组织体系
                                 </button>
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none full-mode-only" data-sub-tab="prevention">
                                      预防与预警
                                 </button>
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-sub-tab="response">
                                     应急响应
                                 </button>
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-sub-tab="post-disposal">
                                     后期处置
                                 </button>
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none" data-sub-tab="support">
                                     应急保障
                                 </button>
                                 <button class="sub-tab-btn px-4 py-2 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700 hover:border-gray-300 focus:outline-none full-mode-only" data-sub-tab="management">
                                      预案管理
                                 </button>
                             </nav>
                         </div>

                         <!-- 子标签内容区域 (Keep the existing detailed content structure) -->
                         <div class="sub-tab-content-container">
                             <!-- 基本信息 & 总则 -->
                             <div id="basic-info" class="sub-tab-content">
                                 <!-- ... (Keep the detailed basic info from previous edit) ... -->
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">基本信息 & 总则</h3>
                                <div class="space-y-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 mb-1">预案名称</p>
                                            <p class="text-base text-gray-800">广西壮族自治区公路交通突发事件应急预案</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 mb-1">预案类型</p>
                                            <p class="text-base text-gray-800">公路交通类</p>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-500 mb-1">编制单位</p>
                                            <p class="text-base text-gray-800">广西交通运输厅</p>
                                        </div>
                                         <div>
                                            <p class="text-sm font-medium text-gray-500 mb-1">适用单位</p>
                                            <div class="text-base text-gray-800 flex flex-wrap gap-2">
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">自治区交通运输厅</span>
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">自治区公路发展中心</span>
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">自治区道路运输发展中心</span>
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">自治区港航发展中心</span>
                                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">自治区交通执法局</span>
                                            </div>
                                        </div>
                                         <div>
                                            <p class="text-sm font-medium text-gray-500 mb-1">适用范围</p>
                                            <div class="text-base text-gray-800 flex flex-wrap gap-2">
                                                <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded">公路交通突发事件</span>
                                                 <!-- Add other applicable scopes if any -->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">编制目的</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">为规范和加强公路交通突发事件的应急管理工作，指导、协调全区交通运输部门和单位建立和完善应急预案体系，有效应对公路 交通突发事件，及时保障、恢复公路交通正常运行，制定本预案。</div>
                                    </div>
                                     <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">编制依据</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">依据《中华人民共和国安全生产法》《中华人民共和国突发事件应对法》《中华人民共和国公路法》《公路安全保护条例》《突发事件应急预案管理办法》《国家突发公共事件总体应急预案》《交通运输综合应急预案》《公路交通突发事件应急预案》《广西壮族自治区突发公共事件总体应急预案》《广西壮族自治区交通运输综合应急预案》等相关规定</div>
                                    </div>
                                     <div class="full-mode-only">
                                         <p class="text-sm font-medium text-gray-500 mb-1">适用范围详述</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">本预案适用于自治区行政区域内发生的Ⅱ级及以上公路交通突发事件的应对工作； 交通运输部或自治区人民政府授权自治区交通运输厅指挥应急处置的公路交通突发事件应急处置工作；要由自治区交通运输厅指导、支持处置的Ⅱ级以下公路交通突发事件或者其他紧急事件的应对工作。
本预案指导全区公路交通突发事件应急预案的编制和全区交通运输部门和单位对公路交通突发事件的应对工作。</div>
                                     </div>
                                    <div>
                                         <p class="text-sm font-medium text-gray-500 mb-2">事件分级与响应条件</p>
                                         <div class="space-y-3">
                                             <div class="border border-gray-200 rounded-md p-3">
                                                 <div class="flex items-center mb-2">
                                                      <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-red-100 text-red-800 text-xs font-medium mr-2">Ⅰ</span>
                                                     <span class="text-sm font-medium text-gray-700">Ⅰ级 - 特别重大</span>
                                                 </div>
                                                 <div class="bg-gray-50 p-2 rounded-md text-sm text-gray-700">
                                                    有下列情形之一的，启动I级应急响应：<br>
●造成高速公路、普通国道交通中断，出现大量车辆积压，并影响到周边省域高速公路、普通国道正常运行，且抢修、处置时间预计在48小时以上的。<br>
●造成国道、省道特大桥梁、特长隧道垮塌，或者造成公路桥梁、隧道、边坡等构造物垮塌并导致30人以上死亡或者失踪的s。<br>
●因重要物资缺乏等原因严重影响全国或者大片区经济整体运行和人民正常生活，需要紧急安排跨省域公路应急通行保障的。<br>
●其他需要由交通运输部、自治区人民政府提供公路交通应急保障的。
                                                 </div>
                                             </div>
                                             <div class="border border-gray-200 rounded-md p-3">
                                                 <div class="flex items-center mb-2">
                                                      <span class="inline-flex items-center justify-center h-6 w-6 rounded-full bg-orange-100 text-orange-800 text-xs font-medium mr-2">Ⅱ</span>
                                                     <span class="text-sm font-medium text-gray-700">II级 - 重大</span>
                                                 </div>
                                                 <div class="bg-gray-50 p-2 rounded-md text-sm text-gray-700">
                                                    有下列情形之一的，启动Ⅱ级应急响应：<br> 
●造成国道、省道交通中断，出现大量车辆积压，且抢修、处置时间预计在24小时以上的。<br>
●造成国道、省道大桥、中长隧道发生垮塌，或者造成公路桥梁、隧道、边坡等构造物垮塌并导致10人以上30人以下死亡或者失踪的。 <br>
●因重要物资缺乏等原因严重影响省域内经济整体运行和人民正常 生活，需要紧急安排跨市域公路应急通行保障的。<br>
●其他需要由自治区交通运输厅提供公路交通应急保障的。
                                                 </div>
                                             </div>
                                             <!-- Add III and IV if they exist -->
                                         </div>
                                     </div>
                                     <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">工作原则</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1.预防为主，依法应对
公路交通突发事件应对要坚持人民至上、生命至上，严格按照 国家相关法律法规要求，坚持预防与应急相结合，常态与非常态相 结合，提高防范意识，做好预案演练、宣传和培训等各项保障工作。
2.统一领导，分级负责
突发事件应对工作在自治区党委、政府的统一领导下，实行分级响应、分级负责，条块结合、属地管理为主的应急管理体制，充分发挥交通运输应急指挥机构的作用。
3.快速反应，协调联动
充分利用应急资源，发挥各部门和社会公众的作用，建立健全 应急处置协同联动机制，明确各方责任，形成统一指挥、功能齐全、反应灵敏、协调有序、运转高效的应急管理体制。
                                         </div>
                                    </div>
                                </div>
                             </div>

                             <!-- 组织体系 -->
                             <div id="organization" class="sub-tab-content" style="display: none;">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">组织体系</h3>
                                <p class="text-sm text-gray-600 mb-4">自治区应急组织体系由自治区级、市级和县级三级组成。以下为自治区级应急指挥机构详情：</p>

                                <div class="org-structure space-y-6">
                                    <!-- 自治区级 指挥机构 -->
                                    <h4 class="text-md font-semibold text-gray-700 border-b pb-2">自治区级应急指挥机构</h4>

                                    <!-- Level 2: 领导小组 -->
                                    <div class="org-card border border-blue-200 rounded-lg overflow-hidden">
                                        <div class="card-header bg-blue-50 p-3 border-b border-blue-200 flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-sitemap text-blue-600 mr-2"></i>
                                                <h4 class="text-base font-medium text-blue-800">领导小组</h4>
                                                <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">层级2</span>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 mr-1">Ⅰ级</span>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">Ⅱ级</span>
                                            </div>
                                        </div>
                                        <div class="card-content p-4">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                <div>
                                                    <p class="text-sm font-medium text-gray-600 mb-1">主要职责</p>
                                                    <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">
（1）在一级响应启动或由上级应急指挥机构统一指挥时，按照上级应急指挥机构指令开展应急处置行动；
（2）统一领导二级低温雨雪冰冻灾害的应急处置工作，发布指挥调度命令，并督导检查执行情况；
（3）组织协调公路、水路、铁路、民航等行业对受灾区域交通状况加密监测，协调抗灾救灾人员、装备、物资运输；组织提供转移受灾群众所需的交通运输工具；
（4）决定启动、终止低温雨雪冰冻灾害二级预警和应急响应；
（5）根据需要，会同自治区有关部门，制定应对低温雨雪冰冻灾害的联合行动方案，并监督实施；
（6）其他相关重大事项。
                                                    </div>
                                                </div>
                                                <div>
                                                    <p class="text-sm font-medium text-gray-600 mb-1">关联人员/单位</p>
                                                    <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700">
                                                        <ul class="space-y-3"> <!-- Replaces the list-disc ul -->
                                                            <li> <!-- Unit: 自治区交通运输厅 (Assumed top level for these roles) -->
                                                                <div class="flex items-center">
                                                                    <i class="fas fa-university mr-2 text-gray-500"></i> <!-- Icon for unit -->
                                                                    <span class="font-semibold text-gray-700">自治区交通运输厅</span>
                                                                </div>
                                                                <ul class="ml-6 mt-1 space-y-1.5 pl-3 border-l border-gray-300">
                                                                    <li> <!-- Category/Department: 厅领导 (Leadership) -->
                                                                        <div class="flex items-center">
                                                                            <i class="fas fa-users mr-2 text-gray-500"></i>
                                                                            <span class="font-medium text-gray-600">厅领导</span>
                                                                        </div>
                                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                                            <li>
                                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 组长 (厅主要领导)</div>
                                                                                <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                                    <li><i class="fas fa-user-circle mr-1"></i>张三 (<i class="fas fa-phone-alt mr-1"></i>13800001111)</li>
                                                                                </ul>
                                                                            </li>
                                                                            <li>
                                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 副组长 (分管厅领导)</div>
                                                                                <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                                    <li><i class="fas fa-user-circle mr-1"></i>李四 (<i class="fas fa-phone-alt mr-1"></i>13900002222)</li>
                                                                                </ul>
                                                                            </li>
                                                                        </ul>
                                                                    </li>
                                                                    <li> <!-- Department: 办公室 -->
                                                                        <div class="flex items-center">
                                                                            <i class="fas fa-door-open mr-2 text-gray-500"></i>
                                                                            <span class="font-medium text-gray-600">办公室</span>
                                                                        </div>
                                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                                            <li>
                                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 主任 (成员)</div>
                                                                                <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                                    <li><i class="fas fa-user-circle mr-1"></i>王五 (<i class="fas fa-phone-alt mr-1"></i>13700003333)</li>
                                                                                </ul>
                                                                            </li>
                                                                        </ul>
                                                                    </li>
                                                                    <li> <!-- Department: 安全监督处 -->
                                                                        <div class="flex items-center">
                                                                            <i class="fas fa-shield-alt mr-2 text-gray-500"></i>
                                                                            <span class="font-medium text-gray-600">安全监督处</span>
                                                                        </div>
                                                                        <ul class="ml-6 mt-1 space-y-1 pl-3 border-l border-gray-200">
                                                                            <li>
                                                                                <div class="text-gray-700"><span class="font-medium text-gray-600">职务:</span> 处长 (成员)</div>
                                                                                <ul class="ml-6 mt-0.5 space-y-0.5 text-xs text-gray-600">
                                                                                    <li><i class="fas fa-user-circle mr-1"></i>赵六 (<i class="fas fa-phone-alt mr-1"></i>13600004444)</li>
                                                                                </ul>
                                                                            </li>
                                                                        </ul>
                                                                    </li>
                                                                    <li> <!-- Placeholder for others -->
                                                                        <div class="flex items-center text-xs text-gray-500 mt-2">
                                                                            <i class="fas fa-ellipsis-h mr-2"></i>
                                                                            <span>... (其他厅领导、相关处室、直属单位负责人)</span>
                                                                        </div>
                                                                    </li>
                                                                </ul>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Level 2: 领导小组办公室 -->
                                    <div class="org-card border border-gray-200 rounded-lg overflow-hidden">
                                        <div class="card-header bg-gray-50 p-3 border-b border-gray-200 flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-building text-green-600 mr-1"></i>
                                                <h5 class="text-base font-medium text-gray-700">领导小组办公室</h5>
                                                <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800">层级2</span>
                                            </div>
                                        </div>
                                        <div class="card-content p-4 text-sm text-gray-600 space-y-1">
                                            <p><b>职责：</b>收集信息、研判、提建议、协调、事件调查、总结评估、指导协调下级、承办领导小组交办工作。</p>
                                            <p><b>主要联系人/单位：</b></p>
                                            <ul class="list-disc pl-4 space-y-1 text-xs">
                                                <li>王五 (安全总监/办公室主任) - 13700003333</li>
                                                <li>孙七 (安全监督处副处长) - 13500005555</li>
                                                <li>周八 (办公室副主任) - 13400006666</li>
                                                <li>... (其他相关处室、单位联系人)</li>
                                            </ul>
                                        </div>
                                    </div>

                                     <!-- Level 2: 应急工作组 (Container/Title for Level 3) -->
                                    <div class="org-card border border-gray-200 rounded-lg overflow-hidden">
                                         <div class="card-header bg-gray-50 p-3 border-b border-gray-200 flex items-center justify-between">
                                             <div class="flex items-center">
                                                <i class="fas fa-users-cog text-green-600 mr-1"></i>
                                                <h5 class="text-base font-medium text-gray-700">应急工作组</h5>
                                                <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800">层级2 (分类)</span>
                                            </div>
                                            <div class="text-xs text-gray-500">
                                                (自动成立@Ⅰ/Ⅱ级响应/预警)
                                            </div>
                                        </div>
                                        <div class="card-content p-4">
                                            <p class="text-sm font-medium text-gray-600 mb-2">下设机构 (层级3)</p>
                                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 full-mode-only">
                                                 <!-- 综合协调组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-tasks text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">综合协调组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>综合协调、协调力量、落实要求、收集汇总情况、起草报告文件、报送文件。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                            <li>孙七 (安全监督处副处长/组长) - 13500005555</li>
                                                            <li>... (各成员单位联系人)</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                <!-- 公路保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-road text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">公路保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>公路状况监测、协调人员/装备/物资运输、抢修保通、除雪融冰、交通管制配合、保通绕行方案、开通"绿色通道"。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                            <li>吴九 (建设管理处处长/组长) - 13300007777</li>
                                                            <li>... (公路中心、高速中心、执法局联系人)</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                 <!-- 道路运输保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-bus text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">道路运输保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>协调转移车辆、协调联运、协调应急保障、组织应急车辆、保障人员/装备/物资运输、保障疏散撤离、指导调整运力、协调资源征调。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                            <li>郑十 (运输处处长/组长) - 13200008888</li>
                                                            <li>... (道运中心、执法局联系人)</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                                 <!-- 水路运输保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-ship text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">水路运输保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>水路状况监测、协调人员/装备/物资运输、抢修保通（通航建筑物、港口）、除雪融冰、水上交通管制配合、优先过闸/引航/装卸、拟定抢修/联运方案、协调转移船舶、协助转移人员。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                             <li>冯十一 (水运管理处处长/组长) - 13100009999</li>
                                                             <li>... (港航中心、执法局联系人)</li>
                                                         </ul>
                                                    </div>
                                                </div>
                                                 <!-- 铁路运输保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-train text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">铁路运输保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>铁路状况监测、协调人员/装备/物资运输、抢修保通、除雪融冰、交通管制配合、优先通行、拟定抢修/绕行/联运方案、开通"绿色通道"、协调转移工具、协助转移人员。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                             <li>陈十二 (铁建处处长/组长) - 13000001010</li>
                                                             <li>... (铁路项目开发处联系人)</li>
                                                         </ul>
                                                    </div>
                                                </div>
                                                 <!-- 民航运输保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-plane text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">民航运输保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>民航状况监测、协调人员/装备/物资运输、抢修保通、除雪融冰、交通管制配合、优先通行、拟定抢修/绕行/联运方案、开通"绿色通道"、协调转移工具、协助转移人员。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                             <li>褚十三 (机场管理处处长/组长) - 15900001212</li>
                                                             <li>... (机场管理处其他联系人)</li>
                                                         </ul>
                                                    </div>
                                                </div>
                                                 <!-- 新闻宣传组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-bullhorn text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">新闻宣传组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>舆情监控与回应、引导舆论、发布信息、组织新闻发布会、指导新闻发布工作、管理相关人员。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                             <li>卫十四 (党委副书记/组长) - 15800001313</li>
                                                             <li>... (办公室、党委、政策法规处、科教处联系人)</li>
                                                         </ul>
                                                    </div>
                                                </div>
                                                 <!-- 通信保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-satellite-dish text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">通信保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>负责应急处置过程中的通信保障。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                             <li>蒋十五 (科教处处长/组长) - 15700001414</li>
                                                             <li>... (厅直属相关单位联系人)</li>
                                                         </ul>
                                                    </div>
                                                </div>
                                                 <!-- 后勤保障组 -->
                                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                                    <div class="bg-yellow-50 px-3 py-2 border-b border-gray-200 flex items-center">
                                                        <i class="fas fa-truck-loading text-yellow-600 mr-1"></i>
                                                        <h5 class="text-sm font-medium text-yellow-800">后勤保障组</h5>
                                                        <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-yellow-100 text-yellow-800">层级3</span>
                                                    </div>
                                                    <div class="p-3 text-xs text-gray-600 space-y-1">
                                                        <p><b>职责：</b>厅机关后勤服务保障、协调厅直属单位后勤保障、提供应急资金保障。</p>
                                                        <p><b>主要联系人/单位：</b></p>
                                                        <ul class="list-disc pl-4 space-y-1 text-xs">
                                                             <li>沈十六 (机关服务中心主任/组长) - 15600001515</li>
                                                             <li>... (财务处、服务中心联系人)</li>
                                                         </ul>
                                                    </div>
                                                </div>
                                             </div>
                                        </div>
                                    </div>

                                     <!-- Level 2: 现场工作组 -->
                                    <div class="org-card border border-gray-200 rounded-lg overflow-hidden">
                                         <div class="card-header bg-gray-50 p-3 border-b border-gray-200 flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-map-marker-alt text-green-600 mr-1"></i>
                                                <h5 class="text-base font-medium text-gray-700">现场工作组</h5>
                                                <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800">层级2</span>
                                            </div>
                                            <div class="text-xs text-gray-500">(视情况成立)</div>
                                        </div>
                                        <div class="card-content p-4 text-sm text-gray-600 space-y-1">
                                            <p><b>职责：</b>现场指导协调、技术支持、维持秩序、疏散人员、报告情况。</p>
                                            <p><b>主要联系人/单位：</b></p>
                                            <ul class="list-disc pl-4 space-y-1 text-xs">
                                                <li>韩十七 (带队领导/厅领导) - 15500001616</li>
                                                <li>... (相关处室、单位、专家联系人)</li>
                                            </ul>
                                        </div>
                                    </div>

                                     <!-- Level 2: 专家组 -->
                                    <div class="org-card border border-gray-200 rounded-lg overflow-hidden">
                                         <div class="card-header bg-gray-50 p-3 border-b border-gray-200 flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-user-tie text-green-600 mr-1"></i>
                                                <h5 class="text-base font-medium text-gray-700">专家组</h5>
                                                <span class="ml-2 text-xs px-1.5 py-0.5 rounded-full bg-green-100 text-green-800">层级2</span>
                                            </div>
                                            <div class="text-xs text-gray-500">(视情况成立)</div>
                                        </div>
                                        <div class="card-content p-4 text-sm text-gray-600 space-y-1">
                                            <p><b>职责：</b>技术研究、咨询服务、分析趋势、评估方案、损失评估、提出建议。</p>
                                            <p><b>主要联系人/单位：</b></p>
                                            <ul class="list-disc pl-4 space-y-1 text-xs">
                                                <li>杨十八 (专家组组长/教授) - 15400001717</li>
                                                <li>... (其他专家联系方式)</li>
                                            </ul>
                                        </div>
                                    </div>

                                     <!-- 市级、县级机构说明 -->
                                    <h4 class="text-md font-semibold text-gray-700 border-b pb-2">市级、县级应急指挥机构</h4>
                                    <p class="text-sm text-gray-600">市级、县级交通运输主管部门及厅直属相关单位参照成立应急指挥机构并明确职责。</p>


                                </div>


                             </div>

                             <!-- 预防与预警 -->
                             <div id="prevention" class="sub-tab-content full-mode-only" style="display: none;">
                                  <!-- ... (Keep the detailed prevention content from previous edit) ... -->
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">预防与预警</h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预防措施</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 气象部门加强气象监测预报预警，做好灾害性天气预测。\n2. 交通部门提前储备除雪融冰物资，对易结冰路段进行排查和防护。\n3. 电力部门对易受低温雨雪影响的电力设施进行巡查和隐患排除。\n4. 各基层组织开展居民防冻防滑知识宣传和培训。</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预警原则</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 早发现、早报告、早预警、早处置的"四早"原则。\n2. 快速反应、协调联动、分级负责、属地为主的原则。\n3. 以人为本、科学预警、精准发布、公众参与的原则。\n4. 确保预警信息发布及时、准确、权威、可靠。</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预警信息收集</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">气象、交通、水利、电力等部门通过监测系统收集灾害性天气等信息，及时向区应急管理局报送，由综合协调组组织分析研判，为预警信息发布提供依据。</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预警分级</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">根据雪灾可能造成的危害程度、紧急程度和发展态势，预警级别由高到低分为红色、橙色、黄色和蓝色四级：\n\n1. 红色预警（I级）：预计未来24小时内可能出现大暴雪（24小时降雪量≥15.0mm），或已出现大暴雪且可能持续；可能或已经造成重大交通阻断、大范围电力通信中断等严重影响。\n2. 橙色预警（II级）：预计未来24小时内可能出现暴雪（24小时降雪量10.0～14.9mm），或已出现暴雪且可能持续；可能或已经造成道路结冰、交通受阻等严重影响。\n3. 黄色预警（III级）：预计未来24小时内可能出现大雪（24小时降雪量5.0～9.9mm），或已出现大雪且可能持续；对交通出行等有较大影响。\n4. 蓝色预警（IV级）：预计未来24小时内可能出现中雪（24小时降雪量2.5～4.9mm），或已出现中雪且可能持续；对交通出行等有一定影响。</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预警发布</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 发布主体：区气象局负责发布气象灾害预警信息，区应急管理局负责统筹协调预警信息发布工作。\n2. 发布内容：包括预警区域、预警级别、预警起始时间、可能影响范围、警示事项、应对措施、发布机构等。\n3. 发布渠道：通过电视、广播、报纸、互联网、手机短信等方式发布预警信息...\n4. 发布流程：区气象局发现灾害性天气→分析研判→提出预警信息发布建议→区应急管理局审核→发布预警信息</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预警措施</p>
                                         <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 红色预警（I级）措施：启动24小时应急值守, 停止一切户外集会、活动, 停课、停工、停业...\n2. 橙色预警（II级）措施：加强值班值守, 考虑停止户外集会和高空等危险作业...\n3. 黄色预警（III级）措施：加强监测预报, 相关部门做好应急准备...\n4. 蓝色预警（IV级）措施：密切关注天气变化, 注意防寒保暖...</div>
                                    </div>
                                </div>
                             </div>

                             <!-- 应急响应 -->
                             <div id="response" class="sub-tab-content" style="display: none;">
                                  <!-- ... (Keep the detailed response content from previous edit) ... -->
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">应急响应</h3>
                                <div class="space-y-6">
                                    <!-- 一级响应 -->
                                    <div class="response-level-card shadow-sm border border-red-200 rounded-lg overflow-hidden">
                                        <div class="card-header bg-red-50 p-4 border-b border-red-200">
                                            <h4 class="text-lg font-medium text-red-800 flex items-center">
                                                <span class="inline-block w-6 h-6 bg-red-600 text-white rounded-full text-center font-bold mr-2">I</span>
                                                <span>特别重大响应</span>
                                            </h4>
                                        </div>
                                        <div class="card-content p-4">
                                            <div class="content-block mb-4">
                                                <p class="block-title flex items-center text-red-700 font-medium mb-2"><i class="fas fa-bullseye mr-2"></i>响应启动条件</p>
                                                <div class="block-content bg-red-50 p-3 rounded text-sm">
                                                    全区范围内出现大范围持续性强降雪、低温、道路结冰等灾害，造成交通、电力、通信等基础设施大面积损毁，或造成重大人员伤亡。
                                                </div>
                                            </div>
                                            <div class="content-block">
                                                <p class="block-title flex items-center text-red-700 font-medium mb-2"><i class="fas fa-tasks mr-2"></i>应急处置流程</p>
                                                <div class="block-content bg-red-50 p-3 rounded text-sm">
                                                    <ol class="list-decimal pl-4 space-y-2">
                                                        <li>立即启动I级响应机制，成立区级应急处置指挥部，区长任指挥长；</li>
                                                        <li>及时向上级部门报告灾情和救灾工作进展情况；</li>
                                                        <li>组织抢通交通要道，确保救援车辆通行；</li>
                                                        <li>调动除雪车辆，对主要道路进行除雪作业；</li>
                                                        <li>调配医疗救护力量，做好伤员转移和救治工作；</li>
                                                        <li>调配电力抢修队伍，抢修电力设施，恢复供电；</li>
                                                        <li>组织转移安置受灾群众，保障基本生活需求。</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                     <!-- 二级响应 -->
                                     <div class="response-level-card shadow-sm border border-orange-200 rounded-lg overflow-hidden">
                                        <div class="card-header bg-orange-50 p-4 border-b border-orange-200">
                                            <h4 class="text-lg font-medium text-orange-800 flex items-center">
                                                <span class="inline-block w-6 h-6 bg-orange-500 text-white rounded-full text-center font-bold mr-2">II</span>
                                                <span>重大响应</span>
                                            </h4>
                                        </div>
                                        <div class="card-content p-4">
                                            <div class="content-block mb-4">
                                                <p class="block-title flex items-center text-orange-700 font-medium mb-2"><i class="fas fa-bullseye mr-2"></i>响应启动条件</p>
                                                <div class="block-content bg-orange-50 p-3 rounded text-sm">
                                                    区域性强降雪、低温、冰冻灾害，造成交通阻断、电力中断等影响，部分地区受灾严重。
                                                </div>
                                            </div>
                                            <div class="content-block">
                                                <p class="block-title flex items-center text-orange-700 font-medium mb-2"><i class="fas fa-tasks mr-2"></i>应急处置流程</p>
                                                <div class="block-content bg-orange-50 p-3 rounded text-sm">
                                                    <ol class="list-decimal pl-4 space-y-2">
                                                        <li>启动重大雪灾应急响应，成立应急指挥部。</li>
                                                        <li>组织部分专业救援队伍赶赴受灾严重区域开展救援。</li>
                                                        <li>加强交通疏导，及时清理主要道路积雪。</li>
                                                        <li>优先保障重要场所电力、通信畅通。</li>
                                                        <li>做好受灾群众基本生活保障工作。</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                     <!-- Add III and IV if they exist -->
                                 </div>
                                  <div class="mt-6 space-y-4 full-mode-only">
                                      <div class="border-l-4 border-blue-500 pl-3">
                                          <p class="text-sm font-medium text-gray-700 mb-1">信息报送</p>
                                          <div class="bg-blue-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">按照"快速、准确、全面"的原则，及时收集和报告灾情信息。涉及人员伤亡的，应在事发后30分钟内电话报告，1小时内书面报告。I、II级事件须每2小时续报一次，直至应急处置工作结束。</div>
                                      </div>
                                      <div class="border-l-4 border-blue-500 pl-3">
                                          <p class="text-sm font-medium text-gray-700 mb-1">新闻发布</p>
                                           <div class="bg-blue-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">按照及时、准确、客观、全面的原则，由区政府新闻办负责组织协调新闻发布工作。重大灾情信息发布，应征得区政府和上级主管部门同意。</div>
                                      </div>
                                      <div class="border-l-4 border-blue-500 pl-3">
                                          <p class="text-sm font-medium text-gray-700 mb-1">响应调整与终止</p>
                                           <div class="bg-blue-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">根据灾害事态发展情况和处置工作实际需要，及时调整应急响应级别。当灾害得到有效控制，险情已经消除，受灾地区社会秩序恢复正常，由启动响应的同级政府决定终止应急响应。</div>
                                      </div>
                                  </div>
                             </div>

                             <!-- 后期处置 -->
                              <div id="post-disposal" class="sub-tab-content" style="display: none;">
                                  <!-- ... (Keep the detailed post-disposal content from previous edit) ... -->
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">后期处置</h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">善后处置</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 及时恢复水、电、气、通信等基础设施功能，恢复正常生产生活秩序。\n2. 救助受灾群众，做好受灾群众的基本生活保障工作，安排好受灾群众临时住所。\n3. 组织开展卫生防疫工作，防止因灾引发疫情。\n4. 调拨救灾资金和物资，迅速调集救灾储备物资，支援灾区人民群众生活。\n5. 对因灾伤亡人员家属进行抚慰，对紧急调集、征用的人力、物力按规定给予补偿。</div>
                                    </div>
                                    <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">总结评估</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 灾害处置工作结束后，区应急指挥部组织开展灾害调查评估工作，全面调查灾害发生经过、损失情况、救援过程和应急处置效果。\n2. 各相关单位对灾害应急处置情况进行总结，分析存在的问题，提出改进建议。\n3. 形成灾害事件调查评估报告，报区政府，同时抄送相关部门。\n4. 根据总结评估结果，进一步完善应急预案，加强应急队伍建设，改进应急保障措施。</div>
                                    </div>
                                </div>
                              </div>

                             <!-- 应急保障 -->
                              <div id="support" class="sub-tab-content" style="display: none;">
                                  <!-- ... (Keep the detailed support content from previous edit) ... -->
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">应急保障</h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">物资保障</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 区应急管理局负责组织调运区级储备的应急物资。\n2. 区城管委负责组织调运融雪剂、铁锹、扫帚等除雪物资。\n3. 区交通局负责除雪设备和车辆的储备和调度。\n4. 各街道、社区和重点单位按职责储备必要的应急装备和物资。</div>
                                    </div>
                                    <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">通信保障</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 区通信管理办公室协调各电信运营商确保通信系统正常运行。\n2. 建立应急广播系统，保障信息及时发布。\n3. 配备应急通信设备，确保关键岗位人员通信畅通。\n4. 当固定通信设施遭到破坏时，启用卫星电话等备用通信手段。</div>
                                    </div>
                                    <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">交通保障</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 区公安分局交警大队负责指挥灾区交通，保障救援道路畅通。\n2. 区交通局负责协调公共交通工具，保障群众疏散和救援物资运输。\n3. 紧急情况下，可征用社会车辆用于抢险救灾。\n4. 建立应急车辆调度机制，确保应急车辆优先通行。</div>
                                    </div>
                                     <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">医疗保障</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 区卫健委负责组织医疗队伍，开展现场救治和卫生防疫工作。\n2. 各医院做好接收伤员的准备，设立专门病区。\n3. 建立伤员转运绿色通道，确保伤员及时得到救治。\n4. 储备充足的医疗物资和设备，确保应急医疗需求。</div>
                                    </div>
                                     <div class="full-mode-only">
                                        <p class="text-sm font-medium text-gray-500 mb-1">经费保障</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 区财政局负责应急救灾专项资金的筹集、拨付和使用监督。\n2. 建立应急资金快速拨付机制，确保应急处置工作经费及时到位。\n3. 对因灾受损的基础设施修复给予资金支持。\n4. 安排专项资金用于灾后恢复重建工作。</div>
                                    </div>
                                </div>
                              </div>

                             <!-- 预案管理 -->
                              <div id="management" class="sub-tab-content full-mode-only" style="display: none;">
                                  <!-- ... (Keep the detailed management content from previous edit) ... -->
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">预案管理</h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预案修订</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 有下列情形之一的，应当及时修订本预案：\n   - 有关法律、法规、规章、标准、上位预案中的有关规定发生变化的\n   - 应急指挥机构及其职责发生重大调整的\n   - 面临的风险发生重大变化的\n   - 在实际应对和演练中发现问题需要修订的\n   - 其他需要修订的情况\n2. 预案修订由区应急管理局负责组织实施...</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">宣传培训</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 应急管理局负责组织对应急预案的宣传教育培训工作。\n2. 各相关单位应当组织本部门、本单位或本行业的应急管理人员学习本预案。\n3. 充分利用广播、电视、报纸、互联网等媒体，加强对公众的宣传教育。\n4. 积极组织专家进学校、进社区、进企业开展防灾减灾知识宣讲活动。</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">预案演练</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">1. 演练形式：桌面推演、功能演练、综合演练等。\n2. 演练频次：区应急管理局应当每年至少组织一次应急演练...\n3. 演练要求：演练前制定周密的演练方案...\n4. 演练内容：预警信息发布、应急响应、指挥协调、人员疏散、物资调配、应急通信等。</div>
                                    </div>
                                     <div>
                                        <p class="text-sm font-medium text-gray-500 mb-1">实施时间</p>
                                        <div class="bg-gray-50 p-3 rounded-md text-sm text-gray-700 whitespace-pre-wrap">本预案自发布之日起实施。</div>
                                    </div>
                                </div>
                              </div>
                         </div>
                     </div>
                 </div>

                <!-- 附件管理 -->
                <div id="attachments" class="tab-content" style="display: none;">
                    <!-- ... (Keep the modified attachments content) ... -->
                     <div class="mb-8">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">附件列表</h3>
                        <div class="overflow-hidden bg-gray-50 rounded-lg border border-gray-200">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">附件名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">格式</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">上传时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">上传人</th>
                                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 text-sm text-gray-700">附件1：区应急指挥机构及成员名单</td>
                                        <td class="px-6 py-4 text-sm text-gray-700"><i class="fas fa-file-pdf text-red-500 mr-1"></i> PDF</td>
                                        <td class="px-6 py-4 text-sm text-gray-700">2023-06-15</td>
                                        <td class="px-6 py-4 text-sm text-gray-700">张三</td>
                                        <td class="px-6 py-4 text-center">
                                            <button class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-download mr-1"></i> 下载
                                            </button>
                                             <button class="text-blue-600 hover:text-blue-900 ml-2 full-mode-only">
                                                <i class="fas fa-eye mr-1"></i> 预览
                                            </button>
                                        </td>
                                    </tr>
                                     <tr>
                                        <td class="px-6 py-4 text-sm text-gray-700">附件2：低温雨雪冰冻灾害应急处置流程图</td>
                                         <td class="px-6 py-4 text-sm text-gray-700"><i class="fas fa-file-word text-blue-500 mr-1"></i> Word</td>
                                         <td class="px-6 py-4 text-sm text-gray-700">2023-06-15</td>
                                         <td class="px-6 py-4 text-sm text-gray-700">张三</td>
                                         <td class="px-6 py-4 text-center">
                                            <button class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-download mr-1"></i> 下载
                                            </button>
                                             <button class="text-blue-600 hover:text-blue-900 ml-2 full-mode-only">
                                                <i class="fas fa-eye mr-1"></i> 预览
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                         <td class="px-6 py-4 text-sm text-gray-700">附件3：应急物资储备清单</td>
                                         <td class="px-6 py-4 text-sm text-gray-700"><i class="fas fa-file-excel text-green-500 mr-1"></i> Excel</td>
                                         <td class="px-6 py-4 text-sm text-gray-700">2023-06-15</td>
                                         <td class="px-6 py-4 text-sm text-gray-700">张三</td>
                                         <td class="px-6 py-4 text-center">
                                            <button class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-download mr-1"></i> 下载
                                            </button>
                                             <button class="text-blue-600 hover:text-blue-900 ml-2 full-mode-only">
                                                <i class="fas fa-eye mr-1"></i> 预览
                                            </button>
                                        </td>
                                    </tr>
                                    <tr>
                                         <td class="px-6 py-4 text-sm text-gray-700">附件4：应急避难场所分布图</td>
                                         <td class="px-6 py-4 text-sm text-gray-700"><i class="fas fa-file-image text-purple-500 mr-1"></i> JPG</td>
                                         <td class="px-6 py-4 text-sm text-gray-700">2023-06-15</td>
                                         <td class="px-6 py-4 text-sm text-gray-700">张三</td>
                                         <td class="px-6 py-4 text-center">
                                            <button class="text-blue-600 hover:text-blue-900">
                                                <i class="fas fa-download mr-1"></i> 下载
                                            </button>
                                             <button class="text-blue-600 hover:text-blue-900 ml-2 full-mode-only">
                                                <i class="fas fa-eye mr-1"></i> 预览
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 附录 -->
            </div>

            <!-- 版本信息 -->
            <div class="mt-6 flex items-center justify-between bg-gray-50 rounded-lg p-4">
                 <!-- ... (Keep version info) ... -->
                 <div class="text-sm text-gray-600">
                    <span class="mr-4"><i class="fas fa-code-branch mr-1"></i> 当前版本: V1.2</span>
                    <span class="mr-4"><i class="fas fa-user mr-1"></i> 修订人: 张三</span>
                    <span><i class="fas fa-calendar-alt mr-1"></i> 修订时间: 2023-11-15</span>
                </div>
                <a href="plan_version_history.html" class="text-blue-600 hover:text-blue-900 text-sm">
                    <i class="fas fa-history mr-1"></i> 查看版本历史
                </a>
            </div>
        </div>
    </main>

    <!-- Hidden templates (not used actively in this version) -->
    <div class="hidden">
        <div id="backgrounds-template">
             <!-- ... -->
        </div>
        <div id="legal-basis-template">
            <!-- ... -->
        </div>
    </div>

    <!-- 页面底部 -->
    <div class="mt-12 text-center text-xs text-gray-500">
        <p>版本号: V1.0.3 | 更新时间: 2024-06-15</p>
    </div>


    <script src="js/sidebarComponent_emergency.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- 步骤 1: 注入侧边栏 ---
            const sidebarPlaceholder = document.getElementById('sidebar-container-placeholder');
            if (sidebarPlaceholder && typeof sidebarHTML !== 'undefined') {
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = sidebarHTML.trim(); // sidebarHTML 来自 js/sidebarComponent.js
                const newSidebarElement = tempDiv.querySelector('aside');

                if (newSidebarElement && newSidebarElement.tagName === 'ASIDE') {
                    // 从 plan_detail.html 应用关键的定位和样式类
                    newSidebarElement.classList.add('fixed', 'top-16', 'bottom-0', 'left-0');

                    // 从组件中移除 w-64 类，以使用 plan_detail.html 的 CSS 中定义的宽度 (.sidebar { width: 250px; })
                    // 这确保了与主内容区域 margin-left 移动的一致性。
                    newSidebarElement.classList.remove('w-64');

                    // 确保占位符的父节点正确地接纳新的侧边栏
                    if (sidebarPlaceholder.parentNode) {
                        sidebarPlaceholder.parentNode.replaceChild(newSidebarElement, sidebarPlaceholder);
                    } else {
                        console.error("未找到侧边栏占位符的父节点。");
                    }
                } else {
                    if (!newSidebarElement) console.error("从 sidebarHTML 创建侧边栏元素失败。");
                    else if (newSidebarElement.tagName !== 'ASIDE') console.error("sidebarHTML 未能生成 ASIDE 元素作为其第一个子元素。");
                }
            } else {
                if (!sidebarPlaceholder) console.error("未找到侧边栏占位符 'sidebar-container-placeholder'。");
                if (typeof sidebarHTML === 'undefined') console.error("'sidebarHTML' 未定义。请确保 js/sidebarComponent.js 已正确加载并全局定义了此变量。");
            }

            // --- 步骤 2: 侧边栏和菜单激活逻辑 (适配新的侧边栏组件) ---
            const currentPage = window.location.pathname.split('/').pop().split('.')[0];
            // 从新注入的侧边栏中选择菜单项
            const menuItems = document.querySelectorAll('.sidebar .sidebar-menu-item');

            menuItems.forEach(item => {
                const itemHref = item.getAttribute('href');
                // 忽略 href="#" 的项
                if (itemHref && itemHref !== '#') {
                    let pageNameFromHref = itemHref.split('/').pop().split('.')[0];

                    // plan_detail.html 页面的特殊处理：高亮 '预案库' (plan_list.html)
                    if (currentPage === 'plan_detail' && pageNameFromHref === 'plan_list') {
                        item.classList.add('active');
                         // 如果 'active' 类改变了颜色和字体粗细，确保这些样式也应用到图标和文本
                        const icon = item.querySelector('i');
                        const span = item.querySelector('span');
                        if (icon) {
                            icon.classList.remove('text-gray-600');
                            icon.classList.add('text-blue-600'); // 假设 active 状态是蓝色
                        }
                        if (span) {
                            span.classList.remove('text-gray-700');
                            span.classList.add('text-blue-600', 'font-medium'); // 假设 active 状态是蓝色和中等粗细
                        }
                    } else if (pageNameFromHref === currentPage) {
                        item.classList.add('active');
                        const icon = item.querySelector('i');
                        const span = item.querySelector('span');
                        if (icon) {
                            icon.classList.remove('text-gray-600');
                            icon.classList.add('text-blue-600');
                        }
                        if (span) {
                            span.classList.remove('text-gray-700');
                            span.classList.add('text-blue-600', 'font-medium');
                        }
                    }
                }
            });

            const sidebarToggle = document.getElementById('sidebar-toggle');
            // 这将选择新注入的侧边栏
            const sidebar = document.querySelector('.sidebar');
            const body = document.body;
            const sidebarState = localStorage.getItem('sidebarState');

            // 从 localStorage 初始化侧边栏状态
            if (sidebar && sidebarToggle) { // 操作前确保元素存在
                if (sidebarState === 'collapsed') {
                    sidebar.classList.add('collapsed');
                    body.classList.remove('sidebar-expanded');
                    body.classList.add('sidebar-collapsed');
                } else {
                    // 如果没有状态或状态是 'expanded'，则默认为展开
                    sidebar.classList.remove('collapsed');
                    body.classList.remove('sidebar-collapsed');
                    body.classList.add('sidebar-expanded'); // 确保如果未折叠则设置此项
                }

                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    const isCollapsed = sidebar.classList.contains('collapsed');
                    body.classList.toggle('sidebar-expanded', !isCollapsed);
                    body.classList.toggle('sidebar-collapsed', isCollapsed);
                    localStorage.setItem('sidebarState', isCollapsed ? 'collapsed' : 'expanded');
                });
            } else {
                if (!sidebar) console.error("注入后未找到侧边栏元素用于切换设置。");
                if (!sidebarToggle) console.error("未找到侧边栏切换按钮用于切换设置。");
            }

            // ... (rest of the code remains unchanged)

            const mainTabButtons = document.querySelectorAll('.border-b > nav > .tab-btn');
            const mainTabContents = {
                 'overview': document.getElementById('overview'),
                 'details': document.getElementById('details'),
                 'attachments': document.getElementById('attachments'),
                 'appendix': document.getElementById('appendix') // 如果附录已移除，这里会是null
             };

            Object.values(mainTabContents).forEach((content, index) => {
                 if (content) {
                    // 初始时，只显示第一个有效的标签页内容
                    // 如果mainTabContents['overview']存在，它应该是第一个
                    if (content.id === 'overview' && mainTabContents['overview']) {
                        content.style.display = 'block';
                    } else if (index === 0 && !mainTabContents['overview']) { // 如果overview不存在，显示第一个可用的
                        content.style.display = 'block';
                    }
                    else {
                        content.style.display = 'none';
                    }
                 }
             });

             if (mainTabButtons.length > 0) {
                // 初始高亮第一个按钮
                // 查找第一个有效按钮并高亮
                let firstActiveButtonSet = false;
                mainTabButtons.forEach(btn => {
                    if (!firstActiveButtonSet && mainTabContents[btn.dataset.target]) {
                        btn.classList.add('text-blue-600', 'border-blue-600');
                        btn.classList.remove('text-gray-500', 'border-transparent');
                        firstActiveButtonSet = true;
                    } else {
                        btn.classList.remove('text-blue-600', 'border-blue-600');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    }
                });
             }

             mainTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    console.log('Main tab button clicked:', button.dataset.target); // 日志
                    const targetId = button.getAttribute('data-target');
                    mainTabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    });
                    Object.values(mainTabContents).forEach(content => {
                         if (content) content.style.display = 'none';
                     });
                    button.classList.add('text-blue-600', 'border-blue-600');
                    button.classList.remove('text-gray-500', 'border-transparent');

                    const activeContent = mainTabContents[targetId];
                    if (activeContent) {
                        activeContent.style.display = 'block';
                         if (targetId === 'details') {
                            console.log('Activating details tab, calling activateFirstSubTab.'); // 日志
                            // body.classList.remove('simple-mode'); // 不再需要模式切换
                            // body.classList.add('full-mode');    // 不再需要模式切换
                            activateFirstSubTab();
                         }
                     } else {
                        console.warn('Content for main tab', targetId, 'not found.'); // 日志
                     }
                });
            });

            const subTabButtons = document.querySelectorAll('#details .sub-tab-btn');
            const subTabContents = document.querySelectorAll('#details .sub-tab-content');
            const subTabNavContainer = document.querySelector('#details .border-b.border-gray-200.mb-6 nav');

            function activateFirstSubTab() {
                console.log('activateFirstSubTab function called.');

                if (!subTabNavContainer) {
                    console.error('Sub-tab navigation container not found!');
                    return;
                }

                // 总是显示子标签导航
                console.log('Always showing sub-tab nav.');
                subTabNavContainer.style.display = 'flex'; // 或者 'block'，取决于其原始布局

                 subTabButtons.forEach(btn => {
                     btn.classList.remove('text-blue-600', 'border-blue-600');
                     btn.classList.add('text-gray-500', 'border-transparent');
                 });
                 subTabContents.forEach(content => {
                     if (content) content.style.display = 'none';
                 });

                 // 默认激活第一个子标签页按钮 (不再关心 full-mode-only 等)
                 const firstButton = subTabButtons[0];

                 console.log('Sub-tab buttons found:', subTabButtons.length);
                 console.log('First sub-tab button to activate:', firstButton ? firstButton.dataset.subTab : 'None found');

                 if (firstButton) {
                     firstButton.classList.add('text-blue-600', 'border-blue-600');
                     firstButton.classList.remove('text-gray-500', 'border-transparent');
                     const firstTargetId = firstButton.getAttribute('data-sub-tab');
                     const firstContent = document.getElementById(firstTargetId);
                     if (firstContent) {
                         console.log('Activating first sub-tab:', firstTargetId);
                         firstContent.style.display = 'block';
                     } else {
                        console.warn('Content for sub-tab', firstTargetId, 'not found.');
                     }
                 } else {
                    console.warn('No sub-tab buttons found to activate.');
                 }
             }

             const initialActiveMainTab = document.querySelector('.tab-btn.text-blue-600');
             if (initialActiveMainTab && initialActiveMainTab.dataset.target === 'details') {
                 console.log('Initial active main tab is details, calling activateFirstSubTab.');
                 activateFirstSubTab();
             }

            subTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    console.log('Sub-tab button clicked:', button.dataset.subTab);
                    const targetId = button.getAttribute('data-sub-tab');

                     subTabButtons.forEach(btn => {
                        btn.classList.remove('text-blue-600', 'border-blue-600');
                        btn.classList.add('text-gray-500', 'border-transparent');
                    });
                     subTabContents.forEach(content => {
                        if (content) content.style.display = 'none';
                    });

                     button.classList.add('text-blue-600', 'border-blue-600');
                     button.classList.remove('text-gray-500', 'border-transparent');
                     const targetContent = document.getElementById(targetId);
                     if (targetContent) {
                        targetContent.style.display = 'block';
                    } else {
                        console.warn('Content for sub-tab', targetId, 'not found.');
                    }
                });
            });

            // const simpleModeBtn = document.getElementById('simple-mode'); // 删除
            // const fullModeBtn = document.getElementById('full-mode'); // 删除

            // function setViewMode(mode) { ... } // 删除整个setViewMode函数

            // Initial mode setting - 删除这部分逻辑
            // let savedMode;
            // if (!simpleModeBtn || !fullModeBtn) { ... }
            // else { ... }
            // console.log('Initial savedMode or forced mode:', savedMode);
            // setViewMode(savedMode);

            // 确保 body 上没有 simple-mode 或 full-mode，如果之前有添加的话
            body.classList.remove('simple-mode', 'full-mode');
            console.log('Removed simple-mode and full-mode from body. Current body classes:', body.className);

            // 再次确保，如果初始激活的是 details，子标签页正确显示 (这部分因为上面已经调用，可能冗余，但无害)
             if (mainTabContents['details'] && mainTabContents['details'].style.display === 'block') {
               console.log('Final check: Details active, calling activateFirstSubTab.');
               activateFirstSubTab();
            }
        });
    </script>
</body>
</html>