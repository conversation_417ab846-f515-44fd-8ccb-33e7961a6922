<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在建项目管理 - 应急管理系统</title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="css/tailwind.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/unified_header.css">
    <!-- 添加 Element Plus 样式 -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
         html, body {
            height: 100%;
            margin: 0;
        }
         body {
            font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
        }
        /* Add specific styles if needed */
         .el-tree-select, .el-select, .el-date-picker {
             width: 100% !important;
         }
          .el-upload-list__item, .el-upload--picture-card {
             width: 100px !important;
             height: 100px !important;
             line-height: 100px !important;
         }
         .el-select__wrapper {
            width: 110px;
         }
    </style>
</head>
<body id="projectsContent" class="bg-gray-100 flex flex-col min-h-screen">

    <!-- Navbar Start -->
    <!-- <header class="top-nav">
        <div class="system-title">
            <strong>广西公路水路安全畅通与应急处置系统</strong>
        </div>
        <nav class="tab-navigation">
            <a href="new_index.html" class="tab-button">风险一张图</a>
            <a href="my_check_tasks.html" class="tab-button active">风险隐患管理</a>
            <a href="plan_list.html" class="tab-button">应急处置</a>
            <button class="tab-button" onclick="alert('应急演练功能暂未开放');">应急演练</button>
        </nav>
    </header> -->
    <!-- Navbar End -->
     <!-- 顶部导航栏容器 -->
     <div id="navigation-container"></div>

    <!-- Flex Container for Sidebar and Main Content -->
    <div class="flex-container h-full" style="display: flex;">
        <!-- Sidebar Placeholder -->
        <div id="sidebar-placeholder"></div>

        <!-- Main Content Area -->
        <main id="main-content" class="flex-grow p-6 bg-gray-100 min-h-screen" style="width: 100%;">
            <div class="py-6" style="width: calc(100% - 240px);">
                <!-- 页面标题部分 -->
                <div class="flex justify-between items-center mb-6">
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800">在建项目管理</h2>
                        <p class="text-gray-600 mt-1">管理和查看在建项目的信息</p>
                    </div>
                    <div>
                        <button class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" @click="openAddModal()">
                            <i class="fas fa-plus mr-2"></i>添加项目
                        </button>
                    </div>
                </div>

                <!-- 项目列表 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">驻地名称</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">驻地类型</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">坐标点位</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">所属项目名称</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">建设单位</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">施工单位</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">驻地地址</th>
                                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50 project-row" v-for="(item, index) in projectsTable" :key="item.id" :data-project-id="`proj00${index}`">
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ index + 1 }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium text-gray-900">{{ item.residentName }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ getResidentTypeLabel(item.residentType) }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ item.coordinate }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ item.projectName }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ getProjectTypeLabel(item.projectType) }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ item.buildUnit }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ item.constructionUnit }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">{{ item.address }}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center text-center text-sm">
                                        <button class="text-blue-600 hover:text-blue-800 focus:outline-none mr-2" title="查看项目详情" @click="editProject('查看', item)">
                                            <i class="fas fa-eye mr-1"></i>查看
                                        </button>
                                        <button class="text-yellow-600 hover:text-yellow-800 focus:outline-none mr-2" title="编辑项目" @click="editProject('编辑', item)">
                                            <i class="fas fa-edit mr-1"></i>编辑
                                        </button>
                                        <button class="text-red-600 hover:text-red-800 focus:outline-none" title="删除项目" @click="deleteProject(item)">
                                            <i class="fas fa-trash mr-1"></i>删除
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- Pagination -->
                    <div class="bg-white px-4 py-3 flex items-center justify-end border-t border-gray-200 sm:px-6" style="text-align: right;">
                         <el-pagination
                            background
                            layout="sizes, total, prev, pager, next"
                            :total="total"
                            v-model:current-page="pageNum"
                            v-model:page-size="pageSize"
                            @size-change="getTable()"
                            @current-change="getTable()"
                        />
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 项目增/改 Modal (Simplified, needs to be developed) -->
    <div id="projectModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden" style="z-index: 9999;">
        <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="projectModalTitle">添加在建项目</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <form id="projectForm">
                    <input type="hidden" id="modalId" name="id">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="modalResidentName" class="block text-sm font-medium text-gray-700 mb-1">驻地名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalResidentName" name="residentName" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalResidentType" class="block text-sm font-medium text-gray-700 mb-1">驻地类型 <span class="text-red-500">*</span></label>
                            <select id="modalResidentType" name="residentType" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option v-for="item in residentTypeData" :key="item.dictValue" :value="item.dictValue">
                                    {{ item.dictLabel }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="modalCoordinate" class="block text-sm font-medium text-gray-700 mb-1">坐标点位 <span class="text-red-500">*</span></label>
                            <div style="display: flex;">
                                <input type="text" id="modalCoordinate" name="coordinate" placeholder="输入/填写驻地地址后获取" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <div id="getAddress" style="width: 50px; background-color: #409eff; color: white; border-radius: 5px; margin-left: 5px; text-align: center; line-height: 37px; cursor: pointer;">
                                    获取
                                </div>
                            </div>
                        </div>
                        <div>
                            <label for="modalProjectName" class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalProjectName" name="projectName" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalProjectType" class="block text-sm font-medium text-gray-700 mb-1">项目类型 <span class="text-red-500">*</span></label>
                            <select id="modalProjectType" name="projectType" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option v-for="item in projectTypeData" :key="item.dictValue" :value="item.dictValue">
                                    {{ item.dictLabel }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="modalBuildUnit" class="block text-sm font-medium text-gray-700 mb-1">建设单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalBuildUnit" name="buildUnit" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalConstructionUnit" class="block text-sm font-medium text-gray-700 mb-1">施工单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalConstructionUnit" name="constructionUnit" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalAddress" class="block text-sm font-medium text-gray-700 mb-1">驻地地址 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalAddress" name="address" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalArea" class="block text-sm font-medium text-gray-700 mb-1">行政区域 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalArea" name="area" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalResidentsNum" class="block text-sm font-medium text-gray-700 mb-1">驻地人数 <span class="text-red-500">*</span></label>
                            <input type="number" id="modalResidentsNum" name="residentsNum" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                        </div>
                        <div>
                            <label for="modalRiskLevel" class="block text-sm font-medium text-gray-700 mb-1">驻地风险 <span class="text-red-500">*</span></label>
                            <select id="modalRiskLevel" name="riskLevel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option v-for="item in riskLeveleData" :key="item.dictValue" :value="item.dictValue">
                                    {{ item.dictLabel }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="modalRoomType" class="block text-sm font-medium text-gray-700 mb-1">房建类型 <span class="text-red-500">*</span></label>
                            <select id="modalRoomType" name="roomType" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option v-for="item in roomTypeData" :key="item.dictValue" :value="item.dictValue">
                                    {{ item.dictLabel }}
                                </option>
                            </select>
                        </div>
                        <div>
                            <label for="modalHeadInv" class="block text-sm font-medium text-gray-700 mb-1">主管部门是否排查 <span class="text-red-500">*</span></label>
                            <select id="modalHeadInv" name="headInv" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalIsCliff" class="block text-sm font-medium text-gray-700 mb-1">是否属于临水、临崖、涉洪区域 <span class="text-red-500">*</span></label>
                            <select id="modalIsCliff" name="isCliff" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalIsCollapse" class="block text-sm font-medium text-gray-700 mb-1">是否属于易垮塌区域 <span class="text-red-500">*</span></label>
                            <select id="modalIsCollapse" name="isCollapse" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalIsRelocate" class="block text-sm font-medium text-gray-700 mb-1">是否搬迁 <span class="text-red-500">*</span></label>
                            <select id="modalIsRelocate" name="isRelocate" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" required>
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalWhistling" class="block text-sm font-medium text-gray-700 mb-1">吹哨人</label>
                            <input type="text" id="modalWhistling" name="whistling" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalWhistlingTel" class="block text-sm font-medium text-gray-700 mb-1">吹哨人联系电话</label>
                            <input type="text" id="modalWhistlingTel" name="whistlingTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalBuilder" class="block text-sm font-medium text-gray-700 mb-1">建设单位包保责任人</label>
                            <input type="text" id="modalBuilder" name="builder" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalBuilderTel" class="block text-sm font-medium text-gray-700 mb-1">建设单位包保责任人联系电话</label>
                            <input type="text" id="modalBuilderTel" name="builderTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalConstruc" class="block text-sm font-medium text-gray-700 mb-1">施工单位包保责任人</label>
                            <input type="text" id="modalConstruc" name="construc" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalConstrucTel" class="block text-sm font-medium text-gray-700 mb-1">施工单位包保责任人联系电话</label>
                            <input type="text" id="modalConstrucTel" name="construcTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalAddresser" class="block text-sm font-medium text-gray-700 mb-1">驻地现场包保责任人</label>
                            <input type="text" id="modalAddresser" name="addresser" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalAddresserTel" class="block text-sm font-medium text-gray-700 mb-1">驻地现场包保责任人联系电话</label>
                            <input type="text" id="modalAddresserTel" name="addresserTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalCountyer" class="block text-sm font-medium text-gray-700 mb-1">县级包保联系人</label>
                            <input type="text" id="modalCountyer" name="countyer" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalCountyerTel" class="block text-sm font-medium text-gray-700 mb-1">县级包保联系人联系电话</label>
                            <input type="text" id="modalCountyerTel" name="countyerTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalMarketer" class="block text-sm font-medium text-gray-700 mb-1">市级包保联系人</label>
                            <input type="text" id="modalMarketer" name="marketer" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalMarketerTel" class="block text-sm font-medium text-gray-700 mb-1">市级包保联系人联系电话</label>
                            <input type="text" id="modalMarketerTel" name="marketerTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalProvincer" class="block text-sm font-medium text-gray-700 mb-1">省级包保联系人</label>
                            <input type="text" id="modalProvincer" name="provincer" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="modalProvincerTel" class="block text-sm font-medium text-gray-700 mb-1">省级包保联系人联系电话</label>
                            <input type="text" id="modalProvincerTel" name="provincerTel" :disabled="operateType == '查看' ? true : false" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        </div>
                    </div>
                    <!-- <div class="mt-4">
                        <label for="modalProjectDescription" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="modalProjectDescription" name="project_description" rows="3" class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
                    </div> -->
                </form>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 mr-2 btn-close-modal">
                  取消
                </button>
                <button id="btnSaveProject" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2" v-if="operateType != '查看'">
                  保存项目
                </button>
            </div>
        </div>
    </div>

    <!-- 查看项目详情 Modal -->
    <div id="viewProjectModal" class="fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 flex items-center justify-center hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">查看在建项目详情</h3>
                <button class="text-gray-400 hover:text-gray-500 focus:outline-none btn-close-modal" data-modal-id="viewProjectModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="px-6 py-4 max-h-[70vh] overflow-y-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-4 pb-4 border-b">
                    <div><strong class="text-gray-600">项目名称:</strong> <span id="view-projectName" class="text-gray-800"></span></div>
                    <div><strong class="text-gray-600">施工单位:</strong> <span id="view-constructionUnit" class="text-gray-800"></span></div>
                    <div><strong class="text-gray-600">项目负责人:</strong> <span id="view-projectManager" class="text-gray-800"></span></div>
                    <div><strong class="text-gray-600">联系电话:</strong> <span id="view-contactPhone" class="text-gray-800"></span></div>
                    <div><strong class="text-gray-600">开工日期:</strong> <span id="view-startDate" class="text-gray-800"></span></div>
                    <div><strong class="text-gray-600">预计完工日期:</strong> <span id="view-endDate" class="text-gray-800"></span></div>
                    <div><strong class="text-gray-600">项目状态:</strong> <span id="view-projectStatus" class="text-gray-800"></span></div>
                </div>
                 <div class="mb-4">
                    <strong class="text-gray-600 block mb-1">项目描述:</strong>
                    <p id="view-projectDescription" class="text-gray-800 bg-gray-50 p-3 rounded min-h-[60px]"></p>
                 </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end bg-gray-50 rounded-b-lg">
                <button class="bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 btn-close-modal" data-modal-id="viewProjectModal">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- Load Libraries (Vue and Element Plus might not be strictly needed for this basic version, but kept for consistency) -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus"></script>
    <script src="https://unpkg.com/element-plus/dist/locale/zh-cn.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="./request/index.js"></script>
    <!-- Page Specific Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- Modal Control --- //
            const openModal = (modal) => {
                if(modal) modal.classList.remove('hidden');
            }
            const closeModal = (modal) => {
                if(modal) modal.classList.add('hidden');
            }

            const modalOrgApp = Vue.createApp({
                data() {
                    return {
                        residentTypeData: [],
                        projectTypeData: [],
                        riskLeveleData: [],
                        roomTypeData: [],

                        projectsTable: [],
                        pageNum: 1,
                        pageSize: 10,
                        total: 0,

                        operateType: ''
                    }
                },
                mounted() {
                    window.Http.get('/system/dict/data/type/resident_type')
                    .then(res => {
                        this.residentTypeData = res.data
                    })

                    window.Http.get('/system/dict/data/type/project_type')
                    .then(res => {
                        this.projectTypeData = res.data
                    })

                    window.Http.get('/system/dict/data/type/risk_level')
                    .then(res => {
                        this.riskLeveleData = res.data
                    })

                    window.Http.get('/system/dict/data/type/room_type')
                    .then(res => {
                        this.roomTypeData = res.data
                    })

                    this.getTable()
                },
                methods: {
                    getTable() {
                        window.Http.get('/risk/projects/list', {
                            pageNum: this.pageNum,
                            pageSize: this.pageSize,
                        })
                        .then(res => {
                            this.projectsTable = res.rows
                            this.total = res.total
                        })
                    },
                    handleSubmit(data) {
                        if(this.operateType == '新增') {
                            delete data.id
                            window.Http.post('/risk/projects/add', data).then(res => {
                                this.getTable()
                            })
                        } else if(this.operateType == '编辑') {
                            window.Http.post('/risk/projects/edit', data).then(res => {
                                this.getTable()
                            })
                        }
                    },
                    openAddModal() {
                        const projectModal = document.getElementById('projectModal');
                        const projectModalTitle = document.getElementById('projectModalTitle');
                        const projectForm = document.getElementById('projectForm');
                        projectModalTitle.textContent = '添加在建项目';
                        projectForm.reset();
                        this.operateType = '新增'
                        openModal(projectModal);
                    },
                    editProject(type, item) {
                        const projectModal = document.getElementById('projectModal');
                        const projectModalTitle = document.getElementById('projectModalTitle');
                        const projectForm = document.getElementById('projectForm');
                        projectModalTitle.textContent = type + '在建项目'
                        projectForm.reset()
                        for (const key in item) {
                            const keyName = key.charAt(0).toUpperCase() + key.slice(1)
                            if(document.getElementById(`modal${keyName}`))  {
                                document.getElementById(`modal${keyName}`).value = item[key]                                
                            }
                        }
                        this.operateType = type
                        openModal(projectModal);
                    },
                    deleteProject(item) {
                        if(confirm(`确认删除项目${item.projectName}?`)) {
                            window.Http.post('/risk/projects/remove', { id: item.id }).then(res => {
                                this.getTable()
                            })
                        }
                    },
                    getResidentTypeLabel(val) {
                        return this.residentTypeData.find(item => item.dictValue == val)?.dictLabel || ''
                    },
                    getProjectTypeLabel(val) {
                        return this.projectTypeData.find(item => item.dictValue == val)?.dictLabel || ''
                    }
                }
            }).use(ElementPlus, {
                locale: ElementPlusLocaleZhCn
            });
            modalOrgApp.mount('#projectsContent');

            const projectModal = document.getElementById('projectModal');
            const projectForm = document.getElementById('projectForm');
            const projectModalTitle = document.getElementById('projectModalTitle');
            const modalProjectIdInput = document.getElementById('modalProjectId');
            const viewProjectModal = document.getElementById('viewProjectModal'); // Get reference to the new modal
            let editingProjectId = null;

            // Close modal buttons - updated to handle multiple modals
            document.querySelectorAll('.btn-close-modal').forEach(button => {
                button.addEventListener('click', () => {
                    const modalToClose = button.closest('.fixed.inset-0'); // Find the parent modal
                    if (modalToClose) {
                        closeModal(modalToClose);
                    }
                });
            });

            // "Add Project" button in main content area
            document.querySelector('.btn-add-project')?.addEventListener('click', () => {
                editingProjectId = null; // Clear editing ID
                projectModalTitle.textContent = '添加在建项目';
                projectForm.reset();
                modalProjectIdInput.value = ''; // Clear hidden project ID
                openModal(projectModal);
            });


            // --- Event Listeners for Project List Buttons --- //
            document.querySelector('#main-content tbody').addEventListener('click', (event) => {
                const button = event.target.closest('button');
                if (!button) return;

                const row = button.closest('tr.project-row');
                const projectId = row ? row.dataset.projectId : null;

                if (button.classList.contains('btn-view-project')) {
                    if (projectId) {
                        populateAndShowViewProjectModal(projectId, row); // Pass the row for data extraction
                    }
                } else if (button.classList.contains('btn-edit-project')) {
                    if (projectId) {
                        editingProjectId = projectId;
                        projectModalTitle.textContent = '编辑在建项目';
                        projectForm.reset(); // Clear form first
                        modalProjectIdInput.value = projectId;

                        // TODO: Populate form with actual project data (fetch from server or use mock data)
                        // For now, simulate populating with some data from the row
                        const cells = row.querySelectorAll('td');
                        document.getElementById('modalProjectName').value = cells[1].textContent;
                        document.getElementById('modalConstructionUnit').value = cells[2].textContent;
                        document.getElementById('modalProjectManager').value = cells[3].textContent;
                        document.getElementById('modalStartDate').value = cells[4].textContent; // This will need to be in YYYY-MM-DD format
                        document.getElementById('modalEndDate').value = cells[5].textContent; // Same here
                        // For status, you'd need to map the text to the select value
                        const statusText = cells[6].querySelector('span').textContent;
                        if (statusText === '在建') document.getElementById('modalProjectStatus').value = 'in_progress';
                        else if (statusText === '已完工') document.getElementById('modalProjectStatus').value = 'completed';
                        // ... add more fields as necessary

                        openModal(projectModal);
                    }
                } else if (button.classList.contains('btn-delete-project')) {
                    if (projectId) {
                        if (confirm(`确认删除项目 (ID: ${projectId}) 吗？`)) {
                            console.log(`Deleting project: ${projectId}`);
                            // TODO: Implement API call to delete project
                            alert(`项目 (ID: ${projectId}) 已删除 (模拟)`);
                            row.remove(); // Remove row from table (visual feedback)
                        }
                    }
                }
            });

            document.getElementById('getAddress')?.addEventListener('click', () => { 
                const address = document.getElementById('modalAddress').value;

                if(!address) {
                    alert('请先输入驻地地址')
                } else {
                    window.Http.post(`/map/getLonAndLatByAddress?address=${address}`)
                    .then(res => {
                        document.getElementById('modalCoordinate').value = `${res.data.lng},${res.data.lat}`;
                    })
                }
            });

            // --- Modal Form Handling --- //
            document.getElementById('btnSaveProject')?.addEventListener('click', () => { 
                 let isValid = true;
                 projectForm.querySelectorAll('[required]').forEach(el => {
                     if (!el.value) {
                         isValid = false;
                         el.classList.add('border-red-500');
                         el.addEventListener('input', () => el.classList.remove('border-red-500'), { once: true });
                     }
                 });
                 if (!isValid) {
                     alert('请填写所有必填项！');
                     return;
                 }

                const formData = new FormData(projectForm);
                const data = Object.fromEntries(formData.entries());

                if (editingProjectId) { // If editing existing project
                    data.id = editingProjectId;
                    console.log('更新在建项目 (ID:', editingProjectId, ') 数据:', data);
                    // TODO: Send PUT/PATCH request to update project data
                    alert(`项目 (ID: ${editingProjectId}) 已更新 (模拟)`);
                } else { // If adding new project
                    // console.log('提交新在建项目数据:', data);
                    // 创建vue实例
                    const vueApp = modalOrgApp._instance.proxy
                    // 调用vue里面的方法
                    vueApp.handleSubmit(data)
                }

                closeModal(projectModal);
            });

            // --- Function to Populate and Show View Project Modal ---
            function populateAndShowViewProjectModal(projectId, projectRow) {
                // Simulate fetching data based on projectId or use data from the row
                // For now, using data from the passed row
                const cells = projectRow.querySelectorAll('td');

                document.getElementById('view-projectName').textContent = cells[1]?.textContent || 'N/A';
                document.getElementById('view-constructionUnit').textContent = cells[2]?.textContent || 'N/A';
                document.getElementById('view-projectManager').textContent = cells[3]?.textContent || 'N/A';
                // Assuming contact phone is not directly in the table, would need actual data source
                document.getElementById('view-contactPhone').textContent = 'N/A'; // Placeholder
                document.getElementById('view-startDate').textContent = cells[4]?.textContent || 'N/A';
                document.getElementById('view-endDate').textContent = cells[5]?.textContent || 'N/A';

                const statusSpan = cells[6]?.querySelector('span');
                document.getElementById('view-projectStatus').textContent = statusSpan?.textContent || 'N/A';
                if (statusSpan) {
                     // Optional: copy badge styles if needed, or just text
                    document.getElementById('view-projectStatus').className = statusSpan.className + ' px-2 inline-flex text-xs leading-5 font-semibold rounded-full';
                }


                // Assuming project description is not in the table, would need actual data source
                document.getElementById('view-projectDescription').textContent = '此项目的详细描述信息将会在这里展示。目前为模拟数据。'; // Placeholder

                openModal(viewProjectModal);
            }

        });
    </script>

    <!-- Load component HTML first -->
    <script src="js/navbarComponent_risk.js"></script>
    <script src="js/sidebarComponent_risk.js"></script>
    <!-- Then load the script to inject them and highlight links -->
    <script src="js/loadComponents.js"></script>

    <!-- 引入导航栏组件 -->
    <script src="js/navigation-component.js"></script>
    <script>
        // 初始化导航栏
        window.NavigationComponent.init('system-management');
    </script>

</body>
</html>